# 第三方Token静默登录方案设计文档

## 1. 需求背景

### 1.1 业务场景
- 需要集成第三方系统，实现用户在第三方系统登录后静默登录我方系统
- 两边系统使用不同的Token体系
- 第三方系统提供用户信息查询接口，通过第三方Token获取用户基础信息

### 1.2 技术要求
- 基于现有UAA服务的登录策略模式进行扩展
- 支持多种客户端类型的静默登录
- 不自动创建用户，仅支持已存在用户的登录

## 2. 方案设计

### 2.1 整体架构
```
第三方系统 → 携带第三方Token → 我方UAA服务 → 验证Token → 获取用户信息 → 用户匹配 → 权限加载 → 返回我方Token
```

### 2.2 登录策略
- **Grant Type**: `third_party_token`
- **认证提供者**: `ThirdPartyTokenAuthenticationProvider`
- **集成方式**: 基于现有策略模式，新增第三方Token认证策略

## 3. 接口设计

### 3.1 请求参数
```json
{
  "grantType": "third_party_token",
  "thirdPartyToken": "第三方系统的JWT token",
  "clientType": "PLATFORM_CONSOLE|SHOP_CONSOLE|SUPPLIER_CONSOLE",
  "enterpriseGuid": "企业ID（放在header头里面处理了）",
  "operSubjectGuid": "租户ID(对应tenantId)"
}
```

### 3.2 响应格式
**成功响应**:
```json
{
  "accessToken": "我方系统生成的token",
  "tokenType": "Bearer",
  "expiresIn": 7200,
  "userInfo": {
    "userId": 123,
    "username": "用户名",
    "mobile": "手机号",
    "platformId": 456
  }
}
```

**失败响应**:
```json
{
  "error": "user_not_found",
  "errorDescription": "用户不存在，请联系管理员"
}
```

## 4. 核心流程

### 4.1 流程图
```mermaid
flowchart TD
    A[第三方系统用户] --> B[携带第三方JWT Token请求登录]
    B --> C{参数验证}
    
    C -->|参数缺失| D[返回参数错误]
    C -->|参数完整| E[解析JWT Token]
    
    E --> F{JWT Token解析}
    F -->|解析失败| G[返回Token无效错误]
    F -->|Token过期| H[返回Token过期错误]
    F -->|解析成功| I[提取用户信息]
    
    I --> J[获取关键信息]
    J --> K[手机号(preferred_username) + 租户ID(operSubjectGuid)]
    
    K --> L[用户匹配查询]
    L --> M{用户是否存在}
    
    M -->|不存在| N[返回用户不存在错误<br/>请联系管理员]
    M -->|存在| O[检查用户状态]
    
    O --> P{用户状态正常?}
    P -->|异常| Q[返回用户状态异常]
    P -->|正常| R[验证客户端类型]
    
    R --> S{客户端类型合法?}
    S -->|不合法| T[返回客户端类型错误]
    S -->|合法| U[加载用户权限]
    
    U --> V[ReloadUserProvider<br/>加载完整权限信息]
    V --> W[生成SecureUser对象]
    W --> X[生成我方系统Token]
    X --> Y[返回登录成功]
```

### 4.2 详细步骤

#### 步骤1: 参数验证
- 验证`grantType`为`third_party_token`
- 验证`thirdPartyToken`不为空
- 验证`clientType`在允许范围内
- 验证`enterpriseGuid`和`operSubjectGuid`不为空

#### 步骤2: JWT Token解析
- 使用Hutool工具解析JWT Token
- 提取`preferred_username`字段作为手机号
- 验证Token是否过期
- 处理Token解析异常

#### 步骤3: 用户匹配（策略B）
- **匹配条件**: `mobile = preferred_username AND platform_id = operSubjectGuid`
- **租户映射**: operSubjectGuid直接映射为我方platformId
- **不自动创建**: 用户不存在时返回错误

#### 步骤4: 权限加载
- 使用传入的`clientType`
- 调用现有的`ReloadUserProvider`加载权限
- 生成完整的`SecureUser`对象

## 5. 技术实现

### 5.1 核心组件

#### 5.1.1 JWT Token解析服务
```java
public interface JwtTokenService {
    /**
     * 解析第三方JWT Token获取用户信息
     */
    ThirdPartyUserInfo parseJwtToken(String jwtToken);
}

public class ThirdPartyUserInfo {
    private String mobile;          // 手机号(来自preferred_username)
    private String tenantId;        // 租户ID(来自operSubjectGuid)
    private String enterpriseId;    // 企业ID(来自enterpriseGuid)
    private String username;        // 用户名
    // 其他JWT中的字段...
}
```

#### 5.1.2 认证提供者
```java
@Component
@GrantType("third_party_token")
public class ThirdPartyTokenAuthenticationProvider 
    implements IAuthenticationProvider<ThirdPartyTokenRequest> {
    
    // 实现authenticate方法
}
```

#### 5.1.3 请求对象
```java
public class ThirdPartyTokenRequest extends AuthenticationRequest {
    @NotBlank
    private String thirdPartyToken;
    
    @NotNull
    private ClientType clientType;
    
    @NotBlank
    private String enterpriseGuid;
    
    @NotBlank
    private String operSubjectGuid;
}
```

### 5.2 支持的客户端类型
- `PLATFORM_CONSOLE` - 平台端
- `SHOP_CONSOLE` - 商家端
- `SUPPLIER_CONSOLE` - 供应商端

### 5.3 错误处理

#### 5.3.1 错误码定义
```java
public enum ThirdPartyAuthError {
    INVALID_JWT_TOKEN("JWT Token无效或格式错误"),
    JWT_TOKEN_EXPIRED("JWT Token已过期"),
    JWT_PARSE_ERROR("JWT Token解析失败"),
    USER_NOT_FOUND("用户不存在，请联系管理员"),
    INVALID_CLIENT_TYPE("不支持的客户端类型"),
    USER_STATUS_ABNORMAL("用户状态异常，无法登录"),
    MISSING_REQUIRED_FIELD("JWT Token缺少必要字段");
}
```

#### 5.3.2 异常处理策略
- **JWT Token无效**: 返回401 Unauthorized
- **JWT Token过期**: 返回401，提示"Token已过期"
- **JWT解析失败**: 返回400，提示"Token格式错误"
- **用户不存在**: 返回404，明确提示"用户不存在，请联系管理员"
- **缺少必要字段**: 返回400，提示"Token缺少必要信息"
- **参数错误**: 返回400 Bad Request

## 6. 配置管理

### 6.1 配置项
```yaml
third-party:
  auth:
    enabled: true                    # 是否启用第三方登录
    jwt-secret: "jwt签名密钥"        # JWT验证密钥(如果需要验证签名)
    jwt-issuer: "预期的JWT签发者"    # JWT签发者验证(可选)
    allowed-client-types:            # 允许的客户端类型
      - PLATFORM_CONSOLE
      - SHOP_CONSOLE
      - SUPPLIER_CONSOLE
```

### 6.2 granter.json更新
```json
{
  "grantType": "third_party_token",
  "thirdPartyToken": "第三方系统JWT Token",
  "clientType": "客户端类型(PLATFORM_CONSOLE/SHOP_CONSOLE/SUPPLIER_CONSOLE)",
  "enterpriseGuid": "企业ID",
  "operSubjectGuid": "租户ID"
}
```

## 7. 安全性考虑

### 7.1 Token安全
- 每次都调用第三方接口验证Token有效性
- 不在本地缓存第三方Token信息
- 支持Token过期处理

### 7.2 用户验证
- 严格的用户匹配策略（手机号+租户ID）
- 用户状态检查
- 客户端类型权限验证

### 7.3 接口安全
- 接口调用超时控制
- 重试机制
- 异常日志记录

## 8. 日志记录

### 8.1 关键节点日志
```java
log.info("第三方Token登录开始，clientType: {}", clientType);
log.info("开始解析JWT Token，token: {}", maskToken(token));
log.info("JWT Token解析成功，mobile: {}, tenantId: {}", mobile, tenantId);
log.info("用户匹配成功，userId: {}, platformId: {}", user.getId(), user.getPlatformId());
log.info("第三方Token登录成功，userId: {}", user.getId());
```

### 8.2 异常日志
- JWT Token解析失败
- 用户匹配失败
- 权限加载异常

## 9. 测试计划

### 9.1 单元测试
- 参数验证测试
- JWT Token解析测试
- 用户匹配逻辑测试
- 权限加载测试

### 9.2 集成测试
- 完整登录流程测试
- 异常场景测试
- 不同客户端类型测试

### 9.3 性能测试
- JWT Token解析性能
- 并发登录测试

## 10. 部署说明

### 10.1 配置更新
- 更新application.yml配置
- 配置JWT验证相关参数
- 设置允许的客户端类型

### 10.2 依赖检查
- 确保Hutool JWT工具可用
- 验证JWT解析功能
- 检查用户数据完整性

## 11. 讨论记录

### 11.1 需求确认 (2025年6月16日)
**用户匹配策略确认**:
- ✅ 采用策略B：基于手机号+租户ID匹配
- ✅ 不自动创建不存在的用户
- ✅ 租户映射：第三方租户ID与我方platformId一致
- ✅ 客户端类型：支持平台端、商家端、供应商端

**JWT Token处理确认**:
- ✅ 第三方Token为JWT格式，可直接解析
- ✅ 使用Hutool工具解析JWT Token
- ✅ 从preferred_username字段获取手机号
- ✅ 请求参数使用驼峰命名：thirdPartyToken、clientType、enterpriseGuid、operSubjectGuid

**技术方案确认**:
- ✅ 基于现有UAA登录策略模式扩展
- ✅ 新增`third_party_token` Grant Type
- ✅ 集成现有的`ReloadUserProvider`权限加载机制
- ✅ 错误处理：明确提示"用户不存在，请联系管理员"

### 11.2 待讨论问题
- [ ] JWT Token的签名验证是否需要
- [ ] JWT Token中还有哪些字段需要使用
- [ ] 是否需要IP白名单限制
- [ ] 缓存策略（如用户信息缓存）
- [ ] 监控和告警机制
- [ ] JWT Token过期时间处理策略

---

**文档版本**: v1.0  
**创建时间**: 2025年6月16日  
**最后更新**: 2025年6月16日  
**状态**: 设计阶段 