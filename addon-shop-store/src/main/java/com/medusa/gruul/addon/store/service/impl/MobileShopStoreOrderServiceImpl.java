package com.medusa.gruul.addon.store.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.addon.store.constant.ShopStoreConstant;
import com.medusa.gruul.addon.store.model.dto.StoreOrderStockUpDTO;
import com.medusa.gruul.addon.store.model.vo.ShopStoreOrderInfoVO;
import com.medusa.gruul.addon.store.model.vo.ShopStoreTransactionSummaryVO;
import com.medusa.gruul.addon.store.mp.entity.ShopStore;
import com.medusa.gruul.addon.store.mp.entity.ShopStoreOrder;
import com.medusa.gruul.addon.store.mp.service.IShopStoreOrderService;
import com.medusa.gruul.addon.store.mp.service.IShopStoreService;
import com.medusa.gruul.addon.store.service.MobileShopStoreOrderService;
import com.medusa.gruul.common.member.dto.MessagesSendDTO;
import com.medusa.gruul.common.member.enums.WechatMsgSendTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderReceiver;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.PackageStatus;
import com.medusa.gruul.order.api.model.OrderPackageKeyDTO;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.user.api.model.UserDataVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 移动端店铺门店订单服务实现层
 *
 * <AUTHOR>
 * @Description 移动端店铺门店订单服务实现层
 * @date 2023-03-16 17:56
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MobileShopStoreOrderServiceImpl implements MobileShopStoreOrderService {
    private final IShopStoreOrderService shopStoreOrderService;
    private final OrderRpcService orderRpcService;
    private final IShopStoreService shopStoreService;
    private final MemberApiService memberApiService;
    private final UserRpcService userRpcService;

    @Override
    public ShopStoreOrderInfoVO getStoreOrderCodeByOrderNo(Long storeId, String oderNo) {
        ShopStoreOrder shopStoreOrder = shopStoreOrderService.lambdaQuery()
                .select(ShopStoreOrder::getCode, ShopStoreOrder::getPickUpTime)
                .eq(ShopStoreOrder::getOrderNo, oderNo)
                .eq(ShopStoreOrder::getShopStoreId, storeId)
                .ne(ShopStoreOrder::getPackageStatus, PackageStatus.WAITING_FOR_DELIVER).one();

        ShopStore shopStore = shopStoreService.lambdaQuery()
                .select(ShopStore::getStoreName, ShopStore::getFunctionaryPhone,
                        ShopStore::getDetailedAddress).
                eq(BaseEntity::getId, storeId).one();

        if (shopStoreOrder == null || shopStore == null) {
            throw new GlobalException("当前门店或门店订单异常");
        }
        // 设置 ShopStoreOrderInfoVO
        return new ShopStoreOrderInfoVO().setShopStore(shopStore).setCode(shopStoreOrder.getCode()).setGetPickUpTime(shopStoreOrder.getPickUpTime());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void storeOrderProceedStockUp(StoreOrderStockUpDTO storeOrderStockUp) {
        Long shopId = ISecurity.userMust().getShopId();
        Set<String> orderNos = storeOrderStockUp.getOrderNos();
        Long storeId = storeOrderStockUp.getShopStoreId();
        Long count = shopStoreOrderService.lambdaQuery()
                .eq(ShopStoreOrder::getShopId, shopId)
                .eq(ShopStoreOrder::getPackageStatus, PackageStatus.WAITING_FOR_DELIVER)
                .eq(ShopStoreOrder::getShopStoreId, storeId)
                .in(ShopStoreOrder::getOrderNo, orderNos)
                .count();

        if (count != orderNos.size()) {
            throw new GlobalException("当前备货订单已发生变化,请刷新页面重试");
        }

        Set<String> setCodes = new HashSet<>(storeOrderStockUp.getOrderNos());
        storeOrderStockUp.getOrderNos()
                .forEach(bean -> {
                    String code;
                    // 生成当前店铺下不重复的核销码
                    do {
                        code = RandomUtil.randomNumbers(CommonPool.NUMBER_TEN);
                        Long quantity = shopStoreOrderService.lambdaQuery().eq(ShopStoreOrder::getCode, code).eq(ShopStoreOrder::getShopId, shopId).count();
                        if (quantity == 0L && !setCodes.contains(code)) {
                            break;
                        }
                    } while (true);
                    setCodes.add(code);
                    //修改门店店铺订单
                    boolean update = shopStoreOrderService.lambdaUpdate()
                            .set(ShopStoreOrder::getCode, code)
                            .set(ShopStoreOrder::getPackageStatus, PackageStatus.WAITING_FOR_RECEIVE)
                            .eq(ShopStoreOrder::getShopId, shopId)
                            .eq(ShopStoreOrder::getPackageStatus, PackageStatus.WAITING_FOR_DELIVER)
                            .eq(ShopStoreOrder::getShopStoreId, storeId)
                            .eq(ShopStoreOrder::getOrderNo, bean)
                            .update();

                    if (!update) {
                        throw new GlobalException("门店订单备货失败");
                    }
                    //发送取货提醒
                    try {
                        orderStockUpMessageSend(bean, storeOrderStockUp.getShopId());
                    } catch (Exception e) {
                        log.info("门店订单备货完成发送取货提醒失败,订单号:{}", bean, e);
                    }
                });
        // 修改订单状态
        orderRpcService.batchStoreOrderDeliver(orderNos, storeId);

    }

    private void orderStockUpMessageSend(String bean, Long storeId) {
        log.info("门店订单备货完成发送取货提醒,订单号:{},店铺id:{}", bean, storeId);

        // 获取门店订单信息
        ShopStoreOrder storeOrder = shopStoreOrderService.lambdaQuery()
                .eq(ShopStoreOrder::getOrderNo, bean)
                .eq(ShopStoreOrder::getShopId, storeId)
                .one();
        if (storeOrder == null) {
            log.info("未找到对应的门店订单, 订单号:{}, 店铺id:{}", bean, storeId);
        }

        // 获取店铺订单信息
        ShopOrder targetShopOrder = orderRpcService.getShopOrderList(bean).stream()
                .filter(shopOrder -> shopOrder.getShopId().equals(storeId))
                .findFirst()
                .orElse(null);
        if (targetShopOrder == null) {
            log.info("未找到对应的店铺订单, 订单号:{}, 店铺id:{}", bean, storeId);
        }

        List<ShopOrderItem> shopOrderItems = getOrderItems(bean, storeId);
        if (shopOrderItems.isEmpty()) {
            log.info("未找到对应的订单商品项, 订单号:{}, 店铺id:{}", bean, storeId);
        }

        MessagesSendDTO messagesSendQO = new MessagesSendDTO();
        StringBuilder messageItem = new StringBuilder();
        for (ShopOrderItem item : shopOrderItems) {
            messageItem.append(item.getProductName()).append("*").append(item.getNum()).append(";");
        }

        // 获取门店信息
        ShopStore shopStore = shopStoreService.getById(storeOrder.getShopStoreId());
        if (shopStore == null) {
            log.info("未找到对应的门店信息, 订单号:{}, 门店id:{}", bean, storeOrder.getShopStoreId());
        }

        messagesSendQO.setTemplateName(WechatMsgSendTypeEnum.ORDER_PICKUP.getMsgTitle());
        messagesSendQO.setPrams(generateStoreOrderDeliverParam(
                messageItem.toString(),
                shopStore.getStoreName(),
                formatPickUpTime(storeOrder.getPickUpTime()),
                storeOrder.getCode(),
                "您的商品已备好，请来取走吧~"));

        // 获取订单信息
        Order order = orderRpcService.getOrder(bean);
        if (Objects.isNull(order)) {
            log.info("订单号:{}不存在", bean);
        }

        // 使用UserRpcService获取用户信息
        UserDataVO userData = userRpcService.userData(order.getBuyerId());
        if (Objects.isNull(userData)) {
            log.info("用户信息不存在, 用户ID:{}", order.getBuyerId());
        }

        messagesSendQO.setOrderNum(Strings.EMPTY);
        messagesSendQO.setPhone(userData.getMobile());
        messagesSendQO.setOperSubjectGuid(ISystem.platformIdMust().toString());
        messagesSendQO.setMemberName(userData.getNickname());
        log.info("门店订单备货完成发送取货提醒,订单号:{},店铺id:{},消息参数:{}", bean, storeId, messagesSendQO.getPrams());
        memberApiService.wechatMessageSendBatch(messagesSendQO);
    }

    /**
     * 获取订单商品项
     *
     * @param orderNo 订单号
     * @param storeId 门店ID
     * @return 订单商品项列表
     */
    private List<ShopOrderItem> getOrderItems(String orderNo, Long storeId) {
        try {
            // 1. 获取订单商品项
            List<ShopOrderItem> allItems = Option.of(orderRpcService.getShopOrderItemList(orderNo))
                    .getOrElse(Collections.emptyList());

            log.info("获取订单商品项,订单号:{},商品项数量:{}", orderNo, allItems.size());

            if (allItems.isEmpty()) {
                throw new GlobalException("门店订单备货失败");
            }

            // 2. 过滤出指定门店的商品项
            return allItems.stream()
                    .filter(item -> {
                        if (item == null) {
                            log.warn("订单商品项为空,订单号:{}", orderNo);
                            return false;
                        }
                        if (item.getShopId() == null) {
                            log.warn("订单商品项店铺ID为空,订单号:{},商品项ID:{}", orderNo, item.getId());
                            return false;
                        }
                        return item.getShopId().equals(storeId);
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取订单商品项异常,订单号:{},门店ID:{}", orderNo, storeId, e);
            return Collections.emptyList();
        }
    }

    private Map<String, String> generateStoreOrderDeliverParam(String thing1,
                                                               String thing9,
                                                               String date3,
                                                               String character_string2,
                                                               String thing8) {
        log.info("生成门店订单备货完成发送取货提醒参数,商品名称:{},门店名称:{},取货时段:{},取货码:{},温馨提示:{}", thing1, thing9, date3, character_string2, thing8);
        Map<String, String> params = new HashMap<>();
        //商品名称
        params.put("thing1", thing1);
        //门店名称
        params.put("thing9", thing9);
        //取货时段
        params.put("date3", date3);
        //取货码
        params.put("character_string2", character_string2);
        //温馨提示
        params.put("thing8", thing8);
        return params;
    }

    /**
     * 格式化取货时间
     * 将"2025-07-03 14:00:00-14:59:59"格式转换为"2025-07-03 14:59"
     *
     * @param pickUpTime 原始取货时间字符串
     * @return 格式化后的时间字符串
     */
    private static String formatPickUpTime(String pickUpTime) {
        if (pickUpTime == null || pickUpTime.isEmpty()) {
            return "";
        }
        try {
            // 处理类似"2025-07-03 14:00:00-14:59:59"的格式
            int lastDashIndex = pickUpTime.lastIndexOf("-");
            if (lastDashIndex > 0) {
                // 获取日期和第一个时间部分
                String dateTime = pickUpTime.substring(0, pickUpTime.indexOf(" ", 10)).trim();
                // 获取结束时间部分
                String endTime = pickUpTime.substring(lastDashIndex + 1);
                // 提取小时和分钟
                if (endTime.length() >= 5) {
                    String timeStr = endTime.substring(0, 5);
                    return dateTime + " " + timeStr;
                }
            }
        } catch (Exception e) {
            // 如果处理过程中出现任何异常，返回原始字符串
            return pickUpTime;
        }
        // 如果格式不匹配，返回原始字符串
        return pickUpTime;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = ShopStoreConstant.SHOP_STORE_ISSUE_LOCK, key = "#storeId+':'+#code")
    public void storeOrderVerification(Long storeId, String code) {
        ShopStoreOrder shopStoreOrder = shopStoreOrderService.lambdaQuery().eq(ShopStoreOrder::getShopId, ISecurity.userMust().getShopId()).eq(ShopStoreOrder::getShopStoreId, storeId).eq(ShopStoreOrder::getCode, code).one();

        if (shopStoreOrder == null) {
            throw new GlobalException("核销码错误");
        }
        if (shopStoreOrder.getPackageStatus() != PackageStatus.WAITING_FOR_RECEIVE) {
            throw new GlobalException("当前订单不属于可核销状态内");
        }
        shopStoreOrder.setPackageStatus(PackageStatus.BUYER_WAITING_FOR_COMMENT);

        shopStoreOrderService.updateById(shopStoreOrder);
        OrderPackageKeyDTO orderPackageKey = JSON.parseObject(shopStoreOrder.getOrderPackage(), OrderPackageKeyDTO.class);
        // 门店订单进行核销
        orderRpcService.storeOrderConfirmPackage(orderPackageKey);

    }

    @Override
    public ShopStoreTransactionSummaryVO getStoreTransactionSummary(Long storeId) {
        ShopStoreTransactionSummaryVO storeTransactionSummary = shopStoreOrderService.getStoreTransactionSummary(storeId, ISecurity.userMust().getShopId());
        // 使用变量存储总金额和交易笔数，以避免重复计算
        Long todayTotalAmount = storeTransactionSummary.getTodayTotalAmount();
        Long todayTransactionCount = storeTransactionSummary.getTodayTransactionCount();
        Long monthTotalAmount = storeTransactionSummary.getMonthTotalAmount();
        Long monthTransactionCount = storeTransactionSummary.getMonthTransactionCount();

        // 计算平均交易金额
        Long todayAverageTransactionValue = todayTransactionCount != 0 ? todayTotalAmount / todayTransactionCount : 0L;
        Long monthAverageTransactionValue = monthTransactionCount != 0 ? monthTotalAmount / monthTransactionCount : 0L;


        storeTransactionSummary.setTodayAverageTransactionValue(todayAverageTransactionValue).setMonthAverageTransactionValue(monthAverageTransactionValue);
        return storeTransactionSummary;

    }
}
