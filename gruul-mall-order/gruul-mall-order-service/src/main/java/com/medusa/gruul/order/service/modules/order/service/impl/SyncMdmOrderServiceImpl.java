package com.medusa.gruul.order.service.modules.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.medusa.gruul.common.ipaas.MdmConfig;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.shop.ShopBindQueryClient;
import com.medusa.gruul.common.ipaas.client.team.StoreQueryByTeamInfoIdsClient;
import com.medusa.gruul.common.ipaas.model.goods.StoreGoodsExtendChannelVO;
import com.medusa.gruul.common.ipaas.model.goods.StoreGoodsSpecAndDetailsFullVO;
import com.medusa.gruul.common.ipaas.model.order.*;
import com.medusa.gruul.common.ipaas.model.shop.StoreBindReqDTO;
import com.medusa.gruul.common.ipaas.model.shop.StoreBindVO;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsReqDTO;
import com.medusa.gruul.common.ipaas.model.team.StoreQueryByTeamInfoIdsRspVO;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.order.api.entity.*;
import com.medusa.gruul.order.api.enums.MdmOrderSyncStatusEnum;
import com.medusa.gruul.order.api.enums.mdm.*;
import com.medusa.gruul.order.service.model.dto.OrderDetailQueryDTO;
import com.medusa.gruul.order.service.modules.order.service.QueryOrderService;
import com.medusa.gruul.order.service.modules.order.service.SyncMdmOrderService;
import com.medusa.gruul.order.service.mp.service.IMdmOrderSyncRecordService;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * MDM订单同步服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncMdmOrderServiceImpl implements SyncMdmOrderService {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final ObjectMapper objectMapper = new ObjectMapper().setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    private final IpaasClientSupport ipaasClientSupport;
    private final ShopRpcService shopRpcService;
    private final QueryOrderService queryOrderService;
    private final UaaRpcService uaaRpcService;
    private final GoodsRpcService goodsRpcService;
    private final MdmConfig mdmConfig;
    private final IMdmOrderSyncRecordService mdmOrderSyncRecordService;

    @Override
    public void syncOrder(String orderNo) {
        List<MdmOrderSyncDTO> syncDTOS = buildSyncDTOS(orderNo);
        if (CollUtil.isEmpty(syncDTOS)) {
            return;
        }

        for (MdmOrderSyncDTO syncDTO : syncDTOS) {
            sendSyncRequest(syncDTO);
        }
    }

    @Override
    public List<MdmOrderSyncDTO> buildSyncDTOS(String orderNo) {
        List<MdmOrderSyncDTO> result = new ArrayList<>();

        Order order = queryOrderService.orderDetail(new OrderDetailQueryDTO().setOrderNo(orderNo)).getOrNull();
        if (order == null) {
            log.error("订单不存在，{}", orderNo);
            return result;
        }

        // 查询用于信息
        UserInfoVO userInfo = this.uaaRpcService.getUserDataByUserId(order.getBuyerId()).getOrNull();
        if (userInfo == null) {
            log.error("用户信息不存在，{}", order.getBuyerId());
            return result;
        }

        List<ShopOrder> shopOrders = order.getShopOrders();
        for (ShopOrder shopOrder : shopOrders) {
            // 获取店铺信息
            ShopInfoVO shopInfo = shopRpcService.getShopInfoByShopId(shopOrder.getShopId());
            if (shopInfo == null) {
                throw new GlobalException("店铺不存在");
            }
            Long enterpriseId = shopInfo.getEnterpriseId();
            StoreBindVO storeBind = new ShopBindQueryClient(ipaasClientSupport).execute(
                    new StoreBindReqDTO()
                            .setEnterpriseId(enterpriseId)
                            .setChannelId(mdmConfig.getChannelId())
                            .setBindStoreId(shopInfo.getId())
            );
            if (storeBind == null) {
                throw new GlobalException("未找到店铺绑定关系");
            }

            MdmOrderSyncDTO mdmOrderSyncDTO = buildSyncDTO(enterpriseId, storeBind.getStoreId(), userInfo, order, shopOrder);
            result.add(mdmOrderSyncDTO);
        }
        return result;
    }

    private MdmOrderSyncDTO buildSyncDTO(Long enterpriseId, Long mdmShopId, UserInfoVO userInfo, Order order, ShopOrder shopOrder) {
        MdmOrderSyncDTO syncDTO = new MdmOrderSyncDTO();

        // 设置订单基本信息
        syncDTO.setOrder(buildOrderInfo(enterpriseId, mdmShopId, userInfo, order, shopOrder));

        // 设置订单支付信息
        syncDTO.setOrderPayment(buildOrderPayment(shopOrder, order.getOrderPayment()));

        // 设置订单优惠信息
        syncDTO.setOrderDiscount(buildOrderDiscount(order.getOrderDiscounts(), shopOrder));

        // 设置订单商品信息
        syncDTO.setOrderItem(buildOrderItems(shopOrder));

        // 设置订单商品优惠信息
        syncDTO.setOrderItemDiscount(buildOrderItemDiscount(order.getOrderDiscounts(), shopOrder));

        // 设置订单退款相关信息
        syncDTO.setOrderRefund(Collections.emptyList());

        // 设置订单退款支付信息
        syncDTO.setOrderRefundPayment(Collections.emptyList());

        // 设置订单退款优惠信息
        syncDTO.setOrderRefundDiscount(Collections.emptyList());

        // 设置订单退款商品信息
        syncDTO.setOrderRefundItem(Collections.emptyList());

        // 设置订单退款商品优惠信息
        syncDTO.setOrderRefundItemDiscount(Collections.emptyList());

        return syncDTO;
    }

    private MdmOrder buildOrderInfo(Long enterpriseId,
                                    Long mdmShopId,
                                    UserInfoVO userInfo,
                                    Order order,
                                    ShopOrder shopOrder) {
        MdmOrder orderInfo = new MdmOrder();
        List<StoreQueryByTeamInfoIdsRspVO> holderStoreInfos = new StoreQueryByTeamInfoIdsClient(ipaasClientSupport).execute(new StoreQueryByTeamInfoIdsReqDTO().setTeamInfoIds(Collections.singletonList(mdmShopId)));
        if (CollUtil.isEmpty(holderStoreInfos)) {
            throw new GlobalException("未找到ipaas门店信息");
        }
        StoreQueryByTeamInfoIdsRspVO holderStoreInfo = holderStoreInfos.get(0);

        // 设置基本信息
        orderInfo.setId(shopOrder.getId())  // 订单ID
                .setOrderNumber(shopOrder.getNo())  // 订单号
                .setVersion(1)  // 版本号
                .setState(MdmOrderStateEnum.PAID.getValue())  // 订单状态
                .setBeforeState(MdmOrderStateEnum.UN_CREATED.getValue())  // 订单之前的状态
                .setMemberName(order.getBuyerNickname())  // 会员名称
                .setMemberGuid(String.valueOf(order.getBuyerId()))  // 会员GUID
                .setMemberPhone(userInfo.getMobile())  // 会员手机号
                .setMemberCardInfoGuid(null)  // 会员卡信息GUID
                .setOrderSource(MdmCreateOrderSourceEnum.PRIVATE_MALL_ORDER.getValue())  // 订单来源
                .setStoreName(shopOrder.getShopName())  // 门店名称
                .setReceivePrice(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount() + shopOrder.getFreightAmount() - shopOrder.getDiscountAmount()).toString())  // 实收金额
                .setOriginPrice(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount() + shopOrder.getFreightAmount() - shopOrder.getDiscountAmount()).toString())  // 订单应收
                .setTotalOriginPrice(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount() + shopOrder.getFreightAmount()).toString())  // 订单商品原始总金额+邮费
                .setItemTotalPrice(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount()).toString())  // 商品总价
                .setDiscountPrice(AmountCalculateHelper.toYuan(shopOrder.getDiscountAmount()).toString())  // 订单优惠金额
                .setActuallyPaidAmount(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount() + shopOrder.getFreightAmount() - shopOrder.getDiscountAmount()).toString())  // 实付金额// 商品总价
                .setPaidAt(order.getOrderPayment().getPayTime().format(DATE_TIME_FORMATTER))  // 支付时间
                .setChangeMoney("0")  // 找零金额
                .setDeviceNumber(null)  // 设备编号
                .setExternalDiscounts(buildExternalDiscounts(order.getOrderDiscounts(), shopOrder))  // 外部优惠
                .setExternalShopcar(Collections.emptyMap())  // 外部购物车
                .setBusinessDay(order.getOrderPayment().getPayTime().format(DATE_FORMATTER))  // 营业日
                .setChannelId(mdmConfig.getChannelId())  // 渠道ID
                .setChannelName(mdmConfig.getChannelName())  // 渠道名称
                .setEnterpriseId(enterpriseId)  // 企业ID
                .setShoppingGuideId(null)  // 导购员ID
                .setShoppingGuideName(null)  // 导购员名称
                .setRewardId(null)  // 提成方案ID
                .setStoreId(0L)  // 门店ID
                .setStoreTeamInfoId(mdmShopId)  // 门店部门ID
                .setHolderStoreId(holderStoreInfo.getId())  // holder的门店ID
                .setMemberRechargeResData(null)  // 会员充值返回数据
                .setOriginCreateOrderHeaders(null)  // 创建订单请求头
                .setCouponRollback(null)  // 优惠券回滚
                .setSequenceNumber(null)  // 流水号
                .setOperaterId(0L)  // 操作员ID
                .setOperaterName(order.getBuyerNickname())  // 操作员名称
                .setOperaterPhone(userInfo.getMobile())  // 操作员手机号
                .setRemark(order.getRemark())  // 备注
                .setCreateAt(order.getCreateTime().format(DATE_TIME_FORMATTER))  // 创建时间
                .setUpdateAt(order.getUpdateTime().format(DATE_TIME_FORMATTER))  // 更新时间
                .setDisabled(false);  // 是否删除

        return orderInfo;
    }

    private List<MdmOrderPayment> buildOrderPayment(ShopOrder shopOrder, OrderPayment orderPayment) {
        List<MdmOrderPayment> orderPaymentInfos = new ArrayList<>();
        MdmOrderPayment orderPaymentInfo = new MdmOrderPayment();
        orderPaymentInfo.setId(orderPayment.getId())  // 记录ID
                .setOrderId(shopOrder.getId())  // 订单ID
                .setOrderNumber(shopOrder.getNo())  // 订单编号
                .setPaymentNumber(null)  // 支付单号
                .setMemberRechargePreNumber(null)  // 会员充值预支付单号
                .setPaymentType(MdmPaymentMethodDetailEnum.fromPayType(orderPayment.getType()).getValue())  // 支付类型：会员支付
                .setPaymentMethodName(MdmPaymentMethodDetailEnum.fromPayType(orderPayment.getType()).getDescription())  // 支付方式名称
                .setPaymentMethodId(MdmPaymentMethodIdEnum.fromPayType(orderPayment.getType()).getValue())  // 支付方式ID
                .setPaymentAmount(AmountCalculateHelper.toYuan(shopOrder.getTotalAmount() + shopOrder.getFreightAmount() - shopOrder.getDiscountAmount()).toString())  // 支付金额
                .setPaymentAuthCode(null)  // 支付授权码
                .setPaymentTerminalId(null)  // 支付终端ID
                .setIsPaySuccess(Boolean.TRUE)  // 是否支付成功
                .setIsPrePaySuccess(Boolean.TRUE)  // 是否预支付成功
                .setIsMemberRechargeSuccess(null)  // 是否会员充值成功
                .setChangeAmount("0")  // 找零金额
                .setMemberPaymentArgs(new HashMap<>())  // 会员支付参数
                .setPaySuccessTime(orderPayment.getPayTime().format(DATE_TIME_FORMATTER))  // 支付成功时间
                .setOriginRequestHeader(null)  // 支付请求头
                .setMemberRechargeSuccessTime(null)  // 会员充值成功时间
                .setSort(0)  // 排序
                .setDisabled(false)  // 是否删除
                .setCreateAt(orderPayment.getCreateTime().format(DATE_TIME_FORMATTER))  // 创建时间
                .setUpdateAt(orderPayment.getUpdateTime().format(DATE_TIME_FORMATTER));  // 更新时间

        orderPaymentInfos.add(orderPaymentInfo);
        return orderPaymentInfos;
    }

    private List<MdmOrderDiscount> buildOrderDiscount(List<OrderDiscount> orderDiscounts, ShopOrder shopOrder) {
        List<MdmOrderDiscount> discountList = new ArrayList<>();
        if (CollUtil.isEmpty(orderDiscounts) || shopOrder == null) {
            return discountList;
        }

        Long shopId = shopOrder.getShopId();
        for (OrderDiscount orderDiscount : orderDiscounts) {
            if (CollUtil.isEmpty(orderDiscount.getDiscountItems())) continue;
            for (OrderDiscountItem discountItem : orderDiscount.getDiscountItems()) {
                if (shopId.equals(discountItem.getShopId())) {
                    MdmOrderDiscount dto = new MdmOrderDiscount();
                    dto.setId(orderDiscount.getId());
                    dto.setOrderId(shopOrder.getId());
                    dto.setOrderNumber(shopOrder.getNo());
                    dto.setDiscountSource(MdmDiscountSource.PRIVATE_MALL.getValue());
                    dto.setDiscountType(MdmOrderDiscountTypeEnum.fromDiscountSourceType(orderDiscount.getSourceType()).getValue());
                    dto.setDiscountAmount(AmountCalculateHelper.toYuan(discountItem.getDiscountAmount()).toString());
                    dto.setDiscountName(orderDiscount.getSourceDesc());
                    dto.setCreateAt(shopOrder.getCreateTime().format(DATE_TIME_FORMATTER));  // 创建时间
                    dto.setUpdateAt(shopOrder.getUpdateTime().format(DATE_TIME_FORMATTER));  // 更新时间
                    dto.setDisabled(false);
                    discountList.add(dto);
                }
            }
        }

        return discountList;
    }

    private List<MdmOrderItem> buildOrderItems(ShopOrder shopOrder) {
        List<MdmOrderItem> items = new ArrayList<>();
        List<ShopOrderItem> shopOrderItems = shopOrder.getShopOrderItems();
        Map<Long, Product> productMap = goodsRpcService.getProductMap(shopOrderItems.stream().map(ShopOrderItem::getProductId).toList());

        for (ShopOrderItem item : shopOrderItems) {
            MdmOrderItem orderItem = new MdmOrderItem();
            Product product = productMap.get(item.getProductId());
            StoreGoodsExtendChannelVO mdmGoodsDetail = item.getMdmGoodsDetail();
            List<StoreGoodsSpecAndDetailsFullVO> mdmSkuList = mdmGoodsDetail.getStoreGoodsSpecDetailsList();
            /*
             * mdm得sku id
             * 普通商品：店铺商品得sku id
             * 代销商品：供应商商品得sku id，这个时候需要找到普通商品对应的sku id
             */
            StoreGoodsSpecAndDetailsFullVO mdmSku;
            if (item.getSellType() == SellType.CONSIGNMENT) {
                mdmSku = mdmSkuList.stream().filter(sku -> sku.getOriginSkuId().equals(item.getMdmSkuId())).findFirst().orElseThrow(
                        () -> new GlobalException("sku信息错误")
                );
            } else {
                mdmSku = mdmSkuList.stream().filter(sku -> sku.getSkuId().equals(item.getMdmSkuId())).findFirst().orElseThrow(
                        () -> new GlobalException("sku信息错误")
                );
            }

            StoreGoodsExtendChannelVO.IdNameVO saleCategory = Optional.ofNullable(mdmGoodsDetail.getSaleCategories().getThree()).orElseThrow(
                    () -> new GlobalException("销售分类错误")
            );

            orderItem.setId(item.getId())  // 记录ID
                    .setOrderId(shopOrder.getId())  // 订单ID
                    .setOrderNumber(shopOrder.getNo())  // 订单编号
                    .setPurchaseQuantity(item.getNum().toString())  // 购买数量
                    .setOriginTotalPriceInShopcaritem(AmountCalculateHelper.toYuan(item.totalPrice()).toString())  // 购物车中商品原价小计
                    .setShopPrice(AmountCalculateHelper.toYuan(item.payPrice()).toString())  // 商品售价
                    .setActualReceivePrice(AmountCalculateHelper.toYuan(item.payPrice()).toString())  // 商品实收金额
                    .setStoreSalesProgramId(mdmGoodsDetail.getStoreSalesProgramId())  // 门店销售方案ID
                    .setStoreSalesProgramGoodsId(mdmGoodsDetail.getStoreSalesProgramGoodsId())  // 门店销售方案商品ID
                    .setSubtotalAfterDiscount(AmountCalculateHelper.toYuan(item.payPrice()).toString())  // 折后小计
                    .setGoodsId(product.getMdmGoodsId())  // 商品ID
                    .setGoodsUuidInShopcar(null)  // 商品在购物车中的uuid编码
                    .setSpuId(null)  // SPU ID
                    .setCategoryId(saleCategory.getId())  // 分类ID
                    .setCategoryName(saleCategory.getName())  // 分类名称
                    .setPictureUrl(item.getImage())  // 商品图片URL
                    .setGoodsSaleName(item.getProductName())  // 商品销售名称
                    .setBarcode(mdmGoodsDetail.getBarcode())  // 商品条码
                    .setGoodsCustomCode(mdmGoodsDetail.getGoodsCustomCode())  // 商品自定义编码
                    .setComboType(mdmGoodsDetail.getComboType())  // 商品组合类型：1-单品
                    .setComboTypeName(mdmGoodsDetail.getComboTypeName())  // 商品组合类型名称
                    .setSellingPrice(AmountCalculateHelper.toYuan(item.getSalePrice()).toString())  // 商品原售价
                    .setCosts(StrUtil.isBlank(mdmSku.getCosts()) ? null : mdmSku.getCosts())  // 商品成本价格
                    .setDiscountPriceInShopcar(null)  // 改价后商品售价
                    .setQuantity(0)  // 商品数量
                    .setGoodsUnitName(mdmGoodsDetail.getGoodsUnitName())  // 商品单位
                    .setGoodsUnitId(mdmGoodsDetail.getGoodsUnit())  // 商品单位ID
                    .setGoodsSpecification(buildGoodsSpecification(mdmSku))  // 商品规格信息
                    .setGoodsProperty(new ArrayList<>())  // 商品属性
                    .setTagName(mdmGoodsDetail.getTagName())  // 商品标签
                    .setPluCode(mdmGoodsDetail.getPluCode())  // PLU码
                    .setIsUpsell(Boolean.TRUE)  // 上架状态
                    .setUpsellTime(null)  // 上架时间
                    .setExtra(null)  // 商品额外数据
                    .setDiscountTotalPriceItem("0")  // 商品改价优惠小计
                    .setRetailDiscountAmount("0.00")  // 商品零售优惠金额
                    .setMemberDiscountAmount("0.00")  // 商品会员优惠金额
                    .setPointsDeductionAmount("None")  // 商品积分抵现分摊金额
                    .setDiscountAmountAll(null)  // 商品优惠总金额
                    .setSpuCode(mdmGoodsDetail.getSpuCode())  // 商品SPU编码
                    .setVipPrice(null)  // 会员价
                    .setMercandiseType(MdmMercandiseTypeEnum.SALE.getValue())  // 商品系统分类
                    .setValuationMethod(mdmGoodsDetail.getValuationMethod())  // 计价方式
                    .setGoodsSpec(mdmGoodsDetail.getGoodsSpec())  // 商品规格：2-多规格
                    .setGoodsPackageSkuId(mdmSku.getSkuId())  // 商品SKU ID
                    .setSkuCode(mdmSku.getSkuCode())  // 商品SKU编码
                    .setDisabled(false)  // 是否删除
                    .setCreateAt(shopOrder.getCreateTime().format(DATE_TIME_FORMATTER))  // 创建时间
                    .setUpdateAt(shopOrder.getUpdateTime().format(DATE_TIME_FORMATTER));  // 更新时间

            items.add(orderItem);
        }

        return items;
    }

    private List<MdmGoodsSpecification> buildGoodsSpecification(StoreGoodsSpecAndDetailsFullVO mdmSku) {
        List<MdmGoodsSpecification> specifications = new ArrayList<>();

        // 添加规格信息
        mdmSku.getStoreGoodsSpecRelationList().forEach(relation -> {
            MdmGoodsSpecification spec = new MdmGoodsSpecification();
            spec.setGoodsSpecId(relation.getGoodsSpecId())
                    .setGoodsSpecName(relation.getGoodsSpecName())
                    .setGoodsSpecDetailId(relation.getGoodsSpecDetailId())
                    .setGoodsSpecDetailName(relation.getGoodsSpecDetailName())
                    .setGoodsSpecDetailSort(relation.getGoodsSpecDetailSort());
        });
        return specifications;
    }

    private List<MdmExternalDiscount> buildExternalDiscounts(List<OrderDiscount> orderDiscounts, ShopOrder shopOrder) {
        List<MdmExternalDiscount> externalDiscounts = new ArrayList<>();
        if (CollUtil.isEmpty(orderDiscounts) || shopOrder == null) {
            return externalDiscounts;
        }

        Long shopId = shopOrder.getShopId();
        for (OrderDiscount orderDiscount : orderDiscounts) {
            if (CollUtil.isEmpty(orderDiscount.getDiscountItems())) continue;
            for (OrderDiscountItem discountItem : orderDiscount.getDiscountItems()) {
                if (shopId.equals(discountItem.getShopId())) {
                    MdmExternalDiscount dto = new MdmExternalDiscount();
                    dto.setId(MdmOrderDiscountTypeEnum.fromDiscountSourceType(orderDiscount.getSourceType()).getValue());
                    dto.setName(orderDiscount.getSourceDesc());
                    dto.setType(MdmOrderDiscountTypeEnum.fromDiscountSourceType(orderDiscount.getSourceType()).getValue());
                    dto.setTotal_discount_price(AmountCalculateHelper.toYuan(discountItem.getDiscountAmount()).toString());
                    externalDiscounts.add(dto);
                }
            }
        }
        return externalDiscounts;
    }

    private List<MdmOrderItemDiscount> buildOrderItemDiscount(List<OrderDiscount> orderDiscounts, ShopOrder shopOrder) {
        List<MdmOrderItemDiscount> orderItemDiscount = new ArrayList<>();
        if (CollUtil.isEmpty(orderDiscounts) || shopOrder == null) {
            return orderItemDiscount;
        }

        Long shopId = shopOrder.getShopId();
        for (OrderDiscount orderDiscount : orderDiscounts) {
            if (CollUtil.isEmpty(orderDiscount.getDiscountItems())) continue;
            for (OrderDiscountItem discountItem : orderDiscount.getDiscountItems()) {
                if (shopId.equals(discountItem.getShopId())) {
                    MdmOrderItemDiscount dto = new MdmOrderItemDiscount();
                    dto.setId(discountItem.getId());
                    dto.setOrderId(shopOrder.getId());
                    dto.setOrderNumber(shopOrder.getNo());
                    dto.setOrderItemId(discountItem.getItemId());
                    dto.setDiscountType(MdmOrderDiscountTypeEnum.fromDiscountSourceType(orderDiscount.getSourceType()).getValue());
                    dto.setDiscountAmount(AmountCalculateHelper.toYuan(discountItem.getDiscountAmount()).toString());
                    dto.setDiscountName(orderDiscount.getSourceDesc());
                    dto.setCreateAt(shopOrder.getCreateTime().format(DATE_TIME_FORMATTER));  // 创建时间
                    dto.setUpdateAt(shopOrder.getUpdateTime().format(DATE_TIME_FORMATTER));  // 更新时间
                    dto.setDisabled(false);
                    orderItemDiscount.add(dto);
                }
            }
        }
        return orderItemDiscount;
    }

    @Override
    public void sendSyncRequest(MdmOrderSyncDTO syncDTO) {
        Long enterpriseId = syncDTO.getOrder().getEnterpriseId();
        String traceId = UUID.randomUUID().toString();
        String requestBody = null;
        String responseBody = null;
        MdmOrderSyncStatusEnum status = MdmOrderSyncStatusEnum.WAIT;
        String errorMsg = null;
        try {
            // 第三层
            Map<String, Object> level3 = new HashMap<>();
            level3.put("company_id", enterpriseId);
            level3.put("data", syncDTO);
            // 第二层
            Map<String, Object> level2 = new HashMap<>();
            level2.put("company_id", String.valueOf(enterpriseId));
            requestBody = objectMapper.writeValueAsString(level3);
            level2.put("order_param", requestBody);
            // 第一层
            Map<String, Object> level1 = new HashMap<>();
            level1.put("data", level2);
            requestBody = objectMapper.writeValueAsString(level1);
            log.info("[MDM订单同步][traceId={}] 请求URL: {}, 请求体: {}", traceId, mdmConfig.getSyncUrl(), requestBody);

            HttpResponse response = HttpRequest.post(mdmConfig.getSyncUrl())
                    .header("Content-Type", "application/json")
                    .header("token", ipaasClientSupport.getAccessToken())
                    .header("companyid", String.valueOf(enterpriseId))
                    .header("userid", mdmConfig.getUserid())
                    .header("account", mdmConfig.getAccount())
                    .body(requestBody)
                    .timeout(10000) // 10秒超时
                    .execute();

            responseBody = response.body();
            int httpStatus = response.getStatus();

            if (httpStatus == HttpStatus.HTTP_OK) {
                status = MdmOrderSyncStatusEnum.SUCCESS;
            } else {
                status = MdmOrderSyncStatusEnum.FAIL;
                errorMsg = "HTTP状态异常: " + httpStatus;
            }
        } catch (Exception e) {
            status = MdmOrderSyncStatusEnum.FAIL;
            errorMsg = e.getMessage();
        } finally {
            // 保存同步记录
            MdmOrderSyncRecord mdmOrderSyncRecord = new MdmOrderSyncRecord()
                    .setOrderNo(syncDTO.getOrder().getOrderNumber())
                    .setRequestBody(requestBody)
                    .setResponseBody(responseBody)
                    .setExtra(syncDTO)
                    .setStatus(status)
                    .setErrorMsg(errorMsg)
                    .setSyncTime(LocalDateTime.now())
                    .setAfsOrderNo(CollUtil.isNotEmpty(syncDTO.getOrderRefund()) ? syncDTO.getOrderRefund().get(0).getRefundNumber() : null);

            mdmOrderSyncRecordService.save(mdmOrderSyncRecord);
        }
    }
}