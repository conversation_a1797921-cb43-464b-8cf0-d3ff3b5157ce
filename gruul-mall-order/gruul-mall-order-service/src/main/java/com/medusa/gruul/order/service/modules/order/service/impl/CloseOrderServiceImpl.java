package com.medusa.gruul.order.service.modules.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.dto.MessagesSendDTO;
import com.medusa.gruul.common.member.enums.WechatMsgSendTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.dto.settlement.BarkOrderDiscountCallbackQO;
import com.medusa.gruul.common.member.dto.settlement.SettlementUnLockedDiscountDTO;
import com.medusa.gruul.common.member.service.SettlementApiService;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.entity.OrderPayment;
import com.medusa.gruul.order.api.entity.OrderReceiver;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.ItemStatus;
import com.medusa.gruul.order.api.enums.OrderCloseType;
import com.medusa.gruul.order.api.enums.OrderStatus;
import com.medusa.gruul.order.api.enums.ShopOrderStatus;
import com.medusa.gruul.order.api.pojo.OrderInfo;
import com.medusa.gruul.order.service.model.dto.OrderDetailQueryDTO;
import com.medusa.gruul.order.service.model.enums.OrderError;
import com.medusa.gruul.order.service.modules.order.service.CloseOrderService;
import com.medusa.gruul.order.service.modules.order.service.QueryOrderService;
import com.medusa.gruul.order.service.mp.service.IOrderPaymentService;
import com.medusa.gruul.order.service.mp.service.IOrderService;
import com.medusa.gruul.order.service.mp.service.IShopOrderItemService;
import com.medusa.gruul.order.service.mp.service.IShopOrderService;
import com.medusa.gruul.order.service.mq.service.OrderRabbitService;
import com.medusa.gruul.order.service.util.OrderUtil;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/6/24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CloseOrderServiceImpl implements CloseOrderService {


    private final Executor globalExecutor;
    private final IOrderService orderService;
    private final IShopOrderService shopOrderService;
    private final QueryOrderService queryOrderService;
    private final OrderRabbitService orderRabbitService;
    private final IOrderPaymentService orderPaymentService;
    private final IShopOrderItemService shopOrderItemService;
    private final MemberApiService memberApiService;
    private final SettlementApiService settlementApiService;
    private CloseOrderService closeOrderService;


    private List<Order> checkOrderStatus(List<Order> orders, OrderStatus currentStatus, OrderStatus targetStatus) {
        if (targetStatus.isSystem()) {
            return orders.stream().filter(order -> order.getStatus() == currentStatus).toList();
        }
        orders.forEach(order -> {
            if (order.getStatus() != currentStatus) {
                throw OrderError.INVALID_ORDER_STATUS.exception();
            }
        });
        return orders;
    }

    @Override
    @Redisson(name = OrderConstant.ORDER_EDIT_LOCK_KEY, batchParamName = "#orderNos", key = "#item")
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Set<String> orderNos, OrderStatus currentStatus, OrderStatus targetStatus) {
        List<Order> orders = checkOrderStatus(
                orderService.lambdaQuery()
                        .select(Order::getType, Order::getActivityId, Order::getStatus, Order::getNo, Order::getBuyerId)
                        .in(Order::getNo, orderNos)
                        .list(),
                currentStatus,
                targetStatus
        );
        if (CollUtil.isEmpty(orders)) {
            log.warn("没有需要更新的订单, orderNos: {}, currentStatus: {}, targetStatus: {}", orderNos, currentStatus, targetStatus);
            return;
        }
        Set<String> needUpdateOrderNos = orders.stream().map(Order::getNo).collect(Collectors.toSet());
        //更新订单
        orderService.lambdaUpdate()
                .in(Order::getNo, needUpdateOrderNos)
                .eq(Order::getStatus, currentStatus)
                .set(Order::getStatus, targetStatus)
                .set(Order::getUpdateTime, LocalDateTime.now())
                .update();
        TenantShop.disable(
                () -> shopOrderService.lambdaUpdate()
                        .in(ShopOrder::getOrderNo, needUpdateOrderNos)
                        .eq(ShopOrder::getStatus, ShopOrderStatus.OK)
                        .set(ShopOrder::getStatus, targetStatus.getShopOrderStatus())
                        .set(ShopOrder::getUpdateTime, LocalDateTime.now())
                        .update()
        );
        if (!targetStatus.isClosed()) {
            log.info("订单状态更新成功, orderNos: {}, currentStatus: {}, targetStatus: {}", orderNos, currentStatus, targetStatus);
            return;
        }
        globalExecutor.execute(
                () -> {
                    // 获取所有订单商品项
                    List<ShopOrderItem> items = TenantShop.disable(
                            () -> shopOrderItemService.lambdaQuery()
                                    .in(ShopOrderItem::getOrderNo, needUpdateOrderNos)
                                    .eq(ShopOrderItem::getStatus, ItemStatus.OK)
                                    .list()
                    );

                    // 使用批量查询获取订单详情和支付信息
                    Map<String, OrderInfo> orderInfoMap = queryOrderService.batchOrderDetailsWithPayment(needUpdateOrderNos);

                    Map<String, List<ShopOrderItem>> orderNoItemsMap = items.stream().collect(Collectors.groupingBy(ShopOrderItem::getOrderNo));
                    forBarkOrderDiscountCallback(needUpdateOrderNos);
                    for (Order order : orders) {
                        String orderNo = order.getNo();
                        OrderInfo orderInfo = orderInfoMap.get(orderNo);
                        // 发送订单关闭消息
                        orderRabbitService.sendOrderClose(
                                orderInfo
                                        .setCloseType(OrderCloseType.FULL)
                                        .setActivityType(order.getType())
                                        .setActivityId(order.getActivityId())
                                        .setOrderNo(orderNo)
                                        .setBuyerId(order.getBuyerId())
                                        .setSkuStocks(OrderUtil.toSkuStocks(orderNoItemsMap.get(orderNo)))
                        );

                    }
                }
        );
    }

    private void sendOrderCloseMessage(
            Map<String, List<ShopOrderItem>> orderNoItemsMap,
            OrderInfo orderInfo,
            String orderNo) {
        try {
            // 获取订单商品信息
            List<ShopOrderItem> shopOrderItems = orderNoItemsMap.get(orderNo);
            if (CollUtil.isEmpty(shopOrderItems)) {
                log.warn("订单商品信息不存在，无法发送关闭通知, orderNo: {}", orderNo);
                return;
            }

            // 构建商品信息字符串
            StringBuilder messageItem = new StringBuilder();
            for (ShopOrderItem item : shopOrderItems) {
                messageItem.append(item.getProductName()).append("*").append(item.getNum()).append(";");
            }

            Order order = orderInfo.getOrder();
            OrderReceiver receiver = order.getOrderReceiver();
            OrderPayment payment = orderInfo.getPayment();

            if (receiver == null) {
                log.warn("订单收货人信息不存在，无法发送关闭通知, orderNo: {}", orderNo);
                return;
            }

            if (payment == null) {
                log.warn("订单支付信息不存在，无法发送关闭通知, orderNo: {}", orderNo);
                return;
            }

            // 构建消息发送对象
            MessagesSendDTO messagesSendQO = new MessagesSendDTO();
            messagesSendQO.setTemplateName(WechatMsgSendTypeEnum.ORDER_DELIVERY.getMsgTitle());

            // 生成小程序参数
            Map<String, String> params = generateOrderCloseAppletsParam(
                    receiver.getName(),                    // 客户名称
                    receiver.getMobile(),                  // 联系方式
                    receiver.getAddress(),                 // 收货地址
                    messageItem.toString(),                // 商品信息
                    String.valueOf(payment.getPayAmount()) // 订单金额
            );
            messagesSendQO.setPrams(params);
            messagesSendQO.setOrderNum(orderNo);
            messagesSendQO.setPhone(receiver.getMobile());
            messagesSendQO.setOperSubjectGuid(ISystem.platformIdMust().toString());
            messagesSendQO.setMemberName(receiver.getName());

            log.info("发送订单关闭小程序订阅消息，参数：{}", JSON.toJSONString(messagesSendQO));
            memberApiService.wechatMessageSendBatch(messagesSendQO);
        } catch (Exception e) {
            log.info("发送订单关闭通知失败, orderNo: {}, error: {}", orderNo, e.getMessage(), e);
        }
    }

    private void forBarkOrderDiscountCallback(Set<String> needUpdateOrderNos) {
        log.info("订单超时自动取消优惠活动回调, orderNos: {}", needUpdateOrderNos);
        try {
            List<ShopOrder> shopOrder;
            shopOrder = TenantShop.disable(() -> shopOrderService.lambdaQuery()
                    .in(ShopOrder::getNo, needUpdateOrderNos)
                    .list());

            if (CollUtil.isEmpty(shopOrder)) {
                shopOrder = TenantShop.disable(() -> shopOrderService.lambdaQuery()
                        .in(ShopOrder::getOrderNo, needUpdateOrderNos)
                        .list());
            }
            log.info("订单超时自动取消优惠活动回调, shopOrder: {}", JSON.toJSONString(shopOrder));

            // 获取所有订单商品项
            List<ShopOrderItem> items = TenantShop.disable(
                    () -> shopOrderItemService.lambdaQuery()
                            .in(ShopOrderItem::getOrderNo, needUpdateOrderNos)
                            .eq(ShopOrderItem::getStatus, ItemStatus.OK)
                            .list()
            );

            // 使用批量查询获取订单详情和支付信息
            Map<String, OrderInfo> orderInfoMap = queryOrderService.batchOrderDetailsWithPayment(needUpdateOrderNos);

            // 按订单号分组商品项
            Map<String, List<ShopOrderItem>> orderNoItemsMap = items.stream().collect(Collectors.groupingBy(ShopOrderItem::getOrderNo));

            for (ShopOrder order : shopOrder) {
                // 订单超时自动取消优惠活动回调
                sendBarkOrderDiscountCallback(order.getOrderNo(), order);

                // 发送订单关闭通知
                String orderNo = order.getOrderNo();
                OrderInfo orderInfo = orderInfoMap.get(orderNo);
                if (orderInfo != null) {
                    sendOrderCloseMessage(orderNoItemsMap, orderInfo, orderNo);
                } else {
                    log.warn("订单信息不存在，无法发送关闭通知, orderNo: {}", orderNo);
                }
            }
        } catch (Exception e) {
            log.info("订单超时自动取消优惠活动回调失败, orderNos: {}, error: {}", needUpdateOrderNos, e.getMessage());
        }
    }

    private Map<String, String> generateOrderCloseAppletsParam(
            String thing19,
            String phoneNumber15,
            String thing22,
            String thing6,
            String amount9) {
        Map<String, String> params = new HashMap<>();
        //客户名称
        params.put("thing19", thing19);
        //联系方式
        params.put("phone_number15", phoneNumber15);
        //收货地址
        params.put("thing22", thing22);
        //商品名称
        params.put("thing6", thing6);
        //订单金额
        params.put("amount9", amount9);
        return params;
    }

    @Override
    public void updateOrderStatus(String orderNo) {
        Order order = orderService.lambdaQuery()
                .eq(Order::getNo, orderNo)
                .eq(Order::getBuyerId, ISecurity.userMust().getId())
                .one();
        if (order == null) {
            throw OrderError.ORDER_NOT_EXIST.exception();
        }
        if (OrderStatus.UNPAID != order.getStatus()) {
            throw OrderError.ORDER_PAID.exception();
        }
        if (order.getStatus().isClosed()) {
            return;
        }
        closeOrderService.updateOrderStatus(Set.of(orderNo), OrderStatus.UNPAID, OrderStatus.BUYER_CLOSED);
    }

    @Override
    @Log("订单支付超时, 系统取消订单")
    public void closeOrderPaidTimeout(String orderNo) {
        closeOrderService.updateOrderStatus(Set.of(orderNo), OrderStatus.UNPAID, OrderStatus.SYSTEM_CLOSED);
    }


    @Override
    @Redisson(name = OrderConstant.ORDER_EDIT_LOCK_KEY, key = "#orderNo")
    @Transactional(rollbackFor = Exception.class)
    public void shopCloseOrder(String orderNo, String shopOrderNo) {
        Order order = Option.of(
                        orderService.lambdaQuery()
                                .select(Order::getType, Order::getActivityId, Order::getNo, Order::getStatus, Order::getBuyerId)
                                .eq(Order::getNo, orderNo)
                                .one()
                )
                .getOrElseThrow(OrderError.ORDER_NOT_EXIST::exception);
        if (OrderStatus.UNPAID != order.getStatus()) {
            throw OrderError.ORDER_PAID.exception();
        }
        //判断是否有其它正常状态店铺订单
        boolean exists = TenantShop.disable(
                () -> shopOrderService.lambdaQuery().eq(ShopOrder::getOrderNo, orderNo).ne(ShopOrder::getNo, shopOrderNo).exists()
        );
        //没有则直接关闭总订单
        if (!exists) {
            closeOrderService.updateOrderStatus(Set.of(orderNo), OrderStatus.UNPAID, OrderStatus.SELLER_CLOSED);
            // 查询订单对应的ShopOrder
            ShopOrder mainShopOrder = TenantShop.disable(() -> shopOrderService.lambdaQuery()
                    .eq(ShopOrder::getOrderNo, orderNo)
                    .one());
            if (mainShopOrder != null) {
                sendBarkOrderDiscountCallback(orderNo, mainShopOrder);
            } else {
                log.warn("未找到店铺订单信息，无法发送优惠活动回调, orderNo: {}", orderNo);
            }
            return;
        }
        OrderPayment unpaidOrderPayment = queryOrderService.getUnpaidOrderPayment(orderNo);
        ShopOrder shopOrder = TenantShop.disable(() -> shopOrderService.lambdaQuery()
                .eq(ShopOrder::getNo, shopOrderNo)
                .eq(ShopOrder::getOrderNo, orderNo)
                .eq(ShopOrder::getStatus, ShopOrderStatus.OK)
                .one()
        );
        if (shopOrder == null) {
            throw OrderError.ORDER_CLOSED.exception();
        }
        //取出店铺所有的商品
        Long shopId = shopOrder.getShopId();
        List<ShopOrderItem> shopOrderItems = TenantShop.disable(() -> shopOrderItemService.lambdaQuery()
                .eq(ShopOrderItem::getOrderNo, orderNo)
                .eq(ShopOrderItem::getShopId, shopId)
                .eq(ShopOrderItem::getStatus, ItemStatus.OK)
                .list()
        );
        long payAmount = unpaidOrderPayment.getPayAmount() - shopOrder.payAmount();
        //重新计算费用
        unpaidOrderPayment.setTotalAmount(unpaidOrderPayment.getTotalAmount() - shopOrder.getTotalAmount())
                .setDiscountAmount(unpaidOrderPayment.getDiscountAmount() - shopOrder.getDiscountAmount())
                .setFreightAmount(unpaidOrderPayment.getFreightAmount() - shopOrder.getFreightAmount())
                .setPayAmount(payAmount);
        boolean isNoNeedToPay = payAmount == 0;
        LocalDateTime now = LocalDateTime.now();
        if (isNoNeedToPay) {
            unpaidOrderPayment.setTransactions(Map.of())
                    .setPayTime(now)
                    .setPayerId(order.getBuyerId())
                    .setType(PayType.BALANCE);
        }
        orderPaymentService.updateById(unpaidOrderPayment);
        //如果需要支付的金额为0 则直接设置为已支付状态
        if (isNoNeedToPay) {
            orderService.lambdaUpdate()
                    .set(Order::getStatus, OrderStatus.PAID)
                    .eq(Order::getNo, orderNo)
                    .update();
        }
        //关闭店铺订单
        TenantShop.disable(
                () -> {
                    shopOrderService.lambdaUpdate()
                            .set(ShopOrder::getStatus, ShopOrderStatus.SELLER_CLOSED)
                            .set(ShopOrder::getUpdateTime, now)
                            .eq(ShopOrder::getId, shopOrder.getId())
                            .eq(ShopOrder::getNo, shopOrderNo)
                            .eq(ShopOrder::getOrderNo, orderNo)
                            .update();
                    shopOrderItemService.lambdaUpdate()
                            .set(ShopOrderItem::getStatus, ItemStatus.CLOSED)
                            .set(ShopOrderItem::getUpdateTime, now)
                            .eq(ShopOrderItem::getOrderNo, orderNo)
                            .eq(ShopOrderItem::getShopId, shopId)
                            .eq(ShopOrderItem::getStatus, ItemStatus.OK)
                            .update();
                }
        );
        sendBarkOrderDiscountCallback(orderNo, shopOrder);
        //发送 订单关闭mq 需要携带 店铺订单下的所有 商品
        CompletableTask.allOf(
                globalExecutor,
                () -> {
                    if (CollUtil.isEmpty(shopOrderItems)) {
                        return;
                    }
                    orderRabbitService.sendOrderClose(
                            new OrderInfo()
                                    .setActivityType(order.getType())
                                    .setActivityId(order.getActivityId())
                                    .setCloseType(OrderCloseType.SHOP)
                                    .setOrderNo(order.getNo())
                                    .setBuyerId(order.getBuyerId())
                                    .setShopId(shopId)
                                    .setSkuStocks(
                                            OrderUtil.toSkuStocks(shopOrderItems)
                                    )
                    );
                }
        );

    }

    private void sendBarkOrderDiscountCallback(String orderNo, ShopOrder shopOrder) {
        try {
            // 发送解锁优惠活动
            doUnLockedDiscount(orderNo);

            // 发送退款优惠活动记录回调
            doBarkOrderDiscountCallback(orderNo, shopOrder);
        } catch (Exception e) {
            // 发送失败不影响业务
            log.info("订单退款 优惠活动记录回调失败, orderNo: {}, shopOrderNo: {}, error: {}", orderNo, shopOrder.getNo(), e.getMessage());
        }

    }

    private void doBarkOrderDiscountCallback(String orderNo, ShopOrder shopOrder) {
        try {
            BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO = new BarkOrderDiscountCallbackQO();
            barkOrderDiscountCallbackQO.setOrderNo(orderNo);
            barkOrderDiscountCallbackQO.setRefundAmount(new BigDecimal(shopOrder.getTotalAmount()));
            barkOrderDiscountCallbackQO.setRefundType(1);
            barkOrderDiscountCallbackQO.setIsIntegralBack(1);
            settlementApiService.barkOrderDiscountCallback(barkOrderDiscountCallbackQO);
        } catch (Exception e) {
            log.error("订单退款 优惠活动记录回调失败, orderNo: {}, shopOrderNo: {}, error: {}", orderNo, shopOrder.getNo(), e.getMessage());
        }
    }

    private void doUnLockedDiscount(String orderNo) {
        try {
            SettlementUnLockedDiscountDTO dto = new SettlementUnLockedDiscountDTO();
            dto.setOrderNo(orderNo);
            Boolean isBefore = settlementApiService.lockedDiscount(dto);
            if (Boolean.FALSE.equals(isBefore)) {
                log.info("订单退款 释放优惠活动失败, orderNo: {}, result: {}", orderNo, isBefore);
            } else {
                log.info("订单退款 释放优惠活动成功, orderNo: {}", orderNo);
            }
        } catch (Exception e) {
            log.error("订单退款 释放优惠活动失败, orderNo: {}, error: {}", orderNo, e.getMessage());
        }
    }

    @Autowired
    public void setCloseOrderService(CloseOrderService closeOrderService) {
        this.closeOrderService = closeOrderService;
    }
}
