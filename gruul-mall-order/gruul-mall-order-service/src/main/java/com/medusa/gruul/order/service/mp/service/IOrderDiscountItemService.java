package com.medusa.gruul.order.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.order.api.entity.OrderDiscountItem;

import java.util.List;
import java.util.Set;

/**
 * 订单优惠项 服务类
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface IOrderDiscountItemService extends IService<OrderDiscountItem> {

    /**
     * 查询订单优惠项
     *
     * @param orderNo 订单号
     * @param shopId  店铺id
     * @param itemId  订单商品项id
     * @return 该商品订单优惠项列表
     */
    List<OrderDiscountItem> getItem(String orderNo, Long shopId, Long itemId);
    
    /**
     * 根据店铺ID列表批量查询订单优惠项列表
     *
     * @param shopIds 店铺ID列表
     * @return 订单优惠项列表
     */
    List<OrderDiscountItem> getOrderDiscountItemsByShopIds(Set<Long> shopIds);
    
    /**
     * 根据shopId、discountId、itemIds查询订单优惠项列表
     *
     * @param shopId     店铺ID
     * @param discountId 优惠ID
     * @param itemIds    订单商品项ID列表
     * @return 订单优惠项列表
     */
    List<OrderDiscountItem> getOrderDiscountItemsByIds(Long shopId, Long discountId, List<Long> itemIds);
}
