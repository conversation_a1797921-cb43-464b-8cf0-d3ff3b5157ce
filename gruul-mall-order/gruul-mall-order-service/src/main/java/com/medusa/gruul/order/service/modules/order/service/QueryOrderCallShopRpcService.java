package com.medusa.gruul.order.service.modules.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.service.model.dto.OrderPlatFormDeliveryDTO;
import com.medusa.gruul.order.service.model.dto.OrderQueryDTO;
import com.medusa.gruul.order.service.model.vo.OrderPlatFormDeliveryVO;
import com.medusa.gruul.order.service.model.vo.OrderStatusCountVO;

/**
 * 订单查询服务
 *
 * <AUTHOR>
 * date 2022/6/16
 */
public interface QueryOrderCallShopRpcService {

    /**
     * 订单分页查询
     *
     * @param queryPage 查询条件
     * @param export    是否是导出查询
     * @return 分页查询结果
     */
    IPage<Order> orderPage(boolean export, OrderQueryDTO queryPage);


    /**
     * 根据订单号，查询自营店铺订单和自营供货商订单
     *
     * @param dto 订单参数
     * @return 返回自营店铺订单和自营供货商订单
     */
    OrderPlatFormDeliveryVO getPlatFormDeliveryOrder(OrderPlatFormDeliveryDTO dto);
    
    /**
     * 统计各状态订单数量
     *
     * @param queryPage 查询条件
     * @return 各状态订单数量统计
     */
    OrderStatusCountVO countOrdersByStatus(OrderQueryDTO queryPage);

    /**
     * 根据当前用户角色获取订单状态统计
     *
     * @param queryPage 查询条件
     * @return 各状态订单数量统计，如果是用户角色则返回null
     */
    OrderStatusCountVO getOrderStatusCountByRole(OrderQueryDTO queryPage);

    /**
     * 分页查询订单并包含状态统计
     *
     * @param export 是否导出
     * @param queryPage 查询条件
     * @return 带有状态统计的订单分页数据
     */
    IPage<Order> orderPageWithStatusCount(boolean export, OrderQueryDTO queryPage);
}
