package com.medusa.gruul.order.service.modules.order.controller;

import com.medusa.gruul.common.member.dto.settlement.MemberPriceApplyCommodityQO;
import com.medusa.gruul.common.member.dto.settlement.MemberPriceApplyCommodityVO;
import com.medusa.gruul.common.member.dto.settlement.SettlementApplyOrderDTO;
import com.medusa.gruul.common.member.dto.settlement.SettlementApplyOrderShowVO;
import com.medusa.gruul.common.member.service.SettlementApiService;
import com.medusa.gruul.common.model.resp.Result;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算台 前端控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/order/settlement")
@RequiredArgsConstructor
public class SettlementController {

    @Resource
    private SettlementApiService settlementApiService;

    /**
     * 获取结算台优惠列表
     * @param dto 结算台优惠列表查询条件
     * @return 结算台优惠列表
     */
    @PostMapping("/get/discount/list")
    public Result<SettlementApplyOrderShowVO> getDiscountApplyList(@RequestBody SettlementApplyOrderDTO dto) {
        return Result.ok(settlementApiService.getDiscountApplyList(dto));
    }

    /**
     * 下单前计算优惠
     *
     * @param dto 订单入参
     * @return 优惠结果对象
     */
    @PostMapping(value = "/discount/calculate")
    public Result<SettlementApplyOrderShowVO> discountCalculate(@RequestBody SettlementApplyOrderDTO dto) {
        return Result.ok(settlementApiService.discountCalculate(dto));
    }

    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    @PostMapping(value = "/get/member/price/commodity", produces = "application/json;charset=utf-8")
    public Result<MemberPriceApplyCommodityVO> getMemberPriceApplyCommodity(@RequestBody MemberPriceApplyCommodityQO qo) {
        return Result.ok(settlementApiService.getMemberPriceApplyCommodity(qo));
    }
}
