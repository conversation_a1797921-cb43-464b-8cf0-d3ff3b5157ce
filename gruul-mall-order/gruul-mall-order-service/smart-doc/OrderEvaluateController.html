<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="smart-doc">
    <title>gruul-mall-order-service</title>
    <link rel="stylesheet" href="font.css">
    <link rel="stylesheet" href="AllInOne.css?v=1694517475408"/>
    
    <style>.literalblock pre, .listingblock pre:not(.highlight), .listingblock pre[class="highlight"], .listingblock pre[class^="highlight "], .listingblock pre.CodeRay, .listingblock pre.prettyprint {
        background: #f7f7f8;
    }

    .hljs {
        padding: 0em;
    }</style>
    <script src="highlight.min.js"></script>
    <script src="jquery.min.js"></script>
</head>
<body class="book toc2 toc-left">
<div id="header"><h1>gruul-mall-order-service</h1>
    <div id="toc" class="toc2">
        <div id="book-search-input"><input id="search" type="text" placeholder="Type to search"></div>
        <div id="toctitle"><span>API Reference</span></div>
        <ul id="accordion" class="sectlevel1">
            <li class=""><a class="dd" href="api.html#header">1.&nbsp;订单发货控制器 包裹</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="api.html#_1_1_查询所有未发货商品">1.1.&nbsp;查询所有未发货商品</a>
                    </li>

                    <li><a
                            href="api.html#_1_2_查询所有已发货商品">1.2.&nbsp;查询所有已发货商品</a>
                    </li>

                    <li><a
                            href="api.html#_1_3_查询订单里所有已发货包裹">1.3.&nbsp;查询订单里所有已发货包裹</a>
                    </li>

                    <li><a
                            href="api.html#_1_4_根据店铺订单号查询第一个物流包裹">1.4.&nbsp;根据店铺订单号查询第一个物流包裹</a>
                    </li>

                    <li><a
                            href="api.html#_1_5_商品发货">1.5.&nbsp;商品发货</a>
                    </li>

                    <li><a
                            href="api.html#_1_6_批量发货">1.6.&nbsp;批量发货</a>
                    </li>

                    <li><a
                            href="api.html#_1_7_包裹确认收货">1.7.&nbsp;包裹确认收货</a>
                    </li>
                </ul>
            </li>
            <li class="open"><a class="dd" href="OrderEvaluateController.html#header">2.&nbsp;前端控制器</a>
                <ul class="sectlevel2" style="display: block">
                    <li><a
                            href="OrderEvaluateController.html#_2_1_分页查询真实评价">2.1.&nbsp;分页查询真实评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_2_用户评价订单">2.2.&nbsp;用户评价订单</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_3_商品详情页评价概况">2.3.&nbsp;商品详情页评价概况</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_4_商品详情页 分页查询评价">2.4.&nbsp;商品详情页 分页查询评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_5_批量设为精选/取消精选">2.5.&nbsp;批量设为精选/取消精选</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_6_商家回复评价">2.6.&nbsp;商家回复评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_7_查询订单商品评价">2.7.&nbsp;查询订单商品评价</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderConfigController.html#header">3.&nbsp;订单配置控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderConfigController.html#_3_1_订单超时时间配置">3.1.&nbsp;订单超时时间配置</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_2_查询订单超时时间配置">3.2.&nbsp;查询订单超时时间配置</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_3_商铺交易信息编辑">3.3.&nbsp;商铺交易信息编辑</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_4_店铺交易信息">3.4.&nbsp;店铺交易信息</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_5_根据店铺id列表 批量查询下单设置">3.5.&nbsp;根据店铺id列表 批量查询下单设置</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderController.html#header">4.&nbsp;订单信息表 前端控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderController.html#_4_1_创建订单并返回订单号">4.1.&nbsp;创建订单并返回订单号</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_2_分页查询订单">4.2.&nbsp;分页查询订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_3_查询订单的创建情况">4.3.&nbsp;查询订单的创建情况</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_4_根据订单号查询订单详情">4.4.&nbsp;根据订单号查询订单详情</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_5_根据订单号 店铺订单号查询店铺订单详情">4.5.&nbsp;根据订单号 店铺订单号查询店铺订单详情</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_6_根据订单号与item id 查询 订单商品项">4.6.&nbsp;根据订单号与item id 查询 订单商品项</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_7_店铺关闭未支付订单">4.7.&nbsp;店铺关闭未支付订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_8_获取未支付的订单支付信息">4.8.&nbsp;获取未支付的订单支付信息</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_9_关闭订单">4.9.&nbsp;关闭订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_10_用户个人订单统计">4.10.&nbsp;用户个人订单统计</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_11_订单批量备注">4.11.&nbsp;订单批量备注</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderDistributionCostController.html#header">5.&nbsp;订单运费计算</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderDistributionCostController.html#_5_1_">5.1.&nbsp;</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderOverviewController.html#header">6.&nbsp;订单统计控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderOverviewController.html#_6_1_平台总订单数，按照已评价的包裹计算">6.1.&nbsp;平台总订单数，按照已评价的包裹计算</a>
                    </li>

                    <li><a
                            href="OrderOverviewController.html#_6_2_店铺订单统计">6.2.&nbsp;店铺订单统计</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderPayController.html#header">7.&nbsp;订单支付</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderPayController.html#_7_1_获取渲染支付数据">7.1.&nbsp;获取渲染支付数据</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="ReceiverController.html#header">8.&nbsp;收货人控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="ReceiverController.html#_8_1_修改订单收货人信息">8.1.&nbsp;修改订单收货人信息</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</div>
<div id="content">
    <div class="sect1"><h2 id="_前端控制器"><a class="anchor" href="#_前端控制器"></a><a class="link" href="#_前端控制器">2.&nbsp;前端控制器</a>
    </h2>
        <div class="sectionbody">
            <div class="sect2"><h3 id="_2_1_分页查询真实评价">
                <a class="anchor" href="#_2_1_分页查询真实评价"></a>
                <a class="link" href="#_2_1_分页查询真实评价">2.1.&nbsp;分页查询真实评价</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate" class="bare">&nbsp;/gruul-mall-order/order/evaluate</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;分页查询真实评价</p></div>
                <div class="paragraph"><p><strong>Query-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">keywords</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">根据订单号 || 商品名称查询</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">type</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价查询类型&lt;br/&gt;(See: 评论查询类型)</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">startTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">成交时间 开始时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">endTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">成交时间 结束时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价星级</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/evaluate?pages=0&id=0&createTime=yyyy-MM-dd HH:mm:ss&updateTime=yyyy-MM-dd HH:mm:ss&version=0&deleted=true&userId=0&shopId=0&productId=0&skuId=0&specs=,&rate=0&medias=,&isExcellent=true&replyTime=yyyy-MM-dd HH:mm:ss&evaluatePerson=0&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&type=CONTENT&startTime=yyyy-MM-dd&endTime=yyyy-MM-dd&keywords=&nickname=&countId=&name=&orders[0].asc=true&orders[0].column=</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "id": 0,
        "createTime": "yyyy-MM-dd HH:mm:ss",
        "updateTime": "yyyy-MM-dd HH:mm:ss",
        "version": 0,
        "deleted": true,
        "orderNo": "",
        "userId": 0,
        "nickname": "",
        "avatar": "",
        "shopId": 0,
        "productId": 0,
        "name": "",
        "skuId": 0,
        "image": "",
        "specs": [
          ""
        ],
        "rate": 0,
        "comment": "",
        "medias": [
          ""
        ],
        "isExcellent": true,
        "shopReply": "",
        "replyTime": "yyyy-MM-dd HH:mm:ss",
        "evaluatePerson": 0
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_2_用户评价订单">
                <a class="anchor" href="#_2_2_用户评价订单"></a>
                <a class="link" href="#_2_2_用户评价订单">2.2.&nbsp;用户评价订单</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate" class="bare">&nbsp;/gruul-mall-order/order/evaluate</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;POST</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/json</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;用户评价订单</p></div>
                <div class="paragraph"><p><strong>Body-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">包裹id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">items</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单包含的商品<br>Validate[max: 1; ]</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─key</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单商品项 itemId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">sku Id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论内容<br>Validate[max: 500; ]</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">上传的文件（图片/视频）</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&quot;商品评分&quot;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -i /gruul-mall-order/order/evaluate --data '{
  "orderNo": "",
  "shopId": 0,
  "items": [
    {
      "key": {
        "productId": 0,
        "skuId": 0
      },
      "comment": "",
      "medias": [
        ""
      ],
      "rate": 0
    }
  ]
}'</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_3_商品详情页评价概况">
                <a class="anchor" href="#_2_3_商品详情页评价概况"></a>
                <a class="link" href="#_2_3_商品详情页评价概况">2.3.&nbsp;商品详情页评价概况</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}/overview" class="bare">&nbsp;/gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}/overview</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;商品详情页评价概况</p></div>
                <div class="paragraph"><p><strong>Query-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">type</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价查询类型&lt;br/&gt;(See: 评论查询类型)</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}/overview?pages=0&id=0&createTime=yyyy-MM-dd HH:mm:ss&updateTime=yyyy-MM-dd HH:mm:ss&version=0&deleted=true&userId=0&shopId=0&productId=0&skuId=0&specs=,&rate=0&medias=,&isExcellent=true&replyTime=yyyy-MM-dd HH:mm:ss&evaluatePerson=0&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&type=CONTENT&countId=&orders[0].asc=true&orders[0].column=</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─totalCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">总条数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─praiseCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">好评总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─contentCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">有内容的评价总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─mediaCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">有图片的评论总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─evaluate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价概况展示的评价</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "totalCount": 0,
    "praiseCount": 0,
    "contentCount": 0,
    "mediaCount": 0,
    "evaluate": {
      "id": 0,
      "createTime": "yyyy-MM-dd HH:mm:ss",
      "updateTime": "yyyy-MM-dd HH:mm:ss",
      "version": 0,
      "deleted": true,
      "orderNo": "",
      "userId": 0,
      "nickname": "",
      "avatar": "",
      "shopId": 0,
      "productId": 0,
      "name": "",
      "skuId": 0,
      "image": "",
      "specs": [
        ""
      ],
      "rate": 0,
      "comment": "",
      "medias": [
        ""
      ],
      "isExcellent": true,
      "shopReply": "",
      "replyTime": "yyyy-MM-dd HH:mm:ss",
      "evaluatePerson": 0
    }
  }
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_4_商品详情页 分页查询评价">
                <a class="anchor" href="#_2_4_商品详情页 分页查询评价"></a>
                <a class="link" href="#_2_4_商品详情页 分页查询评价">2.4.&nbsp;商品详情页 分页查询评价</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}" class="bare">&nbsp;/gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;商品详情页 分页查询评价</p></div>
                <div class="paragraph"><p><strong>Query-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">单页分页条数限制</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">type</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价查询类型&lt;br/&gt;(See: 评论查询类型)</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/evaluate/shop/{shopId}/product/{productId}?pages=0&id=0&createTime=yyyy-MM-dd HH:mm:ss&updateTime=yyyy-MM-dd HH:mm:ss&version=0&deleted=true&userId=0&shopId=0&productId=0&skuId=0&specs=,&rate=0&medias=,&isExcellent=true&replyTime=yyyy-MM-dd HH:mm:ss&evaluatePerson=0&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&type=CONTENT&orders[0].asc=true&countId=&orders[0].column=</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "id": 0,
        "createTime": "yyyy-MM-dd HH:mm:ss",
        "updateTime": "yyyy-MM-dd HH:mm:ss",
        "version": 0,
        "deleted": true,
        "orderNo": "",
        "userId": 0,
        "nickname": "",
        "avatar": "",
        "shopId": 0,
        "productId": 0,
        "name": "",
        "skuId": 0,
        "image": "",
        "specs": [
          ""
        ],
        "rate": 0,
        "comment": "",
        "medias": [
          ""
        ],
        "isExcellent": true,
        "shopReply": "",
        "replyTime": "yyyy-MM-dd HH:mm:ss",
        "evaluatePerson": 0
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_5_批量设为精选/取消精选">
                <a class="anchor" href="#_2_5_批量设为精选/取消精选"></a>
                <a class="link" href="#_2_5_批量设为精选/取消精选">2.5.&nbsp;批量设为精选/取消精选</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate/excellent/{isExcellent}" class="bare">&nbsp;/gruul-mall-order/order/evaluate/excellent/{isExcellent}</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;PUT</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/json</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;批量设为精选/取消精选</p></div>
                <div class="paragraph"><p><strong>Path-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">设为精选/取消精选</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Body-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">evaluateIds</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价ids,[array of int64]</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X PUT -H 'Content-Type: application/json' -i /gruul-mall-order/order/evaluate/excellent/{isExcellent} --data '[
  0
]'</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_6_商家回复评价">
                <a class="anchor" href="#_2_6_商家回复评价"></a>
                <a class="link" href="#_2_6_商家回复评价">2.6.&nbsp;商家回复评价</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate/{evaluateId}/reply" class="bare">&nbsp;/gruul-mall-order/order/evaluate/{evaluateId}/reply</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;PUT</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/json</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;商家回复评价</p></div>
                <div class="paragraph"><p><strong>Path-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">evaluateId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评价id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Body-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">reply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">     商家回复内容</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X PUT -H 'Content-Type: application/json' -i /gruul-mall-order/order/evaluate/{evaluateId}/reply</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_2_7_查询订单商品评价">
                <a class="anchor" href="#_2_7_查询订单商品评价"></a>
                <a class="link" href="#_2_7_查询订单商品评价">2.7.&nbsp;查询订单商品评价</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/evaluate/{orderNo}" class="bare">&nbsp;/gruul-mall-order/order/evaluate/{orderNo}</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;WuDi</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;查询订单商品评价</p></div>
                <div class="paragraph"><p><strong>Path-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Query-parameters:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                        <col style="width: 20%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Parameter</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Required</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/evaluate/{orderNo}?shopId=0&productId=0&skuId=0</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">id<br>{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─createTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─updateTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─version</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─deleted</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─orderNo</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">订单号</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─userId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─nickname</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户昵称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─avatar</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">用户头像</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─name</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品 sku id</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─image</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">商品sku图片</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─specs</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">规格</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─rate</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评分</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─comment</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─medias</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论图片/视频</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─isExcellent</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">是否是精选评价<br>excellentTime;</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─shopReply</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">卖家回复</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─replyTime</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">买家回复时间 reply_time</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─evaluatePerson</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">评论人数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "createTime": "yyyy-MM-dd HH:mm:ss",
    "updateTime": "yyyy-MM-dd HH:mm:ss",
    "version": 0,
    "deleted": true,
    "orderNo": "",
    "userId": 0,
    "nickname": "",
    "avatar": "",
    "shopId": 0,
    "productId": 0,
    "name": "",
    "skuId": 0,
    "image": "",
    "specs": [
      ""
    ],
    "rate": 0,
    "comment": "",
    "medias": [
      ""
    ],
    "isExcellent": true,
    "shopReply": "",
    "replyTime": "yyyy-MM-dd HH:mm:ss",
    "evaluatePerson": 0
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="page-footer"><span class="copyright">Generated by smart-doc at 2023-09-12 19:17:55</span><span
            class="footer-modification">Suggestions,contact,support and error reporting on<a
            href="https://gitee.com/smart-doc-team/smart-doc" target="_blank">&nbsp;Gitee</a>&nbsp;or<a
            href="https://github.com/smart-doc-group/smart-doc.git" target="_blank">&nbsp;Github</a></span></footer>
</div>
<script src="search.js?v=1694517475408"></script>
<script>
    $(function () {
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            const $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
        hljs.highlightAll()
    });
</script>
</body>
</html>
