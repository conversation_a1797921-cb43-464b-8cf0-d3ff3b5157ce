<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="smart-doc">
    <title>gruul-mall-order-service</title>
    <link rel="stylesheet" href="font.css">
    <link rel="stylesheet" href="AllInOne.css?v=1694517475408"/>
    
    <style>.literalblock pre, .listingblock pre:not(.highlight), .listingblock pre[class="highlight"], .listingblock pre[class^="highlight "], .listingblock pre.CodeRay, .listingblock pre.prettyprint {
        background: #f7f7f8;
    }

    .hljs {
        padding: 0em;
    }</style>
    <script src="highlight.min.js"></script>
    <script src="jquery.min.js"></script>
</head>
<body class="book toc2 toc-left">
<div id="header"><h1>gruul-mall-order-service</h1>
    <div id="toc" class="toc2">
        <div id="book-search-input"><input id="search" type="text" placeholder="Type to search"></div>
        <div id="toctitle"><span>API Reference</span></div>
        <ul id="accordion" class="sectlevel1">
            <li class=""><a class="dd" href="api.html#header">1.&nbsp;订单发货控制器 包裹</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="api.html#_1_1_查询所有未发货商品">1.1.&nbsp;查询所有未发货商品</a>
                    </li>

                    <li><a
                            href="api.html#_1_2_查询所有已发货商品">1.2.&nbsp;查询所有已发货商品</a>
                    </li>

                    <li><a
                            href="api.html#_1_3_查询订单里所有已发货包裹">1.3.&nbsp;查询订单里所有已发货包裹</a>
                    </li>

                    <li><a
                            href="api.html#_1_4_根据店铺订单号查询第一个物流包裹">1.4.&nbsp;根据店铺订单号查询第一个物流包裹</a>
                    </li>

                    <li><a
                            href="api.html#_1_5_商品发货">1.5.&nbsp;商品发货</a>
                    </li>

                    <li><a
                            href="api.html#_1_6_批量发货">1.6.&nbsp;批量发货</a>
                    </li>

                    <li><a
                            href="api.html#_1_7_包裹确认收货">1.7.&nbsp;包裹确认收货</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderEvaluateController.html#header">2.&nbsp;前端控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderEvaluateController.html#_2_1_分页查询真实评价">2.1.&nbsp;分页查询真实评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_2_用户评价订单">2.2.&nbsp;用户评价订单</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_3_商品详情页评价概况">2.3.&nbsp;商品详情页评价概况</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_4_商品详情页 分页查询评价">2.4.&nbsp;商品详情页 分页查询评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_5_批量设为精选/取消精选">2.5.&nbsp;批量设为精选/取消精选</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_6_商家回复评价">2.6.&nbsp;商家回复评价</a>
                    </li>

                    <li><a
                            href="OrderEvaluateController.html#_2_7_查询订单商品评价">2.7.&nbsp;查询订单商品评价</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderConfigController.html#header">3.&nbsp;订单配置控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderConfigController.html#_3_1_订单超时时间配置">3.1.&nbsp;订单超时时间配置</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_2_查询订单超时时间配置">3.2.&nbsp;查询订单超时时间配置</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_3_商铺交易信息编辑">3.3.&nbsp;商铺交易信息编辑</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_4_店铺交易信息">3.4.&nbsp;店铺交易信息</a>
                    </li>

                    <li><a
                            href="OrderConfigController.html#_3_5_根据店铺id列表 批量查询下单设置">3.5.&nbsp;根据店铺id列表 批量查询下单设置</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderController.html#header">4.&nbsp;订单信息表 前端控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderController.html#_4_1_创建订单并返回订单号">4.1.&nbsp;创建订单并返回订单号</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_2_分页查询订单">4.2.&nbsp;分页查询订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_3_查询订单的创建情况">4.3.&nbsp;查询订单的创建情况</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_4_根据订单号查询订单详情">4.4.&nbsp;根据订单号查询订单详情</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_5_根据订单号 店铺订单号查询店铺订单详情">4.5.&nbsp;根据订单号 店铺订单号查询店铺订单详情</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_6_根据订单号与item id 查询 订单商品项">4.6.&nbsp;根据订单号与item id 查询 订单商品项</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_7_店铺关闭未支付订单">4.7.&nbsp;店铺关闭未支付订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_8_获取未支付的订单支付信息">4.8.&nbsp;获取未支付的订单支付信息</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_9_关闭订单">4.9.&nbsp;关闭订单</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_10_用户个人订单统计">4.10.&nbsp;用户个人订单统计</a>
                    </li>

                    <li><a
                            href="OrderController.html#_4_11_订单批量备注">4.11.&nbsp;订单批量备注</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderDistributionCostController.html#header">5.&nbsp;订单运费计算</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderDistributionCostController.html#_5_1_">5.1.&nbsp;</a>
                    </li>
                </ul>
            </li>
            <li class="open"><a class="dd" href="OrderOverviewController.html#header">6.&nbsp;订单统计控制器</a>
                <ul class="sectlevel2" style="display: block">
                    <li><a
                            href="OrderOverviewController.html#_6_1_平台总订单数，按照已评价的包裹计算">6.1.&nbsp;平台总订单数，按照已评价的包裹计算</a>
                    </li>

                    <li><a
                            href="OrderOverviewController.html#_6_2_店铺订单统计">6.2.&nbsp;店铺订单统计</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="OrderPayController.html#header">7.&nbsp;订单支付</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="OrderPayController.html#_7_1_获取渲染支付数据">7.1.&nbsp;获取渲染支付数据</a>
                    </li>
                </ul>
            </li>
            <li class=""><a class="dd" href="ReceiverController.html#header">8.&nbsp;收货人控制器</a>
                <ul class="sectlevel2" style="display: none">
                    <li><a
                            href="ReceiverController.html#_8_1_修改订单收货人信息">8.1.&nbsp;修改订单收货人信息</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</div>
<div id="content">
    <div class="sect1"><h2 id="_订单统计控制器"><a class="anchor" href="#_订单统计控制器"></a><a class="link" href="#_订单统计控制器">6.&nbsp;订单统计控制器</a>
    </h2>
        <div class="sectionbody">
            <div class="sect2"><h3 id="_6_1_平台总订单数，按照已评价的包裹计算">
                <a class="anchor" href="#_6_1_平台总订单数，按照已评价的包裹计算"></a>
                <a class="link" href="#_6_1_平台总订单数，按照已评价的包裹计算">6.1.&nbsp;平台总订单数，按照已评价的包裹计算</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/overview/platform" class="bare">&nbsp;/gruul-mall-order/order/overview/platform</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;张治保
date 2022/10/25</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;平台总订单数，按照已评价的包裹计算</p></div>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/overview/platform</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": 0
}</code></pre>
                    </div>
                </div>
            </div>

            <div class="sect2"><h3 id="_6_2_店铺订单统计">
                <a class="anchor" href="#_6_2_店铺订单统计"></a>
                <a class="link" href="#_6_2_店铺订单统计">6.2.&nbsp;店铺订单统计</a>
            </h3>
                <div class="paragraph"><p><strong>URL:</strong><a href="/gruul-mall-order/order/overview/shop" class="bare">&nbsp;/gruul-mall-order/order/overview/shop</a>
                </p></div>
                <div class="paragraph"><p><strong>Type:</strong>&nbsp;GET</p></div>
                <div class="paragraph"><p><strong>Author:</strong>&nbsp;张治保
date 2022/10/25</p></div>
                <div class="paragraph"><p><strong>Content-Type:</strong>&nbsp;application/x-www-form-urlencoded;charset=UTF-8</p></div>
                <div class="paragraph"><p><strong>Description:</strong>&nbsp;店铺订单统计</p></div>
                <div class="paragraph"><p><strong>Request-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="bash">curl -X GET -i /gruul-mall-order/order/overview/shop</code></pre>
                    </div>
                </div>
                <div class="paragraph"><p><strong>Response-fields:</strong></p></div>
                <table class="tableblock frame-all grid-all spread">
                    <colgroup>
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                        <col style="width: 25%;">
                    </colgroup>
                    <thead>
                    <tr>
                        <th class="tableblock halign-left valign-top">Field</th>
                        <th class="tableblock halign-left valign-top">Type</th>
                        <th class="tableblock halign-left valign-top">Description</th>
                        <th class="tableblock halign-left valign-top">Since</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─unpaid</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">待付款订单数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─undelivered</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">待发货订单数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>

                    <tr>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">└─unreceived</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">待收货订单数</p></td>
                        <td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td>
                    </tr>
                    </tbody>
                </table>
                <div class="paragraph"><p><strong>Response-example:</strong></p></div>
                <div class="listingblock">
                    <div class="content">
                        <pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "unpaid": 0,
    "undelivered": 0,
    "unreceived": 0
  }
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="page-footer"><span class="copyright">Generated by smart-doc at 2023-09-12 19:17:55</span><span
            class="footer-modification">Suggestions,contact,support and error reporting on<a
            href="https://gitee.com/smart-doc-team/smart-doc" target="_blank">&nbsp;Gitee</a>&nbsp;or<a
            href="https://github.com/smart-doc-group/smart-doc.git" target="_blank">&nbsp;Github</a></span></footer>
</div>
<script src="search.js?v=1694517475408"></script>
<script>
    $(function () {
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            const $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
        hljs.highlightAll()
    });
</script>
</body>
</html>
