(function(e,j){typeof exports=="object"&&typeof module<"u"?module.exports=j(require("vue"),require("vue-router"),require("@/components/chrome-tab/index.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/composables/useConvert"),require("@/components/SchemaForm.vue"),require("@vueuse/core"),require("@/store/modules/GDRegionData"),require("@/components/q-address"),require("lodash-es"),require("@/utils/date"),require("vue-clipboard3"),require("@element-plus/icons-vue"),require("@/components/remark/remark-flag.vue"),require("@/components/remark/remark-popup.vue"),require("@/utils/Storage"),require("@/components/q-editor/editor.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/chrome-tab/index.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/PageManage.vue","element-plus","@/composables/useConvert","@/components/SchemaForm.vue","@vueuse/core","@/store/modules/GDRegionData","@/components/q-address","lodash-es","@/utils/date","vue-clipboard3","@element-plus/icons-vue","@/components/remark/remark-flag.vue","@/components/remark/remark-popup.vue","@/utils/Storage","@/components/q-editor/editor.vue","@/apis/http"],j):(e=typeof globalThis<"u"?globalThis:e||self,e.Integral=j(e.IntegralContext.Vue,e.IntegralContext.VueRouter,e.IntegralContext.ChromeTab,e.IntegralContext.QTable,e.IntegralContext.QTableColumn,e.IntegralContext.PageManageTwo,e.IntegralContext.ElementPlus,e.IntegralContext.UseConvert,e.IntegralContext.SchemaForm,e.IntegralContext.VueUse,e.IntegralContext.GDRegionData,e.IntegralContext.QAddressIndex,e.IntegralContext.Lodash,e.IntegralContext.DateUtil,e.IntegralContext.VueClipboard3,e.IntegralContext.ElementPlusIconsVue,e.IntegralContext.RemarkFlag,e.IntegralContext.RemarkPopup,e.IntegralContext.Storage,e.IntegralContext.EditorTwo,e.IntegralContext.Request))})(this,function(e,j,ge,se,B,ie,_,de,ce,pe,Ve,ye,_e,xe,Ne,he,Ce,ke,Ee,me,R){"use strict";var ue=document.createElement("style");ue.textContent=`@charset "UTF-8";[data-v-a070ac46]{font-size:14px}.commodity[data-v-a070ac46]{display:flex;justify-content:center;align-items:center;justify-content:flex-start;align-items:flex-start}.commodity__right[data-v-a070ac46]{height:68px;margin-left:10px;display:flex;flex-direction:column;justify-content:space-between}.commodity__right--name[data-v-a070ac46]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.commodity__right--price[data-v-a070ac46]{width:150px;margin-top:15px;font-size:12px}.del[data-v-a070ac46]{cursor:pointer;margin:0 10px;color:red}.bj[data-v-a070ac46]{cursor:pointer;margin:0 10px;color:#409eff}.show[data-v-a070ac46]{display:none}.input-with-select[data-v-a070ac46]{width:350px;float:right;position:relative;z-index:9}[data-v-a070ac46] .body--header{width:14px;height:14px;float:left;border:0;transform:translate(5px,50px)}[data-v-a070ac46] .m__table--head.padding:after{content:""}[data-v-a070ac46] .m__table--body:after{content:""}.notShipment__info[data-v-81d54a6d]{display:flex;justify-content:center;align-items:center;flex-direction:column;color:#838383;align-items:start}.notShipment__show[data-v-81d54a6d]{font-size:14px;color:#333;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.el-table td[data-v-81d54a6d]{border-bottom:none}.tableStyle[data-v-81d54a6d]:before{width:0}.el-table[data-v-81d54a6d]{border-radius:9px;border:1px solid #d5d5d5}.el-table th.is-leaf[data-v-81d54a6d]{border-bottom:none}.footer[data-v-81d54a6d]{display:flex;justify-content:flex-end;align-items:center}.demo-tabs>.el-tabs__content[data-v-03b6b323]{padding:32px;color:#6b778c;font-size:32px;font-weight:600}[data-v-71fc9953]{font-size:14px}.goods-Infor[data-v-71fc9953]{display:flex;justify-content:center;align-items:center;align-items:flex-start}.goods-Infor__name[data-v-71fc9953]{height:68px;display:flex;justify-content:center;align-items:center;flex-direction:column;justify-content:space-around;align-items:flex-start;overflow:hidden}.goods-Infor__name--integral[data-v-71fc9953]{color:#ff7417}.base-vip-table[data-v-71fc9953]{overflow:auto}.base-vip-table-top[data-v-71fc9953]{display:flex;justify-content:center;align-items:center;justify-content:space-between;width:100%}.base-vip-table-top__left[data-v-71fc9953]{color:#333}.base-vip-table-top__left span[data-v-71fc9953]:after{content:"|";padding:0 5px}.base-vip-table-top__left span[data-v-71fc9953]:last-child:after{content:""}.operation[data-v-71fc9953]{text-align:right;display:flex}.ellipsis[data-v-71fc9953]{width:250px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;white-space:normal;text-overflow:unset;word-break:break-all}.content[data-v-71fc9953]{display:flex;flex-direction:column;height:45px;justify-content:space-around}.fix[data-v-71fc9953]{width:100%;cursor:pointer;display:flex;justify-content:center;align-items:center;height:40px;background:#f9f9f9}.fix__arrow[data-v-71fc9953]{margin-left:4px}.fix__down[data-v-71fc9953]{transform:rotate(180deg)}.warning[data-v-71fc9953]{line-height:24px;padding:14px;margin-bottom:10px;background:rgba(233,0,0,.05);color:#e90000;font-size:14px}.warning span[data-v-71fc9953]{font-size:18px;font-weight:700;color:#f72020}.item[data-v-71fc9953]{height:25px;line-height:25px}.title[data-v-1dc77ea1]{margin:15px 10px;display:flex;justify-content:center;align-items:center;justify-content:flex-start;font-size:14px;color:#555cfd;font-weight:700}.title[data-v-1dc77ea1]:before{content:"";display:inline-block;height:14px;width:2.5px;background:#555cfd;margin-right:9px}.use_rules[data-v-1dc77ea1]{font-size:14px;font-family:sans-serif,sans-serif-Normal;font-weight:400;text-align:LEFT;color:#333;line-height:30px}.use_rules__msg[data-v-1dc77ea1]{font-size:14px;text-align:LEFT;color:#d3d3d3}.save[data-v-1dc77ea1]{margin:30px 0;text-align:center}.get_rules[data-v-1dc77ea1]{text-indent:1em;padding:10px 0}.p20[data-v-1dc77ea1]{padding:0 0 0 20px}
`,document.head.appendChild(ue);const we={class:"q_plugin_container"},be=e.defineComponent({__name:"Integral",setup(N){const C=e.ref("goods"),g=j.useRoute(),l=[{label:"积分商品",name:"goods"},{label:"积分订单",name:"order"}],s={goods:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Xe)),order:e.defineAsyncComponent(()=>Promise.resolve().then(()=>zt)),rules:e.defineAsyncComponent(()=>Promise.resolve().then(()=>sl))};e.watchEffect(()=>{g.query.type&&g.query.type==="order"&&(C.value=g.query.type)});const x=m=>{m&&(C.value=m)};return(m,E)=>(e.openBlock(),e.createElementBlock("div",we,[e.createVNode(ge,{"tab-list":l,value:C.value,onHandleTabs:x},null,8,["value"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(s[C.value])))]))}}),K="addon-integral/integral/",re=K+"product/",ee=K+"order/",Se=N=>R.get({url:re+"list",params:N}),fe=(N,C)=>R.put({url:re+`updateStatus/${N}`,data:C}),Te=N=>R.del({url:re+`delete/${N}`}),Ie=N=>R.get({url:ee+"list",params:N}),Re=()=>R.get({url:K+"rules/info"}),De=N=>R.post({url:K+"rules/save",data:N}),Oe=N=>R.post({url:K+"rules/update",data:N}),Be=N=>R.get({url:ee+`deliver/single/undeliver/${N}`}),Ge=N=>R.put({url:ee+"deliver",data:N}),Le=N=>R.put({url:ee+`deliver/complete/${N}`}),Ue=()=>R.get({url:"gruul-mall-freight/fright/list",params:{current:1,size:1e3}}),Ae=()=>R.put({url:"addon-integral/integral/order/warning"}),$e={class:"handle_container"},Pe={class:"commodity"},Me={style:{width:"68px",height:"68px"}},qe={class:"commodity__right"},ze={class:"commodity__right--name"},Fe={key:1},je={style:{color:"#666"}},He={style:{color:"#ecb789"}},Ye={key:0},Qe={class:"shop-name"},We=e.defineComponent({__name:"integral-goods",setup(N){const{divTenThousand:C}=de(),g=e.reactive({size:20,current:1,total:0}),l=e.ref({status:"",keywords:""}),s=j.useRouter(),x=e.ref([]),m=e.ref([]),E=[{label:"关键词",labelWidth:60,prop:"keywords",valueType:"copy",fieldProps:{placeholder:"请输关键词"}}];w();async function w(){const{status:c,keywords:n}=l.value,{code:k,data:b,msg:S}=await Se({keyword:n,status:c,...g});if(k!==200){_.ElMessage.error(S||"获取积分商品列表失败");return}x.value=b.records,g.total=b.total}const I=()=>{Object.keys(l.value).forEach(c=>l.value[c]=""),w()},h=async()=>{if(!m.value.length){_.ElMessage.error("请选择商品");return}try{if(!await _.ElMessageBox.confirm("确定进行删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const n=m.value.map(k=>k.id);await G(n),m.value=[]}catch{return}},G=async c=>{const{code:n,data:k,msg:b}=await Te(c);if(n!==200){_.ElMessage.error(b||"删除失败");return}_.ElMessage.success(b||"删除成功"),w()},$=async c=>{if(!m.value.length){_.ElMessage.error("请选择商品");return}try{if(!await _.ElMessageBox.confirm(`确定进行${c==="SELL_OFF"?"下":"上"}架?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const k=m.value.map(b=>b.id);await z(c,k),m.value=[]}catch{return!1}};async function z(c,n){const{code:k,data:b,msg:S}=await fe(c,n);if(k!==200)return _.ElMessage.error(S||"操作失败"),!1;_.ElMessage.success("操作成功"),w()}const H=()=>{s.push({name:"addIntegralMallGoods"})},P=c=>{s.push({name:"addIntegralMallGoods",query:{id:c.id}})},T=async(c,n)=>{switch(c){case"DELETE":try{if(!await _.ElMessageBox.confirm("确定进行删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;G([n.id])}catch(k){console.log(k)}break}},o=c=>{g.size=c,w()},t=c=>{g.current=c,w()},u=async c=>{const{code:n,data:k,msg:b}=await fe(c.status==="SELL_OFF"?"SELL_ON":"SELL_OFF",[c.id]);if(n!==200)return _.ElMessage.error(b||"操作失败"),!1;w()},d=()=>{m.value=[],w()},y={REAL_PRODUCT:"实物商品",VIRTUAL_PRODUCT:"虚拟商品"};return(c,n)=>{const k=e.resolveComponent("el-tab-pane"),b=e.resolveComponent("el-tabs"),S=e.resolveComponent("el-button"),L=e.resolveComponent("el-image"),U=e.resolveComponent("el-tooltip"),D=e.resolveComponent("el-switch");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(ce,{modelValue:l.value,"onUpdate:modelValue":n[0]||(n[0]=f=>l.value=f),columns:E,onSearchHandle:w,onHandleReset:I},null,8,["modelValue"]),e.createVNode(b,{modelValue:l.value.status,"onUpdate:modelValue":n[1]||(n[1]=f=>l.value.status=f),class:"demo-tabs tab_container",onTabChange:d},{default:e.withCtx(()=>[e.createVNode(k,{label:"全部",name:""}),e.createVNode(k,{label:"已上架",name:"SELL_ON"}),e.createVNode(k,{label:"已下架",name:"SELL_OFF"})]),_:1},8,["modelValue"]),e.createElementVNode("div",$e,[e.createVNode(S,{type:"primary",onClick:H},{default:e.withCtx(()=>n[5]||(n[5]=[e.createTextVNode("新增积分商品")])),_:1}),l.value.status!=="SELL_ON"?(e.openBlock(),e.createBlock(S,{key:0,disabled:m.value.length===0,onClick:n[2]||(n[2]=f=>$("SELL_ON"))},{default:e.withCtx(()=>n[6]||(n[6]=[e.createTextVNode("批量上架")])),_:1},8,["disabled"])):e.createCommentVNode("",!0),l.value.status!=="SELL_OFF"?(e.openBlock(),e.createBlock(S,{key:1,disabled:m.value.length===0,onClick:n[3]||(n[3]=f=>$("SELL_OFF"))},{default:e.withCtx(()=>n[7]||(n[7]=[e.createTextVNode("批量下架")])),_:1},8,["disabled"])):e.createCommentVNode("",!0),e.createVNode(S,{disabled:m.value.length===0,onClick:h},{default:e.withCtx(()=>n[8]||(n[8]=[e.createTextVNode("批量删除")])),_:1},8,["disabled"])]),e.createVNode(e.unref(se),{ref:"multipleTableRef","checked-item":m.value,"onUpdate:checkedItem":n[4]||(n[4]=f=>m.value=f),data:x.value,selection:!0,style:{overflow:"auto"},"no-border":""},{default:e.withCtx(()=>[e.createVNode(B,{label:"商品",prop:"goodInfo",align:"left",width:"280"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",Pe,[e.createElementVNode("div",Me,[e.createVNode(L,{style:{width:"68px",height:"68px"},src:f.pic,fit:"fill"},null,8,["src"])]),e.createElementVNode("div",qe,[e.createElementVNode("div",ze,[f.name.length>=30?(e.openBlock(),e.createBlock(U,{key:0,effect:"dark",content:f.name,placement:"top-start"},{default:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(f==null?void 0:f.name),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",Fe,e.toDisplayString(f==null?void 0:f.name),1))]),e.createElementVNode("div",je,e.toDisplayString(y[f==null?void 0:f.productType]),1)])])]),_:1}),e.createVNode(B,{label:"积分",prop:"productType",align:"left",width:"180"},{default:e.withCtx(({row:f})=>[e.createElementVNode("div",He,[e.createElementVNode("span",null,e.toDisplayString(f.integralPrice),1),n[11]||(n[11]=e.createElementVNode("span",null,"积分",-1)),f.salePrice!=="0"?(e.openBlock(),e.createElementBlock("span",Ye,[n[9]||(n[9]=e.createTextVNode("+")),n[10]||(n[10]=e.createElementVNode("span",null,"￥",-1)),e.createElementVNode("span",null,e.toDisplayString(e.unref(C)(f.salePrice)),1)])):e.createCommentVNode("",!0)])]),_:1}),e.createVNode(B,{label:"商品库存",prop:"stock",align:"left",width:"120"}),e.createVNode(B,{label:"商品状态",width:"120",align:"left"},{default:e.withCtx(({row:f})=>[e.createElementVNode("span",Qe,[e.createVNode(D,{"model-value":f.status==="SELL_ON",class:"ml-2","inline-prompt":"","active-text":"上架","inactive-text":"下架",onChange:A=>u(f)},null,8,["model-value","onChange"])])]),_:1}),e.createVNode(B,{label:"操作",align:"right",width:"120",fixed:"right"},{default:e.withCtx(({row:f})=>[e.createVNode(S,{type:"primary",link:"",onClick:A=>P(f)},{default:e.withCtx(()=>n[12]||(n[12]=[e.createTextVNode("编辑")])),_:2},1032,["onClick"]),f.status==="SELL_OFF"?(e.openBlock(),e.createBlock(S,{key:0,type:"danger",link:"",onClick:A=>T("DELETE",f)},{default:e.withCtx(()=>n[13]||(n[13]=[e.createTextVNode("删除")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["checked-item","data"]),e.createVNode(ie,{class:"pagination","page-size":g.size,"page-num":g.current,total:g.total,onHandleSizeChange:o,onHandleCurrentChange:t},null,8,["page-size","page-num","total"])],64)}}}),dl="",Z=(N,C)=>{const g=N.__vccOpts||N;for(const[l,s]of C)g[l]=s;return g},Xe=Object.freeze(Object.defineProperty({__proto__:null,default:Z(We,[["__scopeId","data-v-a070ac46"]])},Symbol.toStringTag,{value:"Module"})),Je={style:{display:"flex","justify-content":"space-between","border-bottom":"1px dashed #ccc",height:"90px"}},Ke={style:{display:"flex","justify-content":"space-around","flex-direction":"column"}},Ze={style:{"font-weight":"bold"}},ve={key:0},et={key:1},tt={style:{display:"flex","justify-content":"space-around","flex-direction":"column"}},lt={key:0,style:{width:"300px"}},ot={style:{margin:"5px 0",display:"flex","justify-content":"space-between",height:"30px"}},at={class:"send"},nt={key:0,class:"send",style:{"font-weight":"bold"}},rt={key:1,class:"send",style:{"font-weight":"bold"}},st={class:"send"},it={style:{display:"flex"}},dt={class:"notShipment__info",style:{width:"200px","padding-left":"10px"}},ct={class:"notShipment__show"},pt={style:{"text-align":"center"}},mt={style:{"font-size":"12px"}},ut={class:"footer"},ft=e.defineComponent({__name:"notShipment",props:{currentNo:{type:String,required:!0},isShow:{type:Boolean,required:!0}},emits:["update:isShow","upload-list"],setup(N,{emit:C}){const g=N,l=e.ref([]),s=C,x=pe.useVModel(g,"isShow",s),m=e.ref({sellerRemark:"",buyerRemark:"",payTime:"",deliveryTime:"",accomplishTime:"",buyerId:"",buyerNickname:"",createTime:"",freightPrice:0,image:"",salePrice:"",integralOrderReceiver:{address:"",area:[],createTime:"",id:"",mobile:"",name:"",orderNo:"",updateTime:""},no:"",num:1,price:"",productName:"",status:"PAID",productType:"REAL_PRODUCT"}),E=e.ref(),w=e.ref(),I=e.reactive({address:[],company:[{required:!0,message:"请选择物流公司",trigger:"change"}],expressNo:[{required:!0,message:"请填写物流单号",trigger:"blur"},{min:8,max:20,message:"请填写正确的物流单号",trigger:"blur"}],expressCode:[{required:!0,message:"请选择物流公司",trigger:"change"}]}),h=e.reactive({deliverType:"EXPRESS",receiver:{name:"",mobile:"",address:""},expressCompany:"",expressCode:"",addressaddress:"",expressNo:"",expressName:""});G(),$();async function G(){if(!g.currentNo){_.ElMessage.error("获取订单号失败"),x.value=!1;return}const{code:o,data:t,msg:u}=await Be(g.currentNo);if(o!==200){_.ElMessage.error(u||"获取订单详情失败"),x.value=!1;return}m.value=t,E.value.toggleRowSelection(m.value,!0)}async function $(){const{data:o}=await Ue();l.value=o==null?void 0:o.records}const z=async()=>{var o;try{if(await((o=w.value)==null?void 0:o.validate())){const u=P(h.deliverType);console.log(u);const{code:d,msg:y,data:c}=await Ge([u]);if(d!==200){_.ElMessage.error(y||"发货失败");return}_.ElMessage.success("发货成功"),x.value=!1,s("upload-list")}}catch{}},H=o=>{var t,u,d;h.expressName=((d=(u=(t=l.value)==null?void 0:t.filter(y=>y.logisticsCompanyCode===o))==null?void 0:u.pop())==null?void 0:d.logisticsCompanyName)||""},P=o=>{var c,n;const{deliverType:t,expressCode:u,expressNo:d,expressName:y}=h;return o==="EXPRESS"?{integralOrderNo:g.currentNo,integralOrderDeliverType:((c=m.value)==null?void 0:c.productType)==="REAL_PRODUCT"?"EXPRESS":"WITHOUT",expressCompanyName:u,expressNo:d,expressName:y}:{integralOrderNo:g.currentNo,integralOrderDeliverType:((n=m.value)==null?void 0:n.productType)==="REAL_PRODUCT"?"EXPRESS":"WITHOUT"}},T=()=>{x.value=!1};return(o,t)=>{const u=e.resolveComponent("el-option"),d=e.resolveComponent("el-select"),y=e.resolveComponent("el-col"),c=e.resolveComponent("el-row"),n=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-input"),b=e.resolveComponent("el-avatar"),S=e.resolveComponent("el-table-column"),L=e.resolveComponent("el-table"),U=e.resolveComponent("el-form"),D=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(U,{ref_key:"ruleFormRef",ref:w,model:h,class:"notShipment",rules:I},{default:e.withCtx(()=>{var f,A,F,Y,Q,p,i;return[e.createElementVNode("div",Je,[e.createElementVNode("div",Ke,[e.createElementVNode("div",Ze,e.toDisplayString((A=(f=m.value)==null?void 0:f.integralOrderReceiver)==null?void 0:A.name),1),e.createElementVNode("div",null,e.toDisplayString((Y=(F=m.value)==null?void 0:F.integralOrderReceiver)==null?void 0:Y.mobile),1),m.value.integralOrderReceiver.address?(e.openBlock(),e.createElementBlock("div",ve,e.toDisplayString((Q=m.value.integralOrderReceiver.area)==null?void 0:Q.join(""))+" "+e.toDisplayString(m.value.integralOrderReceiver.address),1)):(e.openBlock(),e.createElementBlock("div",et,"暂无地址信息"))]),e.createElementVNode("div",tt,[((p=m.value)==null?void 0:p.productType)==="REAL_PRODUCT"?(e.openBlock(),e.createElementBlock("div",lt,[h.deliverType!=="WITHOUT"?(e.openBlock(),e.createBlock(n,{key:0,"label-width":"120px",prop:"expressCode"},{label:e.withCtx(()=>t[3]||(t[3]=[e.createElementVNode("div",{class:"send"},"物流公司",-1)])),default:e.withCtx(()=>[e.createVNode(c,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{span:20},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:h.expressCode,"onUpdate:modelValue":t[0]||(t[0]=V=>h.expressCode=V),placeholder:"请选择物流公司",class:"m-2",style:{width:"100%",height:"30px"},onChange:H},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,V=>(e.openBlock(),e.createBlock(u,{key:V.logisticsCompanyName,label:V.logisticsCompanyName,value:V.logisticsCompanyCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),h.deliverType==="EXPRESS"?(e.openBlock(),e.createBlock(n,{key:1,"label-width":"120px",prop:"expressNo"},{label:e.withCtx(()=>t[4]||(t[4]=[e.createElementVNode("div",null,"物流单号",-1)])),default:e.withCtx(()=>[e.createVNode(c,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{span:20},{default:e.withCtx(()=>[e.createVNode(k,{modelValue:h.expressNo,"onUpdate:modelValue":t[1]||(t[1]=V=>h.expressNo=V),placeholder:"",style:{width:"100%",height:"30px"},maxlength:"40"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)])):((i=m.value)==null?void 0:i.productType)==="VIRTUAL_PRODUCT"?(e.openBlock(),e.createBlock(n,{key:1,"label-width":"90px"},{default:e.withCtx(()=>t[5]||(t[5]=[e.createElementVNode("div",{style:{color:"#f20404"}},"您可通过网络形式(如:聊天工具将商品发给用户)发货",-1)])),_:1})):e.createCommentVNode("",!0)])]),e.createElementVNode("div",ot,[e.createVNode(n,{"label-width":"70px",left:""},{label:e.withCtx(()=>t[6]||(t[6]=[e.createElementVNode("div",{class:"send"},"订单号：",-1)])),default:e.withCtx(()=>[e.createElementVNode("div",at,e.toDisplayString(g.currentNo),1)]),_:1}),e.createVNode(n,{"label-width":"90px"},{label:e.withCtx(()=>t[7]||(t[7]=[e.createElementVNode("div",{class:"send"},"配送方式：",-1)])),default:e.withCtx(()=>{var V;return[((V=m.value)==null?void 0:V.productType)==="VIRTUAL_PRODUCT"?(e.openBlock(),e.createElementBlock("div",nt,"无需物流")):(e.openBlock(),e.createElementBlock("div",rt,"快递配送"))]}),_:1}),e.createVNode(n,{"label-width":"90px"},{label:e.withCtx(()=>t[8]||(t[8]=[e.createElementVNode("div",{class:"send"},"下单时间：",-1)])),default:e.withCtx(()=>[e.createElementVNode("div",st,e.toDisplayString(m.value.createTime),1)]),_:1})]),e.createVNode(L,{ref_key:"tableRef",ref:E,"empty-text":"暂无数据~",data:[m.value],"max-height":"250",style:{width:"100%","margin-bottom":"20px"},"header-row-style":()=>({fontSize:"14px",color:"#333333",fontWeight:"bold"})},{default:e.withCtx(()=>[e.createVNode(S,{label:"商品",align:"center"},{default:e.withCtx(({row:V})=>[e.createElementVNode("div",it,[e.createVNode(b,{style:{width:"68px",height:"68px","flex-shrink":"0"},shape:"square",size:"large",src:V.image},null,8,["src"]),e.createElementVNode("div",dt,[e.createElementVNode("div",ct,e.toDisplayString(V.productName),1)])])]),_:1}),e.createVNode(S,{label:"数量",width:"200",align:"center"},{default:e.withCtx(({row:V})=>[e.createElementVNode("div",pt,[e.createElementVNode("div",mt,e.toDisplayString(V.num)+" 件",1)])]),_:1})]),_:1},8,["data"]),h.deliverType==="PRINT_EXPRESS"?(e.openBlock(),e.createBlock(n,{key:0,"label-width":"90px",prop:""},{label:e.withCtx(()=>t[9]||(t[9]=[e.createElementVNode("div",null,"发货地址",-1)])),default:e.withCtx(()=>[e.createVNode(c,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(y,{span:20},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:h.addressaddress,"onUpdate:modelValue":t[2]||(t[2]=V=>h.addressaddress=V),placeholder:"选择发货地址",style:{width:"100%",height:"30px"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.deliveryAddressData,V=>(e.openBlock(),e.createBlock(u,{key:V.id,value:V.id,label:`${e.unref(ye.AddressFn)(e.unref(Ve.regionData),[V.provinceCode,V.cityCode,V.regionCode])}${V.address}`},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)]}),_:1},8,["model","rules"]),e.createElementVNode("footer",ut,[e.createVNode(D,{onClick:T},{default:e.withCtx(()=>t[10]||(t[10]=[e.createTextVNode("取消")])),_:1}),t[12]||(t[12]=e.createTextVNode()),e.createVNode(D,{type:"primary",onClick:z},{default:e.withCtx(()=>t[11]||(t[11]=[e.createTextVNode("确认")])),_:1})])],64)}}}),pl="",gt=Z(ft,[["__scopeId","data-v-81d54a6d"]]),Vt={class:"my-header"},yt=["id"],_t=e.defineComponent({__name:"order-shipment",props:{isShow:{type:Boolean,default:!1},currentNo:{type:String,default:""}},emits:["update:isShow","upload-list"],setup(N,{emit:C}){const g=N,l=C,s=pe.useVModel(g,"isShow",l),x=()=>{s.value=!1};return(m,E)=>{const w=e.resolveComponent("el-dialog");return e.openBlock(),e.createBlock(w,{modelValue:e.unref(s),"onUpdate:modelValue":E[2]||(E[2]=I=>e.isRef(s)?s.value=I:null),width:"700","before-close":x,"destroy-on-close":""},{header:e.withCtx(({titleId:I,titleClass:h})=>[e.createElementVNode("div",Vt,[e.createElementVNode("h4",{id:I,class:e.normalizeClass(h)},"商品发货",10,yt)])]),default:e.withCtx(()=>[e.createVNode(gt,{"is-show":e.unref(s),"onUpdate:isShow":E[0]||(E[0]=I=>e.isRef(s)?s.value=I:null),"current-no":g.currentNo,onUploadList:E[1]||(E[1]=I=>l("upload-list"))},null,8,["is-show","current-no"])]),_:1},8,["modelValue"])}}}),ml="",xt=Z(_t,[["__scopeId","data-v-03b6b323"]]),Nt={UNPAID:"未支付",PAID:"待发货",ON_DELIVERY:"已发货",ACCOMPLISH:"已完成",SYSTEM_CLOSED:"已关闭"},ht={key:1},Ct={class:"handle_container"},kt={class:"base-vip-table-top"},Et={class:"base-vip-table-top__left"},wt={class:"goods-Infor"},bt={class:"goods-Infor__name"},St={class:"ellipsis"},Tt={key:1},It={class:"goods-Infor__name--integral"},Rt={class:"commodity__right--price"},Dt={key:0},Ot={class:"content"},Bt={style:{width:"200px"}},Gt={style:{width:"100px",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},Lt={key:0},Ut={style:{width:"200px",overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"}},At={style:{display:"inline",color:"#ecb789"}},$t={class:"commodity__right--price content"},Pt={key:0},Mt={class:"money_text",style:{"margin-bottom":"5px"}},qt=e.defineComponent({__name:"integral-order",emits:["search-data","changeShow"],setup(N,{emit:C}){Ne();const g=e.reactive({size:20,current:1,total:0}),l=e.ref({no:"",consignee:"",productName:"",date:[],status:"",affiliationPlatform:""}),{divTenThousand:s}=de(),x=e.ref(!1),m=e.ref([]),E=e.ref(""),w=j.useRouter(),I=e.ref([]),h=e.ref([]),G=e.ref(!1),$=e.ref(""),z=e.ref("calc(100vh - 310px)"),H=[{label:"订单号",labelWidth:60,prop:"no",valueType:"copy",fieldProps:{placeholder:"请输入订单号",maxlength:20}},{label:"收货人姓名",prop:"consignee",labelWidth:90,valueType:"copy",fieldProps:{placeholder:"请输入收货人姓名",maxlength:30}},{label:"商品名称",prop:"productName",valueType:"copy",fieldProps:{placeholder:"请输入商品名称",maxlength:30}},{label:"下单时间",prop:"date",valueType:"date-picker",fieldProps:{type:"daterange",startPlaceholder:"开始时间",endPlaceholder:"结束时间",valueFormat:"YYYY-MM-DD"}},{label:"所属渠道",prop:"affiliationPlatform",valueType:"select",options:[{label:"小程序",value:"WECHAT_MINI_APP"},{label:"公众号",value:"WECHAT_MP"},{label:"PC商城",value:"PC"},{label:"H5商城",value:"H5"},{label:"IOS",value:"IOS"},{label:"安卓",value:"ANDROID"},{label:"鸿蒙",value:"HARMONY"}],fieldProps:{placeholder:"请选择"}}];T();const P=e.ref("0");async function T(){var W,X;const p=_e.cloneDeep(l.value);Array.isArray(p.date)&&(p.startTime=(W=p.date)==null?void 0:W[0],p.endTime=(X=p.date)==null?void 0:X[1]),delete p.date;const{code:i,data:V,msg:M}=await Ie({...g,...p});if(i!==200){_.ElMessage.error(M||"获取订单失败");return}I.value=V.records,g.total=V.total,P.value=V.waitDeliveryNum}const o=()=>{Object.keys(l.value).forEach(p=>l.value[p]=""),T()},t=p=>{$.value=p.no,G.value=!0},u=p=>{m.value=[p.no],p.sellerRemark&&(E.value=p.sellerRemark),x.value=!0},d=p=>{g.size=p,T()},y=p=>{g.current=p,T()},c=()=>{h.value=[],T()},n=p=>{w.push({name:"integralMallOrderDetail",query:{no:p}})},k=async p=>{try{await _.ElMessageBox.confirm("确认关闭订单?","提示",{confirmButtonText:"确定",cancelButtonText:"关闭",type:"warning"});const{code:i,msg:V,data:M}=await Le(p.no);if(i!==200){_.ElMessage.error(V||"关闭订单失败");return}_.ElMessage.success("关闭订单成功"),p.status="SYSTEM_CLOSED"}catch{}},b=e.ref(!1);e.watch(()=>b.value,p=>{z.value=p?"calc(100vh - 450px)":"calc(100vh - 310px)"});const S=e.ref("0"),L=new xe,U=e.ref(" "),D=e.ref("全部订单"),f=async p=>{if(l.value.status=p,T(),p==="PAID"){const{code:i,data:V,msg:M}=await Ae();if(i!==200)return _.ElMessage.error(M||"获取待发货订单总数失败");S.value=V}},A=p=>{if(U.value=" ",D.value=p,D.value==="近一个月订单"){const i=L.getLastMonth(new Date);F(i)}else if(D.value==="近三个月订单"){const i=L.getLastThreeMonth(new Date);F(i)}else l.value.date=[],T()},F=async p=>{const i=L.getYMDs(new Date);l.value.date=[p,i],T()},Y={WECHAT_MINI_APP:"小程序",WECHAT_MP:"公众号",PC:"PC商城",H5:"H5商城",IOS:"IOS",ANDROID:"安卓",HARMONY:"鸿蒙"},Q=()=>{if(console.log(h.value),!h.value.length)return _.ElMessage.error("请选择商品");const p=h.value.filter(i=>i.status==="PAID");if(!p.length)return _.ElMessage.error("暂无待发货订单");new Ee().setItem("integralOrderSendGoods",p,60*60*24),w.push({name:"integralMallOrderDelivery"})};return(p,i)=>{const V=e.resolveComponent("el-icon"),M=e.resolveComponent("el-dropdown-item"),W=e.resolveComponent("el-dropdown-menu"),X=e.resolveComponent("el-dropdown"),q=e.resolveComponent("el-tab-pane"),te=e.resolveComponent("el-badge"),le=e.resolveComponent("el-tabs"),oe=e.resolveComponent("el-button"),ae=e.resolveComponent("el-image"),ne=e.resolveComponent("el-tooltip"),v=e.resolveComponent("el-row"),J=e.resolveComponent("el-link");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(ce,{modelValue:l.value,"onUpdate:modelValue":i[0]||(i[0]=r=>l.value=r),columns:H,onSearchHandle:T,onHandleReset:o},null,8,["modelValue"]),e.createVNode(le,{modelValue:U.value,"onUpdate:modelValue":i[1]||(i[1]=r=>U.value=r),class:"demo-tabs tab_container",onTabChange:f},{default:e.withCtx(()=>[e.createVNode(q,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(D.value),1),e.createVNode(X,{placement:"bottom-end",trigger:"click",onCommand:A},{dropdown:e.withCtx(()=>[e.createVNode(W,null,{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["近一个月订单","近三个月订单","全部订单"],r=>e.createVNode(M,{key:r,command:r},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(r),1)]),_:2},1032,["command"])),64))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{height:"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(V,{class:"el-icon--right",style:{position:"relative",top:"12px"}},{default:e.withCtx(()=>[e.createVNode(e.unref(he.ArrowDown))]),_:1})])]),_:1})]),_:1}),e.createVNode(q,{label:"待付款",name:"UNPAID"}),e.createVNode(q,{name:"PAID"},{label:e.withCtx(()=>[P.value?(e.openBlock(),e.createBlock(te,{key:0,value:P.value,class:"item"},{default:e.withCtx(()=>i[7]||(i[7]=[e.createElementVNode("span",null,"待发货",-1)])),_:1},8,["value"])):(e.openBlock(),e.createElementBlock("span",ht,"待发货"))]),_:1}),e.createVNode(q,{label:"待收货",name:"ON_DELIVERY"}),e.createVNode(q,{label:"已完成",name:"ACCOMPLISH"}),e.createVNode(q,{label:"已关闭",name:"SYSTEM_CLOSED"})]),_:1},8,["modelValue"]),e.createElementVNode("div",Ct,[e.createVNode(oe,{onClick:Q},{default:e.withCtx(()=>i[8]||(i[8]=[e.createTextVNode("批量发货")])),_:1})]),e.createVNode(e.unref(se),{"checked-item":h.value,"onUpdate:checkedItem":i[2]||(i[2]=r=>h.value=r),data:I.value,class:"base-vip-table",style:e.normalizeStyle({height:z.value}),"header-selection":"","no-border":""},{header:e.withCtx(({row:r})=>[e.createElementVNode("div",kt,[e.createElementVNode("div",Et,[e.createElementVNode("span",null,"订单号 : "+e.toDisplayString(r.no),1),e.createElementVNode("span",null,"创建时间 : "+e.toDisplayString(r.createTime),1),e.createElementVNode("span",null,e.toDisplayString(Y[r.affiliationPlatform]),1)]),e.createVNode(Ce,{content:r.sellerRemark,onSeeRemark:a=>u(r)},null,8,["content","onSeeRemark"])])]),default:e.withCtx(()=>[e.createVNode(B,{label:"商品",width:"390",align:"left"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",wt,[e.createVNode(ae,{class:"customer-Infor__img",fit:"cover",style:{width:"68px",height:"68px","margin-right":"10px"},src:r.image},null,8,["src"]),e.createElementVNode("div",bt,[e.createElementVNode("div",St,[r.productName.length>=35?(e.openBlock(),e.createBlock(ne,{key:0,effect:"dark",content:r.productName,placement:"top-start"},{default:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(r.productName),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",Tt,e.toDisplayString(r.productName),1))]),e.createElementVNode("div",It,[e.createElementVNode("div",Rt,[e.createElementVNode("span",null,e.toDisplayString(r.price),1),i[11]||(i[11]=e.createElementVNode("span",null,"积分",-1)),r.salePrice!=="0"?(e.openBlock(),e.createElementBlock("span",Dt,[i[9]||(i[9]=e.createTextVNode("+")),i[10]||(i[10]=e.createElementVNode("span",null,"￥",-1)),e.createElementVNode("span",null,e.toDisplayString(e.unref(s)(r.salePrice)),1)])):e.createCommentVNode("",!0)])])])])]),_:1}),e.createVNode(B,{label:"客户信息",align:"left",width:"250"},{default:e.withCtx(({row:r})=>{var a;return[e.createElementVNode("div",Ot,[e.createElementVNode("div",Bt,[e.createElementVNode("span",Gt,e.toDisplayString(r.integralOrderReceiverVO.name?r.integralOrderReceiverVO.name:r.buyerNickname),1),r.integralOrderReceiverVO.mobile?(e.openBlock(),e.createElementBlock("span",Lt,"("+e.toDisplayString(r.integralOrderReceiverVO.mobile)+")",1)):e.createCommentVNode("",!0)]),e.createElementVNode("div",Ut,e.toDisplayString((a=r.integralOrderReceiverVO.area)==null?void 0:a.join(""))+" "+e.toDisplayString(r.integralOrderReceiverVO.address),1)])]}),_:1}),e.createVNode(B,{prop:"sex",label:"交易信息",align:"left",width:"230",class:"rate_size"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",At,[e.createElementVNode("div",$t,[e.createElementVNode("div",null,[e.createTextVNode(e.toDisplayString(r.price)+"积分 ",1),r.salePrice!=="0"?(e.openBlock(),e.createElementBlock("span",Pt,[i[12]||(i[12]=e.createTextVNode("+")),i[13]||(i[13]=e.createElementVNode("span",null,"￥",-1)),e.createElementVNode("span",null,e.toDisplayString(e.unref(s)(r.salePrice)),1)])):e.createCommentVNode("",!0)]),e.createElementVNode("div",null,"(运费"+e.toDisplayString(r.freightPrice&&e.unref(s)(r.freightPrice).toFixed(2))+"元)",1)])])]),_:1}),e.createVNode(B,{label:"订单状态",width:"120",align:"left"},{default:e.withCtx(({row:r})=>[e.createVNode(v,{justify:"space-between",align:"middle"},{default:e.withCtx(()=>[e.createVNode(v,{justify:"space-between",align:"middle",style:{"flex-direction":"column"}},{default:e.withCtx(()=>[e.createElementVNode("div",Mt,e.toDisplayString(e.unref(Nt)[r.status]),1)]),_:2},1024)]),_:2},1024)]),_:1}),e.createVNode(B,{label:"操作",align:"right",fixed:"right",width:"160"},{default:e.withCtx(({row:r})=>[["PAID"].includes(r.status)?(e.openBlock(),e.createBlock(J,{key:0,underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a=>t(r)},{default:e.withCtx(()=>i[14]||(i[14]=[e.createTextVNode(" 发货 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(J,{underline:!1,style:{"margin-left":"15px"},type:"primary",onClick:a=>n(r.no)},{default:e.withCtx(()=>i[15]||(i[15]=[e.createTextVNode("查看详情")])),_:2},1032,["onClick"]),["UNPAID"].includes(r.status)?(e.openBlock(),e.createBlock(J,{key:1,underline:!1,style:{"margin-left":"15px"},type:"danger",onClick:a=>k(r)},{default:e.withCtx(()=>i[16]||(i[16]=[e.createTextVNode(" 关闭订单 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["checked-item","data","style"]),e.createVNode(ie,{"page-size":g.size,"page-num":g.current,total:g.total,onHandleSizeChange:d,onHandleCurrentChange:y},null,8,["page-size","page-num","total"]),e.createVNode(ke,{isShow:x.value,"onUpdate:isShow":i[3]||(i[3]=r=>x.value=r),ids:m.value,"onUpdate:ids":i[4]||(i[4]=r=>m.value=r),remark:E.value,"onUpdate:remark":i[5]||(i[5]=r=>E.value=r),"remark-type":"INTEGRAL",onSuccess:c},null,8,["isShow","ids","remark"]),e.createVNode(xt,{isShow:G.value,"onUpdate:isShow":i[6]||(i[6]=r=>G.value=r),"current-no":$.value,onUploadList:T},null,8,["isShow","current-no"])],64)}}}),ul="",zt=Object.freeze(Object.defineProperty({__proto__:null,default:Z(qt,[["__scopeId","data-v-71fc9953"]])},Symbol.toStringTag,{value:"Module"})),Ft={style:{padding:"16px",overflow:"auto"}},jt={class:"use_rules p20"},Ht={key:0,style:{border:"1px solid #ccc"}},Yt={key:1},Qt={class:"use_rules p20"},Wt={key:1},Xt={class:"use_rules p20"},Jt={key:0,style:{border:"1px solid #ccc"}},Kt={key:1},Zt={class:"use_rules p20"},vt={class:"get_rules"},el={class:"get_rules"},tl={class:"get_rules"},ll={class:"save"},ol={key:0},al={key:0},nl={key:1},rl=e.defineComponent({__name:"IntegralRules",setup(N){const C=e.ref(!1),g=e.ref(!1),l=e.ref({indate:0,useRule:"<div>（1）积分使用过程中不找零、不兑现、不开发票，不可转移至其他账户。</div><div>（2）使用积分进行兑换，兑换申请一经提交, 一律不能退货</div><div>（3）如因积分商品缺货等原因导致的退货退款，积分会原路返还</div><div>（4）兑换礼品涉及运费和配送费由用户自行承担。</div><div>（5）成都掌控者保留最终解释权。</div>",ruleInfo:"<div>月度滚动过期制。 每个自然月的第1天00：00分自动清零 已满一年的积分。 举例：2022年8月1日开始清除2023年7月31日</div>",integralGainRule:[{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SHARE",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"LOGIN",open:!1},{rulesParameter:{basicsValue:1,extendValue:{}},gainRuleType:"SING_IN",open:!1},{rulesParameter:{consumeJson:[{consumeGrowthValueType:"",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""},{consumeGrowthValueType:"",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""}]},gainRuleType:"CONSUME",open:!1}]}),s=e.ref({LOGIN:[{key:0,value:0},{key:0,value:0}],SING_IN:[{key:0,value:0},{key:0,value:0}],CONSUME:[{key:0,value:0},{key:0,value:0}]});E();const x=e.ref([{consumeGrowthValueType:"ORDER_QUANTITY",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""},{consumeGrowthValueType:"ORDER_AMOUNT",isSelected:!1,orderQuantityAndAmount:"",presentedGrowthValue:""}]),m=e.ref("ORDER_QUANTITY");async function E(){var d;const{code:o,data:t,msg:u}=await Re();if(o!==200){_.ElMessage.error(u||"积分规则获取失败");return}if(t&&t.useRule){G(t),l.value=t,x.value=t.integralGainRule[3].rulesParameter.consumeJson,((d=t.integralGainRule[3].rulesParameter.consumeJson[0])==null?void 0:d.isSelected)===!0?m.value="ORDER_QUANTITY":m.value="ORDER_AMOUNT";return}g.value=!0}const w=(o=!1)=>{C.value=!C.value,o&&E()},I=async()=>{if(!z())return;const o=$();if(o==="COMPLETE"){h();const{code:t,msg:u}=await(g.value?De(l.value):Oe(l.value));if(t!==200){_.ElMessage.error(u||`${g.value?"保存":"修改"}积分规则失败`);return}_.ElMessage.success(`${g.value?"保存":"修改"}积分规则成功`),E(),P()}else _.ElMessage.info(`连续${o==="LOGIN"?"登录":"签到"}天数输入重复，请修改`)};function h(){l.value.integralGainRule.forEach(o=>{let t={};const u=s.value[o.gainRuleType];if(u){for(let d=0;d<u.length;d++){const y=u[d].key,c=u[d].value;t[y]=c}o.rulesParameter.extendValue=t}o.gainRuleType==="CONSUME"&&(o.open?x.value.forEach(d=>{d.isSelected=d.consumeGrowthValueType===m.value}):x.value.forEach(d=>{d.isSelected=!1}),o.rulesParameter.consumeJson=x.value)})}function G(o){o.integralGainRule.forEach(t=>{let u=[];if(s.value[t.gainRuleType]){for(const y in t.rulesParameter.extendValue)u.push({key:y,value:t.rulesParameter.extendValue[y]});s.value[t.gainRuleType]=u}})}function $(){const o=new Map;if(s.value.LOGIN.filter(d=>!o.has(d.key)&&o.set(d.key,d.value)).length!==s.value.LOGIN.length)return"LOGIN";o.clear();const u=s.value.SING_IN.filter(d=>!o.has(d.key)&&o.set(d.key,d.value)).length;return u!==s.value.SING_IN.length?(console.log("singInLen",u),"SING_IN"):"COMPLETE"}function z(){const{indate:o,useRule:t,ruleInfo:u,integralGainRule:d}=l.value;if(!o)return _.ElMessage.info("请输入积分有效期"),!1;if(!t.trim().length||t==="<p><br></p>")return _.ElMessage.info("请输入积分规则"),!1;if(!u.trim().length||u==="<p><br></p>")return _.ElMessage.info("请输入积分值信息"),!1;if(d.every(n=>!!n.rulesParameter.basicsValue))return _.ElMessage.info("请输入首次赠送积分值信息"),!1;const c=H();return console.log(c),c?(_.ElMessage.info(c),!1):!0}function H(){let o="";const t=s.value.LOGIN,u=s.value.SING_IN,d=s.value.SING_IN;if(t.forEach(y=>{if(!y.key)return o="请输入连续登录天数",o;if(!y.value)return o="请输入连续登录赠送积分值",o}),o||(u.forEach(y=>{if(!y.key)return o="请输入连续签到天数",o;if(!y.value)return o="请输入连续登签到赠送积分值",o}),o)||(d.forEach(y=>{if(!y.key)return o;if(!y.value)return o="请输入赠送的积分值",o}),o))return o}function P(){C.value=!1,g.value=!1}const T=o=>{console.log(o),o==="ORDER_QUANTITY"&&(x.value[0].isSelected=!0,x.value[1].isSelected=!1),o==="ORDER_AMOUNT"&&(x.value[1].isSelected=!0,x.value[0].isSelected=!1)};return(o,t)=>{var L,U,D,f,A,F,Y,Q,p,i,V,M,W,X,q,te,le,oe,ae,ne,v,J,r;const u=e.resolveComponent("el-button"),d=e.resolveComponent("el-input-number"),y=e.resolveComponent("el-checkbox"),c=e.resolveComponent("el-input"),n=e.resolveComponent("el-radio"),k=e.resolveComponent("el-form-item"),b=e.resolveComponent("el-radio-group"),S=e.resolveDirective("dompurify-html");return e.openBlock(),e.createElementBlock("div",Ft,[e.withDirectives(e.createVNode(u,{round:"",type:"primary",onClick:t[0]||(t[0]=a=>w(!1))},{default:e.withCtx(()=>t[21]||(t[21]=[e.createTextVNode("编辑积分规则")])),_:1},512),[[e.vShow,!C.value]]),t[54]||(t[54]=e.createElementVNode("div",{class:"title"},"积分使用规则",-1)),e.createElementVNode("div",jt,[C.value?(e.openBlock(),e.createElementBlock("div",Ht,[e.createVNode(me,{modelValue:l.value.useRule,"onUpdate:modelValue":t[1]||(t[1]=a=>l.value.useRule=a),height:200},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",Yt,null,512)),[[S,l.value.useRule]])]),t[55]||(t[55]=e.createElementVNode("div",{class:"title"},"积分有效期",-1)),e.createElementVNode("div",Qt,[e.createElementVNode("div",null,[t[22]||(t[22]=e.createTextVNode(" 积分有效期为 ")),C.value?(e.openBlock(),e.createBlock(d,{key:0,modelValue:l.value.indate,"onUpdate:modelValue":t[2]||(t[2]=a=>l.value.indate=a),controls:!1,min:1,max:12,style:{width:"50px"}},null,8,["modelValue"])):(e.openBlock(),e.createElementBlock("span",Wt,e.toDisplayString(l.value.indate),1)),t[23]||(t[23]=e.createTextVNode(" 个月 "))])]),t[56]||(t[56]=e.createElementVNode("div",{class:"title"},"积分值规则",-1)),e.createElementVNode("div",Xt,[C.value?(e.openBlock(),e.createElementBlock("div",Jt,[e.createVNode(me,{modelValue:l.value.ruleInfo,"onUpdate:modelValue":t[3]||(t[3]=a=>l.value.ruleInfo=a)},null,8,["modelValue"])])):e.withDirectives((e.openBlock(),e.createElementBlock("div",Kt,null,512)),[[S,l.value.ruleInfo]])]),t[57]||(t[57]=e.createElementVNode("div",{class:"title"},"积分获取规则",-1)),e.createElementVNode("div",Zt,[C.value?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(y,{modelValue:l.value.integralGainRule[0].open,"onUpdate:modelValue":t[4]||(t[4]=a=>l.value.integralGainRule[0].open=a)},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode("分享")])),_:1},8,["modelValue"]),t[50]||(t[50]=e.createElementVNode("span",{class:"use_rules__msg"},"（限制用户首次分享才可获得积分）",-1)),e.createElementVNode("div",vt,[t[25]||(t[25]=e.createTextVNode(" 每日首次分享获得 ")),e.createVNode(d,{modelValue:l.value.integralGainRule[0].rulesParameter.basicsValue,"onUpdate:modelValue":t[5]||(t[5]=a=>l.value.integralGainRule[0].rulesParameter.basicsValue=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[26]||(t[26]=e.createTextVNode(" 点积分 "))]),e.createVNode(y,{modelValue:l.value.integralGainRule[1].open,"onUpdate:modelValue":t[6]||(t[6]=a=>l.value.integralGainRule[1].open=a)},{default:e.withCtx(()=>t[27]||(t[27]=[e.createTextVNode("登录")])),_:1},8,["modelValue"]),t[51]||(t[51]=e.createElementVNode("span",{class:"use_rules__msg"},"（每日首次登录获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),e.createElementVNode("div",el,[t[28]||(t[28]=e.createTextVNode(" 每日首次登录获得 ")),e.createVNode(d,{modelValue:l.value.integralGainRule[1].rulesParameter.basicsValue,"onUpdate:modelValue":t[7]||(t[7]=a=>l.value.integralGainRule[1].rulesParameter.basicsValue=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[29]||(t[29]=e.createTextVNode(" 点积分, 第 ")),e.createVNode(d,{modelValue:s.value.LOGIN[0].key,"onUpdate:modelValue":t[8]||(t[8]=a=>s.value.LOGIN[0].key=a),controls:!1,min:1,max:6,style:{width:"100px"}},null,8,["modelValue"]),t[30]||(t[30]=e.createTextVNode(" 天连续登录将额外获赠 ")),e.createVNode(d,{modelValue:s.value.LOGIN[0].value,"onUpdate:modelValue":t[9]||(t[9]=a=>s.value.LOGIN[0].value=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[31]||(t[31]=e.createTextVNode(" 点积分； 第 ")),e.createVNode(d,{modelValue:s.value.LOGIN[1].key,"onUpdate:modelValue":t[10]||(t[10]=a=>s.value.LOGIN[1].key=a),controls:!1,min:1,max:7,style:{width:"100px"}},null,8,["modelValue"]),t[32]||(t[32]=e.createTextVNode(" 天连续登录将额外获得 ")),e.createVNode(d,{modelValue:s.value.LOGIN[1].value,"onUpdate:modelValue":t[11]||(t[11]=a=>s.value.LOGIN[1].value=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[33]||(t[33]=e.createTextVNode(" 点积分。 "))]),e.createVNode(y,{modelValue:l.value.integralGainRule[2].open,"onUpdate:modelValue":t[12]||(t[12]=a=>l.value.integralGainRule[2].open=a),style:{"margin-right":"30px"}},{default:e.withCtx(()=>t[34]||(t[34]=[e.createTextVNode("签到")])),_:1},8,["modelValue"]),t[52]||(t[52]=e.createElementVNode("span",{class:"use_rules__msg"},"（每日签到获得积分，7天中有2个节点可以额外赠送积分，第8天00:00数据清空）",-1)),e.createElementVNode("div",tl,[t[35]||(t[35]=e.createTextVNode(" 每日首次签到获得 ")),e.createVNode(d,{modelValue:l.value.integralGainRule[2].rulesParameter.basicsValue,"onUpdate:modelValue":t[13]||(t[13]=a=>l.value.integralGainRule[2].rulesParameter.basicsValue=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[36]||(t[36]=e.createTextVNode(" 点积分, 第 ")),e.createVNode(d,{modelValue:s.value.SING_IN[0].key,"onUpdate:modelValue":t[14]||(t[14]=a=>s.value.SING_IN[0].key=a),max:6,controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[37]||(t[37]=e.createTextVNode(" 天连续签到将额外获赠 ")),e.createVNode(d,{modelValue:s.value.SING_IN[0].value,"onUpdate:modelValue":t[15]||(t[15]=a=>s.value.SING_IN[0].value=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[38]||(t[38]=e.createTextVNode("点积分； 第 ")),e.createVNode(d,{modelValue:s.value.SING_IN[1].key,"onUpdate:modelValue":t[16]||(t[16]=a=>s.value.SING_IN[1].key=a),controls:!1,min:1,style:{width:"100px"},max:7},null,8,["modelValue"]),t[39]||(t[39]=e.createTextVNode(" 天连续签到将额外获得 ")),e.createVNode(d,{modelValue:s.value.SING_IN[1].value,"onUpdate:modelValue":t[17]||(t[17]=a=>s.value.SING_IN[1].value=a),controls:!1,min:1,style:{width:"100px"}},null,8,["modelValue"]),t[40]||(t[40]=e.createTextVNode(" 点积分。 "))]),e.createVNode(y,{modelValue:l.value.integralGainRule[3].open,"onUpdate:modelValue":t[18]||(t[18]=a=>l.value.integralGainRule[3].open=a),style:{"margin-right":"30px"}},{default:e.withCtx(()=>t[41]||(t[41]=[e.createTextVNode("消费获得")])),_:1},8,["modelValue"]),t[53]||(t[53]=e.createElementVNode("span",{class:"use_rules__msg"},"（成功交易笔数及实付金额只统计订单状态为【已完成】的数值，且将【已结算订单、积分订单、储值充值】剔除）",-1)),e.createVNode(b,{modelValue:m.value,"onUpdate:modelValue":t[19]||(t[19]=a=>m.value=a),class:"ml-4",style:{"margin-top":"5px",display:"flex","flex-direction":"column","align-items":"start","margin-left":"20px"},onChange:T},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(x.value,(a,il)=>(e.openBlock(),e.createElementBlock(e.Fragment,{key:il},[a.consumeGrowthValueType==="ORDER_QUANTITY"?(e.openBlock(),e.createBlock(k,{key:0},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:a.isSelected,"onUpdate:modelValue":O=>a.isSelected=O,label:"ORDER_QUANTITY"},{default:e.withCtx(()=>[t[42]||(t[42]=e.createTextVNode(" 每成功交易(已完成) ")),e.createVNode(c,{modelValue:a.orderQuantityAndAmount,"onUpdate:modelValue":O=>a.orderQuantityAndAmount=O,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),t[43]||(t[43]=e.createTextVNode(" 笔订单，奖励 ")),e.createVNode(c,{modelValue:a.presentedGrowthValue,"onUpdate:modelValue":O=>a.presentedGrowthValue=O,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),t[44]||(t[44]=e.createTextVNode(" 点积分 "))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)):a.consumeGrowthValueType==="ORDER_AMOUNT"?(e.openBlock(),e.createBlock(k,{key:1},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:a.isSelected,"onUpdate:modelValue":O=>a.isSelected=O,label:"ORDER_AMOUNT"},{default:e.withCtx(()=>[t[45]||(t[45]=e.createTextVNode(" 实付金额(不含运费)，每满 ")),e.createVNode(c,{modelValue:a.orderQuantityAndAmount,"onUpdate:modelValue":O=>a.orderQuantityAndAmount=O,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),t[46]||(t[46]=e.createTextVNode(" 元，奖励 ")),e.createVNode(c,{modelValue:a.presentedGrowthValue,"onUpdate:modelValue":O=>a.presentedGrowthValue=O,style:{width:"100px"}},null,8,["modelValue","onUpdate:modelValue"]),t[47]||(t[47]=e.createTextVNode(" 点积分 "))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)):e.createCommentVNode("",!0)],64))),128))]),_:1},8,["modelValue"]),e.createElementVNode("div",ll,[e.createVNode(u,{round:"",onClick:t[20]||(t[20]=a=>w(!0))},{default:e.withCtx(()=>t[48]||(t[48]=[e.createTextVNode("返回")])),_:1}),e.createVNode(u,{round:"",type:"primary",onClick:I},{default:e.withCtx(()=>t[49]||(t[49]=[e.createTextVNode("保存")])),_:1})])],64)):(e.openBlock(),e.createElementBlock(e.Fragment,{key:1},[(L=l.value.integralGainRule)!=null&&L[0].open?(e.openBlock(),e.createElementBlock("div",ol," 每日首次分享获得 "+e.toDisplayString(l.value.integralGainRule[0].rulesParameter.basicsValue)+" 点积分 ",1)):e.createCommentVNode("",!0),e.withDirectives(e.createElementVNode("div",null," 每日首次登录获得 "+e.toDisplayString((U=l.value.integralGainRule)==null?void 0:U[1].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((D=s.value.LOGIN)==null?void 0:D[0].key)+" 天连续登录将额外获赠 "+e.toDisplayString((f=s.value.LOGIN)==null?void 0:f[0].value)+" 点积分； 第 "+e.toDisplayString((A=s.value.LOGIN)==null?void 0:A[1].key)+" 天连续登录将额外获得 "+e.toDisplayString((F=s.value.LOGIN)==null?void 0:F[1].value)+" 点积分。 ",513),[[e.vShow,(Y=l.value.integralGainRule)==null?void 0:Y[1].open]]),e.withDirectives(e.createElementVNode("div",null," 每日首次签到获得 "+e.toDisplayString((Q=l.value.integralGainRule)==null?void 0:Q[2].rulesParameter.basicsValue)+" 点积分, 第 "+e.toDisplayString((p=s.value.SING_IN)==null?void 0:p[0].key)+" 天连续签到将额外获赠 "+e.toDisplayString((i=s.value.SING_IN)==null?void 0:i[0].value)+" 点积分； 第 "+e.toDisplayString((V=s.value.SING_IN)==null?void 0:V[1].key)+" 天连续签到将额外获得 "+e.toDisplayString((M=s.value.SING_IN)==null?void 0:M[1].value)+" 点积分。 ",513),[[e.vShow,(W=l.value.integralGainRule)==null?void 0:W[2].open]]),e.withDirectives(e.createElementVNode("div",null,[(X=x.value)!=null&&X[0].isSelected?(e.openBlock(),e.createElementBlock("p",al," 每成功交易（已完成），"+e.toDisplayString((q=x.value)==null?void 0:q[0].orderQuantityAndAmount)+" 笔订单，奖励 "+e.toDisplayString((te=x.value)==null?void 0:te[0].presentedGrowthValue)+" 点积分 ",1)):(oe=(le=x.value)==null?void 0:le[1])!=null&&oe.isSelected?(e.openBlock(),e.createElementBlock("p",nl," 实付金额（不含运费），每满 "+e.toDisplayString((ne=(ae=x.value)==null?void 0:ae[1])==null?void 0:ne.orderQuantityAndAmount)+" 元，奖励 "+e.toDisplayString((J=(v=x.value)==null?void 0:v[1])==null?void 0:J.presentedGrowthValue)+" 点积分 ",1)):e.createCommentVNode("",!0)],512),[[e.vShow,(r=l.value.integralGainRule)==null?void 0:r[3].open]])],64))])])}}}),gl="",sl=Object.freeze(Object.defineProperty({__proto__:null,default:Z(rl,[["__scopeId","data-v-1dc77ea1"]])},Symbol.toStringTag,{value:"Module"}));return be});
