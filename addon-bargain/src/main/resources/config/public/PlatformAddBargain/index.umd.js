(function(e,k){typeof exports=="object"&&typeof module<"u"?module.exports=k(require("vue"),require("@/apis/http"),require("element-plus"),require("@/composables/useConvert"),require("vue-router"),require("@/components/q-input-number/q-input-number.vue"),require("@/utils/date")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","element-plus","@/composables/useConvert","vue-router","@/components/q-input-number/q-input-number.vue","@/utils/date"],k):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAddBargain=k(e.PlatformAddBargainContext.Vue,e.PlatformAddBargainContext.Request,e.PlatformAddBargainContext.ElementPlus,e.PlatformAddBargainContext.UseConvert,e.PlatformAddBargainContext.VueRouter,e.PlatformAddBargainContext.QInputNumber,e.PlatformAddBargainContext.DateUtil))})(this,function(e,k,w,F,Y,z,$){"use strict";var R=document.createElement("style");R.textContent=`@charset "UTF-8";.com[data-v-ca7caa5f]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-ca7caa5f]{width:62px;height:62px}.com__name[data-v-ca7caa5f]{width:113px;font-size:14px;color:#2e99f3;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;margin-left:12px}.add[data-v-db948267]{margin-top:30px;overflow:scroll}.add .p16[data-v-db948267]{padding-left:16px;padding-right:16px}.tool[data-v-db948267]{margin-top:auto;align-items:center;position:sticky;bottom:0;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:9}.bargaining_amount[data-v-db948267]{position:relative;width:100%}.bargaining_amount__description[data-v-db948267]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-db948267]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-db948267]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-db948267]{display:flex;width:100%}.discount_msg[data-v-db948267]{display:inline-block;width:400px;flex:1}.rules[data-v-db948267]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-db948267]{width:300px;display:flex}.text[data-v-db948267]{font-size:14px;color:#333}.goodsData[data-v-db948267]{border:1px solid #ccc}.goods-list[data-v-db948267]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-db948267]{display:flex}.goods-list__goods-list__info-name[data-v-db948267]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-db948267]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-db948267]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-db948267]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-db948267]{font-size:16px}.ruleform-date[data-v-db948267]{width:100%;display:flex;align-items:center}.flex[data-v-db948267]{margin-top:10px;height:50px}.flex-item[data-v-db948267]{width:40%}.coupon-rules[data-v-db948267]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-db948267]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(R);const G="addon-bargain/bargain",H=b=>k.post({url:G,data:b}),J=b=>k.get({url:G+`/${b.shopId}/${b.activityId}`}),j={class:"com"},O={class:"com__name"},K=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}}},setup(b,{expose:y}){const g=b,{divTenThousand:_,mulTenThousand:x}=F(),V=e.ref([]);e.watch(()=>g.productList,a=>{const m=I(a);console.log("flatGoodList",m),V.value=o(m)}),e.watch(()=>g.flatGoodList,a=>{V.value=o(a)});function A(a){return a.skuItem.stockType==="LIMITED"?Number(a.skuItem.skuStock):1/0}function I(a,m){if(!a.length)return[];const c=[];return a.forEach(d=>{d.skuIds.forEach((p,u)=>{c.push({productId:d.productId,productName:d.productName,productPic:d.pic,skuItem:{productId:d.productId,skuId:p,skuName:d.specs[u],skuPrice:d.salePrices[u],skuStock:d.stocks[u],stockType:d.stockTypes[u]},rowTag:0,stock:0,isJoin:!0,floorPrice:.01})})}),c}function o(a,m){let c=0,d=a.length;for(let p=0;p<d;p++){const u=a[p];p===0&&(u.rowTag=1,c=0),p!==0&&(u.productId===a[p-1].productId?(u.rowTag=0,a[c].rowTag=a[c].rowTag+1):(u.rowTag=1,c=p))}return a}const f=({row:a,column:m,rowIndex:c,columnIndex:d})=>{if(d===0)return{rowspan:a.rowTag,colspan:a.rowTag?1:0}};function S(a){return a.stockType==="UNLIMITED"?"不限购":a.skuStock}function h(){return e.toRaw(V.value).filter(m=>m.isJoin).map(m=>{const{productId:c,productPic:d,floorPrice:p,productName:u,stock:C,skuItem:{skuId:E,skuStock:l,skuPrice:r,skuName:t,stockType:n}}=m;return{activityId:"",productId:c,productPic:d,floorPrice:x(p).toString(),productName:u,stock:C,skuId:E,skuStock:+l,skuPrice:r,skuName:t,stockType:n}})}function U(){let a=!0;const m=V.value;if(!m.length)w.ElMessage.warning("请选择商品"),a=!1;else for(let c=0;c<m.length;c++)if(m[c].isJoin&&!m[c].stock){w.ElMessage.warning("商品库存必须大于零"),a=!1;break}return a}return y({getProduct:h,validateProduct:U}),(a,m)=>{const c=e.resolveComponent("el-image"),d=e.resolveComponent("el-table-column"),p=e.resolveComponent("el-input"),u=e.resolveComponent("el-input-number"),C=e.resolveComponent("el-switch"),E=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(E,{data:V.value,"span-method":f},{default:e.withCtx(()=>[e.createVNode(d,{label:"商品信息",width:"215"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",j,[e.createVNode(c,{class:"com__pic",src:l.productPic},null,8,["src"]),e.createElementVNode("div",O,e.toDisplayString(l.productName),1)])]),_:1}),e.createVNode(d,{label:"规格"},{default:e.withCtx(({row:l})=>[e.createTextVNode(e.toDisplayString(l.skuItem.skuName),1)]),_:1}),e.createVNode(d,{label:"砍价低价（元）"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,[g.isEdit?(e.openBlock(),e.createBlock(p,{key:0,style:{width:"80px"},disabled:"",placeholder:e.unref(_)(l.floorPrice).toString()},null,8,["placeholder"])):(e.openBlock(),e.createBlock(u,{key:1,modelValue:l.floorPrice,"onUpdate:modelValue":r=>l.floorPrice=r,min:.01,style:{width:"80px"},disabled:g.isEdit,precision:2,max:e.unref(_)(l.skuItem.skuPrice).toNumber(),controls:!1},null,8,["modelValue","onUpdate:modelValue","disabled","max"]))]),e.createElementVNode("div",null,"销售价"+e.toDisplayString(e.unref(_)(l.skuItem.skuPrice)),1)]),_:1}),e.createVNode(d,{label:"砍价库存"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,[e.createVNode(u,{"model-value":+l.stock,min:0,style:{width:"80px"},max:A(l),disabled:g.isEdit,precision:0,controls:!1,"onUpdate:modelValue":r=>l.stock=r},null,8,["model-value","max","disabled","onUpdate:modelValue"])]),e.createElementVNode("div",null,"库存"+e.toDisplayString(S(l.skuItem)),1)]),_:1}),e.createVNode(d,{label:"是否参与"},{default:e.withCtx(({row:l})=>[e.createVNode(C,{modelValue:l.isJoin,"onUpdate:modelValue":r=>l.isJoin=r,size:"large",disabled:g.isEdit},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1})]),_:1},8,["data"])}}}),oe="",L=(b,y)=>{const g=b.__vccOpts||b;for(const[_,x]of y)g[_]=x;return g},X=L(K,[["__scopeId","data-v-ca7caa5f"]]),Q={class:"add"},W={class:"ruleform-date"},Z={class:"use_discount"},v={class:"bargaining_amount"},ee={class:"tool"},te=e.defineComponent({__name:"PlatformAddBargain",setup(b){const y=Y.useRouter(),g=Y.useRoute(),_=new $,x=e.ref(),V=e.reactive({form:{shopId:"",shopName:"",name:"",startTime:"",endTime:"",bargainingPeople:0,bargainValidityPeriod:5,isSelfBargain:!1,stackable:{coupon:!1,vip:!1,full:!1},status:"ILLEGAL_SELL_OFF",helpCutAmount:"FIX_BARGAIN",bargainProducts:[],productNum:0},isEditDisable:!1,fullReductionTime:[],rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:u,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:C,trigger:["blur","change"]}]},chooseGoodsPopup:!1}),A=e.ref([]),I=e.ref([]),{form:o,isEditDisable:f,rules:S}=e.toRefs(V),h=e.ref(),U={79111:"砍价商品在相同时间段内已存在",79112:"砍价活动不存在",79113:"当前商品不是砍价商品"};a();async function a(r=g.query){if(r.shopId&&r.activityId){f.value=!0;const{code:t,data:n,msg:s}=await J(r);if(t!==200)return w.ElMessage.error(s||"获取活动详情失败");o.value={...n,shopName:"",bargainProducts:n.bargainActivityProducts,productNum:n.bargainValidityPeriod},o.value.bargainProducts=n.bargainActivityProducts,I.value=m(n.bargainActivityProducts)}}function m(r){return r.map(t=>{const{productId:n,skuPrice:s,productPic:N,productName:M,skuId:T,skuStock:D,stock:P,skuName:q,floorPrice:B,stockType:i}=t;return{floorPrice:B,isJoin:!0,productId:n,productName:M,productPic:N,stock:P,skuItem:{productId:n,skuId:T,skuName:q,skuPrice:s,skuStock:D,stockType:i}}})}const c=async()=>{if(!(!h.value||!await h.value.validate())&&x.value&&x.value.validateProduct()){o.value.bargainProducts=x.value.getProduct(),o.value.productNum=o.value.bargainProducts.length;const{code:t,data:n,msg:s}=await H(o.value);if(t===200)return d();if([79111,79112,79113].includes(t))return w.ElMessage.error(U[t]||"添加活动失败");w.ElMessage.error(s||"添加活动失败")}};function d(){w.ElMessage.success("添加活动成功"),y.push({name:"bargainIndex"})}function p(r){const t=_.getYMD(new Date),n=_.getYMD(r);return t===n?!1:new Date().getTime()>r.getTime()}function u(r,t,n){t?t&&o.value.endTime?l(new Date(o.value.endTime).getTime(),n,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):l(new Date(t).getTime(),n,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):n(new Error("请选择活动开始日期"))}function C(r,t,n){t?t&&o.value.startTime?l(new Date(t).getTime(),n,"开始日期和结束日期最少间隔5分钟",new Date(o.value.startTime).getTime(),1e3*60*5):l(new Date(t).getTime(),n,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):n(new Error("请选择活动结束日期"))}function E(r,t=new Date().getTime()){const n=r-t;return(s=1e3)=>n>=s}function l(r,t,n,s,N){E(r,s)(N)?t():t(new Error(n))}return(r,t)=>{const n=e.resolveComponent("el-input"),s=e.resolveComponent("el-form-item"),N=e.resolveComponent("el-date-picker"),M=e.resolveComponent("el-input-number"),T=e.resolveComponent("el-radio"),D=e.resolveComponent("el-radio-group"),P=e.resolveComponent("el-checkbox"),q=e.resolveComponent("el-form"),B=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Q,[t[23]||(t[23]=e.createElementVNode("h1",{class:"title p16"},"基本信息",-1)),e.createVNode(q,{ref_key:"ruleFormRef",ref:h,class:"p16",model:e.unref(o),rules:e.unref(S),"label-width":"auto","inline-message":!1,"label-position":"left"},{default:e.withCtx(()=>[e.createVNode(s,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(n,{modelValue:e.unref(o).name,"onUpdate:modelValue":t[0]||(t[0]=i=>e.unref(o).name=i),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"15",placeholder:"活动名称不超过15个字",disabled:e.unref(f)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(s,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",W,[e.createVNode(s,{prop:"startTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:e.unref(o).startTime,"onUpdate:modelValue":t[1]||(t[1]=i=>e.unref(o).startTime=i),type:"datetime",disabled:e.unref(f),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":p},null,8,["modelValue","disabled"]),t[11]||(t[11]=e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1))]),_:1}),e.createVNode(s,{prop:"endTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:e.unref(o).endTime,"onUpdate:modelValue":t[2]||(t[2]=i=>e.unref(o).endTime=i),disabled:e.unref(f),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":p},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(s,{label:"砍价到底的人数",prop:"bargainingPeople",required:""},{default:e.withCtx(()=>[e.createVNode(M,{modelValue:e.unref(o).bargainingPeople,"onUpdate:modelValue":t[3]||(t[3]=i=>e.unref(o).bargainingPeople=i),style:{width:"135px"},disabled:e.unref(f),precision:0,controls:!1,min:2},null,8,["modelValue","disabled"]),t[12]||(t[12]=e.createElementVNode("span",{class:"msg"},"砍到底价所需人数",-1))]),_:1}),e.createVNode(s,{label:"砍价有效期",prop:"bargainValidityPeriod",required:""},{default:e.withCtx(()=>[e.createVNode(z,{modelValue:e.unref(o).bargainValidityPeriod,"onUpdate:modelValue":t[4]||(t[4]=i=>e.unref(o).bargainValidityPeriod=i),controls:!1,precision:0,min:5,disabled:e.unref(f)},{append:e.withCtx(()=>t[13]||(t[13]=[e.createTextVNode(" 分钟 ")])),_:1},8,["modelValue","disabled"]),t[14]||(t[14]=e.createElementVNode("span",{class:"msg"},"砍价有效期是指从用户发起砍价到砍价截止的时间",-1))]),_:1}),e.createVNode(s,{label:"是否自我砍价",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createVNode(D,{modelValue:e.unref(o).isSelfBargain,"onUpdate:modelValue":t[5]||(t[5]=i=>e.unref(o).isSelfBargain=i),class:"ml-4",disabled:e.unref(f)},{default:e.withCtx(()=>[e.createVNode(T,{value:!1},{default:e.withCtx(()=>t[15]||(t[15]=[e.createTextVNode("否")])),_:1}),e.createVNode(T,{value:!0},{default:e.withCtx(()=>t[16]||(t[16]=[e.createTextVNode("是")])),_:1})]),_:1},8,["modelValue","disabled"]),t[17]||(t[17]=e.createElementVNode("span",{class:"msg"},"是否用户发起砍价的同时为自己砍1次价",-1))]),_:1}),e.createVNode(s,{label:"优惠叠加"},{default:e.withCtx(()=>[e.createElementVNode("div",Z,[e.createVNode(P,{modelValue:e.unref(o).stackable.vip,"onUpdate:modelValue":t[6]||(t[6]=i=>e.unref(o).stackable.vip=i),label:"会员价",disabled:e.unref(f)},null,8,["modelValue","disabled"]),e.createVNode(P,{modelValue:e.unref(o).stackable.coupon,"onUpdate:modelValue":t[7]||(t[7]=i=>e.unref(o).stackable.coupon=i),label:"优惠券",disabled:e.unref(f)},null,8,["modelValue","disabled"]),e.createVNode(P,{modelValue:e.unref(o).stackable.full,"onUpdate:modelValue":t[8]||(t[8]=i=>e.unref(o).stackable.full=i),label:"满减",disabled:e.unref(f)},null,8,["modelValue","disabled"]),t[18]||(t[18]=e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 优惠叠加可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1))])]),_:1}),e.createVNode(s,{label:"单次砍价金额范围",prop:"fullReductionName"},{default:e.withCtx(()=>[e.createElementVNode("div",v,[e.createVNode(D,{modelValue:e.unref(o).helpCutAmount,"onUpdate:modelValue":t[9]||(t[9]=i=>e.unref(o).helpCutAmount=i),class:"ml-4",disabled:e.unref(f)},{default:e.withCtx(()=>[e.createVNode(T,{value:"RANDOM_BARGAIN"},{default:e.withCtx(()=>t[19]||(t[19]=[e.createTextVNode("随机砍价")])),_:1}),e.createVNode(T,{value:"FIX_BARGAIN"},{default:e.withCtx(()=>t[20]||(t[20]=[e.createTextVNode("固定砍价")])),_:1})]),_:1},8,["modelValue","disabled"])]),t[21]||(t[21]=e.createElementVNode("div",null,[e.createElementVNode("p",{class:"msg"},"a.固定砍价 =（原价 - 砍价底价）/砍价人数"),e.createElementVNode("p",{class:"msg"}," b.随机砍价：最低砍价金额 = 1 ，最高砍价金额 = (原价 - 砍价底价) * 100 / 砍价到底人数 * 2 单位：分，最后一人砍完剩余价格 ")],-1))]),_:1}),e.createVNode(s,{label:"活动商品",required:""},{default:e.withCtx(()=>t[22]||(t[22]=[e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)])),_:1})]),_:1},8,["model","rules"]),e.createVNode(X,{ref_key:"selectGoodsTableRef",ref:x,class:"f1 p16","product-list":A.value,"is-edit":e.unref(f),"flat-good-list":I.value},null,8,["product-list","is-edit","flat-good-list"])]),e.createElementVNode("div",ee,[e.createVNode(B,{round:"",plain:"",onClick:t[10]||(t[10]=i=>e.unref(y).back())},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode("返回")])),_:1}),e.unref(f)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(B,{key:0,type:"primary",round:"",onClick:c},{default:e.withCtx(()=>t[25]||(t[25]=[e.createTextVNode("保存")])),_:1}))])],64)}}}),ae="";return L(te,[["__scopeId","data-v-db948267"]])});
