(function(e,n){typeof exports=="object"&&typeof module<"u"?module.exports=n(require("vue"),require("@/components/q-address"),require("@/apis/http"),require("element-china-area-data"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@/components/q-address","@/apis/http","element-china-area-data","element-plus"],n):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopShipmentSelect=n(e.ShopShipmentSelectContext.Vue,e.ShopShipmentSelectContext.QAddressIndex,e.ShopShipmentSelectContext.Request,e.ShopShipmentSelectContext.ElementChinaAreaData,e.ShopShipmentSelectContext.ElementPlus))})(this,function(e,n,x,g,f){"use strict";const S=r=>x.get({url:"gruul-mall-shop/shop/logistics/address/list",params:r}),u=r=>x.get({url:"gruul-mall-freight/logistics/express/usable/list",params:r});return e.defineComponent({__name:"ShopShipmentSelect",props:{properties:{type:Object,default:{}}},setup(r){const s=r,l=e.reactive(s.properties.deliverDialogFormData||{deliverType:"WITHOUT",receiver:{name:"",mobile:"",address:""},expressCompany:"",addressaddress:"",expressNo:""}),a=e.ref([]),y=e.ref([]);_(),w();async function w(){const{code:i,data:t}=await u({size:1e3,current:1});if(i!==200){f.ElMessage.error("获取物流公司失败");return}y.value=t.records,s.properties.loadCompanySelectListData(t.records)}async function _(){const{data:i,code:t}=await S({});t!==200?f.ElMessage({message:"请刷新重试...",type:"warning"}):a.value=i.records,console.log("deliveryAddressData.value",a.value);const d=a.value.find(p=>p.defSend==="YES");d&&(l.addressaddress=d.id,s.properties.loadDeliverDialogFormData(l),s.properties.loadDeliveryAddressData(a.value))}const c=()=>{s.properties.loadDeliverDialogFormData(l)};return(i,t)=>{const d=e.resolveComponent("el-option"),p=e.resolveComponent("el-select"),m=e.resolveComponent("el-col"),h=e.resolveComponent("el-row"),C=e.resolveComponent("el-form-item"),V=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock(e.Fragment,null,[l.deliverType!=="WITHOUT"?(e.openBlock(),e.createBlock(C,{key:0,"label-width":"90px",prop:"expressCompany"},{label:e.withCtx(()=>t[3]||(t[3]=[e.createElementVNode("div",{class:"send"},"物流服务",-1)])),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:l.expressCompany,"onUpdate:modelValue":t[0]||(t[0]=o=>l.expressCompany=o),class:"m-2",placeholder:"请选择物流服务",style:{width:"100%",height:"30px"},onChange:c},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(y.value,o=>(e.openBlock(),e.createBlock(d,{key:o.logisticsCompanyName,label:o.logisticsCompanyName,value:o.logisticsCompanyCode},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),l.deliverType==="EXPRESS"?(e.openBlock(),e.createBlock(C,{key:1,"label-width":"90px",prop:"expressNo"},{label:e.withCtx(()=>t[4]||(t[4]=[e.createElementVNode("div",null,"物流单号",-1)])),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:l.expressNo,"onUpdate:modelValue":t[1]||(t[1]=o=>l.expressNo=o),placeholder:"",style:{width:"100%",height:"30px"},maxlength:"40",onChange:c},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0),l.deliverType==="PRINT_EXPRESS"?(e.openBlock(),e.createBlock(C,{key:2,"label-width":"90px",prop:""},{label:e.withCtx(()=>t[5]||(t[5]=[e.createElementVNode("div",null,"发货地址",-1)])),default:e.withCtx(()=>[e.createVNode(h,{style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(m,{span:20},{default:e.withCtx(()=>[e.createVNode(p,{modelValue:l.addressaddress,"onUpdate:modelValue":t[2]||(t[2]=o=>l.addressaddress=o),placeholder:"选择发货地址",style:{width:"100%",height:"30px"},onChange:c},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,o=>(e.openBlock(),e.createBlock(d,{key:o.id,value:o.id,label:`${e.unref(n.AddressFn)(e.unref(g.regionData),[o.provinceCode,o.cityCode,o.regionCode])}${o.address}`},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)],64)}}})});
