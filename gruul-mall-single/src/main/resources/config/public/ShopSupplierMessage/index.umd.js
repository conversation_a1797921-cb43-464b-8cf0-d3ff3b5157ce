(function(e,r){typeof exports=="object"&&typeof module<"u"?module.exports=r(require("vue"),require("@/views/message/components/aside/Index.vue"),require("@/views/message/components/main/Index.vue"),require("@/views/message/components/types"),require("@/composables/stomp/StompHandler"),require("@/composables/stomp/typs"),require("@/apis/http"),require("@/store/modules/shopInfo")):typeof define=="function"&&define.amd?define(["vue","@/views/message/components/aside/Index.vue","@/views/message/components/main/Index.vue","@/views/message/components/types","@/composables/stomp/StompHandler","@/composables/stomp/typs","@/apis/http","@/store/modules/shopInfo"],r):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSupplierMessage=r(e.ShopSupplierMessageContext.Vue,e.ShopSupplierMessageContext.MessageAside,e.ShopSupplierMessageContext.MessageMain,e.ShopSupplierMessageContext.MessageTypes,e.ShopSupplierMessageContext.StompHandler,e.ShopSupplierMessageContext.StompTypes,e.ShopSupplierMessageContext.Request,e.ShopSupplierMessageContext.ShopInfoStore))})(this,function(e,r,v,d,w,x,u,f){"use strict";var _=document.createElement("style");_.textContent=`@charset "UTF-8";.cust_service_container[data-v-9fdaa6e3]{width:100%;height:100%;overflow:hidden;padding:28px 26px}.cust_service_container .customer-service.el-container[data-v-9fdaa6e3]{height:100%}.cust_service_container .cust_service_left[data-v-9fdaa6e3]{width:316px;overflow:hidden;height:100%;padding:0;border:2px solid #e9ecf0}.cust_service_container .el-main[data-v-9fdaa6e3]{flex:1;padding:0;background:rgb(245,247,251)}.cust_service_container .el-main .empty[data-v-9fdaa6e3]{height:100%;display:flex;justify-content:center;align-items:center}
`,document.head.appendChild(_);const y=(s,n,t)=>u.get({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/chat-rooms",params:{...n,keywords:s,senderType:t},showLoading:!1}),S=(s,n)=>u.get({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/chat-room",params:{...s,...n}}),C=s=>u.post({url:"/gruul-mall-carrier-pigeon/pigeon/group-chat-room-messages/message",data:s,showLoading:!1}),h=e.ref(f.useShopInfoStore().getterShopInfo);f.useShopInfoStore().$subscribe((s,n)=>{const t=n.shopInfo;h.value=t,!(!t||!t.id||!t.token)&&(o.value=null,a.initLoad())});const a=e.reactive(new d.IPage(s=>y(M.value,s,4),30)),o=e.ref(null),M=e.ref(""),I=e.ref(!1),k=s=>{o.value=s,e.nextTick(()=>{s.chatWithShopInfo.shopId&&c.value.initLoad()})},L=s=>{M.value=s,a.initLoad()},P=s=>{I.value=s},c=e.ref(new d.IPage(s=>{if(!o.value)return Promise.reject();o.value.lastMessage&&(o.value.lastMessage.show=!1);const{chatWithShopInfo:n}=o.value;return S({senderShopId:n.shopId},s)},20)),T=s=>{if(!o.value)return;o.value.lastMessage&&(o.value.lastMessage.show=!1);const{chatWithShopInfo:n}=o.value;C({receiverShopId:n.shopId,messageType:s.messageType,content:s.message})},q=()=>{a.initLoad()},U=async()=>{await a.initLoad().then(s=>{c.value=new d.IPage(n=>{if(!o.value)return Promise.reject();o.value.lastMessage&&(o.value.lastMessage.show=!1);const{chatWithShopInfo:t}=o.value;return S({senderShopId:t.shopId},n)},20)}),w.stompHookMount(x.Channel.SUPPLIER_SHOP,{success:q,fail:()=>{},subscribe:s=>{b(s)}})},b=async s=>{var t;await a.initLoad();const n=s.sender.senderShopInfo.shopId;if(n===((t=o.value)==null?void 0:t.chatWithShopInfo.shopId)||n===h.value.id){const{sender:i,receiver:p,messageType:m,message:g}=s,l={sender:i,receiver:p,messageType:m,message:g,read:!1,handled:!1,show:!0,sendTime:String(+new Date)};c.value.concatData(l)}},B=()=>{c.value.loadMore()},N={class:"cust_service_container"},V={key:1,class:"empty"},W=e.defineComponent({__name:"ShopSupplierMessage",setup(s){return e.onMounted(U),(n,t)=>{const i=e.resolveComponent("el-aside"),p=e.resolveComponent("el-empty"),m=e.resolveComponent("el-main"),g=e.resolveComponent("el-container");return e.openBlock(),e.createElementBlock("div",N,[e.createVNode(g,{class:"customer-service"},{default:e.withCtx(()=>[e.createVNode(i,{class:"cust_service_left"},{default:e.withCtx(()=>[e.createVNode(r,{currentSelectUser:e.unref(o),messageUsers:e.unref(a).records,onChange:e.unref(k),onKeywordChange:e.unref(L),onSearchFocus:e.unref(P)},null,8,["currentSelectUser","messageUsers","onChange","onKeywordChange","onSearchFocus"])]),_:1}),e.createVNode(m,null,{default:e.withCtx(()=>{var l;return[(l=e.unref(o))!=null&&l.chatWithShopInfo.shopId?(e.openBlock(),e.createBlock(v,{key:0,messages:e.unref(c).records,"search-focus":e.unref(I),"shop-info":e.unref(h),user:e.unref(o),onMessageSubmit:e.unref(T),onLoadMore:e.unref(B)},null,8,["messages","search-focus","shop-info","user","onMessageSubmit","onLoadMore"])):(e.openBlock(),e.createElementBlock("div",V,[e.createVNode(p,{description:"暂无消息"})]))]}),_:1})]),_:1})])}}}),F="";return((s,n)=>{const t=s.__vccOpts||s;for(const[i,p]of n)t[i]=p;return t})(W,[["__scopeId","data-v-9fdaa6e3"]])});
