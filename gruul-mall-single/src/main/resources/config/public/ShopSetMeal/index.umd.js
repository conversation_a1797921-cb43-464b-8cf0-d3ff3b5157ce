(function(e,S){typeof exports=="object"&&typeof module<"u"?module.exports=S(require("vue"),require("@vueuse/core"),require("@/components/SchemaForm.vue"),require("vue-router"),require("@/components/PageManage.vue"),require("element-plus"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@vueuse/core","@/components/SchemaForm.vue","vue-router","@/components/PageManage.vue","element-plus","@/apis/http"],S):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopSetMeal=S(e.ShopSetMealContext.Vue,e.ShopSetMealContext.VueUse,e.ShopSetMealContext.SchemaForms,e.ShopSetMealContext.VueRouter,e.ShopSetMealContext.PageManageTwo,e.ShopSetMealContext.ElementPlus,e.ShopSetMealContext.Request))})(this,function(e,S,L,M,O,s,y){"use strict";var N=document.createElement("style");N.textContent=`@charset "UTF-8";.origin[data-v-0cca3893]{display:flex;justify-content:start;align-items:center}.origin--name[data-v-0cca3893]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}[data-v-0cca3893] .el-button.is-link{padding:0}[data-v-0cca3893] .el-button+.el-button{margin-left:10px}
`,document.head.appendChild(N);const I=e.defineComponent({__name:"head-operation",props:{modelValue:{type:Object,default(){return{}}}},emits:["update:modelValue","search"],setup(o,{emit:p}){const r=o,_=[{label:"活动名称",prop:"keyword",valueType:"copy",fieldProps:{placeholder:"请输入活动名称"}},{label:"活动状态",prop:"setMealStatus",valueType:"select",options:[{value:"",label:"全部状态"},{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"MERCHANT_SELL_OFF",label:"已下架"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],fieldProps:{placeholder:"请选择"}}],f=p,u=S.useVModel(r,"modelValue",f),C=i=>{i&&Object.keys(i).forEach(m=>r.modelValue[m]=i[m])},c=()=>{Object.keys(r.modelValue).forEach(i=>r.modelValue[i]=""),f("search")};return(i,m)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(L,{"v-model":e.unref(u),columns:_,onSearchHandle:m[0]||(m[0]=T=>f("search")),onChangeFormItem:C,onHandleReset:c},null,8,["v-model"]),m[1]||(m[1]=e.createElementVNode("div",{class:"grey_bar"},null,-1))],64))}});var k=(o=>(o.NOT_STARTED="未开始",o.PROCESSING="进行中",o.OVER="已结束",o.MERCHANT_SELL_OFF="已下架",o.ILLEGAL_SELL_OFF="违规下架",o))(k||{});const x="addon-matching-treasure/setMeal",B=o=>y.post({url:`${x}/${o.shopId}/${o.setMealId}`}),R=o=>y.get({url:x,params:o}),b=o=>y.del({url:x,data:o}),F={class:"handle_container",style:{"margin-top":"16px"}},D={class:"table_container"},G={class:"origin"},$={class:"origin--name",style:{width:"154px","margin-left":"5px"}},q=e.defineComponent({__name:"set-meal-list",props:{search:{type:Object,default:()=>({})},showOffShelfBtn:{type:Boolean,default:!0}},setup(o,{expose:p}){const r=M.useRouter(),_=M.useRoute(),f=o,u=e.ref([]),C=e.ref(),c=e.reactive({size:10,current:1,total:0});e.computed(()=>u.value.some(t=>t.setMealStatus!=="PROCESSING")),e.watch(()=>_.query.flag,t=>{t&&h()});const i=e.ref([]),m=t=>{i.value=t},T=async()=>{try{if(!await s.ElMessageBox.confirm("确定批量删除套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const a=i.value.filter(l=>l.setMealStatus!=="PROCESSING").map(l=>({shopId:l.shopId,setMealId:l.id}));a.length?w(a):s.ElMessage.warning("进行中的活动不能被删除")}catch(t){return t}},j=()=>r.push({name:"bundlePriceBaseinfo"});async function h(){const{setMealStatus:t,keyword:a}=f.search,d={...c,...{setMealStatus:t,keyword:a}},{code:V,data:g}=await R(d);if(V!==200)return s.ElMessage.error("获取套餐列表失败");u.value=g.records,c.current=g.current,c.size=g.size,c.total=g.total}const H=(t,a)=>{r.push({name:"bundlePriceBaseinfo",query:{setMealId:t.id,shopId:t.shopId,isLookUp:a}})},A=async t=>{if(t.setMealStatus==="PROCESSING"){s.ElMessage.warning("进行中的活动不能被删除");return}try{if(!await s.ElMessageBox.confirm("确定删除该套餐?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{code:l}=await b([{setMealId:t.id,shopId:t.shopId}]);if(l!==200){s.ElMessage.error("删除失败");return}s.ElMessage.success("删除成功"),h()}catch{return}},w=async t=>{const{code:a}=await b(t);if(a!==200){s.ElMessage.error("删除失败");return}s.ElMessage.success("删除成功"),h()},U=t=>{c.current=1,c.size=t,h()},J=t=>{c.current=t,h()},K=async t=>{s.ElMessageBox.confirm("确定下架该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:a,msg:l}=await B({shopId:t.shopId,setMealId:t.id});if(a!==200){s.ElMessage.error(l);return}s.ElMessage.success("下架成功"),h()})},Q=async t=>{s.ElMessageBox.alert(t.violationExplain||"暂无违规原因","违规原因",{center:!0,showConfirmButton:!1})};return e.onBeforeMount(()=>{h()}),p({chooseList:i,handleDelBatch:w,initactiveList:h}),(t,a)=>{const l=e.resolveComponent("el-button"),d=e.resolveComponent("el-table-column"),V=e.resolveComponent("el-image"),g=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",F,[e.createVNode(l,{type:"primary",round:"",onClick:j},{default:e.withCtx(()=>a[0]||(a[0]=[e.createTextVNode("新增套餐")])),_:1}),e.createVNode(l,{round:"",disabled:i.value.length===0,onClick:T},{default:e.withCtx(()=>a[1]||(a[1]=[e.createTextVNode("批量删除")])),_:1},8,["disabled"])]),e.createElementVNode("div",D,[e.createVNode(g,{ref_key:"multipleTableRef",ref:C,data:u.value,"header-cell-style":{background:"#f6f8fa",height:"48px"},"header-row-style":{color:"#333"},onSelectionChange:m},{default:e.withCtx(()=>[e.createVNode(d,{type:"selection",width:"30",selectable:n=>n.setMealStatus!=="PROCESSING"},null,8,["selectable"]),e.createVNode(d,{label:"套餐信息",width:"260px"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",G,[e.createVNode(V,{style:{width:"40px",height:"40px"},src:n.setMealMainPicture},null,8,["src"]),e.createElementVNode("div",$,e.toDisplayString(n.setMealName),1)])]),_:1}),e.createVNode(d,{label:"套餐类型",width:"140"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(n.setMealType==="OPTIONAL_PRODUCT"?"自选套餐":"固定套餐"),1)]),_:1}),e.createVNode(d,{label:"状态",width:"140"},{default:e.withCtx(({row:n})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:n.setMealStatus==="ILLEGAL_SELL_OFF"?"#F12F22":""})},e.toDisplayString(e.unref(k)[n.setMealStatus]),5)]),_:1}),e.createVNode(d,{label:"活动时间",width:"200"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",null,"起："+e.toDisplayString(n.startTime),1),e.createElementVNode("div",null,"止："+e.toDisplayString(n.endTime),1)]),_:1}),e.createVNode(d,{label:"活动商品"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(n.productCount),1)]),_:1}),e.createVNode(d,{label:"支付单数",width:"140"},{default:e.withCtx(({row:n})=>[e.createElementVNode("span",null,e.toDisplayString(n.payOrder||0),1)]),_:1}),e.createVNode(d,{label:"操作",fixed:"right",align:"right",width:"150"},{default:e.withCtx(({row:n})=>[e.createVNode(l,{link:"",type:"primary",onClick:E=>H(n,"true")},{default:e.withCtx(()=>a[2]||(a[2]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),["PROCESSING"].includes(n.setMealStatus)?(e.openBlock(),e.createBlock(l,{key:0,link:"",type:"primary",onClick:E=>K(n)},{default:e.withCtx(()=>a[3]||(a[3]=[e.createTextVNode("下架")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),n.setMealStatus==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createBlock(l,{key:1,link:"",type:"primary",onClick:E=>Q(n)},{default:e.withCtx(()=>a[4]||(a[4]=[e.createTextVNode("违规原因")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),n.setMealStatus!=="PROCESSING"?(e.openBlock(),e.createBlock(l,{key:2,link:"",type:"danger",onClick:E=>A(n)},{default:e.withCtx(()=>a[5]||(a[5]=[e.createTextVNode("删除")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"])]),e.createVNode(O,{"page-num":c.current,"load-init":!0,"page-size":c.size,total:c.total,onReload:h,onHandleSizeChange:U,onHandleCurrentChange:J},null,8,["page-num","page-size","total"])],64)}}}),W="",z=((o,p)=>{const r=o.__vccOpts||o;for(const[_,f]of p)r[_]=f;return r})(q,[["__scopeId","data-v-0cca3893"]]),P={class:"q_plugin_container"};return e.defineComponent({__name:"ShopSetMeal",setup(o){const p=e.reactive({setMealStatus:"",keyword:""}),r=e.ref(),_=()=>{r.value.initactiveList()};return(f,u)=>(e.openBlock(),e.createElementBlock("div",P,[e.createVNode(I,{modelValue:p,"onUpdate:modelValue":u[0]||(u[0]=C=>p=C),onSearch:_},null,8,["modelValue"]),e.createVNode(z,{ref_key:"mealListRef",ref:r,search:p},null,8,["search"])]))}})});
