(function(e,k){typeof exports=="object"&&typeof module<"u"?module.exports=k(require("vue"),require("vue-router"),require("element-plus"),require("@/composables/useConvert"),require("@/apis/http"),require("@/utils/date"),require("@/components/q-upload/q-upload.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","element-plus","@/composables/useConvert","@/apis/http","@/utils/date","@/components/q-upload/q-upload.vue"],k):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAddSetMeal=k(e.PlatformAddSetMealContext.Vue,e.PlatformAddSetMealContext.VueRouter,e.PlatformAddSetMealContext.ElementPlus,e.PlatformAddSetMealContext.UseConvert,e.PlatformAddSetMealContext.Request,e.PlatformAddSetMealContext.DateUtil,e.PlatformAddSetMealContext.QUpload))})(this,function(e,k,E,O,z,F,j){"use strict";var Y=document.createElement("style");Y.textContent=`@charset "UTF-8";.com[data-v-df180bb7]{display:flex;justify-content:center;align-items:center}.com__pic[data-v-df180bb7]{width:62px;height:62px}.com__name[data-v-df180bb7]{font-size:14px;display:flex;align-items:center;flex-direction:column;justify-content:center;margin-left:10px;align-items:baseline}.com__name--text[data-v-df180bb7]{width:113px;height:50px;line-height:24px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.flex[data-v-df180bb7]{display:flex;justify-content:space-around;align-items:center}[data-v-df180bb7].el-table .cell{line-height:20px}.tool[data-v-fe3aff81]{margin-top:auto;align-items:center;position:sticky;bottom:0;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:9}.add[data-v-fe3aff81]{padding:30px 16px 10px;overflow-y:auto;scrollbar-width:none;-ms-overflow-style:none}.add[data-v-fe3aff81]::-webkit-scrollbar{display:none}.bargaining_amount[data-v-fe3aff81]{position:relative;width:100%}.bargaining_amount__description[data-v-fe3aff81]{position:absolute;top:-35px;right:0;width:480px}.title[data-v-fe3aff81]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-fe3aff81]{font-size:12px;margin-left:12px;color:#c4c4c4}.use_discount[data-v-fe3aff81]{display:flex;width:100%}.discount_msg[data-v-fe3aff81]{display:inline-block;width:400px;flex:1}.rules[data-v-fe3aff81]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-fe3aff81]{width:300px;display:flex}.text[data-v-fe3aff81]{font-size:14px;color:#333}.goodsData[data-v-fe3aff81]{border:1px solid #ccc}.goods-list[data-v-fe3aff81]{width:90%;margin-top:20px;height:300px;border:1px solid transparent}.goods-list__info[data-v-fe3aff81]{display:flex}.goods-list__goods-list__info-name[data-v-fe3aff81]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-fe3aff81]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-fe3aff81]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-fe3aff81]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-fe3aff81]{font-size:16px}.ruleform-date[data-v-fe3aff81]{width:100%;display:flex;align-items:center}.flex[data-v-fe3aff81]{margin-top:10px;height:50px}.flex-item[data-v-fe3aff81]{width:40%}.coupon-rules[data-v-fe3aff81]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-fe3aff81]{position:fixed;left:50%;bottom:30px}
`,document.head.appendChild(Y);const J={class:"com"},X={class:"com__name"},K={class:"com__name--text",style:{"margin-top":"5px"}},Q={class:"flex"},W={class:"dialog-footer"},Z=e.defineComponent({__name:"select-goods-table",props:{productList:{type:Array,default(){return[]}},isEdit:{type:Boolean,default:!1},flatGoodList:{type:Array,default(){return[]}},productAttributes:{type:String,default:"MAIN_PRODUCT"}},setup(_,{expose:N}){const g=_,{divTenThousand:M,mulTenThousand:C}=O(),b=e.ref([]),h=e.ref(!1);e.watch(()=>g.productList,a=>{console.log("表格变化",a);const l=P(a);console.log("flatGoodList",l),b.value=S(l)},{deep:!0}),e.watch(()=>g.flatGoodList,a=>{b.value=S(a)});function n(a){return a.skuItem.stockType==="LIMITED"?Number(a.skuItem.skuStock):1e5}function m(a){return M(a.skuItem.skuPrice).toNumber()}function P(a,l){if(!a.length)return[];const f=[];return a.forEach(r=>{r.skuIds.forEach((p,s)=>{(r.stocks[s]>0&&r.stockTypes[s]==="LIMITED"||r.stockTypes[s]==="UNLIMITED")&&f.push({productId:r.productId,productName:r.productName,productPic:r.pic,shopId:r.shopId,skuItem:{productId:r.productId,skuId:p,skuName:r.specs[s],skuPrice:r.salePrices[s],skuStock:r.stocks[s],stockType:r.stockTypes[s]},rowTag:0,matchingStock:1,isJoin:!0,matchingPrice:.01})})}),f}function S(a,l){let f=0,r=a.length;for(let p=0;p<r;p++){const s=a[p];p===0&&(s.rowTag=1,f=0),p!==0&&(s.productId===a[p-1].productId?(s.rowTag=0,a[f].rowTag=a[f].rowTag+1):(s.rowTag=1,f=p))}return a}const $=({row:a,column:l,rowIndex:f,columnIndex:r})=>{if(r===0)return{rowspan:a.rowTag,colspan:a.rowTag?1:0}};function D(a){return a.stockType==="UNLIMITED"?"无限库存":`${"库存"+a.skuStock}`}function U(){return e.toRaw(b.value).filter(l=>l.isJoin).map(l=>{const{setMealId:f,productId:r,productPic:p,matchingPrice:s,productName:c,matchingStock:t,shopId:o,skuItem:{skuId:d,skuStock:V,skuPrice:T,skuName:x,stockType:w}}=l;return{setMealId:f||"",shopId:o,productId:r,productPic:p,productName:c,productAttributes:g.productAttributes,skuId:d,skuName:x,skuStock:+V,skuPrice:T,stockType:w,matchingPrice:C(s).toString(),matchingStock:t}})}function y(){let a=!0;const l=b.value;if(!l.length)E.ElMessage.warning("请选择商品"),a=!1;else for(let f=0;f<l.length;f++)if(l[f].isJoin&&!l[f].matchingStock){E.ElMessage.warning("商品库存必须大于零"),a=!1;break}return a}const u=e.ref({stock:1,price:.01}),I=e.ref("0"),H=a=>{I.value=a.productId,h.value=!0},A=()=>{u.value={stock:1,price:.01},h.value=!1},q=()=>{b.value.forEach(a=>{a.productId===I.value&&(a.matchingStock=u.value.stock>n(a)?n(a):u.value.stock,a.matchingPrice=u.value.price>m(a)?m(a):u.value.price)}),h.value=!1};return N({getProduct:U,validateProduct:y}),(a,l)=>{const f=e.resolveComponent("el-image"),r=e.resolveComponent("el-button"),p=e.resolveComponent("el-table-column"),s=e.resolveComponent("el-input-number"),c=e.resolveComponent("el-table"),t=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock("div",null,[e.createVNode(c,{data:b.value,"max-height":500,"span-method":$},{default:e.withCtx(()=>[e.createVNode(p,{label:"商品信息",width:"215"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",J,[e.createVNode(f,{src:o.productPic,class:"com__pic"},null,8,["src"]),e.createElementVNode("div",X,[g.isEdit?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(r,{key:0,class:"com__batch",type:"primary",link:"",onClick:d=>H(o)},{default:e.withCtx(()=>l[4]||(l[4]=[e.createTextVNode(" 批量设置 ")])),_:2},1032,["onClick"])),e.createElementVNode("div",K,e.toDisplayString(o.productName),1)])])]),_:1}),e.createVNode(p,{label:"规格"},{default:e.withCtx(({row:o})=>[e.createTextVNode(e.toDisplayString(o.skuItem.skuName),1)]),_:1}),e.createVNode(p,{label:"套餐库存"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",null,[e.createVNode(s,{controls:!1,disabled:g.isEdit,max:n(o),min:0,"model-value":+o.matchingStock,precision:0,style:{width:"80px"},"onUpdate:modelValue":d=>o.matchingStock=d},null,8,["disabled","max","model-value","onUpdate:modelValue"])]),e.createElementVNode("div",null,e.toDisplayString(D(o.skuItem)),1)]),_:1}),e.createVNode(p,{label:"套餐价（元）"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",null,[e.createVNode(s,{controls:!1,disabled:g.isEdit,max:m(o),min:.01,"model-value":+o.matchingPrice,precision:2,style:{width:"80px"},"onUpdate:modelValue":d=>o.matchingPrice=d},null,8,["disabled","max","model-value","onUpdate:modelValue"])]),e.createElementVNode("div",null,"销售价"+e.toDisplayString(m(o)),1)]),_:1})]),_:1},8,["data"]),e.createVNode(t,{modelValue:h.value,"onUpdate:modelValue":l[3]||(l[3]=o=>h.value=o),title:"批量设置",width:"500","destroy-on-close":"",center:"",top:"30vh",onClose:A},{footer:e.withCtx(()=>[e.createElementVNode("div",W,[e.createVNode(r,{onClick:l[2]||(l[2]=o=>h.value=!1)},{default:e.withCtx(()=>l[7]||(l[7]=[e.createTextVNode("取消")])),_:1}),e.createVNode(r,{type:"primary",onClick:q},{default:e.withCtx(()=>l[8]||(l[8]=[e.createTextVNode(" 确定 ")])),_:1})])]),default:e.withCtx(()=>[e.createElementVNode("div",Q,[l[5]||(l[5]=e.createTextVNode(" 套餐库存 ")),e.createVNode(s,{modelValue:u.value.stock,"onUpdate:modelValue":l[0]||(l[0]=o=>u.value.stock=o),precision:0,controls:!1,min:0,onChange:a.handleChange},null,8,["modelValue","onChange"]),l[6]||(l[6]=e.createTextVNode(" 套餐价 ")),e.createVNode(s,{modelValue:u.value.price,"onUpdate:modelValue":l[1]||(l[1]=o=>u.value.price=o),precision:2,controls:!1,min:.01,onChange:a.handleChange},null,8,["modelValue","onChange"])])]),_:1},8,["modelValue"])])}}}),ie="",B=(_,N)=>{const g=_.__vccOpts||_;for(const[M,C]of N)g[M]=C;return g},L=B(Z,[["__scopeId","data-v-df180bb7"]]),v="addon-matching-treasure/setMeal",ee=(_,N)=>z.get({url:`${v}/${_}/${N}`}),te={class:"add"},oe={class:"ruleform-date"},ae={class:"use_discount"},le={class:"tool"},ne=e.defineComponent({__name:"PlatformAddSetMeal",setup(_){const N=k.useRouter(),g=k.useRoute(),M=new F,C=e.ref(),b=e.ref(),h=e.reactive({form:{setMealId:"",shopId:"",shopName:"",setMealName:"",setMealDescription:"",setMealMainPicture:"",setMealType:"FIXED_COMBINATION",setMealStatus:"NOT_STARTED",startTime:"",endTime:"",stackable:{coupon:!1,vip:!1,full:!1},mainProduct:[],matchingProducts:[],shopMode:"",distributionMode:""},isEditDisable:!1,fullReductionTime:[],rules:{setMealName:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],setMealDescription:[{required:!0,message:"请输入套餐描述",trigger:"blur"}],product:[{required:!0,message:"请选择商品",trigger:["blur","change"]}],startTime:[{required:!0,message:"请选择开始日期",trigger:["blur","change"]},{validator:f,trigger:["blur","change"]}],endTime:[{required:!0,message:"请选择结束日期",trigger:["blur","change"]},{validator:r,trigger:["blur","change"]}],setMealMainPicture:[{required:!0,message:"请添加套餐主图",trigger:["blur","change"]}],setMealType:[{required:!0,message:"请选择套餐类型",trigger:["blur","change"]}]},flatMainGoodList:[],flatMatchingGoodList:[],choosedMainGoods:[],choosedMatchingGoods:[],choosedMainGoodsPopup:!1,choosedMatchingGoodsPopup:!1}),{form:n,isEditDisable:m,rules:P,choosedMainGoodsPopup:S,choosedMatchingGoodsPopup:$,choosedMainGoods:D,choosedMatchingGoods:U,flatMainGoodList:y,flatMatchingGoodList:u}=e.toRefs(h),I=e.ref(),{mulTenThousand:H,divTenThousand:A}=O();q();async function q(c=g.query){if(c.shopId&&c.setMealId){m.value=!0;const{code:t,data:o,msg:d}=await ee(c.shopId,c.setMealId);if(t!==200)return E.ElMessage.error(d||"获取活动详情失败");n.value=o,y.value=a(o.mainProduct),u.value=a(o.matchingProducts)}}function a(c){return c.map(t=>{const{productId:o,skuPrice:d,productPic:V,productName:T,skuId:x,skuStock:w,stockType:G,skuName:R,setMealId:i,matchingPrice:de,matchingStock:re,productAttributes:se}=t;return{isJoin:!0,productId:o,productName:T,productPic:V,setMealId:i,matchingPrice:A(de).toString(),matchingStock:re,productAttributes:se,skuItem:{productId:o,skuId:x,skuName:R,skuPrice:d,skuStock:w,stockType:G}}})}function l(c){const t=M.getYMD(new Date),o=M.getYMD(c);return t===o?!1:new Date().getTime()>c.getTime()}function f(c,t,o){t?t&&n.value.endTime?s(new Date(n.value.endTime).getTime(),o,"开始日期和结束日期最少间隔5分钟",new Date(t).getTime(),1e3*60*5):s(new Date(t).getTime(),o,"开始日期必须是一个将来的时间",new Date().getTime(),1e3):o(new Error("请选择活动开始日期"))}function r(c,t,o){t?t&&n.value.startTime?s(new Date(t).getTime(),o,"开始日期和结束日期最少间隔5分钟",new Date(n.value.startTime).getTime(),1e3*60*5):s(new Date(t).getTime(),o,"结束日期最少大当前时间5分钟",new Date().getTime()+1e3,1e3*60*5):o(new Error("请选择活动结束日期"))}function p(c,t=new Date().getTime()){const o=c-t;return(d=1e3)=>o>=d}function s(c,t,o,d,V){p(c,d)(V)?t():t(new Error(o))}return(c,t)=>{const o=e.resolveComponent("el-input"),d=e.resolveComponent("el-form-item"),V=e.resolveComponent("el-date-picker"),T=e.resolveComponent("el-checkbox"),x=e.resolveComponent("el-radio"),w=e.resolveComponent("el-radio-group"),G=e.resolveComponent("el-form"),R=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",te,[t[22]||(t[22]=e.createElementVNode("h1",{class:"title"},"基本信息",-1)),e.createVNode(G,{ref_key:"ruleFormRef",ref:I,model:e.unref(n),rules:e.unref(P),"label-width":"auto","inline-message":!1,"label-position":"left"},{default:e.withCtx(()=>[e.createVNode(d,{label:"套餐名称",prop:"setMealName"},{default:e.withCtx(()=>[e.createVNode(o,{modelValue:e.unref(n).setMealName,"onUpdate:modelValue":t[0]||(t[0]=i=>e.unref(n).setMealName=i),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"10",placeholder:"限10个字",disabled:e.unref(m)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(d,{label:"套餐描述",prop:"setMealDescription"},{default:e.withCtx(()=>[e.createVNode(o,{modelValue:e.unref(n).setMealDescription,"onUpdate:modelValue":t[1]||(t[1]=i=>e.unref(n).setMealDescription=i),modelModifiers:{trim:!0},style:{width:"551px"},maxlength:"40",placeholder:"套餐描述不超过40个字",disabled:e.unref(m)},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(d,{label:"活动日期",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",oe,[e.createVNode(d,{prop:"startTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:e.unref(n).startTime,"onUpdate:modelValue":t[2]||(t[2]=i=>e.unref(n).startTime=i),type:"datetime",disabled:e.unref(m),placeholder:"请选择开始时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":l},null,8,["modelValue","disabled"]),t[11]||(t[11]=e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1))]),_:1}),e.createVNode(d,{prop:"endTime","inline-message":!1},{default:e.withCtx(()=>[e.createVNode(V,{modelValue:e.unref(n).endTime,"onUpdate:modelValue":t[3]||(t[3]=i=>e.unref(n).endTime=i),disabled:e.unref(m),type:"datetime",placeholder:"请选择结束时间","value-format":"YYYY-MM-DD HH:mm:ss",format:"YYYY/MM/DD HH:mm:ss","disabled-date":l},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(d,{label:"套餐主图",prop:"setMealMainPicture"},{default:e.withCtx(()=>[e.createVNode(j,{src:e.unref(n).setMealMainPicture,"onUpdate:src":t[4]||(t[4]=i=>e.unref(n).setMealMainPicture=i),disabled:e.unref(m),width:100,height:100},null,8,["src","disabled"])]),_:1}),e.createVNode(d,{label:"优惠叠加"},{default:e.withCtx(()=>[e.createElementVNode("div",ae,[e.createVNode(T,{modelValue:e.unref(n).stackable.vip,"onUpdate:modelValue":t[5]||(t[5]=i=>e.unref(n).stackable.vip=i),label:"会员价",disabled:e.unref(m)},null,8,["modelValue","disabled"]),e.createVNode(T,{modelValue:e.unref(n).stackable.coupon,"onUpdate:modelValue":t[6]||(t[6]=i=>e.unref(n).stackable.coupon=i),label:"优惠券",disabled:e.unref(m)},null,8,["modelValue","disabled"]),e.createVNode(T,{modelValue:e.unref(n).stackable.full,"onUpdate:modelValue":t[7]||(t[7]=i=>e.unref(n).stackable.full=i),label:"满减",disabled:e.unref(m)},null,8,["modelValue","disabled"]),t[12]||(t[12]=e.createElementVNode("div",{class:"msg discount_msg"},[e.createTextVNode(" 优惠叠加可能导致实付金额为 "),e.createElementVNode("strong",{style:{color:"red"}},"0"),e.createTextVNode(" (实付金额 = 活动价 - 会员优惠 - 优惠券优惠 - 满减优惠) ")],-1))])]),_:1}),e.createVNode(d,{label:"套餐类型",prop:"setMealType"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(n).setMealType,"onUpdate:modelValue":t[8]||(t[8]=i=>e.unref(n).setMealType=i),class:"ml-4",disabled:e.unref(m)},{default:e.withCtx(()=>[e.createVNode(x,{value:"OPTIONAL_PRODUCT"},{default:e.withCtx(()=>t[13]||(t[13]=[e.createTextVNode("自选商品套餐")])),_:1}),e.createVNode(x,{value:"FIXED_COMBINATION"},{default:e.withCtx(()=>t[14]||(t[14]=[e.createTextVNode("固定套餐")])),_:1})]),_:1},8,["modelValue","disabled"]),t[15]||(t[15]=e.createElementVNode("span",{class:"msg"},"自选套餐：主商品+至少1种搭配商品以上 ；固定套餐：主商品+库存不为0的所有搭配商品各1件以上",-1))]),_:1}),e.createVNode(d,{label:"配送方式",prop:"distributionMode"},{default:e.withCtx(()=>[e.createVNode(w,{modelValue:e.unref(n).distributionMode,"onUpdate:modelValue":t[9]||(t[9]=i=>e.unref(n).distributionMode=i),disabled:e.unref(m),class:"ml-4",onChange:c.handleChangeDistributionMode},{default:e.withCtx(()=>[e.unref(n).shopMode!=="O2O"?(e.openBlock(),e.createBlock(x,{key:0,label:"EXPRESS"},{default:e.withCtx(()=>t[16]||(t[16]=[e.createTextVNode("快递配送")])),_:1})):e.createCommentVNode("",!0),e.createVNode(x,{value:"INTRA_CITY_DISTRIBUTION"},{default:e.withCtx(()=>t[17]||(t[17]=[e.createTextVNode("同城配送")])),_:1}),e.createVNode(x,{value:"SHOP_STORE"},{default:e.withCtx(()=>t[18]||(t[18]=[e.createTextVNode("到店自提")])),_:1}),e.unref(n).shopMode!=="O2O"?(e.openBlock(),e.createBlock(x,{key:1,label:"VIRTUAL"},{default:e.withCtx(()=>t[19]||(t[19]=[e.createTextVNode("虚拟配送")])),_:1})):e.createCommentVNode("",!0)]),_:1},8,["modelValue","disabled","onChange"])]),_:1}),e.createVNode(d,{label:"主商品(限1种商品)",required:""},{default:e.withCtx(()=>t[20]||(t[20]=[e.createElementVNode("span",{class:"msg"},"主商品：必买商品，此商品详情页展示搭配套餐 搭配商品：用户选择购买",-1)])),_:1})]),_:1},8,["model","rules"]),e.createVNode(L,{ref_key:"selectMainGoodsTableRef",ref:C,"product-list":e.unref(D),"is-edit":e.unref(m),"flat-good-list":e.unref(y),style:{"margin-bottom":"20px"}},null,8,["product-list","is-edit","flat-good-list"]),e.createVNode(d,{label:"搭配商品（限4种）",required:""},{default:e.withCtx(()=>t[21]||(t[21]=[e.createElementVNode("span",{class:"msg"},"是否参与：SKU的粒度设置商品是否参与活动，是(默认)则参与活动，反则否",-1)])),_:1}),e.createVNode(L,{ref_key:"selectMatchingGoodsTableRef",ref:b,"product-list":e.unref(U),"is-edit":e.unref(m),"flat-good-list":e.unref(u),"product-attributes":"MATCHING_PRODUCTS"},null,8,["product-list","is-edit","flat-good-list"])]),e.createElementVNode("div",le,[e.createVNode(R,{round:"",plain:"",onClick:t[10]||(t[10]=i=>e.unref(N).back())},{default:e.withCtx(()=>t[23]||(t[23]=[e.createTextVNode("返回")])),_:1})])],64)}}}),ce="";return B(ne,[["__scopeId","data-v-fe3aff81"]])});
