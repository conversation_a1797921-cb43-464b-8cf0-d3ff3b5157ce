(function(e,i){typeof exports=="object"&&typeof module<"u"?module.exports=i(require("vue"),require("element-plus"),require("@/components/q-icon/q-icon.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/components/q-icon/q-icon.vue","@/apis/http"],i):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformOverviewBasic=i(e.PlatformOverviewBasicContext.Vue,e.PlatformOverviewBasicContext.ElementPlus,e.PlatformOverviewBasicContext.QIcon,e.PlatformOverviewBasicContext.Request))})(this,function(e,i,c,d){"use strict";var l=document.createElement("style");l.textContent=`@charset "UTF-8";.basic[data-v-a0997f66]{color:#fff;padding:22px 29px 20px;background:#fff;margin-bottom:10px}.basic__content[data-v-a0997f66]{display:flex;align-items:center;justify-content:space-between}.basic__item[data-v-a0997f66]{margin-top:28px;width:180px;height:132px;border-radius:5px;display:flex;flex-direction:column;align-items:center;justify-content:center;font-size:12px}.basic__item--cont[data-v-a0997f66]{display:flex;justify-content:center;align-items:center;margin-top:10px}.basic__item--icon[data-v-a0997f66]{text-align:center;color:#fff}.basic__item--num[data-v-a0997f66]{font-size:24px;font-weight:400;text-align:center;margin-left:10px}.basicsupplier[data-v-a0997f66]{background:linear-gradient(225deg,#fab8b3 0%,#f185a2 100%)}.basicprocure[data-v-a0997f66]{background:linear-gradient(225deg,#b4f2ce 0%,#50c580 100%)}
`,document.head.appendChild(l);const p=()=>d.get({url:"addon-supplier/supplier-overview/supplier-purchase-order-number"}),m={class:"basic__item basicsupplier"},f={class:"basic__item--cont"},_={class:"basic__item--num"},u={class:"basic__item basicprocure"},v={class:"basic__item--cont"},x={class:"basic__item--num"},b=e.defineComponent({__name:"PlatformOverviewBasic",props:{properties:{type:Object,default:{}}},setup(a){const r=a,n=e.ref(0);o();async function o(){const{code:s,data:t}=await p();if(s!==200)return i.ElMessage.error("获取订单数量失败");n.value=t}return(s,t)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",m,[e.createVNode(c,{class:"basic__item--icon",size:"44px",name:"icon-gongyingshang"}),e.createElementVNode("div",f,[t[0]||(t[0]=e.createElementVNode("div",null,"供应商数量",-1)),e.createElementVNode("div",_,e.toDisplayString(r.properties.supplierNumber),1)])]),e.createElementVNode("div",u,[e.createVNode(c,{class:"basic__item--icon",size:"44px",name:"icon-caigou"}),e.createElementVNode("div",v,[t[1]||(t[1]=e.createElementVNode("div",null,"采购订单数量",-1)),e.createElementVNode("div",x,e.toDisplayString(Number(n.value)),1)])]),t[2]||(t[2]=e.createElementVNode("text",null,null,-1))],64))}}),g="";return((a,r)=>{const n=a.__vccOpts||a;for(const[o,s]of r)n[o]=s;return n})(b,[["__scopeId","data-v-a0997f66"]])});
