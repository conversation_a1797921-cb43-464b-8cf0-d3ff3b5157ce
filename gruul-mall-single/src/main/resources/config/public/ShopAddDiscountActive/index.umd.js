(function(e,m){typeof exports=="object"&&typeof module<"u"?module.exports=m(require("vue"),require("vue-router"),require("@/apis/http"),require("@/utils/date"),require("decimal.js"),require("@/components/q-choose-goods-popup/q-choose-goods-popup.vue"),require("element-plus"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/apis/http","@/utils/date","decimal.js","@/components/q-choose-goods-popup/q-choose-goods-popup.vue","element-plus","@/composables/useConvert"],m):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAddDiscountActive=m(e.ShopAddDiscountActiveContext.Vue,e.ShopAddDiscountActiveContext.VueR<PERSON>er,e.ShopAddDiscountActiveContext.Request,e.ShopAddDiscountActiveContext.DateUtil,e.ShopAddDiscountActiveContext.Decimal,e.ShopAddDiscountActiveContext.QChooseGoodsPopup,e.ShopAddDiscountActiveContext.ElementPlus,e.ShopAddDiscountActiveContext.UseConvert))})(this,function(e,m,D,S,T,M,u,F){"use strict";var E=document.createElement("style");E.textContent=`@charset "UTF-8";.add[data-v-8758cc7f]{height:100%;position:relative;display:flex;flex-direction:column;overflow-y:scroll;padding-left:16px;padding-right:16px;padding-top:30px}.title[data-v-8758cc7f]{font-size:14px;color:#606266;font-weight:700;margin-bottom:40px}.msg[data-v-8758cc7f]{font-size:12px;margin-left:12px;color:#c4c4c4}.rules[data-v-8758cc7f]{display:flex;margin-top:10px;height:50px}.period-validity[data-v-8758cc7f]{width:300px;display:flex}.text[data-v-8758cc7f]{font-size:14px;color:#333}.goodsData[data-v-8758cc7f]{border:1px solid #ccc}.goods-list[data-v-8758cc7f]{width:90%;margin-top:20px;border:1px solid transparent}.goods-list__info[data-v-8758cc7f]{display:flex}.goods-list__goods-list__info-name[data-v-8758cc7f]{display:flex;flex-direction:column;justify-content:space-around;align-items:flex-start;padding:0 16px}.goods-list__goods-list__info-name--name[data-v-8758cc7f]{width:462px;font-size:14px;color:#2e99f3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.goods-list__goods-list__info-name--price[data-v-8758cc7f]{font-size:14px;text-align:LEFT;color:#f12f22}.goods-list__goods-list__info-name--price[data-v-8758cc7f]:before{content:"￥";font-size:12px;text-align:LEFT;color:#f12f22}.my-header[data-v-8758cc7f]{font-size:16px}.ruleform-date[data-v-8758cc7f]{width:100%;display:flex;align-items:center}.flex[data-v-8758cc7f]{margin-top:10px;height:50px}.flex-item[data-v-8758cc7f]{width:40%}.coupon-rules[data-v-8758cc7f]{height:60px;display:flex;justify-content:center;align-items:center}.nav-button[data-v-8758cc7f]{position:fixed;left:50%;bottom:30px}.commodityForm[data-v-8758cc7f]{box-sizing:border-box;padding-bottom:62px}.commodityForm__tool[data-v-8758cc7f]{margin-top:auto;align-items:center;position:sticky;bottom:0;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:100}
`,document.head.appendChild(E);const q=[{label:"满X元减",value:"FULL_REDUCTION"},{label:"满X元折",value:"FULL_DISCOUNT"}],U="addon-full-reduction/full/reduction",O=p=>D.post({url:U,data:p}),P=p=>D.post({url:`${U}/detail`,data:p}),Y={class:"add"},B={class:"ruleform-date"},z={key:0,class:"flex",style:{width:"100%",display:"flex"}},G={key:1,class:"flex",style:{width:"100%",display:"flex"}},H={class:"goods-list__info"},$={class:"goods-list__goods-list__info-name"},j={class:"goods-list__goods-list__info-name--name"},X={class:"commodityForm__tool"},Q=e.defineComponent({__name:"ShopAddDiscountActive",setup(p){const C=m.useRouter(),f=m.useRoute(),h=new S,x=e.reactive({form:{id:null,name:"",time:{start:"",end:""},rules:[],productType:"ALL_PRODUCT",productIds:[]},isEditDisable:!1,goodsTotal:0,fullReductionTime:[],rules:{name:[{required:!0,message:"请输入活动名称",trigger:"blur"}],"time.start":[{required:!0,message:"请选择开始日期",trigger:["blur","change"]}],"time.end":[{required:!0,message:"请选择结束日期",trigger:["blur","change"]}],productType:[{required:!0,message:"请输入满减规则",trigger:["blur","change"]}]},chooseGoodsPopup:!1,chooseGoodsList:[]}),{form:l,isEditDisable:a,rules:W,chooseGoodsPopup:y,chooseGoodsList:s,goodsTotal:_e}=e.toRefs(x),_=e.ref(),{mulTenThousand:J,divTenThousand:K}=F();Z();async function Z(){const n=f.query.id;if(!n)return;l.value.id=n;const{code:t,data:d,msg:i}=await P({id:n});if(t!==200){u.ElMessage.error(i||"获取活动信息失败");return}a.value=f.query.isLookUp==="true",d.rules=k(d.rules,oe),s.value=d.products.map(c=>({productId:c.id,productName:c.name,pic:c.image})),delete d.products,x.form=d}const v=async()=>{if(a.value){R();return}if(!_.value||!await _.value.validate())return;if(!ee(l.value.rules)){u.ElMessage.error("满减规则输入有误");return}if(["SPECIFIED_PRODUCT_PARTICIPATE","SPECIFIED_PRODUCT_NOT_PARTICIPATE"].includes(l.value.productType)&&!s.value.length){u.ElMessage.error("请选择商品");return}l.value.productIds=s.value.map(c=>c.productId);const t=k(l.value.rules,te),{code:d,msg:i}=await O({...x.form,rules:t});if(d!==200){u.ElMessage.error(i||"活动创建失败");return}u.ElMessage.success("活动创建成功"),de(),R()};function R(){C.push({name:"applyDiscountIndex"})}function ee(n){return n.every(t=>["FULL_DISCOUNT","FULL_REDUCTION"].includes(t.type)?t.type==="FULL_DISCOUNT"?t.conditionAmount&&t.discountRatio&&t.discountRatio>0&&t.discountRatio<=9.9:t.conditionAmount&&t.discountAmount&&t.conditionAmount>=t.discountAmount:!1)}function k(n,t){return n.map(d=>{const{type:i,conditionAmount:c,discountAmount:g,discountRatio:N}=d;return d.type==="FULL_DISCOUNT"?{type:i,conditionAmount:t(c),discountRatio:N}:d.type==="FULL_REDUCTION"?{type:i,conditionAmount:t(c),discountAmount:t(g)}:d})}function te(n){return n?J(n).toNumber():0}function oe(n){return n?K(n).toNumber():0}const ne=n=>{s.value=n.tempGoods},le=async n=>{await u.ElMessageBox.confirm("确定移除该商品?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"})&&(s.value=s.value.filter(d=>d.productId!==n))};function de(){_.value&&(_.value.resetFields(),s.value=[])}const ie=n=>{l.value.rules.splice(n,1)},ae=()=>{l.value.rules.push({type:null,conditionAmount:0,discountAmount:0,discountRatio:0})},I=n=>{const t=h.getYMDs(n),d=h.getYMDs(new Date);return new T(new Date(t).getTime()).lessThan(new Date(d).getTime())||new T(new Date(n).getTime()).greaterThanOrEqualTo(new Date(re(6)).getTime())};function re(n){let t=new Date;return t.setMonth(t.getMonth()+Number(n)),t.toLocaleString().replace(/\//g,"-")}return(n,t)=>{const d=e.resolveComponent("el-input"),i=e.resolveComponent("el-form-item"),c=e.resolveComponent("el-date-picker"),g=e.resolveComponent("el-link"),N=e.resolveComponent("el-option"),se=e.resolveComponent("el-select"),V=e.resolveComponent("el-table-column"),b=e.resolveComponent("el-input-number"),L=e.resolveComponent("el-table"),w=e.resolveComponent("el-radio"),ce=e.resolveComponent("el-radio-group"),pe=e.resolveComponent("el-image"),A=e.resolveComponent("el-button"),me=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Y,[t[23]||(t[23]=e.createElementVNode("h1",{class:"title"},"基本信息",-1)),e.createVNode(me,{ref_key:"ruleFormRef",ref:_,"inline-message":!1,model:e.unref(l),rules:e.unref(W),"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(i,{label:"活动名称",prop:"name"},{default:e.withCtx(()=>[e.createVNode(d,{modelValue:e.unref(l).name,"onUpdate:modelValue":t[0]||(t[0]=o=>e.unref(l).name=o),modelModifiers:{trim:!0},disabled:e.unref(a),maxlength:"10",placeholder:"请输入活动名称",style:{width:"551px"}},null,8,["modelValue","disabled"]),t[7]||(t[7]=e.createElementVNode("span",{class:"msg"},"活动名称不超过10个字",-1))]),_:1}),e.createVNode(i,{label:"活动时间",required:""},{default:e.withCtx(()=>[e.createElementVNode("div",B,[e.createVNode(i,{"inline-message":!1,prop:"time.start"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:e.unref(l).time.start,"onUpdate:modelValue":t[1]||(t[1]=o=>e.unref(l).time.start=o),disabled:e.unref(a),"disabled-date":I,format:"YYYY/MM/DD HH:mm:ss",placeholder:"请选择开始时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"]),t[8]||(t[8]=e.createElementVNode("span",{style:{margin:"0 10px"}},"至",-1))]),_:1}),e.createVNode(i,{"inline-message":!1,prop:"time.end"},{default:e.withCtx(()=>[e.createVNode(c,{modelValue:e.unref(l).time.end,"onUpdate:modelValue":t[2]||(t[2]=o=>e.unref(l).time.end=o),disabled:e.unref(a),"disabled-date":I,format:"YYYY/MM/DD HH:mm:ss",placeholder:"请选择结束时间",type:"datetime","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})])]),_:1}),e.createVNode(i,{"label-width":"80px",style:{"margin-bottom":"0"}},{default:e.withCtx(()=>[e.createVNode(g,{disabled:e.unref(a)||e.unref(l).rules.length>9,underline:!1,type:"primary",onClick:ae},{default:e.withCtx(()=>t[9]||(t[9]=[e.createTextVNode("添加规则 ")])),_:1},8,["disabled"]),t[10]||(t[10]=e.createElementVNode("span",{class:"msg"},"(限定10条)",-1))]),_:1}),e.createVNode(i,{label:"活动规则"},{default:e.withCtx(()=>[e.createVNode(L,{"cell-style":{height:"60px"},data:e.unref(l).rules,"header-cell-style":{textAlign:"center",fontSize:"14px",color:"#606266"},border:"",style:{width:"90%"}},{default:e.withCtx(()=>[e.createVNode(V,{label:"满减条件",width:"170"},{default:e.withCtx(({row:o})=>[e.createVNode(i,{prop:"rules"},{default:e.withCtx(()=>[e.createVNode(se,{modelValue:o.type,"onUpdate:modelValue":r=>o.type=r,disabled:e.unref(a),placeholder:"全部类型"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(q),r=>(e.openBlock(),e.createBlock(N,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024)]),_:1}),e.createVNode(V,{label:"满减规则",width:"300"},{default:e.withCtx(({row:o})=>[o.type==="FULL_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",z,[e.createVNode(i,{"label-width":"0%",prop:"conditionAmount"},{default:e.withCtx(()=>[t[11]||(t[11]=e.createElementVNode("span",null,"满",-1)),e.createVNode(b,{modelValue:o.conditionAmount,"onUpdate:modelValue":r=>o.conditionAmount=r,modelModifiers:{number:!0},controls:!1,disabled:e.unref(a),max:99999,min:1,precision:2,style:{width:"60%",margin:"0 5px"}},null,8,["modelValue","onUpdate:modelValue","disabled"]),t[12]||(t[12]=e.createElementVNode("span",null,"元,打",-1))]),_:2},1024),e.createVNode(i,{"label-width":"0%",prop:"discountRatio"},{default:e.withCtx(()=>[e.createVNode(b,{modelValue:o.discountRatio,"onUpdate:modelValue":r=>o.discountRatio=r,modelModifiers:{number:!0},controls:!1,disabled:e.unref(a),max:9.9,min:.1,precision:1,style:{width:"80%"}},null,8,["modelValue","onUpdate:modelValue","disabled"]),t[13]||(t[13]=e.createElementVNode("span",{style:{"margin-left":"5px"}},"折",-1))]),_:2},1024)])):e.createCommentVNode("",!0),o.type==="FULL_REDUCTION"?(e.openBlock(),e.createElementBlock("div",G,[e.createVNode(i,{"label-width":0,prop:"requiredAmount"},{default:e.withCtx(()=>[t[14]||(t[14]=e.createElementVNode("span",null,"满",-1)),e.createVNode(b,{modelValue:o.conditionAmount,"onUpdate:modelValue":r=>o.conditionAmount=r,modelModifiers:{number:!0},controls:!1,disabled:e.unref(a),max:999999,min:1,precision:2,style:{width:"90px",margin:"0 5px"}},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:2},1024),e.createVNode(i,{"label-width":0,prop:"discountAmount"},{default:e.withCtx(()=>[t[15]||(t[15]=e.createElementVNode("span",null,"元,减",-1)),e.createVNode(b,{modelValue:o.discountAmount,"onUpdate:modelValue":r=>o.discountAmount=r,modelModifiers:{number:!0},controls:!1,disabled:e.unref(a),max:o.conditionAmount,min:1,precision:2,style:{width:"90px",margin:"0 5px"}},null,8,["modelValue","onUpdate:modelValue","disabled","max"]),t[16]||(t[16]=e.createElementVNode("span",{style:{"margin-left":"5px"}},"元",-1))]),_:2},1024)])):e.createCommentVNode("",!0)]),_:1}),e.createVNode(V,{align:"center",label:"操作",fixed:"right"},{default:e.withCtx(({$index:o})=>[e.createVNode(g,{disabled:e.unref(a),underline:!1,type:"danger",onClick:r=>ie(o)},{default:e.withCtx(()=>t[17]||(t[17]=[e.createTextVNode("删除 ")])),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),e.createVNode(i,{label:"商品选择",prop:"productType"},{default:e.withCtx(()=>[e.createVNode(ce,{modelValue:e.unref(l).productType,"onUpdate:modelValue":t[3]||(t[3]=o=>e.unref(l).productType=o),disabled:e.unref(a)},{default:e.withCtx(()=>[e.createVNode(w,{value:"ALL_PRODUCT"},{default:e.withCtx(()=>t[18]||(t[18]=[e.createTextVNode("全部商品参与")])),_:1}),e.createVNode(w,{value:"SPECIFIED_PRODUCT_PARTICIPATE"},{default:e.withCtx(()=>t[19]||(t[19]=[e.createTextVNode("指定商品参与")])),_:1}),e.createVNode(w,{value:"SPECIFIED_PRODUCT_NOT_PARTICIPATE"},{default:e.withCtx(()=>t[20]||(t[20]=[e.createTextVNode("指定商品不参与")])),_:1})]),_:1},8,["modelValue","disabled"]),e.unref(l).productType!=="ALL_PRODUCT"?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass([e.unref(s).length&&"goodsData","goods-list"])},[e.createVNode(L,{data:e.unref(s),"header-cell-style":{fontSize:"14px",color:"#606266",background:"#f2f2f2",height:"54px",fontWeight:"normal"},height:"500px",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(V,{label:`商品信息${e.unref(s).length?`(已选择${e.unref(s).length}款商品)`:""}`},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",H,[e.createVNode(pe,{"preview-src-list":[o.pic],"preview-teleported":!0,src:o.pic,fit:"",style:{width:"60px",height:"60px"}},null,8,["preview-src-list","src"]),e.createElementVNode("div",$,[e.createElementVNode("div",j,e.toDisplayString(o.productName),1)])])]),_:1},8,["label"]),e.createVNode(V,{width:"110",fixed:"right"},{header:e.withCtx(()=>[e.createVNode(A,{disabled:e.unref(a),plain:"",round:"",size:"small",type:"primary",onClick:t[4]||(t[4]=o=>y.value=!0)},{default:e.withCtx(()=>t[21]||(t[21]=[e.createTextVNode(" 添加商品 ")])),_:1},8,["disabled"])]),default:e.withCtx(({row:o})=>[e.createVNode(g,{disabled:e.unref(a),underline:!1,type:"primary",onClick:r=>le(o.productId)},{default:e.withCtx(()=>t[22]||(t[22]=[e.createTextVNode(" 删除 ")])),_:2},1032,["disabled","onClick"])]),_:1})]),_:1},8,["data"])],2)):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["model","rules"]),e.createVNode(M,{modelValue:e.unref(y),"onUpdate:modelValue":t[5]||(t[5]=o=>e.isRef(y)?y.value=o:null),pointGoodsList:e.unref(s),searchConsignmentProduct:!0,onOnConfirm:ne},null,8,["modelValue","pointGoodsList"])]),e.createElementVNode("div",X,[e.createVNode(A,{plain:"",round:"",onClick:t[6]||(t[6]=o=>e.unref(C).back())},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(a)?"返回":"取消"),1)]),_:1}),e.unref(a)?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(A,{key:0,round:"",type:"primary",onClick:v},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode("确定")])),_:1}))])],64)}}}),ue="";return((p,C)=>{const f=p.__vccOpts||p;for(const[h,x]of C)f[h]=x;return f})(Q,[["__scopeId","data-v-8758cc7f"]])});
