(function(e,f){typeof exports=="object"&&typeof module<"u"?module.exports=f(require("vue"),require("vue-router"),require("@/components/q-map/q-map.vue"),require("element-plus"),require("@/libs/validate"),require("@/apis/http"),require("@/views/material/selectMaterial.vue")):typeof define=="function"&&define.amd?define(["vue","vue-router","@/components/q-map/q-map.vue","element-plus","@/libs/validate","@/apis/http","@/views/material/selectMaterial.vue"],f):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopStoreInfo=f(e.ShopStoreInfoContext.Vue,e.ShopStoreInfoContext.VueRouter,e.ShopStoreInfoContext.QMap,e.ShopStoreInfoContext.ElementPlus,e.ShopStoreInfoContext.LibsValidate,e.ShopStoreInfoContext.Request,e.ShopStoreInfoContext.selectMaterial))})(this,function(e,f,k,m,E,_,q){"use strict";var x=document.createElement("style");x.textContent=`@charset "UTF-8";.q_plugin_container[data-v-9d4670c1]{padding-top:30px;overflow-y:scroll}.q_plugin_container[data-v-9d4670c1] .el-form{padding-left:16px;padding-right:16px}.q_plugin_container .disabled_point[data-v-9d4670c1]{cursor:not-allowed!important;position:relative}.q_plugin_container .disabled_point[data-v-9d4670c1]:after{position:absolute;width:100%;height:100%;content:"";top:0;left:0;background:rgba(0,0,0,.2);z-index:100}.q_plugin_container .storeForm[data-v-9d4670c1]{overflow:hidden}.storeForm__tool[data-v-9d4670c1]{display:flex;align-items:center;position:sticky;bottom:0;padding:15px 0;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin-top:auto}.q_plugin_container .DeliveryDay[data-v-9d4670c1]{width:108px;background:#d5d5d5;text-align:center;font-size:14px;color:#333}.q_plugin_container .selectMaterialStyle[data-v-9d4670c1]{width:60px;height:60px;border-radius:5px;overflow:hidden;border:1px dashed #ccc;cursor:pointer;display:flex;justify-content:center;align-items:center}.selectMaterialStyle__span[data-v-9d4670c1]{color:#999;font-size:20px}
`,document.head.appendChild(x);const I=p=>_.post({url:"addon-shop-store/store/issue",data:p}),D=p=>_.post({url:"addon-shop-store/store/update",data:p}),M=(p,c)=>_.get({url:`addon-shop-store/store/info/${p}`,params:c}),B={class:"q_plugin_container f1"},T={style:{width:"150px"}},L={style:{width:"150px"}},F={style:{display:"flex"}},z={style:{display:"flex","margin-left":"13px"}},H=["src"],$=["src"],U=["src"],A=["src"],R={class:"storeForm__tool"},P=e.defineComponent({__name:"ShopStoreInfo",setup(p){const c=e.ref(!1),b=l=>{c.value=l},d=e.ref(""),u=l=>{s.value||(c.value=!0,d.value=l)},j=l=>{d.value==="storeLogo"&&(o.value.storeLogo=l==null?void 0:l.shift()),d.value==="showstoreImg0"&&(a.value[0]=l),d.value==="showstoreImg1"&&(a.value[1]=l),d.value==="showstoreImg2"&&(a.value[2]=l)},O=l=>{d.value==="storeLogo"&&(o.value.storeLogo=l==null?void 0:l.shift()),d.value==="showstoreImg0"&&(a.value[0]=l),d.value==="showstoreImg1"&&(a.value[1]=l),d.value==="showstoreImg2"&&(a.value[2]=l)},C=e.ref(0),g=f.useRoute(),h=f.useRouter(),V=e.ref(),a=e.ref([]),o=e.ref({id:"",storeName:"",storeLogo:"",storeImg:"",storePhone:"",functionaryName:"",functionaryPhone:"",businessStartTime:"00:00:00",businessEndTime:"23:59:59",detailedAddress:"",startDeliveryDay:0,endDeliveryDay:0,location:{type:"Point",coordinates:["121.583336","29.990282"]},shopAssistantList:[]}),G=e.reactive({storeName:[{required:!0,message:"请填写门店名称",trigger:"blur"}],storeLogo:[{required:!0,message:"请上传门店logo",trigger:"blur"}],storeImg:[{required:!0,message:"请上传门店图片",trigger:"blur"}],functionaryName:[{required:!0,message:"请填写负责人姓名",trigger:"blur"}],functionaryPhone:[{required:!0,validator:X,trigger:"blur"}],businessStartTime:[{required:!0,message:"请填写营业开始时间",trigger:"blur"}],businessEndTime:[{required:!0,message:"请填写营业结束时间",trigger:"blur"}],startDeliveryDay:[{required:!0,message:"请填写开始提货时间",trigger:"blur"}],endDeliveryDay:[{required:!0,message:"请填写结束提货时间",trigger:"blur"}],detailedAddress:[{required:!0,message:"请选择地址",trigger:"blur"},{min:2,max:200,message:"输入长度必须在2~200以内",trigger:"blur"}]}),w=e.ref(!0),s=e.ref(!1);K();async function K(){if(g.query.shopId&&g.query.id){g.query.lookType==="OnlyLook"&&(s.value=!0);const{code:l,data:t,msg:i}=await M(g.query.shopId,{id:g.query.id});l===200&&t?(a.value=t.storeImg.split(","),o.value=t,C.value=Date.now()):m.ElMessage.error(i||"获取失败")}}const Q=l=>{if(!s.value){if(g.query.id&&w.value){w.value=!1;return}o.value.detailedAddress=l.address,o.value.location.coordinates=l.position}};function X(l,t,i){t===""?i(new Error("请填写手机号")):E.REGEX_MOBILE(t)?i():i(new Error("请填写正确的手机号"))}const J=async()=>{if(!(!V.value||(o.value.storeImg=a.value.map(t=>t[0].trim()).join(","),!await V.value.validate()))){if(o.value.startDeliveryDay>o.value.endDeliveryDay)return m.ElMessage.error("结束提货时间应大于等于开始提货时间");if(!a.value.length){m.ElMessage.error("请上传门店图片");return}o.value.id?Y():W()}},W=async()=>{const{code:l,msg:t}=await I(o.value);l===200?(m.ElMessage.success("新增门店成功"),h.push({path:"/shop/store"})):m.ElMessage.error(t||"新增失败")},Y=async()=>{const{code:l,msg:t}=await D(o.value);l===200?(m.ElMessage.success("修改门店信息成功"),h.push({path:"/shop/store"})):m.ElMessage.error(t||"修改门店信息失败")},y=(l,t)=>{const i=[];for(let n=l;n<=t;n++)i.push(n);return i},Z=()=>{if(o.value.businessEndTime){const l=Number(o.value.businessEndTime.split(":")[0]);return y(l+1,24)}return[]},ee=()=>y(1,59),te=()=>y(1,59),oe=()=>{if(o.value.businessStartTime){const l=Number(o.value.businessStartTime.split(":")[0]);return y(0,l-1).concat(l-1,24)}return[]},le=()=>y(0,58),se=()=>y(0,58);return(l,t)=>{const i=e.resolveComponent("el-input"),n=e.resolveComponent("el-form-item"),v=e.resolveComponent("el-time-picker"),re=e.resolveComponent("el-row"),N=e.resolveComponent("el-input-number"),ae=e.resolveComponent("el-form"),S=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",B,[e.createVNode(ae,{ref_key:"StoreFormRef",ref:V,model:o.value,rules:G,"label-width":"100px"},{default:e.withCtx(()=>[e.createVNode(n,{label:"门店名称",prop:"storeName"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.value.storeName,"onUpdate:modelValue":t[0]||(t[0]=r=>o.value.storeName=r),disabled:s.value,maxlength:"25",placeholder:"请输入门店名称"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(n,{label:"负责人姓名",prop:"functionaryName"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.value.functionaryName,"onUpdate:modelValue":t[1]||(t[1]=r=>o.value.functionaryName=r),disabled:s.value,maxlength:"8",placeholder:"请输入负责人姓名"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(n,{label:"负责人电话",prop:"functionaryPhone"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.value.functionaryPhone,"onUpdate:modelValue":t[2]||(t[2]=r=>o.value.functionaryPhone=r),disabled:s.value,maxlength:"11",placeholder:"请输入负责人电话"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(n,{label:"营业时间"},{default:e.withCtx(()=>[e.createVNode(re,{style:{display:"flex",width:"100%"}},{default:e.withCtx(()=>[e.createElementVNode("div",T,[e.createVNode(n,{"label-width":"0px",prop:"businessStartTime"},{default:e.withCtx(()=>[e.createVNode(v,{modelValue:o.value.businessStartTime,"onUpdate:modelValue":t[3]||(t[3]=r=>o.value.businessStartTime=r),disabled:s.value,"disabled-hours":Z,"disabled-minutes":ee,"disabled-seconds":te,format:"HH:mm:ss",placeholder:"开始时间","value-format":"HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})]),t[17]||(t[17]=e.createElementVNode("div",{style:{margin:"0 10px"}},"至",-1)),e.createElementVNode("div",L,[e.createVNode(n,{"label-width":"0px",prop:"businessEndTime"},{default:e.withCtx(()=>[e.createVNode(v,{modelValue:o.value.businessEndTime,"onUpdate:modelValue":t[4]||(t[4]=r=>o.value.businessEndTime=r),disabled:s.value,"disabled-hours":oe,"disabled-minutes":le,"disabled-seconds":se,format:"HH:mm:ss",placeholder:"结束时间","value-format":"HH:mm:ss"},null,8,["modelValue","disabled"])]),_:1})])]),_:1})]),_:1}),e.createVNode(n,{label:"提货时间"},{default:e.withCtx(()=>[e.createVNode(n,{"label-width":"0px",prop:"startDeliveryDay"},{default:e.withCtx(()=>[e.createElementVNode("div",F,[e.createVNode(N,{modelValue:o.value.startDeliveryDay,"onUpdate:modelValue":t[5]||(t[5]=r=>o.value.startDeliveryDay=r),controls:!1,disabled:s.value,min:0,step:1,"step-strictly":"",style:{width:"110px"}},null,8,["modelValue","disabled"]),t[18]||(t[18]=e.createElementVNode("div",{class:"DeliveryDay"},"天后开始提货",-1))])]),_:1}),e.createVNode(n,{"label-width":"0px",prop:"endDeliveryDay"},{default:e.withCtx(()=>[e.createElementVNode("div",z,[e.createVNode(N,{modelValue:o.value.endDeliveryDay,"onUpdate:modelValue":t[6]||(t[6]=r=>o.value.endDeliveryDay=r),controls:!1,disabled:s.value,min:o.value.startDeliveryDay,step:1,"step-strictly":"",style:{width:"110px"}},null,8,["modelValue","disabled","min"]),t[19]||(t[19]=e.createElementVNode("div",{class:"DeliveryDay"},"天后结束提货",-1))])]),_:1})]),_:1}),e.createVNode(n,{label:"详细地址",prop:"detailedAddress"},{default:e.withCtx(()=>[e.createVNode(i,{modelValue:o.value.detailedAddress,"onUpdate:modelValue":t[7]||(t[7]=r=>o.value.detailedAddress=r),disabled:s.value,maxlength:"60",placeholder:"请选择经纬度"},null,8,["modelValue","disabled"])]),_:1}),e.createVNode(n,null,{default:e.withCtx(()=>[(e.openBlock(),e.createBlock(k,{key:C.value,coordinates:o.value.location.coordinates,onChange:Q,disabled:s.value},null,8,["coordinates","disabled"]))]),_:1}),e.createVNode(n,{label:"门店logo",prop:"storeLogo"},{default:e.withCtx(()=>[!o.value.storeLogo&&!s.value?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[8]||(t[8]=r=>u("storeLogo"))},t[20]||(t[20]=[e.createElementVNode("span",{class:"selectMaterialStyle__span"},"+",-1)]),2)):o.value.storeLogo?(e.openBlock(),e.createElementBlock("img",{key:1,src:o.value.storeLogo,alt:"",class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[9]||(t[9]=r=>u("storeLogo"))},null,10,H)):e.createCommentVNode("",!0),e.createVNode(n,{label:"门店图片",prop:"storeImg"},{default:e.withCtx(()=>[!a.value[0]&&!s.value?(e.openBlock(),e.createElementBlock("div",{key:0,class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[10]||(t[10]=r=>u("showstoreImg0"))},t[21]||(t[21]=[e.createElementVNode("span",{class:"selectMaterialStyle__span"},"+",-1)]),2)):a.value[0]?(e.openBlock(),e.createElementBlock("img",{key:1,src:a.value[0],alt:"",class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[11]||(t[11]=r=>u("showstoreImg0"))},null,10,$)):e.createCommentVNode("",!0),!a.value[1]&&!s.value?(e.openBlock(),e.createElementBlock("div",{key:2,style:{margin:"0 20px"},class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[12]||(t[12]=r=>u("showstoreImg1"))},t[22]||(t[22]=[e.createElementVNode("span",{class:"selectMaterialStyle__span"},"+",-1)]),2)):a.value[1]?(e.openBlock(),e.createElementBlock("img",{key:3,src:a.value[1],style:{margin:"0 20px"},alt:"",class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[13]||(t[13]=r=>u("showstoreImg1"))},null,10,U)):e.createCommentVNode("",!0),!a.value[2]&&!s.value?(e.openBlock(),e.createElementBlock("div",{key:4,class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[14]||(t[14]=r=>u("showstoreImg2"))},t[23]||(t[23]=[e.createElementVNode("span",{class:"selectMaterialStyle__span"},"+",-1)]),2)):a.value[2]?(e.openBlock(),e.createElementBlock("img",{key:5,src:a.value[2],alt:"",class:e.normalizeClass({selectMaterialStyle:!0,disabled_point:s.value}),onClick:t[15]||(t[15]=r=>u("showstoreImg2"))},null,10,A)):e.createCommentVNode("",!0)]),_:1})]),_:1})]),_:1},8,["model","rules"]),e.createElementVNode("div",R,[e.createVNode(S,{round:"",onClick:t[16]||(t[16]=r=>e.unref(h).go(-1))},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.value?"返回":"取消"),1)]),_:1}),s.value?e.createCommentVNode("",!0):(e.openBlock(),e.createBlock(S,{key:0,style:{"margin-left":"40px"},round:"",type:"primary",onClick:J},{default:e.withCtx(()=>t[24]||(t[24]=[e.createTextVNode(" 保存")])),_:1}))]),e.createVNode(q,{"dialog-visible":c.value,"upload-files":1,onSelectMaterialFn:b,onCroppedFileChange:j,onCheckedFileLists:O},null,8,["dialog-visible"])])}}}),ne="";return((p,c)=>{const b=p.__vccOpts||p;for(const[d,u]of c)b[d]=u;return b})(P,[["__scopeId","data-v-9d4670c1"]])});
