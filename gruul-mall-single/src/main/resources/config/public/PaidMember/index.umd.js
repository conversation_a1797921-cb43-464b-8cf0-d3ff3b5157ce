(function(e,g){typeof exports=="object"&&typeof module<"u"?module.exports=g(require("vue"),require("element-plus"),require("@/apis/http"),require("@/composables/useConvert"),require("lodash-es")):typeof define=="function"&&define.amd?define(["vue","element-plus","@/apis/http","@/composables/useConvert","lodash-es"],g):(e=typeof globalThis<"u"?globalThis:e||self,e.PaidMember=g(e.PaidMemberContext.Vue,e.PaidMemberContext.ElementPlus,e.PaidMemberContext.Request,e.PaidMemberContext.UseConvert,e.PaidMemberContext.Lodash))})(this,function(e,g,E,q,j){"use strict";var H=document.createElement("style");H.textContent=`@charset "UTF-8";.title[data-v-0ef5eb16]{font-size:14px;color:#323233;font-weight:700;margin-bottom:20px}.msg[data-v-0ef5eb16]{font-size:12px;color:#c4c4c4}.nav-button[data-v-0ef5eb16]{width:1010px;position:fixed;bottom:10px;padding:15px 0;display:flex;justify-content:center;box-shadow:0 0 10px #d5d5d5;background-color:#fff;z-index:999;margin:0 auto 0 -55px}.title[data-v-6151f9e1]{font-weight:700;font-size:1.2em;line-height:1.8}.el-row[data-v-6151f9e1]{padding-left:15px;line-height:1.6}.level[data-v-228b1287]{line-height:43px;color:#999;line-height:63px}.pricing[data-v-228b1287]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;display:flex;flex-wrap:wrap}.pricing div[data-v-228b1287]:after{content:"、"}.pricing div[data-v-228b1287]:last-child:after{content:""}.interests[data-v-228b1287]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(H);const Y=()=>E.get({url:"addon-member/paid/member/list "}),W=()=>E.get({url:"gruul-mall-user/user/member/rights/usable"}),K=b=>E.post({url:"addon-member/paid/member/save",data:b}),Q=b=>E.post({url:"addon-member/paid/member/update",data:b}),X=b=>E.get({url:`addon-member/paid/member/info?id=${b}`}),Z=b=>E.del({url:`addon-member/paid/member/${b}`}),v=b=>E.post({url:"addon-member/paid/member/saveLabel",data:b}),ee={style:{padding:"0 40px"}},te={style:{width:"450px"}},le={style:{width:"100%"}},oe=e.defineComponent({__name:"EditPayingMembers",props:{memberId:{type:String,default:""},memberLevel:{type:Number,default:void 0}},setup(b,{expose:M}){const V=b,{mulHundred:r,divHundred:p,divTenThousand:k,mulTenThousand:S}=q();e.ref(!1);const O=e.ref(),_=e.ref(new Map),a=e.ref([]),c=e.ref(0),x=e.ref(0),i=e.reactive({id:"",paidMemberName:"",paidRuleJson:[],relevancyRightsList:[]}),D={INTEGRAL_MULTIPLE:G,GOODS_DISCOUNT:J},R={INTEGRAL_MULTIPLE:"积分值为2-9.9倍保留一位小数",GOODS_DISCOUNT:"商品折扣值为0.1-9.9折保留一位小数"},h=[{name:"1个月",label:"ONE_MONTH"},{name:"3个月",label:"THREE_MONTH"},{name:"12个月",label:"TWELVE_MONTH"},{name:"3年",label:"THREE_YEAR"},{name:"5年",label:"FIVE_YEAR"}],y=e.reactive({paidMemberName:[{required:!0,message:"请输入会员名称",trigger:"blur"}],paidRuleJson:[{validator:U,trigger:"blur"}]});function U(o,t,d){for(let u=0;u<t.length;u++)if(!t[u].effectiveDurationType)return d("请完善付费规则");d()}m();const A=async()=>{if(!i.paidRuleJson.length)return g.ElMessage.error("请填写付费规则"),Promise.reject("请填写付费规则");const o=O.value;if(await w(o),B()){const t=j.cloneDeep(i);t.relevancyRightsList=Array.from(_.value.values()).map(C=>(C.rightsType==="GOODS_DISCOUNT"?C.extendValue=Number(r(c.value)):C.rightsType==="INTEGRAL_MULTIPLE"?C.extendValue=Number(r(x.value)):delete C.extendValue,C)),t.paidRuleJson=t.paidRuleJson.map(C=>(C.price=Number(S(C.price)),C));const{code:d,msg:u}=V.memberId?await Q(t):await K(t);return d===200?(g.ElMessage.success("保存成功"),Promise.resolve("保存成功")):(g.ElMessage.error(u||"保存失败"),Promise.reject(u||"保存失败"))}else return Promise.reject("校验失败")},n=()=>{i.paidRuleJson.push({price:0,effectiveDurationType:""})},l=o=>{i.paidRuleJson.splice(o,1)},f=o=>{_.value.has(o.memberRightsId)?_.value.delete(o.memberRightsId):_.value.set(o.memberRightsId,o)};function w(o){return o?new Promise((t,d)=>{o==null||o.validate((u,C)=>{u?t("success valid"):d(C)})}):Promise.reject(new Error("no form instance input"))}function B(){const o=[];_.value.forEach(d=>{D[d.rightsType]&&(console.log("tempArr",d.rightsType==="GOODS_DISCOUNT"?c.value:x.value),o.push({type:D[d.rightsType](d.rightsType==="GOODS_DISCOUNT"?c.value:x.value),tips:R[d.rightsType]||""}))});const t=o.filter(d=>!d.type);return t.length&&g.ElMessage.warning(t[0].tips),!t.length}function G(o){const t=Number(o);return t>=2&&t<=9.9&&F(String(o))}function F(o,t){const d=/^\d+(\.\d?)?$/;return o!==""?d.test(o):!1}function J(o){console.log(o);const t=Number(o);return t>=.1&&t<=9.9&&F(String(o))}async function m(){const{code:o,data:t}=await W();if(o!==200)return g.ElMessage.error("获取会员权益失败");a.value=t,L()}async function L(){if(V.memberId){const{code:o,data:t,msg:d}=await X(String(V.memberId));if(o===200&&t){const{id:u,paidMemberName:C,paidRuleJson:$,relevancyRightsList:I}=t;i.id=u,i.paidMemberName=C,i.paidRuleJson=$.map(T=>(T.price=Number(k(T.price)),T)),i.relevancyRightsList=I,I.forEach(T=>{T.rightsType==="GOODS_DISCOUNT"?c.value=Number(p(T.extendValue)):T.rightsType==="INTEGRAL_MULTIPLE"&&(x.value=Number(p(T.extendValue))),_.value.set(T.memberRightsId,T)})}else g.ElMessage.error(d||"获取失败")}}return M({handleSubmit:A}),(o,t)=>{const d=e.resolveComponent("el-form-item"),u=e.resolveComponent("el-input"),C=e.resolveComponent("el-option"),$=e.resolveComponent("el-select"),I=e.resolveComponent("el-table-column"),T=e.resolveComponent("el-input-number"),z=e.resolveComponent("el-link"),fe=e.resolveComponent("el-table"),be=e.resolveComponent("el-checkbox"),_e=e.resolveComponent("el-row"),ge=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",ee,[t[8]||(t[8]=e.createElementVNode("h1",{class:"title"},"基本信息",-1)),e.createVNode(ge,{ref_key:"ruleFormRef",ref:O,model:i,rules:y,"label-width":"auto"},{default:e.withCtx(()=>[e.createVNode(d,{label:"会员等级"},{default:e.withCtx(()=>[e.createTextVNode(" SVIP"+e.toDisplayString(V.memberLevel),1)]),_:1}),e.createVNode(d,{label:"等级名称",prop:"paidMemberName"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:i.paidMemberName,"onUpdate:modelValue":t[0]||(t[0]=s=>i.paidMemberName=s),modelModifiers:{trim:!0},maxlength:8,minlength:3,placeholder:"请输入等级名称",style:{width:"226px"}},null,8,["modelValue"])]),_:1}),e.createVNode(d,{label:"付费规则",prop:"paidRuleJson",required:""},{default:e.withCtx(()=>[e.withDirectives(e.createElementVNode("div",te,[e.createVNode(fe,{data:i.paidRuleJson,height:i.paidRuleJson.length>4?"220px":o.undef,size:"small",style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(I,{label:"有效期"},{default:e.withCtx(({row:s})=>[e.createVNode($,{modelValue:s.effectiveDurationType,"onUpdate:modelValue":N=>s.effectiveDurationType=N,class:"m-2",placeholder:"请选择"},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(h,N=>e.createVNode(C,{key:N.name,label:N.name,value:N.label},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(N.name),1)]),_:2},1032,["label","value"])),64))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(I,{label:"价格"},{default:e.withCtx(({row:s})=>[e.createVNode(T,{modelValue:s.price,"onUpdate:modelValue":N=>s.price=N,min:.01,precision:2,"controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e.createVNode(I,{label:"操作",width:"50px"},{default:e.withCtx(({$index:s})=>[e.createVNode(z,{underline:!1,type:"primary",onClick:N=>l(s)},{default:e.withCtx(()=>t[3]||(t[3]=[e.createTextVNode("删除")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","height"])],512),[[e.vShow,i.paidRuleJson.length]]),e.createElementVNode("div",le,[e.createVNode(z,{underline:!1,type:"primary",onClick:n},{default:e.withCtx(()=>t[4]||(t[4]=[e.createTextVNode("添加规则")])),_:1})])]),_:1}),t[7]||(t[7]=e.createElementVNode("h1",{class:"title"},"权益礼包",-1)),e.createVNode(d,null,{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.value,s=>(e.openBlock(),e.createBlock(_e,{key:s.id,style:{width:"100%"}},{default:e.withCtx(()=>[e.createVNode(d,{"label-width":"0",style:{margin:"10px 0"}},{default:e.withCtx(()=>[(e.openBlock(),e.createBlock(be,{key:s.id+_.value.has(s.id),checked:_.value.has(s.id),label:{name:s.rightsName,memberRightsId:s.id,extendValue:0},onChange:N=>f({name:s.rightsName,rightsType:s.rightsType,memberRightsId:s.id,extendValue:0})},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(s.rightsName),1)]),_:2},1032,["checked","label","onChange"])),s.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createBlock(u,{key:0,modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=N=>c.value=N),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>t[5]||(t[5]=[e.createTextVNode("折")])),_:1},8,["modelValue"])):s.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createBlock(u,{key:1,modelValue:x.value,"onUpdate:modelValue":t[2]||(t[2]=N=>x.value=N),style:{width:"130px",margin:"0 20px"}},{append:e.withCtx(()=>t[6]||(t[6]=[e.createTextVNode("倍")])),_:1},8,["modelValue"])):e.createCommentVNode("",!0)]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["model","rules"])])}}}),he="",P=(b,M)=>{const V=b.__vccOpts||b;for(const[r,p]of M)V[r]=p;return V},ne=P(oe,[["__scopeId","data-v-0ef5eb16"]]),re=e.defineComponent({__name:"SetMemberLabel",props:{labelInfo:{type:Object,default:()=>({})}},setup(b,{expose:M}){const V=e.ref(null),r=e.reactive({id:"",name:"",fontColor:"",labelColor:"",priceLabelName:"",priceFontColor:"",priceLabelColor:""}),p=b,k={fontColor:{required:!0,message:"请选择会员名称字体颜色",trigger:"change"},labelColor:{required:!0,message:"请选择标签颜色",trigger:"change"},priceLabelName:{required:!0,message:"请输入会员价标签",trigger:"blur"},priceFontColor:{required:!0,message:"请选择会员价字体颜色",trigger:"change"},priceLabelColor:{required:!0,message:"请选择会员价标签颜色",trigger:"change"}};return(()=>{Object.keys(r).forEach(_=>{var a;r[_]=((a=p==null?void 0:p.labelInfo)==null?void 0:a[_])||r[_]})})(),M({getFormModel:()=>new Promise((_,a)=>{V.value?V.value.validate((c,x)=>{c?_(r):a(x)}):a("non form inst")})}),(_,a)=>{const c=e.resolveComponent("el-col"),x=e.resolveComponent("el-color-picker"),i=e.resolveComponent("el-form-item"),D=e.resolveComponent("el-row"),R=e.resolveComponent("el-input"),h=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(h,{ref_key:"formRef",ref:V,model:r,rules:k},{default:e.withCtx(()=>[a[5]||(a[5]=e.createElementVNode("div",{class:"title"},"会员名称标签",-1)),e.createVNode(D,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:24},{default:e.withCtx(()=>[e.createTextVNode("会员名称："+e.toDisplayString(r.name),1)]),_:1}),e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createVNode(i,{label:"会员名称字体",prop:"fontColor"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:r.fontColor,"onUpdate:modelValue":a[0]||(a[0]=y=>r.fontColor=y)},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createVNode(i,{label:"标签颜色",prop:"labelColor"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:r.labelColor,"onUpdate:modelValue":a[1]||(a[1]=y=>r.labelColor=y)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a[6]||(a[6]=e.createElementVNode("div",{class:"title"},"会员价标签",-1)),e.createVNode(D,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:24},{default:e.withCtx(()=>[e.createVNode(i,{label:"会员价标签",prop:"priceLabelName"},{default:e.withCtx(()=>[e.createVNode(R,{modelValue:r.priceLabelName,"onUpdate:modelValue":a[2]||(a[2]=y=>r.priceLabelName=y),placeholder:"请输入会员价标签",maxlength:5},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createVNode(i,{label:"会员价字体",prop:"priceFontColor"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:r.priceFontColor,"onUpdate:modelValue":a[3]||(a[3]=y=>r.priceFontColor=y)},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(c,{span:12},{default:e.withCtx(()=>[e.createVNode(i,{label:"标签颜色",prop:"priceLabelColor"},{default:e.withCtx(()=>[e.createVNode(x,{modelValue:r.priceLabelColor,"onUpdate:modelValue":a[4]||(a[4]=y=>r.priceLabelColor=y)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])}}}),ue="",ae=P(re,[["__scopeId","data-v-6151f9e1"]]),ie={class:"level"},de={class:"pricing"},se={key:0},ce={key:1},me={key:2},pe=e.defineComponent({__name:"PaidMember",setup(b){const{divTenThousand:M,divHundred:V}=q(),r=e.ref([]),p=e.reactive({id:"",currentMemberLevel:void 0}),k=e.ref(!1),S=e.ref(null);i();const O=async n=>{g.ElMessageBox.confirm("确定需要删除?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const{code:l,msg:f}=await Z(n);l===200?(g.ElMessage.success("删除成功"),i()):g.ElMessage.error(f)})},_=()=>{p.id="",p.currentMemberLevel=r.value.length+1,k.value=!0},a=(n,l)=>{p.id=n,p.currentMemberLevel=l,k.value=!0},c=()=>{k.value=!1,p.id="",p.currentMemberLevel=void 0},x=async()=>{var n;await((n=S==null?void 0:S.value)==null?void 0:n.handleSubmit()),c(),i()};async function i(){const{code:n,data:l,msg:f}=await Y();n===200?r.value=l:g.ElMessage.error(f||"获取列表失败")}function D(n){return`${{ONE_MONTH:"1个月",THREE_MONTH:"3个月",TWELVE_MONTH:"12个月",THREE_YEAR:"3年",FIVE_YEAR:"5年"}[n.effectiveDurationType]}${M(n.price)}`}const R=e.ref(null),h=e.reactive({showLabelDialog:!1,currentSetLabelForm:{id:"",name:"",fontColor:"",labelColor:"",priceLabelName:"",priceFontColor:"",priceLabelColor:""}}),y=n=>{Object.keys(h.currentSetLabelForm).forEach(l=>{var f,w;(f=n==null?void 0:n.labelJson)!=null&&f[l]&&(h.currentSetLabelForm[l]=(w=n==null?void 0:n.labelJson)==null?void 0:w[l])}),h.currentSetLabelForm.id=n==null?void 0:n.id,h.currentSetLabelForm.name=n==null?void 0:n.paidMemberName,h.showLabelDialog=!0},U=()=>{Object.keys(h.currentSetLabelForm).forEach(n=>{h.currentSetLabelForm[n]=""})},A=async()=>{var w;const n=await((w=R.value)==null?void 0:w.getFormModel()),{code:l,msg:f}=await v(n);l===200?(g.ElMessage.success({message:f||"标签设置成功"}),i(),h.showLabelDialog=!1):g.ElMessage.error({message:f||"标签设置失败"})};return(n,l)=>{const f=e.resolveComponent("el-button"),w=e.resolveComponent("el-table-column"),B=e.resolveComponent("el-link"),G=e.resolveComponent("el-table"),F=e.resolveComponent("el-dialog"),J=e.resolveComponent("el-tab-pane");return e.openBlock(),e.createBlock(J,{label:"付费会员",name:"PayingMember"},{default:e.withCtx(()=>[e.createVNode(f,{round:"",style:{"margin-bottom":"15px"},type:"primary",disabled:r.value.length>=5,onClick:_},{default:e.withCtx(()=>l[3]||(l[3]=[e.createTextVNode("添加等级")])),_:1},8,["disabled"]),e.createVNode(G,{ref:"multipleTableRef",data:r.value,"header-row-style":{fontSize:"14px"},"header-cell-style":{background:"#f6f8fa",color:"#333333",height:"48px"},"cell-style":{fontSize:"14px",color:"#666"}},{default:e.withCtx(()=>[e.createVNode(w,{label:"会员等级",width:"160"},{default:e.withCtx(({$index:m})=>[e.createElementVNode("div",ie,"SVIP"+e.toDisplayString(m+1),1)]),_:1}),e.createVNode(w,{label:"付费会员名称",width:"220"},{default:e.withCtx(({row:m})=>[e.createElementVNode("span",null,e.toDisplayString(m.paidMemberName),1)]),_:1}),e.createVNode(w,{label:"定价",width:"320"},{default:e.withCtx(({row:m})=>[e.createElementVNode("div",de,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.paidRuleJson,L=>(e.openBlock(),e.createElementBlock("div",{key:L.id},e.toDisplayString(D(L))+"元",1))),128))])]),_:1}),e.createVNode(w,{label:"会员权益"},{default:e.withCtx(({row:m})=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(m.relevancyRightsList,L=>e.withDirectives((e.openBlock(),e.createElementBlock("div",{key:L.id,class:"interests"},[L.rightsType==="GOODS_DISCOUNT"?(e.openBlock(),e.createElementBlock("div",se,"商品折扣"+e.toDisplayString(e.unref(V)(L.extendValue))+"折",1)):L.rightsType==="INTEGRAL_MULTIPLE"?(e.openBlock(),e.createElementBlock("div",ce,"积分"+e.toDisplayString(e.unref(V)(L.extendValue))+"倍",1)):(e.openBlock(),e.createElementBlock("div",me,e.toDisplayString(L.rightsName),1))])),[[e.vShow,m.relevancyRightsList.length]])),128))]),_:1}),e.createVNode(w,{align:"right",fixed:"right",label:"操作",width:"180"},{default:e.withCtx(({row:m,$index:L})=>[e.createVNode(B,{underline:!1,size:"small",style:{"margin-right":"12px"},type:"primary",onClick:o=>a(m.id,L+1)},{default:e.withCtx(()=>l[4]||(l[4]=[e.createTextVNode(" 编辑 ")])),_:2},1032,["onClick"]),e.createVNode(B,{underline:!1,type:"primary",size:"small",onClick:o=>y(m)},{default:e.withCtx(()=>l[5]||(l[5]=[e.createTextVNode("标签设置")])),_:2},1032,["onClick"]),e.createVNode(B,{style:{"margin-left":"12px"},underline:!1,size:"small",type:"danger",onClick:o=>O(m.id)},{default:e.withCtx(()=>l[6]||(l[6]=[e.createTextVNode(" 删除 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),e.createVNode(F,{modelValue:k.value,"onUpdate:modelValue":l[0]||(l[0]=m=>k.value=m),title:p.id?"编辑会员":"添加会员","destroy-on-close":"",onClose:c},{footer:e.withCtx(()=>[e.createVNode(f,{onClick:c},{default:e.withCtx(()=>l[7]||(l[7]=[e.createTextVNode("取 消")])),_:1}),e.createVNode(f,{type:"primary",onClick:x},{default:e.withCtx(()=>l[8]||(l[8]=[e.createTextVNode("保 存")])),_:1})]),default:e.withCtx(()=>[e.createVNode(ne,{ref_key:"editMemberRef",ref:S,"member-id":p.id,"member-level":p.currentMemberLevel},null,8,["member-id","member-level"])]),_:1},8,["modelValue","title"]),e.createVNode(F,{modelValue:h.showLabelDialog,"onUpdate:modelValue":l[2]||(l[2]=m=>h.showLabelDialog=m),title:"标签设置","destroy-on-close":"",onClose:U},{footer:e.withCtx(()=>[e.createVNode(f,{onClick:l[1]||(l[1]=m=>h.showLabelDialog=!1)},{default:e.withCtx(()=>l[9]||(l[9]=[e.createTextVNode("取 消")])),_:1}),e.createVNode(f,{type:"primary",onClick:A},{default:e.withCtx(()=>l[10]||(l[10]=[e.createTextVNode("保 存")])),_:1})]),default:e.withCtx(()=>[e.createVNode(ae,{ref_key:"setLabelRef",ref:R,"label-info":h.currentSetLabelForm},null,8,["label-info"])]),_:1},8,["modelValue"])]),_:1})}}}),Ce="";return P(pe,[["__scopeId","data-v-228b1287"]])});
