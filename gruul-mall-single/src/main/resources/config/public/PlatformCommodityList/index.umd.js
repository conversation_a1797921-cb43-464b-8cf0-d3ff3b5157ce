(function(e,O){typeof exports=="object"&&typeof module<"u"?module.exports=O(require("vue"),require("@/apis/shops"),require("@/apis/good"),require("@/components/schema-form/index.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/composables/useConvert"),require("vue-clipboard3"),require("@/composables/usePlatformGoodStatus"),require("vue-router"),require("@/components/q-upload/q-upload.vue"),require("@element-plus/icons-vue"),require("lodash-es"),require("@/components/element-plus/el-table/ElTableEmpty/index.vue")):typeof define=="function"&&define.amd?define(["vue","@/apis/shops","@/apis/good","@/components/schema-form/index.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/PageManage.vue","element-plus","@/composables/useConvert","vue-clipboard3","@/composables/usePlatformGoodStatus","vue-router","@/components/q-upload/q-upload.vue","@element-plus/icons-vue","lodash-es","@/components/element-plus/el-table/ElTableEmpty/index.vue"],O):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformCommodityList=O(e.PlatformCommodityListContext.Vue,e.PlatformCommodityListContext.ShopAPI,e.PlatformCommodityListContext.GoodAPI,e.PlatformCommodityListContext.SchemaForm,e.PlatformCommodityListContext.QTable,e.PlatformCommodityListContext.QTableColumn,e.PlatformCommodityListContext.PageManageTwo,e.PlatformCommodityListContext.ElementPlus,e.PlatformCommodityListContext.UseConvert,e.PlatformCommodityListContext.VueClipboard3,e.PlatformCommodityListContext.PlatformGoodStatus,e.PlatformCommodityListContext.VueRouter,e.PlatformCommodityListContext.QUpload,e.PlatformCommodityListContext.ElementPlusIconsVue,e.PlatformCommodityListContext.Lodash,e.PlatformCommodityListContext.ElTableEmpty))})(this,function(e,O,B,Q,v,w,ee,S,j,te,oe,ae,G,le,ne,ie){"use strict";var H=document.createElement("style");H.textContent=`@charset "UTF-8";.commodity{display:flex;justify-content:center;align-items:center;font-size:12px;text-align:left;justify-content:flex-start}.commodity__left{width:68px;height:68px;margin-right:10px}.commodity__right--name{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.commodity__right--price{color:#ff7417;margin:4px 0}.commodity__right--sup{width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.el-row[data-v-5e084717]{padding:5px 0;line-height:1.5}.details[data-v-5e084717]{max-height:770px}.details-more[data-v-5e084717]{max-height:440px;overflow-y:auto}.details-more[data-v-5e084717] img{max-width:100%}[data-v-61d81865]{font-size:14px}.q-table[data-v-61d81865]{overflow:auto;transition:height .5s}.shop-name[data-v-61d81865]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.see[data-v-61d81865]{width:82px;height:36px;background:#eaf5fe;border-radius:21px;font-size:12px;color:#309af3;line-height:36px;text-align:center;cursor:pointer}.violation-evidence[data-v-61d81865]{width:80px;height:80px;border-radius:8px;object-fit:contain;vertical-align:top}.violation-evidence+.violation-evidence[data-v-61d81865]{margin-left:5px}.notification[data-v-61d81865]{color:#666}.evidence[data-v-61d81865]{display:flex;flex-wrap:wrap}[data-v-61d81865] .el-form-item__error{padding:6px 26px}.first-rules[data-v-61d81865]{margin-left:28px}.first-rules[data-v-61d81865] .el-form-item__error{padding:5px 0!important}.no_data[data-v-61d81865]{margin-top:100px}.no_data img[data-v-61d81865]{width:170px;height:170px}.no_data .cont[data-v-61d81865]{color:#737b80;text-align:center;margin-top:20px}.explainData>div[data-v-61d81865]{display:flex;margin-top:8px}.explainData>div img[data-v-61d81865]{margin-top:6px}.explainData .explainData_label[data-v-61d81865]{width:80px;text-align:right;margin-right:8px}
`,document.head.appendChild(H);const se={style:{background:"#f9f9f9"}},re=e.defineComponent({__name:"CommoditySearch",props:{tabsActive:{type:String,default:" "}},emits:["getSearchParams","showChange"],setup(p,{emit:_}){const V=p,C=_,n=e.reactive({platformCategoryId:"",shopId:"",productType:"",sellType:"",supplierGoodsName:"",status:"",cascaderModel:""}),L=[{value:"",label:"全部商品"},{value:"VIRTUAL_PRODUCT",label:"虚拟商品"},{value:"REAL_PRODUCT",label:"实物商品"}],N=[{value:"",label:"全部"},{value:"PURCHASE",label:"采购商品"},{value:"CONSIGNMENT",label:"代销商品"}],g=e.ref([]),m=e.ref([]),r=[{label:"供应商名称",labelWidth:85,prop:"shopId",valueType:"select",options:g,fieldProps:{placeholder:"请输入店铺名称",props:{value:"id",label:"name",expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:l=>{u(l)}}},{label:"商品名称",labelWidth:75,prop:"supplierGoodsName",valueType:"copy",fieldProps:{placeholder:"请输入商品名称"}},{label:"平台类目",prop:"cascaderModel",valueType:"cascader",options:m,fieldProps:{placeholder:"请选择平台类目",props:{value:"id",label:"name",expandTrigger:"hover"},showAllLevels:!1,onChange:l=>{l.length>1&&(n.platformCategoryId=l[l.length-1],n.cascaderModel=void 0)}}},{label:"商品类型",labelWidth:75,prop:"productType",valueType:"select",options:L,fieldProps:{placeholder:"请选择"}},{label:"销售方式",labelWidth:75,prop:"sellType",valueType:"select",options:N,fieldProps:{placeholder:"请选择"}}],x=()=>{n.platformCategoryId=n.platformCategoryId==="0"?"":n.platformCategoryId,C("getSearchParams",n)},R=()=>{Object.keys(n).forEach(l=>n[l]=""),x()},u=async l=>{const s=await B.doGetSeachSupplierSearchList({supplierName:l});g.value=(s==null?void 0:s.data.map(d=>({...d,label:d.name,value:d.id})))||[]};e.watch(()=>V.tabsActive,l=>{l!==" "&&(n.status="")});const b=e.reactive({pageSize:20,pageNum:1,total:0});P();async function P(){const{data:l}=await O.doGetCategory({current:b.pageNum,size:1e3});b.total=l.total,h(l.records,"secondCategoryVos"),l.records.unshift({categoryId:"0",id:"0",name:"全部类目",parentId:"0",sort:1}),m.value=l.records}function h(l,s){l.forEach(d=>{d[s]&&(d.children=d[s],delete d[s],d.children.length&&h(d.children,"categoryThirdlyVos"))})}const i=l=>{l&&Object.keys(l).forEach(s=>n[s]=l[s])};return(l,s)=>(e.openBlock(),e.createElementBlock("div",se,[e.createVNode(Q,{"v-model":n,columns:r,"has-unfold":"",onChangeFormItem:i,onSearchHandle:x,onHandleReset:R},null,8,["v-model"])]))}}),de={class:"commodity"},ce={class:"commodity__left"},pe={class:"commodity__right"},me=["title"],fe=["title"],ge=e.defineComponent({__name:"CommodityInfo",props:{info:{type:Object,default(){return{}}}},setup(p){const _=p;return(V,C)=>{var N,g;const n=e.resolveComponent("el-image"),L=e.resolveComponent("el-tooltip");return e.openBlock(),e.createElementBlock("div",de,[e.createElementVNode("div",ce,[e.createVNode(n,{style:{width:"68px",height:"68px"},src:(g=(N=_.info.albumPics)==null?void 0:N.split(","))==null?void 0:g.shift(),fit:"fill"},null,8,["src"])]),e.createElementVNode("div",pe,[_.info.productName.length>=30?(e.openBlock(),e.createBlock(L,{key:0,class:"box-item",effect:"dark",content:_.info.productName,placement:"bottom"},{default:e.withCtx(()=>[e.createElementVNode("div",{class:"commodity__right--name",title:_.info.productName},e.toDisplayString(_.info.productName),9,me)]),_:1},8,["content"])):(e.openBlock(),e.createElementBlock("div",{key:1,class:"commodity__right--name",title:_.info.productName},e.toDisplayString(_.info.productName),9,fe))])])}}}),Ye="",he={class:"details"},_e=["innerHTML"];var W=(p=>(p.CONSIGNMENT="代销商品",p.PURCHASE="采购商品",p.OWN="自有商品",p))(W||{});const xe=e.defineComponent({__name:"commodityDetail",props:{commodityId:{type:String,default:""},shopId:{type:String,default:""}},setup(p){const{divTenThousand:_}=j(),{toClipboard:V}=te(),C=p,n=e.ref({});(async()=>{const{code:g,msg:m,data:r}=await B.doGetSupplierCommodityDetails(C.commodityId,{shopId:C.shopId});g===200?n.value=r:S.ElMessage.error({message:m})})();const N=async g=>{try{await V(g),S.ElMessage.success("复制成功")}catch{S.ElMessage.error("复制失败")}};return(g,m)=>{const r=e.resolveComponent("el-col"),x=e.resolveComponent("el-row"),R=e.resolveComponent("el-link"),u=e.resolveComponent("el-table-column"),b=e.resolveComponent("el-image"),P=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",he,[e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:24,style:{"font-weight":"600"}},{default:e.withCtx(()=>{var h;return[e.createTextVNode(e.toDisplayString((h=n.value)==null?void 0:h.name),1)]}),_:1})]),_:1}),e.createVNode(x,{gutter:8},{default:e.withCtx(()=>{var h,i,l;return[e.createVNode(r,{span:(h=n.value)!=null&&h.collectionUrl?8:12},{default:e.withCtx(()=>[e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:6},{default:e.withCtx(()=>m[1]||(m[1]=[e.createTextVNode("平台类目：")])),_:1}),e.createVNode(r,{span:18},{default:e.withCtx(()=>{var s,d,F,M,$,q;return[e.createTextVNode(e.toDisplayString((d=(s=n.value)==null?void 0:s.platformCategoryName)==null?void 0:d.oneName)+"/"+e.toDisplayString((M=(F=n.value)==null?void 0:F.platformCategoryName)==null?void 0:M.twoName)+"/"+e.toDisplayString((q=($=n.value)==null?void 0:$.platformCategoryName)==null?void 0:q.threeName),1)]}),_:1})]),_:1})]),_:1},8,["span"]),e.createVNode(r,{span:(i=n.value)!=null&&i.collectionUrl?8:12},{default:e.withCtx(()=>[e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:6},{default:e.withCtx(()=>m[2]||(m[2]=[e.createTextVNode("销售方式：")])),_:1}),e.createVNode(r,{span:18},{default:e.withCtx(()=>{var s;return[e.createTextVNode(e.toDisplayString(W[(s=n.value)==null?void 0:s.sellType]),1)]}),_:1})]),_:1})]),_:1},8,["span"]),(l=n.value)!=null&&l.collectionUrl?(e.openBlock(),e.createBlock(r,{key:0,span:8},{default:e.withCtx(()=>[e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:6},{default:e.withCtx(()=>m[3]||(m[3]=[e.createTextVNode("采集地址：")])),_:1}),e.createVNode(r,{span:18},{default:e.withCtx(()=>[e.createVNode(R,{type:"primary",onClick:m[0]||(m[0]=s=>N(n.value.collectionUrl))},{default:e.withCtx(()=>m[4]||(m[4]=[e.createTextVNode("复制")])),_:1})]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)]}),_:1}),e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:24},{default:e.withCtx(()=>{var h;return[e.createVNode(P,{data:(h=n.value)==null?void 0:h.storageSkus,"max-height":"230px",size:"small"},{default:e.withCtx(()=>[e.createVNode(u,{label:"规格"},{default:e.withCtx(({row:i})=>{var l,s;return[e.createTextVNode(e.toDisplayString((l=i==null?void 0:i.specs)!=null&&l.length?(s=i==null?void 0:i.specs)==null?void 0:s.join(";"):"单规格"),1)]}),_:1}),e.createVNode(u,{label:"SKU图"},{default:e.withCtx(({row:i})=>[e.createVNode(b,{src:i==null?void 0:i.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(u,{label:"实售价(元)"},{default:e.withCtx(({row:i})=>[e.createTextVNode(e.toDisplayString(e.unref(_)(i==null?void 0:i.salePrice)),1)]),_:1}),e.createVNode(u,{label:"指导价(元)"},{default:e.withCtx(({row:i})=>[e.createTextVNode(e.toDisplayString(e.unref(_)(i==null?void 0:i.price)),1)]),_:1}),e.createVNode(u,{label:"重量(kg)",prop:"weight"})]),_:1},8,["data"])]}),_:1})]),_:1}),e.createVNode(x,{gutter:8},{default:e.withCtx(()=>[e.createVNode(r,{span:24},{default:e.withCtx(()=>{var h;return[e.createElementVNode("div",{class:"details-more",innerHTML:(h=n.value)==null?void 0:h.detail},null,8,_e)]}),_:1})]),_:1})])}}}),Je="",K=(p,_)=>{const V=p.__vccOpts||p;for(const[C,n]of _)V[C]=n;return V},ye=K(xe,[["__scopeId","data-v-5e084717"]]),Ce={class:"tab_container"},Ne={class:"commodity__right--price"},Ve=["title"],ue=["title"],be={key:0},Ee={key:1},Te={style:{"margin-left":"28px"}},ke={class:"evidence"},Se={class:"dialog-footer"},Le={style:{"line-height":"30px"},class:"explainData"},we={style:{display:"flex"}},Pe={style:{flex:"1"}},Ie=["src"];var X=(p=>(p.CONSIGNMENT="代销商品",p.PURCHASE="采购商品",p.OWN="自有商品",p))(X||{});const De=e.defineComponent({__name:"PlatformCommodityList",props:{properties:{type:Object,default:()=>({})}},setup(p){const V=p.properties.VITE_PLATFORM_NAME,{divTenThousand:C}=j(),n={PROHIBITED:"违禁品",COUNTERFEIT:"假冒伪劣",EXCESSIVE_PLATFORM_INTERVENTION:"平台介入率太高",TITLE_IRREGULARITY:"标题有问题",OTHER:"其他"},L=ae.useRoute(),N=e.ref(L.query.name?String(L.query.name):""),g=e.ref([]),m=e.ref([]),r=e.reactive({pageSize:20,pageNum:1,total:0}),x=e.ref({platformCategoryId:"",sellType:"",productType:"",status:"",shopId:"",supplierGoodsName:"",sort:""});e.ref("");const R=e.ref(),u=e.ref(),b=e.ref(!1);E();const P=e.ref(),h={violationType:[{required:!0,message:"类型为必选项",trigger:"change"}],violationExplain:[{required:!0,message:"违规说明为必填项",trigger:"blur"}],violationEvidence:[{required:!0,message:"相关证据为必选项",trigger:"change"}]},i=e.ref(!1),l=V||"启山智软",s=e.ref({violationType:[],violationExplain:"",violationEvidence:"",rummager:l}),d=e.ref([]),F=a=>{d.value.splice(a,1),s.value.violationEvidence=d.value.join(",")},M=(a,t)=>{d.value.push(a),s.value.violationEvidence=d.value.join(",")},$=e.reactive([{label:"PLATFORM_SELL_OFF",name:"下架"}]);let q={};const Oe=async(a,t)=>{var c;if(t.status===a){const y=(c=$.find(T=>a))==null?void 0:c.name;S.ElMessage.error(`该商品已${y}`);return}q=t,i.value=!0},Be=async()=>{var a;(a=P.value)==null||a.validate(async t=>{if(t){const c={productIds:[q.id],productViolation:s.value};try{const{code:y,success:T}=await B.doUpdateSupplierSellStatus(c,"PLATFORM_SELL_OFF");y===200&&T?(S.ElMessage.success("更新成功"),E(),i.value=!1):S.ElMessage.error("更新失败")}catch{return}}})},Re=()=>{s.value={violationType:[],violationExplain:"",violationEvidence:"",rummager:l},d.value=[],i.value=!1},A=e.ref(!1),I=e.ref({rummager:"",violationType:"",violationExplain:"",violationEvidence:[],examineDateTime:""}),qe=async a=>{await B.doPostSupplierRestoreSale({shopId:a.shopId,productId:a.id}),E()},Fe=async(a={})=>{var y;const t=ne.cloneDeep(a);t.violationEvidence=t!=null&&t.violationEvidence?t.violationEvidence.split(","):[];const c=[];(y=t==null?void 0:t.violationType)==null||y.forEach(T=>{c.push(n[T])}),t.violationType=c.join(","),I.value=t,A.value=!0},Me=a=>{r.pageSize=a,E()},$e=a=>{r.pageNum=a,E()},Ae=a=>{x.value=a,E()};async function E(){let a={...x.value,current:r.pageNum,size:r.pageSize},t=200,c={records:[],total:0};const{code:y,data:T,success:D}=await B.doGetSupplierList({...a,supplierProductStatus:N.value});t=y,c=T,t===200?(m.value=c.records,console.log(m.value,"tableList.value"),r.total=c.total):S.ElMessage.error("获取商品列表失败")}const Ue=()=>{E()},Y=a=>{g.value=[a],b.value=!0},ze=a=>{const t=(a==null?void 0:a.salePrices)||[],c=Math.max(...t),y=Math.min(...t);return c===y?C(y).toFixed(2):`${C(y).toFixed(2)}~${C(c).toFixed(2)}`},je=()=>{const a=["SALE_PRICE_ASC","SALE_PRICE_DESC",""],c=(a.indexOf(x.value.sort)+1)%a.length;x.value.sort=a[c],E()};return(a,t)=>{const c=e.resolveComponent("el-tab-pane"),y=e.resolveComponent("el-tabs"),T=e.resolveComponent("el-tooltip"),D=e.resolveComponent("el-link"),U=e.resolveComponent("el-dialog"),Ge=e.resolveComponent("el-checkbox-button"),He=e.resolveComponent("el-checkbox-group"),z=e.resolveComponent("el-form-item"),We=e.resolveComponent("el-input"),Ke=e.resolveComponent("el-icon"),Xe=e.resolveComponent("el-form"),J=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(re,{ref_key:"commoditySearchRef",ref:R,"tabs-active":N.value,onGetSearchParams:Ae},null,8,["tabs-active"]),e.createElementVNode("div",Ce,[e.createVNode(y,{modelValue:N.value,"onUpdate:modelValue":t[0]||(t[0]=o=>N.value=o),class:"tabs",onTabChange:Ue},{default:e.withCtx(()=>[e.createVNode(c,{label:"全部",name:""}),e.createVNode(c,{label:"已上架",name:"SELL_ON"}),e.createVNode(c,{label:"已下架",name:"SELL_OFF"}),e.createVNode(c,{label:"违规下架",name:"PLATFORM_SELL_OFF"})]),_:1},8,["modelValue"])]),e.createVNode(e.unref(v),{ref_key:"multipleTableRef",ref:u,"checked-item":g.value,"onUpdate:checkedItem":t[1]||(t[1]=o=>g.value=o),data:m.value,style:{"margin-top":"10px"},"no-border":"",class:"q-table",onChangeSort:je},{noData:e.withCtx(()=>[e.createVNode(ie)]),default:e.withCtx(()=>[e.createVNode(w,{label:"商品",prop:"goodInfo",align:"left",width:"350"},{default:e.withCtx(({row:o})=>[e.createVNode(ge,{info:o,style:{height:"83px"}},null,8,["info"])]),_:1}),e.createVNode(w,{label:"销售价",align:"left",width:"180"},{default:e.withCtx(({row:o})=>[e.createElementVNode("div",Ne,"￥"+e.toDisplayString(ze(o)),1)]),_:1}),e.createVNode(w,{label:"所属供应商",align:"left",width:"180"},{default:e.withCtx(({row:o})=>{var f;return[((f=o.supplierName)==null?void 0:f.length)>=10?(e.openBlock(),e.createBlock(T,{key:0,class:"box-item",effect:"dark",content:o.supplierName,placement:"bottom"},{default:e.withCtx(()=>[e.createElementVNode("span",{title:o.supplierName,class:"shop-name"},e.toDisplayString(o.supplierName),9,Ve)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",{key:1,title:o.supplierName,class:"shop-name"},e.toDisplayString(o.supplierName),9,ue))]}),_:1}),e.createVNode(w,{label:"销售方式",prop:"sellType",align:"left",width:"170"},{default:e.withCtx(({row:o})=>[e.createTextVNode(e.toDisplayString(X[o==null?void 0:o.sellType]),1)]),_:1}),e.createVNode(w,{label:"商品状态",prop:"status",align:"left",width:"110"},{default:e.withCtx(({row:o})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:(o==null?void 0:o.status)==="PLATFORM_SELL_OFF"?"red":""})},e.toDisplayString(e.unref(oe.usePlatformGoodStatus)(o==null?void 0:o.status)),5)]),_:1}),e.createVNode(w,{label:"操作",align:"right",width:"200",fixed:"right"},{default:e.withCtx(({row:o})=>[(o==null?void 0:o.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createElementBlock("div",be,[e.createVNode(D,{underline:!1,type:"primary",round:"",onClick:f=>Y(o)},{default:e.withCtx(()=>t[10]||(t[10]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),e.createVNode(D,{underline:!1,type:"primary",round:"",style:{"margin-left":"15px"},onClick:f=>qe(o)},{default:e.withCtx(()=>t[11]||(t[11]=[e.createTextVNode(" 恢复销售 ")])),_:2},1032,["onClick"]),e.createVNode(D,{underline:!1,type:"primary",round:"",style:{"margin-left":"15px"},onClick:f=>{var k;return Fe((k=o==null?void 0:o.extra)==null?void 0:k.productViolation)}},{default:e.withCtx(()=>t[12]||(t[12]=[e.createTextVNode(" 违规原因 ")])),_:2},1032,["onClick"])])):(e.openBlock(),e.createElementBlock("div",Ee,[e.createVNode(D,{underline:!1,type:"primary",round:"",onClick:f=>Y(o)},{default:e.withCtx(()=>t[13]||(t[13]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),e.createVNode(D,{underline:!1,type:"danger",round:"",style:{"margin-left":"15px"},onClick:f=>Oe("PLATFORM_SELL_OFF",o)},{default:e.withCtx(()=>t[14]||(t[14]=[e.createTextVNode(" 违规下架 ")])),_:2},1032,["onClick"])]))]),_:1})]),_:1},8,["checked-item","data"]),e.createVNode(ee,{class:"pagination","page-size":r.pageSize,"page-num":r.pageNum,total:r.total,onHandleSizeChange:Me,onHandleCurrentChange:$e},null,8,["page-size","page-num","total"]),e.createVNode(U,{modelValue:b.value,"onUpdate:modelValue":t[2]||(t[2]=o=>b.value=o),title:"商品详情",width:"60%",top:"5vh","destroy-on-close":"",onClose:t[3]||(t[3]=o=>b.value=!1)},{default:e.withCtx(()=>{var o,f,k,Z;return[e.createVNode(ye,{"commodity-id":(f=(o=g.value)==null?void 0:o[0])==null?void 0:f.id,"shop-id":(Z=(k=g.value)==null?void 0:k[0])==null?void 0:Z.shopId},null,8,["commodity-id","shop-id"])]}),_:1},8,["modelValue"]),e.createVNode(U,{modelValue:i.value,"onUpdate:modelValue":t[7]||(t[7]=o=>i.value=o),title:"违规下架",width:"650px",center:"","destroy-on-close":"",onClose:Re},{footer:e.withCtx(()=>[e.createElementVNode("span",Se,[e.createVNode(J,{onClick:t[6]||(t[6]=o=>i.value=!1)},{default:e.withCtx(()=>t[16]||(t[16]=[e.createTextVNode("取消")])),_:1}),e.createVNode(J,{type:"primary",onClick:Be},{default:e.withCtx(()=>t[17]||(t[17]=[e.createTextVNode(" 确定 ")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(Xe,{ref_key:"formSellOffRef",ref:P,model:s.value,"label-width":"120px",rules:h},{default:e.withCtx(()=>[e.createVNode(z,{label:"类型（多选）",prop:"violationType",class:"first-rules"},{default:e.withCtx(()=>[e.createVNode(He,{modelValue:s.value.violationType,"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.violationType=o)},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(n,(o,f)=>e.createVNode(Ge,{key:f,label:f},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(o),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(z,{label:"违规说明",prop:"violationExplain"},{default:e.withCtx(()=>[e.createVNode(We,{modelValue:s.value.violationExplain,"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.violationExplain=o),style:{width:"450px","margin-left":"28px"},maxlength:50,placeholder:"只限50字"},null,8,["modelValue"])]),_:1}),e.createVNode(z,{label:"相关证据",prop:"violationEvidence"},{default:e.withCtx(()=>[e.createElementVNode("div",Te,[t[15]||(t[15]=e.createElementVNode("div",{class:"notification"},"最多5张图片(jpg、gif、jpeg、png、webp、bmp)，单个图片1MB以内",-1)),e.createElementVNode("div",ke,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,(o,f)=>(e.openBlock(),e.createElementBlock("div",{key:f,style:{position:"relative"}},[e.createVNode(G,{src:d.value[f],"onUpdate:src":k=>d.value[f]=k,width:80,height:80,format:{size:1,types:["image/png","image/jpg","image/jpeg","image/gif","image/webp","image/bmp"],width:80,height:80,isBeyondLimit:!0},"is-cropper":!1},null,8,["src","onUpdate:src"]),o?(e.openBlock(),e.createBlock(Ke,{key:0,style:{position:"absolute",right:"-5px",top:"-5px",background:"#fff","border-radius":"50%"},color:"#7f7f7f",size:"20px",onClick:k=>F(f)},{default:e.withCtx(()=>[e.createVNode(e.unref(le.CircleClose))]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]))),128)),e.withDirectives(e.createVNode(G,{width:80,height:80,format:{size:1,types:["image/png","image/jpg","image/jpeg","image/gif","image/webp","image/bmp"],width:80,height:80,isBeyondLimit:!0},"is-cropper":!1,onChange:M},null,512),[[e.vShow,d.value.length<5]])])])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e.createVNode(U,{modelValue:A.value,"onUpdate:modelValue":t[8]||(t[8]=o=>A.value=o),title:"违规原因",width:"500px",center:"",onClose:t[9]||(t[9]=o=>i.value=!1)},{default:e.withCtx(()=>[e.createElementVNode("div",Le,[e.createElementVNode("div",null,[t[18]||(t[18]=e.createElementVNode("p",{class:"explainData_label"},"检查员：",-1)),e.createTextVNode(" "+e.toDisplayString(I.value.rummager),1)]),e.createElementVNode("div",null,[t[19]||(t[19]=e.createElementVNode("p",{class:"explainData_label"},"检查时间：",-1)),e.createTextVNode(" "+e.toDisplayString(I.value.examineDateTime),1)]),e.createElementVNode("div",null,[t[20]||(t[20]=e.createElementVNode("p",{class:"explainData_label"},"类型：",-1)),e.createTextVNode(" "+e.toDisplayString(I.value.violationType),1)]),e.createElementVNode("div",null,[t[21]||(t[21]=e.createElementVNode("p",{class:"explainData_label"},"违规说明：",-1)),e.createTextVNode(" "+e.toDisplayString(I.value.violationExplain),1)]),e.createElementVNode("div",we,[t[22]||(t[22]=e.createElementVNode("p",{class:"explainData_label"},"相关证据：",-1)),e.createElementVNode("div",Pe,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(I.value.violationEvidence,(o,f)=>(e.openBlock(),e.createElementBlock("img",{key:f,src:o,class:"violation-evidence"},null,8,Ie))),128))])])])]),_:1},8,["modelValue"])],64)}}}),Ze="";return K(De,[["__scopeId","data-v-61d81865"]])});
