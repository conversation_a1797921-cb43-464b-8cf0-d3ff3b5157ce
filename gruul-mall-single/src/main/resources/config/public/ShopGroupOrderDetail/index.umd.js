(function(e,c){typeof exports=="object"&&typeof module<"u"?module.exports=c(require("vue"),require("@/utils/http"),require("decimal.js"),require("@/components/PageManage.vue"),require("element-plus"),require("@/composables/useConvert"),require("vue-router")):typeof define=="function"&&define.amd?define(["vue","@/utils/http","decimal.js","@/components/PageManage.vue","element-plus","@/composables/useConvert","vue-router"],c):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopGroupOrderDetail=c(e.ShopGroupOrderDetailContext.Vue,e.ShopGroupOrderDetailContext.UtilsHttp,e.ShopGroupOrderDetailContext.Decimal,e.ShopGroupOrderDetailContext.PageManageTwo,e.ShopGroupOrderDetailContext.ElementPlus,e.ShopGroupOrderDetailContext.UseConvert,e.ShopGroupOrderDetailContext.VueRouter))})(this,function(e,c,me,z,u,T,j){"use strict";var N=document.createElement("style");N.textContent=`@charset "UTF-8";.groupDetail__info[data-v-695398f7]{border:1px solid #d5d5d5;font-size:14px;color:#333;padding-top:25px}.groupDetail__info-row[data-v-695398f7]{display:flex;justify-content:flex-start;align-items:center;margin-bottom:30px}.groupDetail__info-row--label[data-v-695398f7]{width:104px;text-align:right}.groupDetail__info-row--right[data-v-695398f7]{display:flex;justify-content:center;align-items:center;margin-left:11px}.mg[data-v-695398f7]{margin-left:10px}.ellipsis[data-v-695398f7]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}
`,document.head.appendChild(N);const q=o=>c.http.get({url:`addon-team/team/activity/order/summary?teamNo=${o}`}),I=(o,i)=>c.http.get({url:"addon-team/team/activity/order/users",params:{teamNo:o,size:i.size,current:i.current}}),n=o=>(e.pushScopeId("data-v-695398f7"),o=o(),e.popScopeId(),o),U={class:"groupDetail"},k={class:"groupDetail__info"},M={class:"groupDetail__info-row"},H=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"开团人:",-1)),P={class:"groupDetail__info-row--right"},A={class:"mg"},B={class:"groupDetail__info-row"},F=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"活动名称:",-1)),L={class:"groupDetail__info-row--right"},R={class:"groupDetail__info-row"},$=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"开团时间:",-1)),J={class:"groupDetail__info-row--right"},K={class:"groupDetail__info-row"},Q=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"参团人数:",-1)),W={class:"groupDetail__info-row--right"},X={class:"groupDetail__info-row"},Y=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"拼团商品:",-1)),Z={class:"groupDetail__info-row--right"},v={class:"mg"},ee={class:"groupDetail__info-row"},te=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"购买件数:",-1)),oe={class:"groupDetail__info-row--right"},re={class:"groupDetail__info-row"},ae=n(()=>e.createElementVNode("div",{class:"groupDetail__info-row--label"},"拼团状态:",-1)),ne={class:"groupDetail__info-row--right"},ie={class:"ellipsis"},le=e.defineComponent({__name:"ShopGroupOrderDetail",setup(o){const{divTenThousand:i}=T(),_=j.useRoute(),t=e.ref(),m=e.ref([]),l=e.reactive({current:1,size:10,total:0});ce(),f();const se=r=>({SUCCESS:"拼团成功",FAIL:"拼团失败",ING:"拼团中"})[r];async function ce(){const r=_.query.teamNo,{code:h,data:s}=await q(r);if(h!==200)return u.ElMessage.error("获取拼团信息失败");t.value=s}async function f(){const r=_.query.teamNo,{total:h,...s}=l,{code:g,data:p}=await I(r,s);if(g!==200)return u.ElMessage.error("获取拼团用户信息失败");m.value=p.records,l.total=p.total}const de=r=>{l.size=r,f()},_e=r=>{l.current=r,f()};return(r,h)=>{var D,w,V,x,y,C,S,E,O,b,G;const s=e.resolveComponent("el-image"),g=e.resolveComponent("el-col"),p=e.resolveComponent("el-row"),d=e.resolveComponent("el-table-column"),pe=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",U,[e.createElementVNode("div",k,[e.createElementVNode("div",M,[H,e.createElementVNode("div",P,[e.createVNode(s,{src:(D=t.value)==null?void 0:D.commanderAvatar,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("span",A,e.toDisplayString((w=t.value)==null?void 0:w.commanderNickname),1)])]),e.createElementVNode("div",B,[F,e.createElementVNode("div",L,e.toDisplayString((V=t.value)==null?void 0:V.name),1)]),e.createElementVNode("div",R,[$,e.createElementVNode("div",J,e.toDisplayString((x=t.value)==null?void 0:x.openTime),1)]),e.createElementVNode("div",K,[Q,e.createElementVNode("div",W,e.toDisplayString((y=t.value)==null?void 0:y.currentNum)+"/"+e.toDisplayString((C=t.value)==null?void 0:C.totalNum),1)]),e.createElementVNode("div",X,[Y,e.createElementVNode("div",Z,[e.createVNode(s,{src:(S=t.value)==null?void 0:S.productImage,style:{width:"36px",height:"36px"}},null,8,["src"]),e.createElementVNode("span",v,e.toDisplayString((E=t.value)==null?void 0:E.productName),1)])]),e.createElementVNode("div",ee,[te,e.createElementVNode("div",oe,e.toDisplayString((O=t.value)==null?void 0:O.buyNum),1)]),e.createElementVNode("div",re,[ae,e.createElementVNode("div",ne,e.toDisplayString(((b=t.value)==null?void 0:b.status)&&se((G=t.value)==null?void 0:G.status)),1)])]),e.createVNode(pe,{data:m.value,width:"100%"},{default:e.withCtx(()=>[e.createVNode(d,{label:"拼团人信息",width:"200"},{default:e.withCtx(({row:a})=>[e.createVNode(p,{justify:"center",align:"middle"},{default:e.withCtx(()=>[e.createVNode(g,{span:6},{default:e.withCtx(()=>[e.createVNode(s,{src:a.avatar,style:{width:"36px",height:"36px"}},null,8,["src"])]),_:2},1024),e.createVNode(g,{span:18},{default:e.withCtx(()=>[e.createElementVNode("div",ie,e.toDisplayString(a.nickname),1)]),_:2},1024)]),_:2},1024)]),_:1}),e.createVNode(d,{label:"拼团价",align:"center"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(e.unref(i)(a.price)),1)]),_:1}),e.createVNode(d,{label:"实付金额",align:"center"},{default:e.withCtx(({row:a})=>[e.createTextVNode(e.toDisplayString(e.unref(i)(a.amount)),1)]),_:1}),e.createVNode(d,{label:"团员身份",align:"center"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,e.toDisplayString(a.commander?"团长":"团员"),1)]),_:1}),e.createVNode(d,{label:"创建时间",prop:"createTime",width:"180",align:"center"}),e.createVNode(d,{label:"订单号",align:"center",width:"220",fixed:"right"},{default:e.withCtx(({row:a})=>[e.createElementVNode("div",null,e.toDisplayString(a.orderNo),1)]),_:1})]),_:1},8,["data"]),e.createVNode(p,{justify:"end",align:"middle"},{default:e.withCtx(()=>[e.createVNode(z,{"page-size":l.size,"page-num":l.current,total:l.total,onHandleSizeChange:de,onHandleCurrentChange:_e},null,8,["page-size","page-num","total"])]),_:1})])}}}),he="";return((o,i)=>{const _=o.__vccOpts||o;for(const[t,m]of i)_[t]=m;return _})(le,[["__scopeId","data-v-695398f7"]])});
