(function(e,S){typeof exports=="object"&&typeof module<"u"?module.exports=S(require("vue"),require("vue-router"),require("decimal.js"),require("@/components/q-address/q-address.vue"),require("element-plus"),require("vue-clipboard3"),require("element-china-area-data"),require("@/components/q-address"),require("@/apis/http"),require("@/utils/date"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","vue-router","decimal.js","@/components/q-address/q-address.vue","element-plus","vue-clipboard3","element-china-area-data","@/components/q-address","@/apis/http","@/utils/date","@/composables/useConvert"],S):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformPurchaseInfo=S(e.PlatformPurchaseInfoContext.Vue,e.PlatformPurchaseInfoContext.VueRouter,e.PlatformPurchaseInfoContext.Decimal,e.PlatformPurchaseInfoContext.QAddress,e.PlatformPurchaseInfoContext.ElementPlus,e.PlatformPurchaseInfoContext.VueClipboard3,e.PlatformPurchaseInfoContext.ElementChinaAreaData,e.PlatformPurchaseInfoContext.QAddressIndex,e.PlatformPurchaseInfoContext.Request,e.PlatformPurchaseInfoContext.DateUtil,e.PlatformPurchaseInfoContext.UseConvert))})(this,function(e,S,P,ye,k,he,Ne,ue,H,Ve,Ee){"use strict";var Y=document.createElement("style");Y.textContent=`@charset "UTF-8";.details[data-v-048de468]{overflow:auto}.details__status[data-v-048de468]{display:flex;align-items:center}.details__status--steps[data-v-048de468]{padding:20px;margin:20px;width:700px;border-left:1px solid #d5d5d5}.details__status--title[data-v-048de468]{font-size:28px;font-weight:700;color:#515151;width:223px;text-align:center}.details__userInfo[data-v-048de468]{display:flex;margin-bottom:22px;padding:0 30px}.details__userInfo--left[data-v-048de468]{flex:.5}.details__userInfo--left div[data-v-048de468]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--right[data-v-048de468]{margin-left:30px;flex:.5}.details__userInfo--right div[data-v-048de468]:nth-of-type(n+2){margin-bottom:11px}.details__userInfo--title[data-v-048de468]{font-size:14px;color:#333;font-weight:700;margin:17px 0;display:flex;justify-content:space-between;align-items:center}.details__table--main[data-v-048de468]{height:300px}.details__table--main .commodity[data-v-048de468]{display:flex;align-items:stretch}.details__table--main .commodity .commodity__info[data-v-048de468]{margin-left:15px;flex:1;overflow:hidden;display:flex;flex-direction:column;justify-content:flex-start;align-items:flex-start;text-align:left}.details__table--main .commodity .commodity__info--title[data-v-048de468]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:100%}.details__table--main .commodity .commodity__info--spec[data-v-048de468]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-size:.8em;width:100%}.details__subtotal[data-v-048de468]{display:flex;flex-direction:column;align-items:flex-end;width:100%;margin-top:30px;line-height:1.5}.details__subtotal--title[data-v-048de468]{font-size:1.3em;font-weight:600}.details__subtotal .pay-price[data-v-048de468]{font-size:1.2em}.details .text-red[data-v-048de468]{color:red}.proof-img[data-v-048de468]{width:350px;height:350px;object-fit:contain}.copy[data-v-048de468]{margin-left:5px;color:#409eff;cursor:pointer}.content_container[data-v-e393f44e]{overflow-y:auto}.logisticsInfo[data-v-e393f44e]{display:flex;justify-content:space-between}.logisticsInfo__left[data-v-e393f44e]{flex:3}.logisticsInfo__right[data-v-e393f44e]{flex:2}.logisticsInfo__timeline-con[data-v-e393f44e]{max-height:500px;overflow:auto}.logisticsInfo__title[data-v-e393f44e]{font-size:14px;color:#333;font-weight:700;margin:17px 0}.logisticsInfo__text[data-v-e393f44e]{margin-bottom:11px}.logisticsInfo__timeline[data-v-e393f44e]{margin-top:20px}.logisticsInfo__timeline li[data-v-e393f44e]:nth-child(1) .el-timeline-item__timestamp{color:#000}.logisticsInfo__timeline--status[data-v-e393f44e]{font-size:13px;font-weight:700;margin-right:10px;color:#838383}.logisticsInfo__timeline--time[data-v-e393f44e]{color:#838383}.logisticsInfo__divider[data-v-e393f44e]{height:4px;margin:0 -15px;background:#f2f2f2}
`,document.head.appendChild(Y);const Ie=n=>H.get({url:`addon-supplier/supplier/order/delivery/${n}`}),Ce=n=>H.get({url:`addon-supplier/supplier/order/${n}`}),be=(n,c)=>H.get({url:"gruul-mall-freight/logistics/node",params:{companyCode:n,waybillNo:c}}),Te={class:"tab_container"},Se=e.defineComponent({__name:"PlatformPurchaseInfo",setup(n){const c=e.ref("basic"),t={basic:e.defineAsyncComponent(()=>Promise.resolve().then(()=>st)),delivery:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Nt))},l=e.ref(0),r=S.useRoute(),m=e.ref();async function _(){if(r.query.orderNo){const{data:i}=await Ce(r.query.orderNo);m.value=i,l.value=Date.now()}}const g=e.computed(()=>{var i,o;return(o=(i=m==null?void 0:m.value)==null?void 0:i.orderItems)==null?void 0:o.filter(p=>p.packageStatus!=="WAITING_FOR_DELIVER").length});return _(),(i,o)=>{const p=e.resolveComponent("el-tab-pane"),x=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Te,[e.createVNode(x,{modelValue:c.value,"onUpdate:modelValue":o[0]||(o[0]=N=>c.value=N)},{default:e.withCtx(()=>[e.createVNode(p,{label:"订单信息",name:"basic"}),g.value&&g.value>0?(e.openBlock(),e.createBlock(p,{key:0,label:"物流信息",name:"delivery"})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"])]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(t[c.value]),{key:l.value,order:m.value,reload:_},null,8,["order"]))],64)}}}),{divTenThousand:Q}=Ee(),{toClipboard:J}=he();new Ve;const Pe={待支付:1,待审核:2,待发货:2,部分发货:2,待入库:3,已完成:3,已关闭:1},ke={OFFLINE:"线下支付",BALANCE:"余额支付"},De=(n,c)=>{const t=e.ref(),l=e.ref(null);t.value=n;const r=e.reactive({statusText:"",activeStep:0}),m=r.statusText=$e(n);r.activeStep=Pe[m];const _=p=>{J(p).then(()=>{k.ElMessage.success("复制成功")}).catch(()=>k.ElMessage.error("复制失败"))},g=()=>{var x,N,$,a,h,D,O,y,C,b,u,V,E;const p=`
            收货人姓名：${($=(N=(x=t==null?void 0:t.value)==null?void 0:x.extra)==null?void 0:N.receiver)==null?void 0:$.name}

            联系人电话：${(D=(h=(a=t==null?void 0:t.value)==null?void 0:a.extra)==null?void 0:h.receiver)==null?void 0:D.mobile}

            收货地址：${ue.AddressFn(Ne.regionData,((y=(O=t==null?void 0:t.value)==null?void 0:O.extra)==null?void 0:y.receiver.areaCode)||[])}${(u=(b=(C=t==null?void 0:t.value)==null?void 0:C.extra)==null?void 0:b.receiver)==null?void 0:u.address}

            采购备注：${(E=(V=t==null?void 0:t.value)==null?void 0:V.extra)==null?void 0:E.remark}
        `;J(p).then(()=>k.ElMessage.success("复制成功")).catch(()=>k.ElMessage.error("复制失败"))},i=e.computed(()=>p=>Oe(p)),o=e.computed(()=>p=>Be(p));return{orderDetails:t,stepInfo:r,payTypeMap:ke,divTenThousand:Q,computedCalculateFreight:i,computedCalculateCommodityPrice:o,payOrderRef:l,copyOrderNo:_,handleCopyReceiver:g}},Oe=(n=[])=>n.reduce((c,t)=>c.plus(new P(Q(t.freightPrice))),new P(0)),Be=(n=[])=>n.reduce((c,t)=>c.plus(new P(Q(t.salePrice).mul(new P(t.num)))),new P(0)),$e=n=>{if(!n)return"";if(n.status==="UNPAID")return"待支付";if(n.status==="PAYMENT_AUDIT")return"待审核";const c=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],t={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(n.status==="PAID"){let l="COMPLETED";for(const m of n.orderItems){const _=c.findIndex(g=>g===l);if(m.packageStatus==="WAITING_FOR_DELIVER"&&_>0){l="WAITING_FOR_DELIVER";continue}m.packageStatus==="WAITING_FOR_RECEIVE"&&_>1&&(l="WAITING_FOR_RECEIVE")}let r=t[l];return r==="待发货"&&n.orderItems.find(_=>_.packageStatus!=="WAITING_FOR_DELIVER")&&(r="部分发货"),r}return"已关闭"},Ae=()=>{const n=e.ref(!1),c=e.ref("");return{showProof:n,goToShowProof:l=>{var r,m;c.value=((m=(r=l==null?void 0:l.extra)==null?void 0:r.pay)==null?void 0:m.proof)||"",n.value=!0},currentProof:c}},Fe={class:"details content_container"},Re={class:"details__status"},qe={class:"details__status--title"},we={class:"details__status--steps"},Me={class:"details__userInfo"},je={class:"details__userInfo--left"},Le={class:"details__userInfo--left"},We={class:"details__userInfo--title"},ze={class:"details__userInfo--right"},Ge={class:"details__table"},Ue={class:"commodity"},He={class:"commodity__info"},Qe={class:"commodity__info--title"},Ye={class:"commodity__info--spec"},Je={class:"details__subtotal"},Ke={class:"details__subtotal--line"},Xe={class:"details__subtotal--line"},Ze={class:"text-red"},ve={class:"details__subtotal--line"},et={class:"text-red"},tt={class:"details__subtotal--line pay-price"},ot={class:"text-red"},nt=["src"],at=e.defineComponent({__name:"basic",props:{order:{type:Object,default:()=>({})},reload:{type:Function,default:()=>({})}},setup(n){const c=n,{orderDetails:t,stepInfo:l,payTypeMap:r,divTenThousand:m,computedCalculateFreight:_,computedCalculateCommodityPrice:g,copyOrderNo:i,handleCopyReceiver:o}=De(c.order,c.reload),{showProof:p,goToShowProof:x,currentProof:N}=Ae();return($,a)=>{var u,V,E,A,F,R,q,I,B,w,M,j,L,W,z,d,T,X,Z,v,ee,te,oe,ne,ae,se,le,ie,re,de,ce,me,pe,_e;const h=e.resolveComponent("el-step"),D=e.resolveComponent("el-steps"),O=e.resolveComponent("el-image"),y=e.resolveComponent("el-table-column"),C=e.resolveComponent("el-table"),b=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",Fe,[e.createElementVNode("div",Re,[e.createElementVNode("div",qe,[e.createElementVNode("p",null,e.toDisplayString(e.unref(l).statusText),1)]),e.createElementVNode("div",we,[e.createVNode(D,{active:e.unref(l).activeStep,"align-center":"","finish-status":"finish"},{default:e.withCtx(()=>{var s,f,G,U,fe,ge,xe;return[e.createVNode(h,{description:(s=e.unref(t))==null?void 0:s.createTime,title:"买家下单"},null,8,["description"]),e.createVNode(h,{description:(G=(f=e.unref(t))==null?void 0:f.timeNodes)==null?void 0:G.payTime,title:"买家付款"},null,8,["description"]),e.createVNode(h,{description:(fe=(U=e.unref(t))==null?void 0:U.timeNodes)==null?void 0:fe.deliveryTime,title:"卖家发货"},null,8,["description"]),e.createVNode(h,{description:(xe=(ge=e.unref(t))==null?void 0:ge.timeNodes)==null?void 0:xe.receiveTime,title:"买家收货"},null,8,["description"])]}),_:1},8,["active"])])]),e.createElementVNode("div",Me,[e.createElementVNode("div",je,[a[4]||(a[4]=e.createElementVNode("div",{class:"details__userInfo--title"},"订单信息",-1)),e.createElementVNode("div",null,[e.createElementVNode("span",null,"订单编号："+e.toDisplayString((u=e.unref(t))==null?void 0:u.no),1),e.createElementVNode("span",{class:"copy",onClick:a[0]||(a[0]=s=>{var f;return e.unref(i)(((f=e.unref(t))==null?void 0:f.no)||"")})},"复制")]),e.createElementVNode("div",null,"下单时间："+e.toDisplayString((V=e.unref(t))==null?void 0:V.createTime),1),e.createElementVNode("div",null,"支付时间："+e.toDisplayString((A=(E=e.unref(t))==null?void 0:E.timeNodes)==null?void 0:A.payTime),1),e.createElementVNode("div",null,[e.createElementVNode("span",null,"支付方式："+e.toDisplayString(e.unref(r)[(q=(R=(F=e.unref(t))==null?void 0:F.extra)==null?void 0:R.pay)==null?void 0:q.payType]),1),((w=(B=(I=e.unref(t))==null?void 0:I.extra)==null?void 0:B.pay)==null?void 0:w.payType)==="OFFLINE"?(e.openBlock(),e.createElementBlock("span",{key:0,class:"copy",onClick:a[1]||(a[1]=s=>{var f,G,U;return e.unref(x)({extra:{pay:{proof:((U=(G=(f=e.unref(t))==null?void 0:f.extra)==null?void 0:G.pay)==null?void 0:U.proof)||""}}})})},"付款凭证")):e.createCommentVNode("",!0)]),a[5]||(a[5]=e.createElementVNode("div",null,"配送方式：快递配送",-1))]),e.createElementVNode("div",Le,[e.createElementVNode("div",We,[a[6]||(a[6]=e.createElementVNode("span",null,"收货人信息",-1)),e.createElementVNode("span",{class:"copy",onClick:a[2]||(a[2]=(...s)=>e.unref(o)&&e.unref(o)(...s))},"复制")]),e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString((L=(j=(M=e.unref(t))==null?void 0:M.extra)==null?void 0:j.receiver)==null?void 0:L.name),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((d=(z=(W=e.unref(t))==null?void 0:W.extra)==null?void 0:z.receiver)==null?void 0:d.mobile),1),e.createElementVNode("div",null,[a[7]||(a[7]=e.createTextVNode(" 收货地址： ")),e.createVNode(ye,{address:(X=(T=e.unref(t))==null?void 0:T.extra)==null?void 0:X.receiver.areaCode},null,8,["address"]),e.createTextVNode(" "+e.toDisplayString((ee=(v=(Z=e.unref(t))==null?void 0:Z.extra)==null?void 0:v.receiver)==null?void 0:ee.address),1)]),e.createElementVNode("div",null,"采购备注："+e.toDisplayString((oe=(te=e.unref(t))==null?void 0:te.extra)==null?void 0:oe.remark),1)]),e.createElementVNode("div",ze,[a[8]||(a[8]=e.createElementVNode("div",{class:"details__userInfo--title"},"供应商信息",-1)),e.createElementVNode("div",null,"供应商名称："+e.toDisplayString((ae=(ne=e.unref(t))==null?void 0:ne.extraInfo)==null?void 0:ae.supplierName),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((le=(se=e.unref(t))==null?void 0:se.extraInfo)==null?void 0:le.supplierPhone),1)])]),e.createElementVNode("div",Ge,[e.createVNode(C,{data:(ie=e.unref(t))==null?void 0:ie.orderItems,border:"",class:"details__table--main"},{default:e.withCtx(()=>[e.createVNode(y,{align:"center",label:"商品",width:"460"},{default:e.withCtx(({row:s})=>{var f;return[e.createElementVNode("div",Ue,[e.createVNode(O,{src:s==null?void 0:s.image,fit:"cover",style:{width:"70px",height:"70px"}},null,8,["src"]),e.createElementVNode("div",He,[e.createElementVNode("span",Qe,e.toDisplayString(s==null?void 0:s.productName),1),e.createElementVNode("span",Ye,e.toDisplayString((f=s==null?void 0:s.specs)==null?void 0:f.join(";")),1)])])]}),_:1}),e.createVNode(y,{align:"center",label:"采购单价"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(m)(s==null?void 0:s.salePrice)),1)]),_:1}),e.createVNode(y,{align:"center",label:"采购数量",prop:"num"}),e.createVNode(y,{align:"center",label:"采购金额"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(m)(s==null?void 0:s.salePrice).mul(new(e.unref(P))(s.num))),1)]),_:1}),e.createVNode(y,{label:"实际入库量",prop:"used",align:"center"})]),_:1},8,["data"])]),e.createElementVNode("div",Je,[a[11]||(a[11]=e.createElementVNode("div",{class:"details__subtotal--title"},"订单总计",-1)),e.createElementVNode("div",Ke,"采购总数："+e.toDisplayString((de=(re=e.unref(t))==null?void 0:re.orderItems)==null?void 0:de.reduce((s,f)=>s+f.num,0)),1),e.createElementVNode("div",Xe,[a[9]||(a[9]=e.createTextVNode(" 商品总价：")),e.createElementVNode("span",Ze,e.toDisplayString(e.unref(g)((ce=e.unref(t))==null?void 0:ce.orderItems)),1)]),e.createElementVNode("div",ve,[a[10]||(a[10]=e.createTextVNode(" 运费：")),e.createElementVNode("span",et,e.toDisplayString(e.unref(_)((me=e.unref(t))==null?void 0:me.orderItems)),1)]),e.createElementVNode("div",tt,[e.createTextVNode(" 采购金额("+e.toDisplayString(((pe=e.unref(t))==null?void 0:pe.status)==="PAID"?"已付款":"应付款")+")：",1),e.createElementVNode("span",ot,e.toDisplayString(e.unref(m)((_e=e.unref(t))==null?void 0:_e.payAmount))+"元",1)])])]),e.createVNode(b,{modelValue:e.unref(p),"onUpdate:modelValue":a[3]||(a[3]=s=>e.isRef(p)?p.value=s:null),title:"付款凭证",width:"500px"},{default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(N),class:"proof-img"},null,8,nt)]),_:1},8,["modelValue"])],64)}}}),ut="",K=(n,c)=>{const t=n.__vccOpts||n;for(const[l,r]of c)t[l]=r;return t},st=Object.freeze(Object.defineProperty({__proto__:null,default:K(at,[["__scopeId","data-v-048de468"]])},Symbol.toStringTag,{value:"Module"})),lt={class:"content_container"},it={style:{"padding-left":"30px","margin-bottom":"30px"}},rt={class:"logisticsInfo"},dt={class:"logisticsInfo__left"},ct={style:{display:"flex","align-items":"center"}},mt={key:0,style:{"margin-top":"20px"}},pt={class:"logisticsInfo__right"},_t={key:0},ft={class:"logisticsInfo__text"},gt={class:"logisticsInfo__text"},xt={class:"logisticsInfo__text"},yt={key:1,style:{"text-align":"center",height:"100px","line-height":"100px"}},ht=e.defineComponent({__name:"delivery",props:{order:{type:Object,default(){return{}}}},setup(n){const c=S.useRoute(),t=e.ref([]),l=e.ref([]),r=e.ref(0);m();async function m(){const{code:i,data:o}=await Ie(c.query.orderNo);if(i!==200)return k.ElMessage.error("包裹详情获取失败");o.length&&(l.value=o,_())}const _=async()=>{const i=l.value[r.value];if(i.type!=="WITHOUT")try{const{data:o,code:p,msg:x}=await be(i.express.expressCompanyCode,i.express.expressNo);if(p!==200)return k.ElMessage.error("包裹详情获取失败");if(!o.data){g(o,i);return}t.value=o.data.reverse()}catch(o){t.value=[{status:"包裹异常",time:i==null?void 0:i.createTime,context:o.msg}]}};function g(i,o){switch(i.returnCode){case"401":t.value=[{status:"包裹异常",time:o.createTime,context:i.message}];break;case"400":t.value=[{status:"包裹异常",time:o.createTime,context:i.message}];break;default:t.value=[{status:"包裹异常",time:o.createTime,context:i.message}];break}}return(i,o)=>{var C,b,u,V,E,A,F,R,q;const p=e.resolveComponent("el-image"),x=e.resolveComponent("el-table-column"),N=e.resolveComponent("el-table"),$=e.resolveComponent("el-row"),a=e.resolveComponent("el-timeline-item"),h=e.resolveComponent("el-timeline"),D=e.resolveComponent("el-scrollbar"),O=e.resolveComponent("el-tab-pane"),y=e.resolveComponent("el-tabs");return e.openBlock(),e.createElementBlock("div",lt,[e.createElementVNode("div",it,[o[1]||(o[1]=e.createElementVNode("div",{style:{color:"#000","font-size":"16px","margin-left":"-10px","font-weight":"700","margin-bottom":"10px"}},"收货人信息",-1)),e.createElementVNode("div",null,"收货人姓名："+e.toDisplayString((u=(b=(C=l.value)==null?void 0:C[0])==null?void 0:b.receiver)==null?void 0:u.name),1),e.createElementVNode("div",null,"联系人电话："+e.toDisplayString((A=(E=(V=l.value)==null?void 0:V[0])==null?void 0:E.receiver)==null?void 0:A.mobile),1),e.createElementVNode("div",null,"收货地址："+e.toDisplayString((q=(R=(F=l.value)==null?void 0:F[0])==null?void 0:R.receiver)==null?void 0:q.address),1)]),e.createVNode(y,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=I=>r.value=I),type:"card",onTabChange:_},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,(I,B)=>(e.openBlock(),e.createBlock(O,{key:B,label:"包裹"+(B+1),name:B},{default:e.withCtx(()=>{var w,M,j,L,W,z;return[e.createElementVNode("div",rt,[e.createElementVNode("div",dt,[e.createVNode(N,{data:I.orderItems,border:"",style:{width:"500px"}},{default:e.withCtx(()=>[e.createVNode(x,{label:"商品 ",width:"400px"},{default:e.withCtx(({row:d})=>[e.createElementVNode("div",ct,[e.createVNode(p,{src:d==null?void 0:d.image,title:d==null?void 0:d.productName,fits:"cover",shape:"square",size:"large",style:{width:"70px",height:"70px","margin-right":"10px"}},null,8,["src","title"]),e.createElementVNode("div",null,[e.createElementVNode("div",null,e.toDisplayString(d==null?void 0:d.productName),1),e.createElementVNode("div",null,e.toDisplayString(d==null?void 0:d.specs),1)])])]),_:1}),e.createVNode(x,{align:"center",label:"发货数"},{default:e.withCtx(({row:d})=>[e.createTextVNode(e.toDisplayString(d==null?void 0:d.num),1)]),_:1})]),_:2},1032,["data"]),I.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",mt,[o[2]||(o[2]=e.createElementVNode("div",{class:"logisticsInfo__title"},"物流详情",-1)),e.createVNode(D,{ref_for:!0,ref:"scrollbarRef","max-height":"500px"},{default:e.withCtx(()=>[e.createVNode(h,{class:"logisticsInfo__timeline"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,(d,T)=>(e.openBlock(),e.createBlock(a,{key:T,color:T===0?"#409eff":" ",timestamp:`${d.context}`,class:"logisticsInfo__timeline--item",style:{"padding-bottom":"42px"}},{default:e.withCtx(()=>[e.createVNode($,null,{default:e.withCtx(()=>[e.createElementVNode("div",{style:e.normalizeStyle({color:T===0?"#409eff":" "}),class:"logisticsInfo__timeline--status"},e.toDisplayString(d.status),5),e.createElementVNode("div",{style:e.normalizeStyle({color:T===0?"#409eff":" "}),class:"logisticsInfo__timeline--time"},e.toDisplayString(d.time),5)]),_:2},1024)]),_:2},1032,["color","timestamp"]))),128))]),_:2},1024)]),_:2},1536)])):e.createCommentVNode("",!0)]),e.createElementVNode("div",pt,[o[3]||(o[3]=e.createElementVNode("div",{class:"logisticsInfo__title"},"物流信息",-1)),I.type!=="WITHOUT"?(e.openBlock(),e.createElementBlock("div",_t,[e.createElementVNode("div",ft,"收货地址："+e.toDisplayString((M=(w=l.value[r.value])==null?void 0:w.receiver)==null?void 0:M.address),1),e.createElementVNode("div",gt,"物流公司："+e.toDisplayString((L=(j=l.value[r.value])==null?void 0:j.express)==null?void 0:L.expressCompanyName),1),e.createElementVNode("div",xt,"物流单号："+e.toDisplayString((z=(W=l.value[r.value])==null?void 0:W.express)==null?void 0:z.expressNo),1)])):(e.openBlock(),e.createElementBlock("div",yt,"无需物流"))])])]}),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])}}}),Et="",Nt=Object.freeze(Object.defineProperty({__proto__:null,default:K(ht,[["__scopeId","data-v-e393f44e"]])},Symbol.toStringTag,{value:"Module"}));return Se});
