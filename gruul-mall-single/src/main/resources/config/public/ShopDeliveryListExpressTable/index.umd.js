(function(e,s){typeof exports=="object"&&typeof module<"u"?module.exports=s(require("vue"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/composables/useConvert")):typeof define=="function"&&define.amd?define(["vue","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/qszr-core/packages/q-table/QTable","@/composables/useConvert"],s):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopDeliveryListExpressTable=s(e.ShopDeliveryListExpressTableContext.Vue,e.ShopDeliveryListExpressTableContext.QTableColumn,e.ShopDeliveryListExpressTableContext.QTable,e.ShopDeliveryListExpressTableContext.UseConvert))})(this,function(e,s,b,g){"use strict";var _=document.createElement("style");_.textContent=`@charset "UTF-8";.orderIndex-table__img-box[data-v-c94e8ea8]{width:200px;display:flex;justify-content:space-between}.orderIndex-table__img[data-v-c94e8ea8]{flex-shrink:0;border-radius:5px;position:relative}.orderIndex-table__img-mask[data-v-c94e8ea8]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;color:#000}.is-complete[data-v-c94e8ea8]{background:#eef1f6}.header-table[data-v-c94e8ea8]{width:100%;display:flex;justify-content:space-between;align-items:center}.money_text[data-v-c94e8ea8]{font-size:12px;color:#000}
`,document.head.appendChild(_);const C={style:{"margin-right":"36px"}},N={class:"orderIndex-table__img-box"},V={slot:"content"},k={class:"orderIndex-table__img-mask"},u={style:{color:"#838383","font-size":"10px"}},E={class:"avatar_text avatar_text__bottom money_text"},T={style:{color:"#2e99f3","margin-right":"10px"}},S={style:{padding:"0 10px 0"},class:"money_text"},D=e.defineComponent({__name:"ShopDeliveryListExpressTable",props:{properties:{type:Object,default:{}}},setup(c){const a=c,{divTenThousand:i}=g(),p=e.computed(()=>n=>i(n.shopOrders[0].shopOrderItems[0].dealPrice)),d=e.computed(()=>n=>n.shopOrders[0].shopOrderItems.reduce((l,r)=>r.num+l,0)),x=n=>{const l=n.shopOrders[0].orderReceiver;return l||n.orderReceiver},h=(n,l,r="select")=>{a.properties.changeExpressCompanyName(n,l,r)},m=e.ref(!1),q=async()=>{m.value=!0,await e.nextTick(),m.value=!1};return(n,l)=>{const r=e.resolveComponent("el-button"),y=e.resolveComponent("el-row"),L=e.resolveComponent("el-image"),w=e.resolveComponent("el-tooltip"),B=e.resolveComponent("el-option"),O=e.resolveComponent("el-select"),f=e.resolveComponent("el-form-item"),z=e.resolveComponent("el-input");return e.openBlock(),e.createBlock(e.unref(b),{data:a.properties.tableData,class:"orderIndex-table",onScroll:q},{header:e.withCtx(({row:t})=>[e.createElementVNode("div",C,"订单号:"+e.toDisplayString(t.no),1),e.createElementVNode("div",null,"创建时间:"+e.toDisplayString(t.createTime),1),e.createVNode(y,{style:{flex:"1"},justify:"end"},{default:e.withCtx(()=>[e.createVNode(r,{type:"primary",link:"",size:"small",onClick:o=>a.properties.filterOrderList(t)},{default:e.withCtx(()=>l[0]||(l[0]=[e.createTextVNode("移除")])),_:2},1032,["onClick"])]),_:2},1024)]),default:e.withCtx(()=>[e.createVNode(s,{prop:"name",label:"商品"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",N,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.shopOrders[0].shopOrderItems.slice(0,2),o=>(e.openBlock(),e.createBlock(w,{key:o.id,effect:"dark",content:o.productName,placement:"top",disabled:m.value},{default:e.withCtx(()=>[e.createElementVNode("div",V,[e.createVNode(L,{class:"cup",fits:"cover",style:{width:"63px",height:"63px"},shape:"square",size:"large",src:o.image},null,8,["src"])])]),_:2},1032,["content","disabled"]))),128)),e.createElementVNode("div",k,[e.createElementVNode("span",null,"￥"+e.toDisplayString(p.value(t)),1),e.createElementVNode("span",u,"共"+e.toDisplayString(d.value(t))+"件",1)])])]),_:1}),e.createVNode(s,{prop:"age",label:"客户"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",E,[e.createElementVNode("span",T,"买家昵称 : "+e.toDisplayString(t.buyerNickname),1),e.createElementVNode("div",S," (收货人："+e.toDisplayString(x(t).name)+","+e.toDisplayString(x(t).mobile)+") ",1)])]),_:1}),e.createVNode(s,{prop:"sex",label:"操作",align:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(f,{label:"物流服务",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:t.expressCompanyName,"onUpdate:modelValue":o=>t.expressCompanyName=o,onChange:o=>h(o,t,"select"),"value-key":"logisticsCompanyCode",placeholder:"请选择物流服务"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.properties.companySelectList,o=>(e.openBlock(),e.createBlock(B,{label:o.logisticsCompanyName,value:o,key:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024),e.createVNode(f,{label:"运单号码",style:{width:"230px"}},{default:e.withCtx(()=>[e.createVNode(z,{modelValue:t.expressNo,"onUpdate:modelValue":o=>t.expressNo=o,onChange:o=>h(o,t,"input"),style:{height:"28px"},maxlength:"40"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1024)]),_:2},1024)]),_:1})]),_:1},8,["data"])}}}),I="";return((c,a)=>{const i=c.__vccOpts||c;for(const[p,d]of a)i[p]=d;return i})(D,[["__scopeId","data-v-c94e8ea8"]])});
