(function(e,P){typeof exports=="object"&&typeof module<"u"?module.exports=P(require("vue"),require("@/apis/http"),require("element-plus"),require("@/apis/good"),require("@/apis/shops"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/components/BetterPageManage/BetterPageManage.vue"),require("@/composables/useConvert"),require("vue-clipboard3"),require("@/components/schema-form/index.vue"),require("@/components/element-plus/el-table/ElTableEmpty/index.vue"),require("@/components/q-icon/q-icon.vue")):typeof define=="function"&&define.amd?define(["vue","@/apis/http","element-plus","@/apis/good","@/apis/shops","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/components/BetterPageManage/BetterPageManage.vue","@/composables/useConvert","vue-clipboard3","@/components/schema-form/index.vue","@/components/element-plus/el-table/ElTableEmpty/index.vue","@/components/q-icon/q-icon.vue"],P):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformProductReviewList=P(e.PlatformProductReviewListContext.Vue,e.PlatformProductReviewListContext.Request,e.PlatformProductReviewListContext.ElementPlus,e.PlatformProductReviewListContext.GoodAPI,e.PlatformProductReviewListContext.ShopAPI,e.PlatformProductReviewListContext.QTable,e.PlatformProductReviewListContext.QTableColumn,e.PlatformProductReviewListContext.PageManage,e.PlatformProductReviewListContext.UseConvert,e.PlatformProductReviewListContext.VueClipboard3,e.PlatformProductReviewListContext.SchemaForm,e.PlatformProductReviewListContext.ElTableEmpty,e.PlatformProductReviewListContext.QIcon))})(this,function(e,P,D,Y,ee,te,L,oe,ae,ne,le,ie,K){"use strict";var J=document.createElement("style");J.textContent=`@charset "UTF-8";.el-row[data-v-5e084717]{padding:5px 0;line-height:1.5}.details[data-v-5e084717]{max-height:770px}.details-more[data-v-5e084717]{max-height:440px;overflow-y:auto}.details-more[data-v-5e084717] img{max-width:100%}[data-v-9ba9688c]{font-size:14px}.my-header[data-v-9ba9688c]{display:flex;flex-direction:row;justify-content:space-between;gap:16px}.table[data-v-9ba9688c]{overflow-y:auto;transition:height .5s}.commodity-info[data-v-9ba9688c]{display:flex}.commodity-info__main[data-v-9ba9688c]{display:flex;flex-direction:column;justify-content:space-between;margin-left:8px;width:225px;height:68px}.commodity-info__main--name[data-v-9ba9688c]{word-break:break-all;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.commodity-info__main--price[data-v-9ba9688c]{color:#ff7417}.el-link[data-v-9ba9688c]{margin-right:8px}.btns[data-v-9ba9688c]{padding-bottom:8px}.shop-name[data-v-9ba9688c]{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.no_data[data-v-9ba9688c]{margin-top:150px}.no_data img[data-v-9ba9688c]{width:246px;height:154px}.no_data .cont[data-v-9ba9688c]{color:#737b80;text-align:center;margin-top:40px}
`,document.head.appendChild(J);const re=(r={})=>P.get({url:"addon-supplier/supplier/manager/product/audit",params:r}),se=(r,p="")=>P.put({url:`addon-supplier/supplier/manager/product/updateStatus/${p}`,data:r});var X=(r=>(r.UNDER_REVIEW="待审核",r.ALREADY_PASSED="已通过",r.REFUSE="已拒绝",r))(X||{});const de=()=>{let r=[];const p=e.reactive({name:"",platformCategoryId:"",productType:"",shopId:""}),m=e.ref(""),h=e.ref("calc(100vh - 280px)"),d=e.ref([]),b=e.ref(!1),R=e.ref(null),f=e.ref(""),i=e.ref(""),c=e.ref([]),C=e.ref([]),w=[{label:"商品名称",labelWidth:75,prop:"name",valueType:"copy",fieldProps:{placeholder:"请输入商品名称"}},{label:"供应商名称",labelWidth:85,prop:"shopId",valueType:"select",options:c,fieldProps:{placeholder:"请输入店铺名称",props:{value:"id",label:"name",expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:a=>{x(a)}}},{label:"平台类目",prop:"platformCategoryId",valueType:"cascader",options:C,fieldProps:{placeholder:"请选择平台类目",props:{value:"id",label:"name",expandTrigger:"hover"},showAllLevels:!1,onChange:a=>{a.length>1?p.platformCategoryId=a[a.length-1]:p.platformCategoryId=""}}},{label:"商品类型",labelWidth:75,prop:"productType",valueType:"select",options:[{value:"",label:"全部商品"},{value:"VIRTUAL_PRODUCT",label:"虚拟商品"},{value:"REAL_PRODUCT",label:"实物商品"}],fieldProps:{placeholder:"请选择"}}],g=e.ref(!1),E=e.ref(!1),V=e.reactive({explain:""});e.watch(()=>i.value,a=>{f.value=""});const x=async a=>{const l=await Y.doGetSeachSupplierSearchList({supplierName:a});c.value=(l==null?void 0:l.data.map(n=>({...n,label:n.name,value:n.id})))||[]},s=e.reactive({pageSize:20,pageNum:1,total:0});k();async function k(){const{data:a}=await ee.doGetCategory({current:s.pageNum,size:1e3});s.total=a.total,N(a.records,"secondCategoryVos"),C.value=a.records}function N(a,l){a.forEach(n=>{n[l]&&(n.children=n[l],delete n[l],n.children.length&&N(n.children,"categoryThirdlyVos"))})}const u=async()=>{let a=[],l=0;try{console.log(p);const n=await re({...p,..._.page,productAuditStatus:m.value});n.code===200&&(a=n.data.records,l=n.data.total)}finally{_.goods=a,_.total=l}},I=e.computed(()=>(a=[])=>{const l=Math.min(...a.map(y=>Number(y)))/1e4,n=Math.max(...a.map(y=>Number(y)))/1e4;return n===l?n.toFixed(2):`${l.toFixed(2)} ~ ￥${n.toFixed(2)}`}),T=()=>{u()},B={全部:"",待审核:"UNDER_REVIEW",已通过:"ALREADY_PASSED",已拒绝:"REFUSE"},S=()=>{_.page.current=1,u()},U=a=>{a&&Object.keys(a).forEach(l=>p[l]=a[l])},q=()=>{Object.keys(p).forEach(a=>p[a]=""),T()},z=a=>{a?h.value="calc(100vh - 520px)":h.value="calc(100vh - 380px)"},_=e.reactive({page:{size:10,current:1},goods:[],total:0}),$=(a,l)=>{if(a.length===0)return D.ElMessage.error({message:"请选择需要审核的商品信息"});r=a,l?g.value=!0:E.value=!0},v=async(a="",l="")=>{var M;let n={};if(b.value&&(n=await((M=R.value)==null?void 0:M.validateForm())),a==="REFUSE"&&!l)return;const y=n.status?n.status:a,{code:j,msg:F}=await se({explain:l||n.explain,productIds:r==null?void 0:r.map(t=>t.id)},y);j===200?(D.ElMessage.success({message:F||"更新状态成功"}),b.value=!1,a&&l?E.value=!1:n.status||n.explain?b.value=!1:a&&!l&&(g.value=!1),V.explain="",u()):D.ElMessage.error({message:F||"更新状态失败"})},o=()=>{r=[]},H=a=>{_.page.current=a,u()},W=a=>{_.page.size=a,_.page.current=1,u()},G=a=>a==="提交时间"?_.goods.sort((l,n)=>f.value==="just"?Number(new Date(l.submitTime))-Number(new Date(n.submitTime)):Number(new Date(n.submitTime))-Number(new Date(l.submitTime))):_.goods.sort((l,n)=>f.value==="just"?Number(new Date(l.auditTime))-Number(new Date(n.auditTime)):Number(new Date(n.auditTime))-Number(new Date(l.auditTime))),O=a=>{i.value=a,f.value=f.value==="just"?"inverted":"just",_.goods=G(a)};return u(),{tableHeight:h,getSearch:T,handleSearchShow:z,changeItem:U,handleReset:q,currentTab:m,goodsStatus:B,handleTabClick:S,tableList:_,salePriceRange:I,ExamineGoodsEnum:X,initList:u,selectItems:d,handleAuditGoods:$,showAuditDialog:b,auditGoodsRefs:R,handleCloseAuditDialog:o,handleConfirmAudit:v,handleCurrentChange:H,handleSizeChange:W,sortTableList:O,searchParams:p,columns:w,formModel:V,visible:g,refuseVisible:E}},ce=()=>{const r=e.ref(!1),p=e.reactive({shopId:"",id:""});return{previewVisible:r,commodityInfo:p,handlePreviewExamineDetails:h=>{p.id=h.id,p.shopId=h.shopId,r.value=!0}}},pe={class:"details"},me=["innerHTML"];var Z=(r=>(r.CONSIGNMENT="代销商品",r.PURCHASE="采购商品",r.OWN="自有商品",r))(Z||{});const fe=e.defineComponent({__name:"commodityDetail",props:{commodityId:{type:String,default:""},shopId:{type:String,default:""}},setup(r){const{divTenThousand:p}=ae(),{toClipboard:m}=ne(),h=r,d=e.ref({});(async()=>{const{code:f,msg:i,data:c}=await Y.doGetSupplierCommodityDetails(h.commodityId,{shopId:h.shopId});f===200?d.value=c:D.ElMessage.error({message:i})})();const R=async f=>{try{await m(f),D.ElMessage.success("复制成功")}catch{D.ElMessage.error("复制失败")}};return(f,i)=>{const c=e.resolveComponent("el-col"),C=e.resolveComponent("el-row"),w=e.resolveComponent("el-link"),g=e.resolveComponent("el-table-column"),E=e.resolveComponent("el-image"),V=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",pe,[e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:24,style:{"font-weight":"600"}},{default:e.withCtx(()=>{var x;return[e.createTextVNode(e.toDisplayString((x=d.value)==null?void 0:x.name),1)]}),_:1})]),_:1}),e.createVNode(C,{gutter:8},{default:e.withCtx(()=>{var x,s,k;return[e.createVNode(c,{span:(x=d.value)!=null&&x.collectionUrl?8:12},{default:e.withCtx(()=>[e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:6},{default:e.withCtx(()=>i[1]||(i[1]=[e.createTextVNode("平台类目：")])),_:1}),e.createVNode(c,{span:18},{default:e.withCtx(()=>{var N,u,I,T,B,S;return[e.createTextVNode(e.toDisplayString((u=(N=d.value)==null?void 0:N.platformCategoryName)==null?void 0:u.oneName)+"/"+e.toDisplayString((T=(I=d.value)==null?void 0:I.platformCategoryName)==null?void 0:T.twoName)+"/"+e.toDisplayString((S=(B=d.value)==null?void 0:B.platformCategoryName)==null?void 0:S.threeName),1)]}),_:1})]),_:1})]),_:1},8,["span"]),e.createVNode(c,{span:(s=d.value)!=null&&s.collectionUrl?8:12},{default:e.withCtx(()=>[e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:6},{default:e.withCtx(()=>i[2]||(i[2]=[e.createTextVNode("销售方式：")])),_:1}),e.createVNode(c,{span:18},{default:e.withCtx(()=>{var N;return[e.createTextVNode(e.toDisplayString(Z[(N=d.value)==null?void 0:N.sellType]),1)]}),_:1})]),_:1})]),_:1},8,["span"]),(k=d.value)!=null&&k.collectionUrl?(e.openBlock(),e.createBlock(c,{key:0,span:8},{default:e.withCtx(()=>[e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:6},{default:e.withCtx(()=>i[3]||(i[3]=[e.createTextVNode("采集地址：")])),_:1}),e.createVNode(c,{span:18},{default:e.withCtx(()=>[e.createVNode(w,{type:"primary",onClick:i[0]||(i[0]=N=>R(d.value.collectionUrl))},{default:e.withCtx(()=>i[4]||(i[4]=[e.createTextVNode("复制")])),_:1})]),_:1})]),_:1})]),_:1})):e.createCommentVNode("",!0)]}),_:1}),e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:24},{default:e.withCtx(()=>{var x;return[e.createVNode(V,{data:(x=d.value)==null?void 0:x.storageSkus,"max-height":"230px",size:"small"},{default:e.withCtx(()=>[e.createVNode(g,{label:"规格"},{default:e.withCtx(({row:s})=>{var k,N;return[e.createTextVNode(e.toDisplayString((k=s==null?void 0:s.specs)!=null&&k.length?(N=s==null?void 0:s.specs)==null?void 0:N.join(";"):"单规格"),1)]}),_:1}),e.createVNode(g,{label:"SKU图"},{default:e.withCtx(({row:s})=>[e.createVNode(E,{src:s==null?void 0:s.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(g,{label:"实售价(元)"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(s==null?void 0:s.salePrice)),1)]),_:1}),e.createVNode(g,{label:"指导价(元)"},{default:e.withCtx(({row:s})=>[e.createTextVNode(e.toDisplayString(e.unref(p)(s==null?void 0:s.price)),1)]),_:1}),e.createVNode(g,{label:"重量(kg)",prop:"weight"})]),_:1},8,["data"])]}),_:1})]),_:1}),e.createVNode(C,{gutter:8},{default:e.withCtx(()=>[e.createVNode(c,{span:24},{default:e.withCtx(()=>{var x;return[e.createElementVNode("div",{class:"details-more",innerHTML:(x=d.value)==null?void 0:x.detail},null,8,me)]}),_:1})]),_:1})])}}}),Pe="",Q=(r,p)=>{const m=r.__vccOpts||r;for(const[h,d]of p)m[h]=d;return m},he=Q(fe,[["__scopeId","data-v-5e084717"]]),ge=e.defineComponent({__name:"audit-goods",setup(r,{expose:p}){const m=e.reactive({status:"SELL_ON",explain:""}),h=f=>{f==="SELL_ON"&&(m.explain="")},d={explain:{required:!0,message:"请输入原因",trigger:"blur"},status:{required:!0,message:"请选择审核状态",trigger:"change"}},b=e.ref(null);return p({validateForm:()=>new Promise((f,i)=>{b.value?b.value.validate(c=>{c?f(m):i("valid fail")}):i("none form inst")})}),(f,i)=>{const c=e.resolveComponent("el-radio"),C=e.resolveComponent("el-radio-group"),w=e.resolveComponent("el-form-item"),g=e.resolveComponent("el-input"),E=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(E,{ref_key:"formRef",ref:b,model:m,rules:d},{default:e.withCtx(()=>[e.createVNode(w,{prop:"status",label:"审核状态"},{default:e.withCtx(()=>[e.createVNode(C,{modelValue:m.status,"onUpdate:modelValue":i[0]||(i[0]=V=>m.status=V),onChange:h},{default:e.withCtx(()=>[e.createVNode(c,{value:"SELL_ON"},{default:e.withCtx(()=>i[2]||(i[2]=[e.createTextVNode("通过")])),_:1}),e.createVNode(c,{value:"REFUSE"},{default:e.withCtx(()=>i[3]||(i[3]=[e.createTextVNode("拒绝")])),_:1})]),_:1},8,["modelValue"])]),_:1}),m.status==="REFUSE"?(e.openBlock(),e.createBlock(w,{key:0,label:"原因",prop:"explain"},{default:e.withCtx(()=>[e.createVNode(g,{modelValue:m.explain,"onUpdate:modelValue":i[1]||(i[1]=V=>m.explain=V),placeholder:"20字以内",maxlength:20},null,8,["modelValue"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model"])}}}),xe={class:"commodity-info"},_e={class:"commodity-info__main"},Ce={class:"commodity-info__main--name"},Ne={key:1},Ve={class:"commodity-info__main--price"},ue=["title"],ye=["title"],be={class:"dialog-footer"},Ee={class:"my-header"},ke=["id"],Se={class:"dialog-footer"},we={class:"dialog-footer"},Re=e.defineComponent({__name:"PlatformProductReviewList",setup(r){const{getSearch:p,changeItem:m,handleReset:h,currentTab:d,goodsStatus:b,handleTabClick:R,tableHeight:f,tableList:i,salePriceRange:c,ExamineGoodsEnum:C,initList:w,selectItems:g,handleAuditGoods:E,showAuditDialog:V,auditGoodsRefs:x,handleConfirmAudit:s,handleCloseAuditDialog:k,handleCurrentChange:N,handleSizeChange:u,sortTableList:I,searchParams:T,columns:B,formModel:S,visible:U,refuseVisible:q}=de(),{commodityInfo:z,previewVisible:_,handlePreviewExamineDetails:$}=ce();return(v,o)=>{const H=e.resolveComponent("el-config-provider"),W=e.resolveComponent("el-tab-pane"),G=e.resolveComponent("el-tabs"),O=e.resolveComponent("el-image"),a=e.resolveComponent("el-tooltip"),l=e.resolveComponent("el-link"),n=e.resolveComponent("el-dialog"),y=e.resolveComponent("el-button"),j=e.resolveComponent("el-input"),F=e.resolveComponent("el-form-item"),M=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(H,{"value-on-clear":"","empty-values":[void 0,null]},{default:e.withCtx(()=>[e.createVNode(le,{columns:e.unref(B),"v-model":e.unref(T),"has-unfold":"",onChangeFormItem:e.unref(m),onSearchHandle:e.unref(p),onHandleReset:e.unref(h)},null,8,["columns","v-model","onChangeFormItem","onSearchHandle","onHandleReset"])]),_:1}),e.createVNode(G,{modelValue:e.unref(d),"onUpdate:modelValue":o[0]||(o[0]=t=>e.isRef(d)?d.value=t:null),style:{padding:"0 16px 0 16px"},onTabChange:e.unref(R)},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(b),(t,A)=>(e.openBlock(),e.createBlock(W,{key:t,label:A,name:t},null,8,["label","name"]))),128))]),_:1},8,["modelValue","onTabChange"]),e.createVNode(e.unref(te),{"checked-item":e.unref(g),"onUpdate:checkedItem":o[1]||(o[1]=t=>e.isRef(g)?g.value=t:null),data:e.unref(i).goods,style:e.normalizeStyle({height:e.unref(f)}),"no-border":"",class:"table",onChangeSort:e.unref(I)},{noData:e.withCtx(()=>[e.createVNode(ie,{imageIndex:"9",text:"暂无商品"})]),default:e.withCtx(()=>[e.createVNode(L,{label:"商品",align:"left",width:"300"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",xe,[e.createVNode(O,{src:t==null?void 0:t.pic,alt:t==null?void 0:t.name,style:{width:"68px",height:"68px","flex-shrink":"0"}},null,8,["src","alt"]),e.createElementVNode("div",_e,[t.name.length>=30?(e.openBlock(),e.createBlock(a,{key:0,effect:"dark",content:t.name,placement:"top-start"},{default:e.withCtx(()=>[e.createElementVNode("span",Ce,e.toDisplayString(t==null?void 0:t.name),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",Ne,e.toDisplayString(t==null?void 0:t.name),1)),e.createElementVNode("span",Ve,"￥"+e.toDisplayString(e.unref(c)(t==null?void 0:t.salePrices)),1)])])]),_:1}),e.createVNode(L,{label:"所属供应商",align:"left",width:e.unref(d)==="UNDER_REVIEW"?220:150,prop:"shopName"},{default:e.withCtx(({row:t})=>[e.unref(d)==="UNDER_REVIEW"&&t.shopName.length>=20||t.shopName.length>=10?(e.openBlock(),e.createBlock(a,{key:0,effect:"dark",content:t.shopName,placement:"top-start",class:"transparent-tooltip"},{default:e.withCtx(()=>[e.createElementVNode("span",{title:t.shopName,class:"shop-name"},e.toDisplayString(t.shopName),9,ue)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",{key:1,title:t.shopName,class:"shop-name"},e.toDisplayString(t.shopName),9,ye))]),_:1},8,["width"]),e.createVNode(L,{label:"状态",align:"left",width:"100"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t.auditStatus==="UNDER_REVIEW"?"#FD9224":t.auditStatus==="ALREADY_PASSED"?"#00BB2C":"#999"})},e.toDisplayString(e.unref(C)[t==null?void 0:t.auditStatus]),5)]),_:1}),e.createVNode(L,{label:"提交时间",align:"left",prop:"submitTime",width:"130"}),e.unref(d)!=="UNDER_REVIEW"?(e.openBlock(),e.createBlock(L,{key:0,label:"审核时间",align:"left",prop:"auditTime",width:"130"})):e.createCommentVNode("",!0),e.createVNode(L,{label:"操作",align:"right",width:"140",fixed:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(l,{type:"primary",onClick:A=>e.unref($)(t)},{default:e.withCtx(()=>o[12]||(o[12]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),(t==null?void 0:t.auditStatus)==="UNDER_REVIEW"?(e.openBlock(),e.createBlock(l,{key:0,type:"primary",onClick:A=>e.unref(E)([t],!0)},{default:e.withCtx(()=>o[13]||(o[13]=[e.createTextVNode("通过")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(t==null?void 0:t.auditStatus)==="UNDER_REVIEW"?(e.openBlock(),e.createBlock(l,{key:1,type:"primary",onClick:A=>e.unref(E)([t],!1)},{default:e.withCtx(()=>o[14]||(o[14]=[e.createTextVNode("拒绝")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),e.createVNode(a,{class:"box-item",effect:"dark",content:t.explain,placement:"bottom-end"},{default:e.withCtx(()=>[(t==null?void 0:t.auditStatus)==="REFUSE"?(e.openBlock(),e.createBlock(l,{key:0,type:"primary"},{default:e.withCtx(()=>o[15]||(o[15]=[e.createTextVNode("拒绝原因")])),_:1})):e.createCommentVNode("",!0)]),_:2},1032,["content"])]),_:1})]),_:1},8,["checked-item","data","style","onChangeSort"]),e.createVNode(oe,{"page-num":e.unref(i).page.current,"page-size":e.unref(i).page.size,"load-init":"",total:e.unref(i).total,onHandleCurrentChange:e.unref(N),onHandleSizeChange:e.unref(u),onReload:e.unref(w)},null,8,["page-num","page-size","total","onHandleCurrentChange","onHandleSizeChange","onReload"]),e.createVNode(n,{modelValue:e.unref(_),"onUpdate:modelValue":o[2]||(o[2]=t=>e.isRef(_)?_.value=t:null),title:"商品详情",center:"",width:"900px","destroy-on-close":"",top:"3vh"},{default:e.withCtx(()=>[e.createVNode(he,{"commodity-id":e.unref(z).id,"shop-id":e.unref(z).shopId},null,8,["commodity-id","shop-id"])]),_:1},8,["modelValue"]),e.createVNode(n,{modelValue:e.unref(V),"onUpdate:modelValue":o[4]||(o[4]=t=>e.isRef(V)?V.value=t:null),title:"商品审核","destroy-on-close":"",onClose:e.unref(k)},{footer:e.withCtx(()=>[e.createElementVNode("span",be,[e.createVNode(y,{onClick:o[3]||(o[3]=t=>V.value=!1)},{default:e.withCtx(()=>o[16]||(o[16]=[e.createTextVNode("取消")])),_:1}),e.createVNode(y,{type:"primary",onClick:e.unref(s)},{default:e.withCtx(()=>o[17]||(o[17]=[e.createTextVNode("确认")])),_:1},8,["onClick"])])]),default:e.withCtx(()=>[e.createVNode(ge,{ref_key:"auditGoodsRefs",ref:x},null,512)]),_:1},8,["modelValue","onClose"]),e.createVNode(n,{modelValue:e.unref(U),"onUpdate:modelValue":o[7]||(o[7]=t=>e.isRef(U)?U.value=t:null),"show-close":!1,width:"500"},{header:e.withCtx(({close:t,titleId:A,titleClass:Te})=>[e.createElementVNode("div",Ee,[e.createElementVNode("h4",{id:A,class:e.normalizeClass(Te),style:{display:"flex","align-items":"center"}},[e.createVNode(K,{name:"icon-tishi1-copy",svg:"",size:"24"}),o[18]||(o[18]=e.createElementVNode("span",{style:{"margin-left":"5px","font-size":"16px"}},"商品审核",-1))],10,ke),e.createVNode(K,{size:"18",name:"icon-guanbi1",onClick:t},null,8,["onClick"])])]),footer:e.withCtx(()=>[e.createElementVNode("div",Se,[e.createVNode(y,{onClick:o[5]||(o[5]=t=>U.value=!1)},{default:e.withCtx(()=>o[19]||(o[19]=[e.createTextVNode("取消")])),_:1}),e.createVNode(y,{type:"primary",onClick:o[6]||(o[6]=t=>e.unref(s)("SELL_ON"))},{default:e.withCtx(()=>o[20]||(o[20]=[e.createTextVNode(" 确认 ")])),_:1})])]),default:e.withCtx(()=>[o[21]||(o[21]=e.createElementVNode("div",{style:{width:"100%",display:"flex","justify-content":"center",padding:"10px 0"}},[e.createElementVNode("span",{style:{width:"80%",color:"#4e5969"}},"商品审核通过后商品将处于上架状态，确定通过审核吗？")],-1))]),_:1},8,["modelValue"]),e.createVNode(n,{modelValue:e.unref(q),"onUpdate:modelValue":o[11]||(o[11]=t=>e.isRef(q)?q.value=t:null),title:"商品审核",width:"690"},{footer:e.withCtx(()=>[e.createElementVNode("div",we,[e.createVNode(y,{onClick:o[9]||(o[9]=t=>q.value=!1)},{default:e.withCtx(()=>o[22]||(o[22]=[e.createTextVNode("取消")])),_:1}),e.createVNode(y,{type:"primary",onClick:o[10]||(o[10]=t=>e.unref(s)("REFUSE",e.unref(S).explain))},{default:e.withCtx(()=>o[23]||(o[23]=[e.createTextVNode(" 确认 ")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(M,{ref:"formRef",model:e.unref(S)},{default:e.withCtx(()=>[e.createVNode(F,{label:"拒绝原因",prop:"explain"},{default:e.withCtx(()=>[e.createVNode(j,{modelValue:e.unref(S).explain,"onUpdate:modelValue":o[8]||(o[8]=t=>e.unref(S).explain=t),placeholder:"请输入拒绝原因",maxlength:20,"show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}}),De="";return Q(Re,[["__scopeId","data-v-9ba9688c"]])});
