(function(e,i){typeof exports=="object"&&typeof module<"u"?module.exports=i(require("vue"),require("@/utils/http"),require("decimal.js")):typeof define=="function"&&define.amd?define(["vue","@/utils/http","decimal.js"],i):(e=typeof globalThis<"u"?globalThis:e||self,e.PcGroupOrder=i(e.PcGroupOrderContext.Vue,e.PcGroupOrderContext.UtilsHttp))})(this,function(e,i){"use strict";var k=document.createElement("style");k.textContent=`@charset "UTF-8";.user-imgs[data-v-bf4b5b85]{width:58px;height:58px;border-radius:50%;border:1px dashed #999;display:flex;justify-content:center;align-items:center}[data-v-bf4b5b85] .el-card__body{padding:0}em[data-v-bf4b5b85],i[data-v-bf4b5b85]{font-style:normal}.xianshi[data-v-bf4b5b85]{display:block!important}.all[data-v-bf4b5b85]{display:none;width:100%;height:100%;background-color:#10101080;position:fixed;top:0;left:0;z-index:999999}.all .el-card[data-v-bf4b5b85]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);z-index:9999999;width:520px;border-radius:8px;background-color:#fff}.all .el-card .title[data-v-bf4b5b85]{height:44px;line-height:44px;border-bottom:1px solid #bbb}.all .el-card .title span[data-v-bf4b5b85]{margin-left:24px;float:left;font-size:14px;font-weight:700}.all .el-card .title i[data-v-bf4b5b85]{margin:6px 14px 0;float:right;cursor:pointer}.all .el-card .line[data-v-bf4b5b85]{margin:24px 0 14px}.all .el-card .line i[data-v-bf4b5b85]{display:inline-block;width:106px;border-top:1px dashed rgba(10,10,10,.3);transform:translateY(-4px)}.all .el-card .line em[data-v-bf4b5b85]{width:96px;height:24px;font-size:16px;font-weight:700;display:inline-block;margin-left:35px;margin-right:30px}.all .el-card p[data-v-bf4b5b85]{display:inline-block;height:17px;font-size:12px;font-weight:700;margin-bottom:37px}.all .el-card p i[data-v-bf4b5b85]{margin:0 5px;color:#ee1e38}.all .el-card .photo[data-v-bf4b5b85]{margin-bottom:34px;display:flex;justify-content:center;margin-left:-20px}.all .el-card .photo .user-img[data-v-bf4b5b85]{display:inline-flex;position:relative}.all .el-card .photo .user-img i[data-v-bf4b5b85]{padding:2px 5px;background-color:#f7c945;color:#fff;font-weight:700;border-radius:4px;font-size:12px;left:-12px;top:-11px;position:absolute;z-index:9}.all .el-card .photo span[data-v-bf4b5b85]{width:58px;height:58px;border-radius:50%;box-sizing:border-box;margin-left:29px;border:1px dashed #bbb}.all .el-card .photo span[data-v-bf4b5b85]:first-of-type{border:4px solid #f7c945;margin-left:0}.all .el-card .photo span img[data-v-bf4b5b85]{border-radius:50%;width:100%;height:100%}.all .el-card .rule[data-v-bf4b5b85]{border-top:1px solid #bbb;padding:24px 0}.all .el-card .rule p[data-v-bf4b5b85]{font-size:14px;width:100%;padding-right:66px;margin-bottom:16px;text-align:right;color:#101010}.all .el-card .rule p[data-v-bf4b5b85]:last-child{margin-bottom:0}.all .el-card .rule p span[data-v-bf4b5b85]{font-weight:500!important;margin-left:66px;float:left;text-align:justify;text-align-last:justify;text-justify:inter-word;display:block;width:70px;height:20px;color:#a7a7a7}.text[data-v-e6061965]{width:87px;height:24px;line-height:24px;color:#e31436;margin:4px 0 6px;cursor:pointer;border:1px solid #e31436}
`,document.head.appendChild(k);const C=o=>i.http.get({url:`addon-team/team/activity/order/summary?teamNo=${o}`}),v=o=>i.http.get({url:`addon-team/team/activity/order/users?size=300&teamNo=${o}`}),l=o=>(e.pushScopeId("data-v-bf4b5b85"),o=o(),e.popScopeId(),o),G=l(()=>e.createElementVNode("span",null,"我的拼团",-1)),j=[l(()=>e.createElementVNode("svg",{t:"1686211043354",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"8090",width:"24",height:"24"},[e.createElementVNode("path",{d:"M576 512l277.333 277.333-64 64L512 576 234.667 853.333l-64-64L448 512 170.667 234.667l64-64L512 448l277.333-277.333 64 64L576 512z",fill:"#101010","p-id":"8091"})],-1))],M={key:0},O=l(()=>e.createElementVNode("div",{class:"line"},[e.createElementVNode("i"),e.createElementVNode("em",null,"邀请好友参团"),e.createElementVNode("i")],-1)),F={key:1,style:{height:"93px","font-size":"16px","line-height":"93px",color:"#de3224","padding-left":"70px"}},P={key:2,style:{height:"93px","font-size":"16px","line-height":"93px",color:"#de3224"}},I={class:"photo"},L={class:"user-img"},q=l(()=>e.createElementVNode("i",null,"团长",-1)),U=["src"],$=[l(()=>e.createElementVNode("div",{class:"user-imgs"},[e.createElementVNode("em",{style:{"font-size":"20px"}},"+")],-1))],A={class:"rule"},H=l(()=>e.createElementVNode("span",null,"商品名称：",-1)),K={style:{display:"inline-block",width:"300px","white-space":"nowrap",overflow:"hidden","text-overflow":"ellipsis"}},Y=l(()=>e.createElementVNode("p",null,[e.createElementVNode("span",null,"拼团规则："),e.createTextVNode("参团人数不足，系统自动退款")],-1)),J=e.defineComponent({__name:"PcMyTeam",props:{isShow:{type:Boolean,default:!1},teamNo:{type:Object,required:!0},records:{type:Object,required:!0}},emits:["isShowFalse"],setup(o,{emit:p}){var h,V,S,B;const t=o,s=p,d=()=>{s("isShowFalse",!1)},_=e.computed(()=>{var r,c,N,x,g,u;return(r=t.teamNo)!=null&&r.totalNum&&((N=(c=t.records)==null?void 0:c.records)!=null&&N.length)&&((x=t.teamNo)==null?void 0:x.totalNum)-((u=(g=t.records)==null?void 0:g.records)==null?void 0:u.length)||0}),m=e.ref(new Date((h=t.teamNo)==null?void 0:h.openTime).getTime()+Number((V=t.teamNo)==null?void 0:V.effectTimeout)*60*1e3||new Date),f=e.ref(new Date((S=t.teamNo)==null?void 0:S.openTime).getTime()+Number((B=t.teamNo)==null?void 0:B.effectTimeout)*60*1e3||new Date),a=e.ref(new Date().getTime()),n=e.computed(()=>{if(m.value.toString()<a.value.toString())return"00";let r=+f.value-a.value;return r<0?"00":Math.floor(r%(1e3*60*60*24)/(1e3*60*60)).toString().padStart(2,"0")}),y=e.computed(()=>{if(m.value.toString()<a.value.toString())return"00";let r=+f.value-a.value;return r<0?"00":Math.floor(r%(1e3*60*60)/(1e3*60)).toString().padStart(2,"0")}),b=e.computed(()=>{if(m.value.toString()<a.value.toString())return"00";let r=+f.value-a.value;return r<0?"00":Math.floor(r%(1e3*60)/1e3).toString().padStart(2,"0")});return e.onMounted(()=>{setInterval(()=>{a.value=new Date().getTime()},1e3)}),(r,c)=>{const N=e.resolveComponent("el-card");return e.openBlock(),e.createElementBlock("div",{class:e.normalizeClass(t.isShow===!0?"all xianshi":"all")},[e.createVNode(N,{class:"el-card"},{default:e.withCtx(()=>{var x,g,u,w,T,z;return[e.createElementVNode("div",{class:"title"},[G,e.createElementVNode("i",{onClick:d},j)]),((x=t.teamNo)==null?void 0:x.status)==="ING"?(e.openBlock(),e.createElementBlock("div",M,[O,e.createElementVNode("p",null,[e.createTextVNode(" 仅剩"),e.createElementVNode("i",null,e.toDisplayString(((g=t.teamNo)==null?void 0:g.totalNum)-t.teamNo.currentNum||0)+"个",1),e.createTextVNode("名额"),e.createElementVNode("i",null,e.toDisplayString(n.value)+":"+e.toDisplayString(y.value)+":"+e.toDisplayString(b.value),1),e.createTextVNode("后结束 ")])])):((u=t.teamNo)==null?void 0:u.status)==="FAIL"?(e.openBlock(),e.createElementBlock("div",F," 真可惜拼团未能成功！！！ ")):((w=t.teamNo)==null?void 0:w.status)==="SUCCESS"?(e.openBlock(),e.createElementBlock("div",P," 拼团成功！！！ ")):e.createCommentVNode("",!0),e.createElementVNode("div",I,[e.createElementVNode("div",L,[q,(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList((T=t.records)==null?void 0:T.records,(D,W)=>(e.openBlock(),e.createElementBlock("span",{key:W},[e.createElementVNode("img",{src:t.teamNo.commanderAvatar,alt:""},null,8,U)]))),128))]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(_.value,D=>(e.openBlock(),e.createElementBlock("div",{key:D,style:{"margin-left":"20px"}},$))),128))]),e.createElementVNode("div",A,[e.createElementVNode("p",null,[H,e.createElementVNode("em",K,e.toDisplayString((z=t.teamNo)==null?void 0:z.productName),1)]),Y])]}),_:1})],2)}}}),ee="",te="",E=(o,p)=>{const t=o.__vccOpts||o;for(const[s,d]of p)t[s]=d;return t},Q=E(J,[["__scopeId","data-v-bf4b5b85"]]),R=e.defineComponent({__name:"PcGroupOrder",props:{properties:{type:Object,default:()=>{}}},setup(o){const p=o;e.reactive({current:1,size:10});const t=e.ref(!1),s=e.ref(0),d=e.ref(),_=e.ref(),m=async a=>{const n=await C(a);n.code===200&&(d.value=n.data,t.value=!0);const{data:y,code:b}=await v(a);_.value=y,s.value=Date.now()},f=a=>{t.value=a};return(a,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",{class:"text",onClick:n[0]||(n[0]=y=>{var b,h;return m((h=(b=p.properties)==null?void 0:b.order)==null?void 0:h.extra.teamNo)})},"我的拼团"),t.value?(e.openBlock(),e.createBlock(Q,{key:s.value,"is-show":t.value,"team-no":d.value,records:_.value,onIsShowFalse:f},null,8,["is-show","team-no","records"])):e.createCommentVNode("",!0)],64))}}),oe="";return E(R,[["__scopeId","data-v-e6061965"]])});
