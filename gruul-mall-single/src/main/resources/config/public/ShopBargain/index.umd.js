(function(e,S){typeof exports=="object"&&typeof module<"u"?module.exports=S(require("vue"),require("@/components/ChromeTab.vue"),require("@vueuse/core"),require("@/components/SchemaForm.vue"),require("@/components/PageManage.vue"),require("element-plus"),require("@/store/modules/shopInfo"),require("vue-router"),require("@/composables/useConvert"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/ChromeTab.vue","@vueuse/core","@/components/SchemaForm.vue","@/components/PageManage.vue","element-plus","@/store/modules/shopInfo","vue-router","@/composables/useConvert","@/apis/http"],S):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopBargain=S(e.ShopBargainContext.Vue,e.ShopBargainContext.ChromeTabs,e.ShopBargainContext.VueUse,e.ShopBargainContext.SchemaForms,e.ShopBargainContext.PageManageTwo,e.ShopBargainContext.ElementPlus,e.ShopBargainContext.ShopInfoStore,e.ShopBargainContext.VueRouter,e.ShopBargainContext.UseConvert,e.ShopBargainContext.Request))})(this,function(e,S,P,I,R,g,$,D,q,E){"use strict";var F=document.createElement("style");F.textContent=`@charset "UTF-8";.container[data-v-ea4f5a1f]{overflow-y:scroll}[data-v-ea4f5a1f] .el-button.is-link{padding:0}[data-v-ea4f5a1f] .el-button+.el-button{margin-left:10px}.bargain_origin[data-v-9646c793]{display:flex;justify-content:center;align-items:center}.bargain_origin--name[data-v-9646c793]{width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.bargain_origin--shop_name[data-v-9646c793]{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(F);const H={class:"q_plugin_container"},j={class:"fdc1 overh"},U=e.defineComponent({__name:"ShopBargain",setup(o){const h=e.reactive({activeName:"bargainList",tabsList:[{label:"砍价列表",name:"bargainList"},{label:"砍价订单",name:"bargainOrder"}]}),{activeName:d,tabsList:_}=e.toRefs(h),f={bargainList:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ae)),bargainOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>ce))},l=i=>{d.value=i};return(i,y)=>(e.openBlock(),e.createElementBlock("div",H,[e.createVNode(S,{"tab-list":e.unref(_),value:e.unref(d),onHandleTabs:l},null,8,["tab-list","value"]),e.createElementVNode("div",j,[(e.openBlock(),e.createBlock(e.resolveDynamicComponent(f[e.unref(d)])))])]))}}),W=e.defineComponent({__name:"head-search",props:{modelValue:{type:Object,default(){return{}}}},emits:["update:modelValue","search"],setup(o,{emit:h}){const d=o,f=[{label:"活动名称",prop:"keyword",valueType:"copy",fieldProps:{placeholder:"请输入活动名称"}},{label:"活动状态",prop:"status",valueType:"select",options:[{value:"",label:"全部状态"},{value:"NOT_STARTED",label:"未开始"},{value:"PROCESSING",label:"进行中"},{value:"OVER",label:"已结束"},{value:"SHOP_SELL_OFF",label:"已下架"},{value:"ILLEGAL_SELL_OFF",label:"违规下架"}],fieldProps:{placeholder:"请选择"}}],l=h,i=P.useVModel(d,"modelValue",l),y=r=>{r&&Object.keys(r).forEach(u=>d.modelValue[u]=r[u])},x=()=>{Object.keys(d.modelValue).forEach(r=>d.modelValue[r]=""),l("search")};return(r,u)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(I,{"v-model":e.unref(i),columns:f,onSearchHandle:u[0]||(u[0]=V=>l("search")),onChangeFormItem:y,onHandleReset:x},null,8,["v-model"]),u[1]||(u[1]=e.createElementVNode("div",{class:"grey_bar"},null,-1))],64))}}),w="addon-bargain/bargain",J="addon-bargain/bargainOrder",K=o=>E.put({url:w+"/shop/sellOf",data:o}),Q=o=>E.get({url:w+"/illegal/reason/"+o}),X=o=>E.get({url:w,params:o}),z=o=>E.del({url:w,data:o}),Y=o=>E.get({url:J,params:o}),Z={BARGAINING:"砍价中",FAILED_TO_BARGAIN:"砍价失败",SUCCESSFUL_BARGAIN:"砍价成功"};var A=(o=>(o.NOT_STARTED="未开始",o.PROCESSING="进行中",o.OVER="已结束",o.ILLEGAL_SELL_OFF="违规下架",o.SHOP_SELL_OFF="已下架",o))(A||{});const v={class:"handle_container",style:{"padding-top":"16px"}},ee={class:"table_container f1"},te=e.defineComponent({__name:"BargainList",setup(o){const h=e.reactive({keyword:"",status:""}),d=D.useRouter(),_=e.computed(()=>t=>Number(t)===0?"全部":t+""),f=e.ref([]),l=e.reactive({size:10,current:1,total:0}),i=e.ref(!1),y=e.ref(),x=e.ref("");r();async function r(){const{status:t,keyword:a}=h,s={...l,keyword:a,shopId:$.useShopInfoStore().shopInfo.id,status:t},{code:c,data:b}=await X(s);if(c!==200)return g.ElMessage.error("获取活动列表失败");f.value=b.records,l.current=b.current,l.size=b.size,l.total=b.total}const u=async t=>{try{if(!await g.ElMessageBox.confirm("确定删除该活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return;const{shopId:s,id:c}=t,{code:b,data:M,msg:n}=await z([{activityId:c,shopId:s}]);if(b!==200){g.ElMessage.error(n||"删除失败");return}g.ElMessage.success("删除成功"),f.value=f.value.filter(B=>B.id!==t.id),l.total--}catch{}},V=e.ref([]),k=t=>{V.value=t},O=t=>{l.size=t,r()},p=t=>{l.current=t,r()},m=()=>{d.push({name:"bargainBaseinfo"})},N=()=>{g.ElMessageBox.confirm("确定删除选中活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:async t=>{if(console.log("action",t),t==="cancel")return;const a=V.value.filter(c=>c.status!=="PROCESSING").map(c=>({shopId:c.shopId,activityId:c.id})),{code:s}=await z(a);if(s!==200){g.ElMessage.error("删除失败");return}g.ElMessage.success("删除成功"),r()}})},C=(t,a)=>{const{id:s,shopId:c}=t;d.push({name:"bargainBaseinfo",query:{activityId:s,shopId:c,isLookUp:a}})},L=t=>{g.ElMessageBox.confirm("确定下架选中活动?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",callback:async a=>{if(a==="cancel")return;const{code:s}=await K(t);if(s!==200){g.ElMessage.error("下架失败");return}g.ElMessage.success("下架成功"),r()}})},T=async t=>{const{code:a,data:s}=await Q(t);a!==200&&g.ElMessage.error("违规原因获取失败"),x.value=s,i.value=!0};return(t,a)=>{const s=e.resolveComponent("el-button"),c=e.resolveComponent("el-table-column"),b=e.resolveComponent("el-table"),M=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(W,{modelValue:h,"onUpdate:modelValue":a[0]||(a[0]=n=>h=n),onSearch:r},null,8,["modelValue"]),e.createElementVNode("div",v,[e.createVNode(s,{round:"",type:"primary",onClick:m},{default:e.withCtx(()=>a[2]||(a[2]=[e.createTextVNode("新增砍价")])),_:1}),e.createVNode(s,{round:"",disabled:!V.value.length,onClick:N},{default:e.withCtx(()=>a[3]||(a[3]=[e.createTextVNode("批量删除")])),_:1},8,["disabled"])]),e.createElementVNode("div",ee,[e.createVNode(b,{ref_key:"multipleTableRef",ref:y,data:f.value,"header-cell-style":{background:"#f6f8fa",height:"48px"},"header-row-style":{color:"#333"},onSelectionChange:k},{default:e.withCtx(()=>[e.createVNode(c,{type:"selection",width:"30",fixed:"",selectable:n=>n.status!=="PROCESSING"},null,8,["selectable"]),e.createVNode(c,{label:"活动名称"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(n.name),1)]),_:1}),e.createVNode(c,{label:"状态",width:"180"},{default:e.withCtx(({row:n})=>[e.createElementVNode("span",{style:e.normalizeStyle(`color: ${n.status==="ILLEGAL_SELL_OFF"?"#f12f22":"inherit"}`)},e.toDisplayString(e.unref(A)[n.status]),5)]),_:1}),e.createVNode(c,{label:"活动时间"},{default:e.withCtx(({row:n})=>[e.createElementVNode("div",null,"起："+e.toDisplayString(n.startTime),1),e.createElementVNode("div",null,"止："+e.toDisplayString(n.endTime),1)]),_:1}),e.createVNode(c,{label:"活动商品",width:"150"},{default:e.withCtx(({row:n})=>[e.createTextVNode(e.toDisplayString(_.value(n.productNum)),1)]),_:1}),e.createVNode(c,{label:"支付单数",width:"150"},{default:e.withCtx(({row:n})=>[e.createElementVNode("span",null,e.toDisplayString(n.payOrder||0),1)]),_:1}),e.createVNode(c,{label:"操作",fixed:"right",align:"right",width:"200"},{default:e.withCtx(({row:n})=>[e.createVNode(s,{type:"primary",link:"",onClick:B=>C(n,!0)},{default:e.withCtx(()=>a[4]||(a[4]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),n.status==="PROCESSING"?(e.openBlock(),e.createBlock(s,{key:0,type:"primary",link:"",onClick:B=>L({bargainId:n.id,shopId:n.shopId})},{default:e.withCtx(()=>a[5]||(a[5]=[e.createTextVNode("下架")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),n.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createBlock(s,{key:1,type:"primary",link:"",onClick:B=>T(n.id)},{default:e.withCtx(()=>a[6]||(a[6]=[e.createTextVNode("违规原因")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),n.status!=="PROCESSING"?(e.openBlock(),e.createBlock(s,{key:2,type:"danger",link:"",onClick:B=>u(n)},{default:e.withCtx(()=>a[7]||(a[7]=[e.createTextVNode("删除")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"])]),e.createVNode(R,{"page-size":l.size,"page-num":l.current,total:l.total,onHandleCurrentChange:p,onHandleSizeChange:O},null,8,["page-size","page-num","total"]),e.createVNode(M,{modelValue:i.value,"onUpdate:modelValue":a[1]||(a[1]=n=>i.value=n),title:"违规原因",width:"500",center:""},{default:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(x.value),1)]),_:1},8,["modelValue"])],64)}}}),de="",G=(o,h)=>{const d=o.__vccOpts||o;for(const[_,f]of h)d[_]=f;return d},ae=Object.freeze(Object.defineProperty({__proto__:null,default:G(te,[["__scopeId","data-v-ea4f5a1f"]])},Symbol.toStringTag,{value:"Module"})),ne={class:"table_container"},oe={class:"bargain_origin"},re={class:"bargain_origin--name",style:{width:"170px","margin-left":"5px"}},le={class:"bargain_origin"},ie={class:"bargain_origin--shop_name",style:{width:"205px","margin-left":"5px"}},se=e.defineComponent({__name:"BargainOrder",setup(o){const h=e.reactive({form:{keyword:""},bargainOrderList:[],chooseList:[]}),d=D.useRouter(),{form:_,bargainOrderList:f,chooseList:l}=e.toRefs(h),i=e.reactive({size:10,current:1,total:0}),{divTenThousand:y}=q(),x=[{label:"发起人",prop:"keyword",valueType:"copy",labelWidth:55,fieldProps:{placeholder:"请输入活动发起人"}}];r();async function r(){const{code:p,data:m,msg:N}=await Y({...i,keyword:_.value.keyword});if(p!==200){g.ElMessage.error(N||"获取砍价活动详情");return}f.value=m.records,i.size=m.size,i.total=m.total,i.current=m.current}const u=p=>{p&&Object.keys(p).forEach(m=>_.value[m]=p[m])},V=()=>{Object.keys(_.value).forEach(p=>_.value[p]=""),r()},k=p=>{i.size=p,r()},O=p=>{i.current=p,r()};return(p,m)=>{const N=e.resolveComponent("el-image"),C=e.resolveComponent("el-table-column"),L=e.resolveComponent("el-button"),T=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(I,{"v-model":e.unref(_),columns:x,onSearchHandle:r,onChangeFormItem:u,onHandleReset:V},null,8,["v-model"]),e.createElementVNode("div",ne,[e.createVNode(T,{ref:"multipleTableRef",data:e.unref(f),stripe:"","header-cell-style":{background:"#f6f8fa",height:"48px"},"header-row-style":{color:"#333"},onSelectionChange:m[0]||(m[0]=t=>l.value=t)},{default:e.withCtx(()=>[e.createVNode(C,{label:"发起人",width:"240px"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",oe,[e.createVNode(N,{style:{width:"30px",height:"30px","border-radius":"3px"},fit:"",src:t.userHeadPortrait},null,8,["src"]),e.createElementVNode("div",re,e.toDisplayString(t.userNickname),1)])]),_:1}),e.createVNode(C,{label:"商品信息",width:"280px"},{default:e.withCtx(({row:t})=>[e.createElementVNode("div",le,[e.createVNode(N,{style:{width:"36px",height:"36px"},fit:"",src:t.productPic},null,8,["src"]),e.createElementVNode("div",ie,e.toDisplayString(t.productName),1)])]),_:1}),e.createVNode(C,{label:"销售价(元)",width:"120"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.salePrice&&e.unref(y)(t.salePrice)),1)]),_:1}),e.createVNode(C,{label:"底价(元)",width:"120"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.floorPrice&&e.unref(y)(t.floorPrice)),1)]),_:1}),e.createVNode(C,{label:"砍价人数",width:"120"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.bargainingPeople),1)]),_:1}),e.createVNode(C,{label:"砍价开始时间",width:"200px"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.createTime),1)]),_:1}),e.createVNode(C,{label:"砍价结束时间",width:"200px"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",null,e.toDisplayString(t.endTime),1)]),_:1}),e.createVNode(C,{label:"状态",width:"120"},{default:e.withCtx(({row:t})=>[e.createElementVNode("span",{style:e.normalizeStyle({color:t.bargainStatus==="SUCCESSFUL_BARGAIN"?"#00BB2C":""})},e.toDisplayString(e.unref(Z)[t.bargainStatus]),5)]),_:1}),e.createVNode(C,{label:"操作",fixed:"right",width:"130",align:"right"},{default:e.withCtx(({row:t})=>[e.createVNode(L,{link:"",type:"primary",onClick:()=>e.unref(d).push({name:"BargainHelpInfo",query:{id:t.id}})},{default:e.withCtx(()=>m[1]||(m[1]=[e.createTextVNode("帮砍记录")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),e.createVNode(R,{"page-size":i.size,"page-num":i.current,total:i.total,onHandleCurrentChange:O,onHandleSizeChange:k},null,8,["page-size","page-num","total"])],64)}}}),me="",ce=Object.freeze(Object.defineProperty({__proto__:null,default:G(se,[["__scopeId","data-v-9646c793"]])},Symbol.toStringTag,{value:"Module"}));return U});
