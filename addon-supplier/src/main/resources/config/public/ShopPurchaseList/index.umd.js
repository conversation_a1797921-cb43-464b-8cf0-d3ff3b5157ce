(function(e,de){typeof exports=="object"&&typeof module<"u"?module.exports=de(require("vue"),require("@/components/ChromeTab.vue"),require("vue-router"),require("lodash-es"),require("@/components/SchemaForm.vue"),require("@/components/PageManage.vue"),require("decimal.js"),require("element-plus"),require("@/composables/useConvert"),require("@/store/modules/shopInfo"),require("@/components/element-plus/el-table/ElTableEmpty/index.vue"),require("@/components/q-icon/q-icon.vue"),require("@/components/q-upload/q-upload.vue"),require("@vueuse/core"),require("@/utils/date"),require("vue-clipboard3"),require("@element-plus/icons-vue"),require("@/assets/images/icon/paymentVoucher.png"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("@/apis/http")):typeof define=="function"&&define.amd?define(["vue","@/components/ChromeTab.vue","vue-router","lodash-es","@/components/SchemaForm.vue","@/components/PageManage.vue","decimal.js","element-plus","@/composables/useConvert","@/store/modules/shopInfo","@/components/element-plus/el-table/ElTableEmpty/index.vue","@/components/q-icon/q-icon.vue","@/components/q-upload/q-upload.vue","@vueuse/core","@/utils/date","vue-clipboard3","@element-plus/icons-vue","@/assets/images/icon/paymentVoucher.png","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","@/apis/http"],de):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopPurchaseList=de(e.ShopPurchaseListContext.Vue,e.ShopPurchaseListContext.ChromeTabs,e.ShopPurchaseListContext.VueRouter,e.ShopPurchaseListContext.Lodash,e.ShopPurchaseListContext.SchemaForm,e.ShopPurchaseListContext.PageManageTwo,e.ShopPurchaseListContext.Decimal,e.ShopPurchaseListContext.ElementPlus,e.ShopPurchaseListContext.UseConvert,e.ShopPurchaseListContext.ShopInfoStore,e.ShopPurchaseListContext.ElTableEmpty,e.ShopPurchaseListContext.QIcon,e.ShopPurchaseListContext.QUpload,e.ShopPurchaseListContext.VueUse,e.ShopPurchaseListContext.DateUtil,e.ShopPurchaseListContext.VueClipboard3,e.ShopPurchaseListContext.ElementPlusIconsVue,e.ShopPurchaseListContext.paymentVoucher,e.ShopPurchaseListContext.QTable,e.ShopPurchaseListContext.QTableColumn,e.ShopPurchaseListContext.Request))})(this,function(e,de,pe,he,ue,ge,v,B,ie,ce,Ve,me,Re,Me,Ae,ze,Ue,Fe,Se,ne,j){"use strict";var be=document.createElement("style");be.textContent=`@charset "UTF-8";.commodity-info[data-v-7ad0cf7f]{display:flex;align-items:center}.commodity-info img[data-v-7ad0cf7f]{width:60px;height:60px}.commodity-info span[data-v-7ad0cf7f]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.purchase__batch[data-v-7ad0cf7f]{display:flex;justify-content:flex-end}.purchase__remark[data-v-7ad0cf7f]{padding:15px 0}.purchase__total[data-v-7ad0cf7f]{display:flex;flex-direction:column;align-items:flex-end;line-height:1.5}.purchase__total--title[data-v-7ad0cf7f]{font-size:1.2em;font-weight:600}.oneClickProcurement__commodity[data-v-3d4987c0]{display:flex;align-items:center}.oneClickProcurement__commodity img[data-v-3d4987c0]{width:60px;height:60px;flex-shrink:0}.oneClickProcurement__commodity span[data-v-3d4987c0]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.oneClickProcurement__expand[data-v-3d4987c0]{margin-left:20px;margin-right:20px;padding:10px;border:1px solid #efefef}.oneClickProcurement__form[data-v-3d4987c0]{display:flex;justify-content:flex-end}.oneClickProcurement__pagination[data-v-3d4987c0]{padding:15px 0;display:flex;justify-content:flex-end;align-items:center}.oneClickProcurement__total[data-v-3d4987c0]{display:flex;flex-direction:column;align-items:flex-end;line-height:1.5}.oneClickProcurement__total--title[data-v-3d4987c0]{font-size:1.2em;font-weight:600}[data-v-3d4987c0] .el-table thead .el-table__cell{background-color:var(--el-fill-color-light)}.details__img[data-v-82366355]{width:80px;height:80px;vertical-align:top}[data-v-44e56da8]{font-size:14px}.tools[data-v-44e56da8]{padding:16px}.table[data-v-44e56da8]{overflow:auto}.commodity-info[data-v-44e56da8]{display:flex;align-items:center;cursor:pointer}.commodity-info img[data-v-44e56da8]{width:60px;height:60px}.commodity-info p[data-v-44e56da8]{width:217px;overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.commodity-info .type_tag[data-v-44e56da8]{color:#999;font-size:14px;font-weight:400;margin-left:10px}.customer[data-v-44e56da8]{width:100%;display:flex;flex-direction:column;justify-content:center;font-size:14px;color:#333}.customer div[data-v-44e56da8]{display:flex;align-items:center}.customer__name[data-v-44e56da8]{display:inline-block;max-width:156px;margin-right:5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.customer img[data-v-44e56da8]{width:16.25px;height:15.32px;margin-left:5px;cursor:pointer}.count-down[data-v-92288512]{color:#f54319}.invoice-dialog[data-v-21d77e4c]{color:#000}.invoice-dialog-main[data-v-21d77e4c]{display:flex;justify-content:center;align-items:center;justify-content:space-between}.invoice-dialog-main__content[data-v-21d77e4c]{margin:10px 0;display:flex;justify-content:center;align-items:center;justify-content:space-between}.invoice-dialog-main__note[data-v-21d77e4c]{display:flex;justify-content:center;align-items:center;align-items:flex-start;justify-content:flex-start;margin-top:10px}.invoice-dialog-main__type[data-v-21d77e4c]{margin-top:10px;display:flex;flex-direction:column;align-items:flex-start}.invoice-dialog-main--price[data-v-21d77e4c]{color:#fd0505;font-size:14px;font-weight:700}.invoice-dialog-main--price[data-v-21d77e4c]:before{content:"￥";font-size:10px;font-weight:400}.oneClickProcurement__commodity[data-v-bcd44223]{display:flex;align-items:center}.oneClickProcurement__commodity img[data-v-bcd44223]{width:60px;height:60px;flex-shrink:0}.oneClickProcurement__commodity span[data-v-bcd44223]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.oneClickProcurement__form[data-v-bcd44223]{display:flex;justify-content:flex-end;margin-right:98px}.batch__commodity[data-v-66633c0a]{display:flex;align-items:center}.batch__commodity img[data-v-66633c0a]{width:60px;height:60px;flex-shrink:0}.batch__commodity span[data-v-66633c0a]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;margin-left:10px}.batch__form[data-v-66633c0a]{display:flex;justify-content:flex-end}[data-v-436de01a]{font-size:14px}.invoice-title[data-v-436de01a]{display:flex;justify-content:center;align-items:center}.invoice-desc[data-v-436de01a]{display:flex;justify-content:center;align-items:center;color:#fd0505;margin-top:7px}.proof-img[data-v-436de01a]{width:100%;height:350px;object-fit:contain}.copy[data-v-436de01a]{color:#1890ff;margin-left:8px;cursor:pointer}.text-red[data-v-436de01a]{color:red}.text-grey[data-v-436de01a]{color:#999}.customer[data-v-436de01a]{width:100%;height:42px;display:flex;flex-direction:column;justify-content:space-around;font-size:14px;color:#333}.customer div[data-v-436de01a]{display:flex;align-items:center}.customer__name[data-v-436de01a]{display:inline-block;max-width:88px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.customer img[data-v-436de01a]{width:16.25px;height:15.32px;margin-left:5px;cursor:pointer}.good[data-v-b5df09d6]{padding:0 30px 0 45px;box-sizing:border-box;font-size:12px;font-family:Microsoft YaHei,Microsoft YaHei-Normal;font-weight:400;color:#000}.good__info[data-v-b5df09d6]{margin-bottom:24px}.good__img[data-v-b5df09d6]{display:flex;justify-content:flex-start;align-items:flex-start}.good__spec[data-v-b5df09d6]{width:758px}.b[data-v-b5df09d6]{font-size:12px;color:#000}[data-v-f95351ab]{font-size:14px}.commodity-info[data-v-f95351ab]{display:flex;align-items:center}.commodity-info img[data-v-f95351ab]{width:68px;height:68px}.commodity-info-text[data-v-f95351ab]{height:68px;width:217px;display:flex;flex-direction:column;justify-content:space-between;margin-left:8px}.commodity-info-text p[data-v-f95351ab]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.customer[data-v-f95351ab]{width:100%;display:flex;flex-direction:column;justify-content:space-around;font-size:14px;color:#333}.customer div[data-v-f95351ab]{display:flex;align-items:center}.customer__name[data-v-f95351ab]{display:inline-block;max-width:156px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:3px}.customer img[data-v-f95351ab]{width:16.25px;height:15.32px;margin-left:5px;cursor:pointer}.good[data-v-b854e3eb]{padding:0 30px 0 45px;box-sizing:border-box;font-size:12px;font-family:Microsoft YaHei,Microsoft YaHei-Normal;font-weight:400;color:#000}.good__info[data-v-b854e3eb]{margin-bottom:24px}.good__img[data-v-b854e3eb]{display:flex;justify-content:flex-start;align-items:flex-start}.good__spec[data-v-b854e3eb]{width:758px}.b[data-v-b854e3eb]{font-size:12px;color:#000}img[data-v-cdc1ed69]{vertical-align:top}[data-v-7336ff23]{font-size:14px}.commodity-info[data-v-7336ff23]{display:flex;align-items:center;justify-content:flex-start;width:100%}.commodity-info img[data-v-7336ff23]{width:68px;height:68px}.commodity-info .commodity-info-text[data-v-7336ff23]{width:217px;height:68px;margin-left:8px;display:flex;flex-direction:column;justify-content:space-between}.commodity-info .commodity-info-text p[data-v-7336ff23]{overflow:hidden;text-overflow:ellipsis;display:box;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.commodity-info .type_tag[data-v-7336ff23]{color:#999;font-size:14px;font-weight:400}.customer[data-v-7336ff23]{width:100%;display:flex;flex-direction:column;justify-content:space-around;font-size:14px;color:#333}.customer div[data-v-7336ff23]{display:flex;align-items:center}.customer__name[data-v-7336ff23]{display:inline-block;max-width:95px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:3px}.customer img[data-v-7336ff23]{width:16.25px;height:15.32px;margin-left:5px;cursor:pointer}
`,document.head.appendChild(be);const qe={class:"q_plugin_container"},He=e.defineComponent({__name:"ShopPurchaseList",setup(t){const o=e.ref("supply"),a=[{name:"supply",label:"货源"},{name:"purchaseOrder",label:"采购订单"},{name:"waitingPublish",label:"待发布"},{name:"release",label:"已发布"}],n=m=>{m&&(o.value=m)},s={supply:e.defineAsyncComponent(()=>Promise.resolve().then(()=>so)),purchaseOrder:e.defineAsyncComponent(()=>Promise.resolve().then(()=>La)),waitingPublish:e.defineAsyncComponent(()=>Promise.resolve().then(()=>va)),release:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Pn))};return(m,d)=>(e.openBlock(),e.createElementBlock("div",qe,[e.createVNode(de,{"tab-list":a,value:o.value,onHandleTabs:n},null,8,["value"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(s[o.value])))]))}}),je=t=>j.get({url:"addon-supplier/supplier/manager/product/getSupplyList",params:{...t}}),ke=t=>j.post({url:"gruul-mall-order/order/distribution/cost",data:t,showLoading:!1}),Ee=(t={})=>j.post({url:"addon-supplier/supplier/order",data:t}),Ge=t=>j.get({url:`addon-supplier/supplier/order/creation/${t}`}),Ye=(t={})=>j.put({url:"addon-supplier/supplier/order/pay",data:t}),We=t=>j.put({url:`addon-supplier/supplier/order/close/${t}`}),Te=t=>j.get({url:`addon-supplier/supplier/order/storage/${t}`}),Ke=t=>j.put({url:"addon-supplier/supplier/order/storage",data:t}),Xe=t=>j.put({url:`addon-supplier/supplier/order/complete/${t}`}),Je=t=>j.get({url:"addon-supplier/supplier/order/publish",params:t}),_e=(t,o)=>j.post({url:`gruul-mall-carrier-pigeon/pigeon/group-chat-rooms/${t}/${o}`}),Ze=t=>j.get({url:"gruul-mall-goods/manager/product/purchase/issue/products",params:t}),Qe=t=>j.put({url:`gruul-mall-goods/manager/product/purchase/issue/product/updateStatus/${t}`}),xe=(t={})=>j.get({url:"gruul-mall-shop/shop/info/getSupplierInfo",params:t}),ve=(t={})=>j.get({url:"addon-supplier/supplier/order",params:t}),Ie=(t,o,a=!1)=>j.get({url:`gruul-mall-storage/storage/shop/${t}/product/${o}?isSupplier=${a}`}),ye=()=>j.get({url:"gruul-mall-addon-platform/platform/shop/signing/category/choosable/list"}),De=()=>j.get({url:"gruul-mall-shop/shop/logistics/address/list",params:{current:1,size:1e3}}),et=t=>j.get({url:"addon-invoice/invoice/invoice-headers/pageInvoiceHeader",params:{...t,ownerType:"SHOP"}}),tt="addon-invoice/invoice/",ot=t=>j.post({url:`${tt}invoiceRequest`,data:t}),Pe=t=>j.get({url:"addon-invoice/invoice/invoiceSettings",params:t}),at=t=>j.get({url:`addon-invoice/invoice/invoiceRequest/${t}`}),nt=t=>j.get({url:"addon-invoice/invoice/invoiceRequest/pre-request",params:t}),lt=t=>j.put({url:`addon-invoice/invoice/invoiceRequest/${t}`}),rt=t=>j.post({url:"addon-invoice/invoice/invoiceAttachment/re-send",data:t}),$e=()=>j.get({url:"addon-invoice/invoice/invoice-headers/getDefaultInvoiceHeader",params:{invoiceHeaderOwnerType:"SHOP"}}),Be=t=>j.post({url:"addon-supplier/supplier/order/export",data:t}),st=e.defineComponent({__name:"search",emits:["search"],setup(t,{emit:o}){const a=e.ref([]),n=e.ref([]),s=e.reactive({supplierId:"",platformCategoryParentId:"",supplierGoodsName:""}),m=o,d=[{label:"商品名称",prop:"supplierGoodsName",valueType:"copy",fieldProps:{placeholder:"请输入商品名称"}},{label:"供应商名称",labelWidth:85,prop:"supplierId",valueType:"select",options:a,fieldProps:{placeholder:"请输入店铺名称",props:{expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:r=>{_(r)}}},{label:"平台类目",valueType:"cascader",options:n,fieldProps:{placeholder:"请选择平台类目",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"},onChange:r=>{(r==null?void 0:r.length)>1?s.platformCategoryParentId=r[r.length-1]:s.platformCategoryParentId=""}}}],c=async()=>{const{data:r,code:p}=await ye();n.value=r};e.onMounted(()=>c());const u=()=>{const r=he.cloneDeep(s);delete r.undefined,m("search",r)},_=async(r="")=>{var i;const p=await xe({supplierName:r});p.data&&((i=p.data)!=null&&i.length)&&(a.value=p.data.map(y=>({label:y.name,value:y.id})))},g=()=>{Object.keys(s).forEach(r=>s[r]=""),u()};return(r,p)=>(e.openBlock(),e.createBlock(ue,{modelValue:s,"onUpdate:modelValue":p[0]||(p[0]=i=>s=i),columns:d,"show-number":3,onSearchHandle:u,onHandleReset:g},null,8,["modelValue"]))}}),it=t=>{const o=e.ref([]),a=e.ref(0),n=1e4,s=e.computed(()=>{var h;return(h=t.value.storageSkus)==null?void 0:h.reduce((V,P)=>V+=P.purchaseNum||0,0)}),m=e.computed(()=>{var h;return((h=t.value.storageSkus)==null?void 0:h.reduce((V,P)=>{var k;const M=P.purchaseNum?(k=new v(P.salePrice))==null?void 0:k.div(1e4).mul(new v(P.purchaseNum)):new v(0);return new v(V).plus(M)},new v(0)))||new v(0)}),d=e.computed(()=>{var V;const h=((V=t.value.storageSkus)==null?void 0:V.map(P=>(P==null?void 0:P.stockType)==="UNLIMITED"?n:Number((P==null?void 0:P.stock)||0)))||[];return Math.min(...h)}),c=e.computed(()=>m.value.plus(new v(a.value))),u=h=>{var V;(V=t.value.storageSkus)==null||V.forEach(P=>P.purchaseNum=h)},_=async()=>{var V;let h=[];try{const P=await De();h=(V=P==null?void 0:P.data)==null?void 0:V.records}finally{o.value=h}},g=async()=>{var P;const h=o.value.find(M=>M.id===r.receiveId);if(!h)return;let V=0;try{const M=await ke({area:h.area,freeRight:!1,distributionMode:["EXPRESS"],address:h==null?void 0:h.address,shopFreights:[t.value].map(R=>{var O;return{shopId:R.shopId,freights:[{templateId:R.freightTemplateId,skuInfos:(O=R.storageSkus)==null?void 0:O.map(G=>({price:G.salePrice,skuId:G.id,num:G.purchaseNum||0,weight:G.weight}))}]}})});V=Object.values(((P=M==null?void 0:M.data)==null?void 0:P.EXPRESS)||{}).reduce((R,O)=>R+=O,0)}finally{a.value=V}},r=e.reactive({remark:"",receiveId:""}),p=e.computed(()=>({totalNum:s.value,receive:r.receiveId}));return e.watch(()=>p.value,()=>g()),{receiveList:o,freightTotal:a,maxUnlimitedNum:n,totalNum:s,totalPrice:m,maxBatchNum:d,totalOrderPrice:c,changeBatchPurchaseNum:u,fetchReceiveAddress:_,purchaseFormModel:r,getOrderConfig:()=>{const h=dt(o.value,r.receiveId),V="PURCHASE",P=r.remark,M=ct(t);return{receiver:h,sellType:V,remark:P,suppliers:M}},handleRemove:h=>{B.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var V;(V=t.value.storageSkus)==null||V.splice(h,1)})}}},ct=t=>{var a;const o={supplierId:t.value.shopId,productSkus:{}};return(a=t.value.storageSkus)==null||a.forEach(n=>{n.purchaseNum&&(o.productSkus[t.value.id],o.productSkus[t.value.id]||(o.productSkus[t.value.id]=[]),o.productSkus[t.value.id].push({skuId:n.id,num:n.purchaseNum}))}),[o]},dt=(t,o="")=>{const a=t.find(n=>n.id===o);return a?{name:a.contactName,mobile:a.contactPhone,area:a.area,address:a==null?void 0:a.address}:{name:"",mobile:"",area:[],address:""}},pt={class:"purchase"},mt={key:0,class:"purchase__batch"},ft={class:"commodity-info"},ht=["src"],ut={class:"purchase__total"},gt={class:"purchase__total--line"},_t={class:"purchase__total--line"},xt={class:"purchase__total--line"},yt={class:"purchase__total--line"},Nt=e.defineComponent({__name:"purchase",props:{lines:{default:()=>({id:"",albumPics:"",productName:"",salePrices:[],sellType:"PURCHASE",shopId:"",shopName:"",shopOwnProductStockNum:0,storageSkus:[]})}},emits:["update:lines"],setup(t,{expose:o,emit:a}){const n=e.ref(1),s=t,m=a,d=e.computed({get(){return s.lines},set(k){m("update:lines",k)}}),{maxUnlimitedNum:c,totalNum:u,freightTotal:_,totalPrice:g,maxBatchNum:r,totalOrderPrice:p,changeBatchPurchaseNum:i,receiveList:y,fetchReceiveAddress:h,purchaseFormModel:V,getOrderConfig:P,handleRemove:M}=it(d);return e.onMounted(()=>h()),o({getOrderConfig:P}),(k,R)=>{const O=e.resolveComponent("el-input-number"),G=e.resolveComponent("el-form-item"),Y=e.resolveComponent("el-form"),D=e.resolveComponent("el-table-column"),l=e.resolveComponent("el-button"),f=e.resolveComponent("el-table"),x=e.resolveComponent("el-option"),I=e.resolveComponent("el-select"),z=e.resolveComponent("el-input");return e.openBlock(),e.createElementBlock("div",pt,[d.value.storageSkus&&d.value.storageSkus.length>1?(e.openBlock(),e.createElementBlock("div",mt,[e.createVNode(Y,{"show-message":!1,inline:""},{default:e.withCtx(()=>[e.createVNode(G,{label:"采购数(批量)"},{default:e.withCtx(()=>[e.createVNode(O,{modelValue:n.value,"onUpdate:modelValue":R[0]||(R[0]=w=>n.value=w),max:+e.unref(r),min:0,onChange:e.unref(i)},null,8,["modelValue","max","onChange"])]),_:1})]),_:1})])):e.createCommentVNode("",!0),e.createVNode(f,{data:d.value.storageSkus,"max-height":350},{default:e.withCtx(()=>{var w;return[((w=d.value.storageSkus)==null?void 0:w.length)===1?(e.openBlock(),e.createBlock(D,{key:0,label:"商品名称","min-width":"250"},{default:e.withCtx(()=>{var $,K;return[e.createElementVNode("div",ft,[e.createElementVNode("img",{src:(K=($=d.value.albumPics)==null?void 0:$.split(","))==null?void 0:K.shift()},null,8,ht),e.createElementVNode("span",null,e.toDisplayString(d.value.productName),1)])]}),_:1})):(e.openBlock(),e.createBlock(D,{key:1,label:"商品规格","min-width":"250"},{default:e.withCtx(({row:$})=>{var K;return[e.createTextVNode(e.toDisplayString(((K=$==null?void 0:$.specs)==null?void 0:K.join(","))||"单规格"),1)]}),_:1})),e.createVNode(D,{align:"center",label:"供货价",width:"150"},{default:e.withCtx(({row:$})=>[e.createTextVNode(" ￥"+e.toDisplayString(($==null?void 0:$.salePrice)/1e4),1)]),_:1}),e.createVNode(D,{align:"center",label:"起批数",prop:"minimumPurchase",width:"80"}),e.createVNode(D,{align:"center",label:"供应商库存",width:"100"},{default:e.withCtx(({row:$})=>[e.createTextVNode(e.toDisplayString(($==null?void 0:$.stockType)==="UNLIMITED"?"无限库存":$.stock),1)]),_:1}),e.createVNode(D,{align:"center",label:"自有库存",prop:"shopOwnProductStockNum",width:"80"}),e.createVNode(D,{align:"center",label:"采购数",width:"180"},{default:e.withCtx(({row:$})=>[e.createVNode(O,{modelValue:$.purchaseNum,"onUpdate:modelValue":K=>$.purchaseNum=K,max:($==null?void 0:$.stockType)==="UNLIMITED"?+e.unref(c):+$.stock,min:+$.minimumPurchase,placeholder:"请输入",precision:0},null,8,["modelValue","onUpdate:modelValue","max","min"])]),_:1}),e.createVNode(D,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:$})=>[e.createVNode(l,{link:"",size:"small",type:"danger",onClick:K=>e.unref(M)($)},{default:e.withCtx(()=>R[3]||(R[3]=[e.createTextVNode("移除")])),_:2},1032,["onClick"])]),_:1})]}),_:1},8,["data"]),e.createVNode(Y,{"show-message":!1,class:"purchase__remark"},{default:e.withCtx(()=>[e.createVNode(G,{label:"收货人信息"},{default:e.withCtx(()=>[e.createVNode(I,{modelValue:e.unref(V).receiveId,"onUpdate:modelValue":R[1]||(R[1]=w=>e.unref(V).receiveId=w),style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(y),w=>(e.openBlock(),e.createBlock(x,{key:w.id,label:w.contactName+"  "+w.contactPhone+"   "+w.area.join("")+"   "+w.address,value:w.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(G,{label:"采购备注"},{default:e.withCtx(()=>[e.createVNode(z,{modelValue:e.unref(V).remark,"onUpdate:modelValue":R[2]||(R[2]=w=>e.unref(V).remark=w),placeholder:"请输入采购备注(100字以内)",maxlength:"100",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createElementVNode("div",ut,[R[4]||(R[4]=e.createElementVNode("div",{class:"purchase__total--title"},"订单合计",-1)),e.createElementVNode("div",gt,"采购数量："+e.toDisplayString(e.unref(u)),1),e.createElementVNode("div",_t,"商品总价："+e.toDisplayString(e.unref(g)),1),e.createElementVNode("div",xt,"运费："+e.toDisplayString(e.unref(_).toFixed(2)),1),e.createElementVNode("div",yt,"采购金额(应付款)：￥"+e.toDisplayString(e.unref(p)),1)])])}}}),On="",oe=(t,o)=>{const a=t.__vccOpts||t;for(const[n,s]of o)a[n]=s;return a},Ct=oe(Nt,[["__scopeId","data-v-7ad0cf7f"]]),Vt=t=>{const o=e.reactive({page:1,pageSize:5,total:0}),a=e.reactive({remark:"",receiveId:""}),n=e.ref(0),s=t,m=e.ref(0),d=e.ref([]),c=e.ref([]),u=(l,f)=>{d.value=f.map(x=>x.id)},_=l=>{e.nextTick(()=>{var I,z;const f=s.value.findIndex(w=>w.id===l),x=(z=(I=s.value[f])==null?void 0:I.storageSkus)==null?void 0:z.reduce((w,$)=>w+Number($.purchaseNum||0),0);s.value[f].totalPurchase=x})},g=e.computed(()=>l=>{var x;let f=0;return(x=l==null?void 0:l.skus)==null||x.forEach(I=>{I.lotStartingNum>f&&(f=I.lotStartingNum)}),+f}),r=e.computed(()=>l=>{var x,I,z,w,$;if(((I=(x=l==null?void 0:l.storageSkus)==null?void 0:x[0])==null?void 0:I.stockType)==="UNLIMITED")return 1e5;let f=(w=(z=l==null?void 0:l.skus)==null?void 0:z[0])==null?void 0:w.supplierInventory;return($=l==null?void 0:l.skus)==null||$.forEach(K=>{K.supplierInventory<f&&(f=K.supplierInventory)}),+f}),p=e.computed(()=>l=>{const f=((l==null?void 0:l.salePrices)||[]).map(z=>Number(z)),x=Math.min(...f),I=Math.max(...f);return x===I?I/1e4:`${x/1e4}~${I/1e4}`}),i=l=>{var f;return(f=l==null?void 0:l.storageSkus)==null?void 0:f.reduce((x,I)=>x+Number((I==null?void 0:I.shopOwnProductStockNum)||0),0)},y=e.computed(()=>s.value.reduce((l,f)=>{var x;return l+(((x=f.storageSkus)==null?void 0:x.reduce((I,z)=>I+=z.purchaseNum||0,0))||0)},0)),h=e.computed(()=>s.value.reduce((l,f)=>{var x;return l.add(((x=f.storageSkus)==null?void 0:x.reduce((I,z)=>{var $;const w=z.purchaseNum?($=new v(z.salePrice))==null?void 0:$.div(1e4).mul(new v(z.purchaseNum||0)):new v(0);return new v(I).plus(w)},new v(0)))||new v(0))},new v(0))),V=e.computed(()=>h.value.add(new v(m.value))),P=e.computed(()=>l=>{var f;return((f=l==null?void 0:l[0])==null?void 0:f.stockType)==="UNLIMITED"?"无限库存":l==null?void 0:l.reduce((x,I)=>x+=Number(I.stock),0)}),M=(l,f)=>{var x,I;if(typeof f=="number"){const z=s.value.findIndex(w=>w.id===l);z>-1&&((I=(x=s.value[z])==null?void 0:x.storageSkus)==null||I.forEach(w=>{w.purchaseNum=f}),_(l))}},k=async()=>{var x;const l=c.value.find(I=>I.id===a.receiveId);if(!l)return;let f=0;try{const I=await ke({area:l.area,freeRight:!1,distributionMode:["EXPRESS"],address:l==null?void 0:l.address,shopFreights:s.value.map(w=>{var $;return{shopId:w.shopId,freights:[{templateId:w.freightTemplateId,skuInfos:($=w.storageSkus)==null?void 0:$.map(K=>({price:K.salePrice,skuId:K.id,num:K.purchaseNum||0,weight:K.weight}))}]}})});f=Object.values(((x=I==null?void 0:I.data)==null?void 0:x.EXPRESS)||{}).reduce((w,$)=>w+=$,0)}finally{m.value=f}},R=async()=>{var f;let l=[];try{const x=await De();l=(f=x==null?void 0:x.data)==null?void 0:f.records}finally{c.value=l}},O=()=>{const l=bt(c.value,a.receiveId),f="PURCHASE",x=a.remark,I=St(s);return{receiver:l,sellType:f,remark:x,suppliers:I}},G=l=>{B.ElMessageBox.confirm("确认移除当前商品信息").then(()=>{t.value.splice(l,1),o.total=t.value.length,n.value=Date.now()})},Y=(l,f,x)=>{B.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var I,z,w;(w=(z=(I=t.value)==null?void 0:I[f])==null?void 0:z.storageSkus)==null||w.splice(l,1),t.value=t.value.filter($=>$.storageSkus&&$.storageSkus.length>0),e.nextTick(()=>{o.total=t.value.length}),_(x),n.value=Date.now()})},D=e.computed(()=>({totalNum:y.value,receive:a.receiveId}));return e.watch(()=>D.value,()=>k()),{totalPrice:h,totalNum:y,totalOrderPrice:V,freightTotal:m,purchaseFormModel:a,tableData:s,expandRowKeys:d,expandOpen:u,handleChangePurchaseNum:_,minRowBatchNum:g,maxRowBatchNum:r,computedSalePrice:p,computedShopOwnProductStockNum:i,computedSuplier:P,changeRowBatchNum:M,getFreightCount:k,receiveList:c,fetchReceiveAddress:R,getOrderConfig:O,handleRemoveBatch:G,handleRemoveSku:Y,refreshKey:n,paginationOptions:o}},St=t=>{const o=[];return t.value.forEach(a=>{var s;let n=o.findIndex(m=>m.supplierId===a.shopId);n===-1&&(n=o.push({supplierId:a.shopId,productSkus:{}})-1),(s=a.storageSkus)==null||s.forEach(m=>{m.purchaseNum&&(o[n].productSkus[a.id],o[n].productSkus[a.id]||(o[n].productSkus[a.id]=[]),o[n].productSkus[a.id].push({skuId:m.id,num:m.purchaseNum}))})}),o.filter(a=>Object.keys(a==null?void 0:a.productSkus).length>0)},bt=(t,o="")=>{const a=t.find(n=>n.id===o);return a?{name:a.contactName,mobile:a.contactPhone,area:a.area,address:a==null?void 0:a.address}:{name:"",mobile:"",area:[],address:""}},kt={class:"oneClickProcurement"},Et={class:"oneClickProcurement__expand"},Tt={class:"oneClickProcurement__form"},It={class:"oneClickProcurement__commodity"},Dt=["src"],Pt={class:"oneClickProcurement__commodity--name"},$t={class:"oneClickProcurement__pagination"},Bt={class:"oneClickProcurement__total"},Lt={class:"oneClickProcurement__total--line"},wt={class:"oneClickProcurement__total--line"},Ot={class:"oneClickProcurement__total--line"},Rt={class:"oneClickProcurement__total--line"},Mt=1e5,At=e.defineComponent({__name:"batch-purchase",props:{lines:{default:()=>[]}},emits:["update:lines"],setup(t,{expose:o,emit:a}){const n=t,s=e.computed({get(){return n.lines},set(Z){I("update:lines",Z)}}),m=e.computed(()=>s.value.filter((Z,X)=>X>=(x.page-1)*x.pageSize&&X<x.page*x.pageSize)),d=Z=>Z.storageSkus.find(N=>N.stockType==="UNLIMITED")?"UNLIMITED":Z.storageSkus.some(N=>N.stock>0)?"STOCK":!1,c=(Z,X)=>{if(!Z)return 0;if(Z==="UNLIMITED")return"无限库存";if(Z==="STOCK")return M.value(X==null?void 0:X.storageSkus)},{receiveList:u,fetchReceiveAddress:_,purchaseFormModel:g,expandRowKeys:r,expandOpen:p,handleChangePurchaseNum:i,minRowBatchNum:y,maxRowBatchNum:h,computedSalePrice:V,computedShopOwnProductStockNum:P,computedSuplier:M,changeRowBatchNum:k,freightTotal:R,totalNum:O,totalPrice:G,totalOrderPrice:Y,getOrderConfig:D,handleRemoveBatch:l,handleRemoveSku:f,paginationOptions:x}=Vt(s),I=a,z=Z=>{x.pageSize=Z},w=Z=>{x.page=Z};e.onMounted(()=>{x.total=s.value.length,_()});const $=e.ref(),K=Z=>{$.value.toggleRowExpansion(Z)};return o({getOrderConfig:D}),(Z,X)=>{const T=e.resolveComponent("el-option"),N=e.resolveComponent("el-select"),U=e.resolveComponent("el-form-item"),q=e.resolveComponent("el-input"),H=e.resolveComponent("el-form"),le=e.resolveComponent("el-input-number"),F=e.resolveComponent("el-table-column"),L=e.resolveComponent("el-link"),te=e.resolveComponent("el-table"),ee=e.resolveComponent("el-pagination");return e.openBlock(),e.createElementBlock("div",kt,[e.createVNode(H,{"show-message":!1,class:"oneClickProcurement__remark"},{default:e.withCtx(()=>[e.createVNode(U,{label:"收货人信息"},{default:e.withCtx(()=>[e.createVNode(N,{modelValue:e.unref(g).receiveId,"onUpdate:modelValue":X[0]||(X[0]=b=>e.unref(g).receiveId=b),style:{width:"100%"}},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(u),b=>(e.openBlock(),e.createBlock(T,{key:b.id,label:b.contactName+"  "+b.contactPhone+"   "+b.area.join("")+"   "+b.address,value:b.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e.createVNode(U,{label:"采购备注"},{default:e.withCtx(()=>[e.createVNode(q,{modelValue:e.unref(g).remark,"onUpdate:modelValue":X[1]||(X[1]=b=>e.unref(g).remark=b),placeholder:"请输入采购备注(100字以内)",maxlength:"100",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(te,{ref_key:"expandableTable",ref:$,data:m.value,"expand-row-keys":e.unref(r),"row-key":"id","max-height":"450",onExpandChange:e.unref(p),onRowClick:K},{default:e.withCtx(()=>[e.createVNode(F,{type:"expand"},{default:e.withCtx(({row:b,$index:E})=>[e.createElementVNode("div",Et,[e.createElementVNode("div",Tt,[e.createVNode(H,null,{default:e.withCtx(()=>[e.createVNode(U,{label:"采购数(批量)"},{default:e.withCtx(()=>[e.createVNode(le,{modelValue:b.batchNum,"onUpdate:modelValue":C=>b.batchNum=C,max:e.unref(h)(b),min:e.unref(y)(b),precision:0,onChange:C=>e.unref(k)(b.id,C)},null,8,["modelValue","onUpdate:modelValue","max","min","onChange"])]),_:2},1024)]),_:2},1024)]),e.createVNode(te,{data:b.storageSkus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(F,{label:"商品规格"},{default:e.withCtx(({row:C})=>{var A;return[e.createTextVNode(e.toDisplayString(((A=C==null?void 0:C.specs)==null?void 0:A.join(","))||"单规格"),1)]}),_:1}),e.createVNode(F,{label:"供货价",width:"130"},{default:e.withCtx(({row:C})=>[e.createTextVNode(" ￥"+e.toDisplayString((C==null?void 0:C.salePrice)/1e4),1)]),_:1}),e.createVNode(F,{label:"起批数",prop:"minimumPurchase",width:"80"}),e.createVNode(F,{label:"供应商库存",prop:"stock",width:"100"},{default:e.withCtx(({row:C})=>[e.createTextVNode(e.toDisplayString((C==null?void 0:C.stockType)==="UNLIMITED"?"无限库存":C.stock),1)]),_:1}),e.createVNode(F,{label:"自有库存",prop:"shopOwnProductStockNum",width:"80"},{default:e.withCtx(({row:C})=>[e.createTextVNode(e.toDisplayString(C==null?void 0:C.shopOwnProductStockNum),1)]),_:1}),e.createVNode(F,{label:"采购数",prop:"purchaseNum"},{default:e.withCtx(({row:C})=>[e.createVNode(le,{modelValue:C.purchaseNum,"onUpdate:modelValue":A=>C.purchaseNum=A,max:C.stockType==="UNLIMITED"?+Mt:+C.stock,min:+C.minimumPurchase,precision:0,onChange:A=>e.unref(i)(b.id)},null,8,["modelValue","onUpdate:modelValue","max","min","onChange"])]),_:2},1024),e.createVNode(F,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:C})=>[e.createVNode(L,{type:"danger",onClick:A=>e.unref(f)(C,E,b.id)},{default:e.withCtx(()=>X[2]||(X[2]=[e.createTextVNode("移出")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])])]),_:1}),e.createVNode(F,{label:"供应商",prop:"supplierName"}),e.createVNode(F,{label:"商品名称",width:"250"},{default:e.withCtx(({row:b})=>{var E;return[e.createElementVNode("div",It,[e.createElementVNode("img",{src:(E=b.albumPics)==null?void 0:E.split(",").shift()},null,8,Dt),e.createElementVNode("span",Pt,e.toDisplayString(b.productName),1)])]}),_:1}),e.createVNode(F,{label:"供货价"},{default:e.withCtx(({row:b})=>[e.createTextVNode("￥"+e.toDisplayString(e.unref(V)(b)),1)]),_:1}),e.createVNode(F,{label:"供应商库存"},{default:e.withCtx(({row:b})=>[e.createTextVNode(e.toDisplayString(c(d(b),b)),1)]),_:1}),e.createVNode(F,{label:"自有库存",prop:"shopOwnProductStockNum"},{default:e.withCtx(({row:b})=>[e.createTextVNode(e.toDisplayString(e.unref(P)(b)),1)]),_:1}),e.createVNode(F,{label:"采购数",prop:"totalPurchase"}),e.createVNode(F,{label:"操作"},{default:e.withCtx(({$index:b})=>[e.createVNode(L,{type:"danger",onClick:E=>e.unref(l)(b)},{default:e.withCtx(()=>X[3]||(X[3]=[e.createTextVNode("移出")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","expand-row-keys","onExpandChange"]),e.createElementVNode("div",$t,[e.createVNode(ee,{"current-page":e.unref(x).page,"page-size":e.unref(x).pageSize,"page-sizes":[5,10,15,20],total:e.unref(x).total,layout:"total, prev, pager, next, sizes",onSizeChange:z,onCurrentChange:w},null,8,["current-page","page-size","total"])]),e.createElementVNode("div",Bt,[X[4]||(X[4]=e.createElementVNode("div",{class:"oneClickProcurement__total--title"},"订单合计",-1)),e.createElementVNode("div",Lt,"采购数量："+e.toDisplayString(e.unref(O)),1),e.createElementVNode("div",wt,"商品总价："+e.toDisplayString(e.unref(G)),1),e.createElementVNode("div",Ot,"运费："+e.toDisplayString(e.unref(R).toFixed(2)),1),e.createElementVNode("div",Rt,"采购金额(应付款)：￥"+e.toDisplayString(e.unref(Y)),1)])])}}}),Mn="",zt=oe(At,[["__scopeId","data-v-3d4987c0"]]),Ut={class:"details"},Ft={class:"details__line"},qt=["src"],Ht={class:"details__line"},jt=e.defineComponent({__name:"details",props:{detailsInfo:{type:Object,default:()=>({})}},setup(t){const{divTenThousand:o}=ie(),a=t;return(n,s)=>{var c,u,_,g,r;const m=e.resolveComponent("el-table-column"),d=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Ut,[e.createElementVNode("div",Ft,[s[0]||(s[0]=e.createElementVNode("span",{class:"details__line--label"},"商品图片：",-1)),e.createElementVNode("img",{src:(_=(u=(c=a.detailsInfo)==null?void 0:c.albumPics)==null?void 0:u.split(","))==null?void 0:_.shift(),class:"details__img"},null,8,qt)]),e.createElementVNode("div",Ht,"商品名称："+e.toDisplayString((g=a.detailsInfo)==null?void 0:g.productName),1),e.createVNode(d,{data:((r=a.detailsInfo)==null?void 0:r.storageSkus)||[]},{default:e.withCtx(()=>[e.createVNode(m,{label:"商品规格"},{default:e.withCtx(({row:p})=>{var i,y;return[e.createTextVNode(e.toDisplayString((i=p==null?void 0:p.specs)!=null&&i.length?(y=p==null?void 0:p.specs)==null?void 0:y.join(","):"单规格"),1)]}),_:1}),e.createVNode(m,{align:"center",label:"供货价",width:"150"},{default:e.withCtx(({row:p})=>[e.createTextVNode(" ￥"+e.toDisplayString(e.unref(o)(p==null?void 0:p.salePrice)),1)]),_:1}),e.createVNode(m,{align:"center",label:"起批数",width:"150",prop:"minimumPurchase"}),e.createVNode(m,{align:"center",label:"供应商库存",width:"150",prop:"stock"}),e.createVNode(m,{align:"center",label:"自有库存",width:"150",prop:"shopOwnProductStockNum"})]),_:1},8,["data"])])}}}),An="",Gt=oe(jt,[["__scopeId","data-v-82366355"]]),Yt=()=>{const t=e.ref(!1),o=e.ref([]),a=d=>{var c;return(c=d.storageSkus)==null?void 0:c.map(u=>({...u,purchaseNum:1}))},n=(d=[])=>{o.value=d.map(c=>({...c,batchNum:1,totalPurchase:c.storageSkus.length,storageSkus:a(c)}))},s=(d=[])=>{o.value=d.map(c=>({...c,batchNum:1,totalPurchase:c.storageSkus.length,storageSkus:a(c)})),t.value=!0},m=e.ref(!1);return{batchPurchaseLines:o,showBatchDialog:t,openBatchDialog:s,setBatchPurchaseLines:n,batchDialogConfirmLoading:m}},Wt={style:{display:"flex","align-items":"center",padding:"16px 16px 0 16px"}},Kt={key:0,style:{color:"#999","margin-left":"8px"}},Xt={class:"table_container"},Jt={class:"commodity-info"},Zt=["src"],Qt={class:"name fdc"},vt={key:1},eo={class:"type_tag"},to={style:{color:"#f54319"}},oo={class:"customer"},ao={class:"customer__name"},no={class:"dialog-footer"},lo={class:"dialog-footer"},ro=e.defineComponent({__name:"index",setup(t){const o=(T,N)=>T.storageSkus?T.storageSkus.find(H=>H.stockType==="UNLIMITED")?"UNLIMITED":T.storageSkus.some(H=>Number(H.stock)>0)?"STOCK":!1:!1,a=(T,N)=>{if(!T)return 0;if(T==="UNLIMITED")return"无限库存";if(T==="STOCK")return k.value(N==null?void 0:N.storageSkus)},n=e.ref(),s=e.ref(!1),m=e.ref(),d=e.computed(()=>{var T,N;return((N=(T=m.value)==null?void 0:T.getSelectionRows)==null?void 0:N.call(T))||[]}),c=e.ref({}),u=e.ref([]),_=e.ref(!1),g=pe.useRouter(),r=pe.useRoute(),p=e.reactive({page:{current:1,size:10},total:0});r.query.supplierGoodsName&&(c.value.supplierGoodsName=r.query.supplierGoodsName);const i=(T={})=>{c.value=T,p.page.current=1,h()},y=e.ref(),h=async()=>{const T=await je({...p.page,sellType:"PURCHASE",...c.value});T.data&&(p.total=Number(T.data.total),u.value=T.data.records.map(N=>({...N,disabled:!o(N)})))};h();const V=T=>{var N,U,q;if(T)y.value={...T,purchaseNum:1,storageSkus:((U=(N=T.storageSkus)==null?void 0:N.map(H=>({...H,purchaseNum:1})))==null?void 0:U.filter(H=>H.stockType==="LIMITED"&&Number(H.stock||0)>=H.minimumPurchase||H.stockType==="UNLIMITED"))||[]},_.value=!0;else{if(d.value.length===0){B.ElMessage.error({message:"请选择需要采购的商品"});return}D((q=d.value)==null?void 0:q.map(H=>{var le;return{...H,storageSkus:(le=H.storageSkus)==null?void 0:le.filter(F=>F.stockType==="LIMITED"&&Number(F.stock||0)>F.minimumPurchase||F.stockType==="UNLIMITED")}}))}},P=T=>{_e(ce.useShopInfoStore().shopInfo.id,T.shopId).then(({code:N})=>{N===200&&g.push({path:"/message/customer/supplierService",query:{id:T.shopId}})})},M=e.computed(()=>T=>{const N=Math.min(...T.storageSkus.map(q=>Number(q.salePrice))),U=Math.max(...T.storageSkus.map(q=>Number(q.salePrice)));return N===U?U/1e4:`${N/1e4}~￥${U/1e4}`}),k=e.computed(()=>T=>{var N;return((N=T==null?void 0:T[0])==null?void 0:N.stockType)==="UNLIMITED"?"无限库存":T==null?void 0:T.reduce((U,q)=>U+=Number(q.stock),0)}),R=e.ref(null),O=e.ref(!1),G=e.ref(null),{showBatchDialog:Y,openBatchDialog:D,batchPurchaseLines:l,batchDialogConfirmLoading:f}=Yt();let x=0;const I=async()=>{var U,q,H;O.value=!0,x=0;const T=(U=R.value)==null?void 0:U.getOrderConfig();if(!(T!=null&&T.receiver.mobile))return O.value=!1,B.ElMessage.error("请选择收货人信息");const N=await Ee(T);N.code===200?(q=N.data)!=null&&q.mainNo&&w((H=N.data)==null?void 0:H.mainNo):N.code===210004?(B.ElMessage.error(N.msg),h(),z()):B.ElMessage.error(N.msg||"采购失败")},z=()=>{_.value=!1,Y.value=!1,O.value=!1,f.value=!1,h()},w=async T=>{if(x++,x===20){O.value=!1,f.value=!1,B.ElMessage.error("创建失败，请稍后重试");return}const N=await Ge(T);console.log(N),N.data?(B.ElMessage.success("订单提交成功，请在采购订单中支付货款"),z()):setTimeout(()=>{w(T)},1e3)},$=async()=>{var U,q,H;f.value=!0;const T=(U=G.value)==null?void 0:U.getOrderConfig();if(!(T!=null&&T.receiver.mobile))return f.value=!1,B.ElMessage.error("请选择收货人信息");const N=await Ee(T);N.code===200?(q=N.data)!=null&&q.mainNo&&w((H=N.data)==null?void 0:H.mainNo):N.code===210004?(B.ElMessage.error(N.msg),h(),z()):B.ElMessage.error(N.msg||"采购失败")},K=T=>{n.value=T,s.value=!0},Z=T=>{p.page.size=T,h()},X=T=>{p.page.current=T,h()};return(T,N)=>{const U=e.resolveComponent("el-button"),q=e.resolveComponent("el-table-column"),H=e.resolveComponent("el-tooltip"),le=e.resolveComponent("el-table"),F=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(st,{onSearch:i}),e.createElementVNode("div",Wt,[e.createVNode(U,{type:"primary",onClick:N[0]||(N[0]=L=>V())},{default:e.withCtx(()=>N[8]||(N[8]=[e.createTextVNode("一键采购")])),_:1}),d.value.length?(e.openBlock(),e.createElementBlock("span",Kt,"已选"+e.toDisplayString(d.value.length)+"条 ",1)):e.createCommentVNode("",!0)]),e.createElementVNode("div",Xt,[e.createVNode(le,{ref_key:"tableRef",ref:m,data:u.value,"header-cell-style":{background:"#F7F8FA",height:"48px",color:"#333"},"cell-style":{color:"#333333"}},{empty:e.withCtx(()=>[e.createVNode(Ve)]),default:e.withCtx(()=>[e.createVNode(q,{type:"selection",width:"40",selectable:o,fixed:"left"}),e.createVNode(q,{label:"商品",width:"350"},{default:e.withCtx(({row:L})=>{var te,ee;return[e.createElementVNode("div",Jt,[e.createElementVNode("img",{src:(ee=(te=L.albumPics)==null?void 0:te.split(","))==null?void 0:ee.shift()},null,8,Zt),e.createElementVNode("div",Qt,[(L==null?void 0:L.productName.length)>=35?(e.openBlock(),e.createBlock(H,{key:0,class:"box-item",effect:"dark",content:L==null?void 0:L.productName,placement:"top"},{default:e.withCtx(()=>[e.createElementVNode("p",null,e.toDisplayString(L==null?void 0:L.productName),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("p",vt,e.toDisplayString(L.productName),1)),e.createElementVNode("div",eo,e.toDisplayString(L.productType==="REAL_PRODUCT"?"实物商品":"虚拟商品"),1)])])]}),_:1}),e.createVNode(q,{label:"供货价",width:"180"},{default:e.withCtx(({row:L})=>[e.createElementVNode("span",to," ￥"+e.toDisplayString(M.value(L)),1)]),_:1}),e.createVNode(q,{label:"供应商库存",width:"120"},{default:e.withCtx(({row:L})=>[e.createTextVNode(e.toDisplayString(a(o(L),L)),1)]),_:1}),e.createVNode(q,{label:"自有库存",prop:"shopOwnProductStockNum",width:"100"},{default:e.withCtx(({row:L})=>{var te;return[e.createTextVNode(e.toDisplayString((te=L==null?void 0:L.storageSkus)==null?void 0:te.reduce((ee,b)=>ee+Number((b==null?void 0:b.shopOwnProductStockNum)||0),0)),1)]}),_:1}),e.createVNode(q,{label:"所属供应商",prop:"supplierName"},{default:e.withCtx(({row:L})=>[e.createElementVNode("div",oo,[e.createElementVNode("div",null,[e.createElementVNode("span",ao,e.toDisplayString(L.supplierName),1),e.createVNode(me,{name:"icon-xiaoxi-copy",size:"18px",style:{cursor:"pointer"},svg:"",onClick:te=>P(L)},null,8,["onClick"])]),e.createElementVNode("span",null,e.toDisplayString(L.supplierContractNumber),1)])]),_:1}),e.createVNode(q,{label:"操作",width:"210",fixed:"right",align:"right"},{default:e.withCtx(({row:L})=>[e.createElementVNode("div",null,[e.createVNode(U,{link:"",type:"primary",size:"small",onClick:te=>K(L)},{default:e.withCtx(()=>N[9]||(N[9]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),e.createVNode(U,{link:"",size:"small",type:"primary",disabled:!o(L),onClick:te=>V(L)},{default:e.withCtx(()=>N[10]||(N[10]=[e.createTextVNode("一键采购")])),_:2},1032,["disabled","onClick"])])]),_:1})]),_:1},8,["data"])]),e.createVNode(ge,{"page-size":p.page.size,"page-num":p.page.current,total:p.total,onHandleSizeChange:Z,onHandleCurrentChange:X},null,8,["page-size","page-num","total"]),e.createVNode(F,{modelValue:_.value,"onUpdate:modelValue":N[3]||(N[3]=L=>_.value=L),"close-on-click-modal":!1,"destroy-on-close":"",title:"一键采购",width:"1000px"},{footer:e.withCtx(()=>[e.createElementVNode("span",no,[e.createVNode(U,{onClick:N[2]||(N[2]=L=>_.value=!1)},{default:e.withCtx(()=>N[11]||(N[11]=[e.createTextVNode("取消")])),_:1}),e.createVNode(U,{loading:O.value,type:"primary",onClick:I},{default:e.withCtx(()=>N[12]||(N[12]=[e.createTextVNode(" 确认 ")])),_:1},8,["loading"])])]),default:e.withCtx(()=>[e.createVNode(Ct,{ref_key:"purchaseRef",ref:R,lines:y.value,"onUpdate:lines":N[1]||(N[1]=L=>y.value=L)},null,8,["lines"])]),_:1},8,["modelValue"]),e.createVNode(F,{modelValue:e.unref(Y),"onUpdate:modelValue":N[6]||(N[6]=L=>e.isRef(Y)?Y.value=L:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"一键采购",top:"8vh",width:"1000px"},{footer:e.withCtx(()=>[e.createElementVNode("span",lo,[e.createVNode(U,{onClick:N[5]||(N[5]=L=>Y.value=!1)},{default:e.withCtx(()=>N[13]||(N[13]=[e.createTextVNode("取消")])),_:1}),e.createVNode(U,{loading:e.unref(f),type:"primary",onClick:$},{default:e.withCtx(()=>N[14]||(N[14]=[e.createTextVNode(" 确认 ")])),_:1},8,["loading"])])]),default:e.withCtx(()=>[e.createVNode(zt,{ref_key:"batchPurchaseRef",ref:G,lines:e.unref(l),"onUpdate:lines":N[4]||(N[4]=L=>e.isRef(l)?l.value=L:null)},null,8,["lines"])]),_:1},8,["modelValue"]),e.createVNode(F,{modelValue:s.value,"onUpdate:modelValue":N[7]||(N[7]=L=>s.value=L),"close-on-click-modal":!1,"destroy-on-close":"",title:"商品详情",width:"1000px"},{default:e.withCtx(()=>[e.createVNode(Gt,{"details-info":n.value},null,8,["details-info"])]),_:1},8,["modelValue"])],64)}}}),Un="",so=Object.freeze(Object.defineProperty({__proto__:null,default:oe(ro,[["__scopeId","data-v-44e56da8"]])},Symbol.toStringTag,{value:"Module"})),io=e.defineComponent({__name:"search",emits:["search","supplierExport"],setup(t,{emit:o}){const a=e.ref([]),n=o,s=e.reactive({supplierId:"",no:"",date:""}),m=[{label:"订单号",prop:"no",valueType:"copy",fieldProps:{placeholder:"请输入订单号"}},{label:"供应商名称",labelWidth:85,prop:"supplierId",valueType:"select",options:a,fieldProps:{placeholder:"请输入店铺名称",props:{expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:g=>{c(g)}}},{label:"下单时间",prop:"date",valueType:"date-picker",fieldProps:{type:"daterange",startPlaceholder:"开始时间",endPlaceholder:"结束时间",format:"YYYY/MM/DD",valueFormat:"YYYY-MM-DD"}}],d=()=>{var r,p;const g=he.cloneDeep(s);g.startTime=Array.isArray(g.date)?(r=g.date)==null?void 0:r[0]:void 0,g.endTime=Array.isArray(g.date)?(p=g.date)==null?void 0:p[1]:void 0,delete g.date,n("search",g)},c=async(g="")=>{var p;const r=await xe({supplierName:g});r.data&&((p=r.data)!=null&&p.length)&&(a.value=r.data.map(i=>({label:i.name,value:i.id})))},u=()=>{Object.keys(s).forEach(g=>s[g]=""),d()},_=()=>{n("supplierExport",s)};return(g,r)=>{const p=e.resolveComponent("el-button");return e.openBlock(),e.createBlock(ue,{modelValue:s,"onUpdate:modelValue":r[0]||(r[0]=i=>s=i),columns:m,"show-number":3,onSearchHandle:d,onHandleReset:u},{otherOperations:e.withCtx(()=>[e.createVNode(p,{class:"from_btn",round:"",type:"primary",onClick:_},{default:e.withCtx(()=>r[1]||(r[1]=[e.createTextVNode("导出")])),_:1})]),_:1},8,["modelValue"])}}}),Le={UNPAID:"待支付",PAYMENT_AUDIT:"待审核",WAITING_FOR_DELIVER:"待发货",WAITING_FOR_PUTIN:"待入库",FINISHED:"已完成",CLOSED:"已关闭"};function co(t,o=1e3,a={}){const{immediate:n=!0,immediateCallback:s=!1}=a;let m=null;const d=e.ref(!1);function c(){m&&(clearInterval(m),m=null)}function u(){d.value=!1,c()}function _(){o<=0||(d.value=!0,s&&t(),c(),m=setInterval(t,o))}return n&&_(),{isActive:d,pause:u,resume:_,clean:c}}const po=(t,o)=>{const a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"});if(!o)return{...e.toRefs(a)};const m=new Date(t.replaceAll("-","/")).getTime()+parseInt(o)*1e3-new Date().getTime(),{day:d,hours:c,minutes:u,seconds:_}=mo(m);return a.day=d,a.hours=c,a.minutes=u,a.seconds=_,{...e.toRefs(a)}},mo=t=>{let s=0,m=0,d=0,c=0;for(;t>864e5;)t-=864e5,s++;for(;t>36e5;)t-=36e5,m++;for(;t>6e4;)t-=6e4,d++;for(;t>1e3;)t-=1e3,c++;return{day:String(s).padStart(2,"0"),hours:String(m).padStart(2,"0"),minutes:String(d).padStart(2,"0"),seconds:String(c).padStart(2,"0")}},fo={class:"count-down"},ho={key:0},uo=e.defineComponent({__name:"index",props:{createTime:{default:""},payTimeout:{default:""}},setup(t){const o=t,a=e.reactive({day:"00",hours:"00",minutes:"00",seconds:"00"}),n=e.ref(!0),{clean:s}=co(()=>{const{day:m,hours:d,minutes:c,seconds:u}=po(o.createTime,o.payTimeout);a.day=m.value,a.hours=d.value,a.minutes=c.value,a.seconds=u.value,m.value==="00"&&d.value==="00"&&c.value==="00"&&u.value==="00"?(n.value=!0,e.nextTick(()=>s())):n.value=!1},1e3,{immediate:!0,immediateCallback:!0});return e.onBeforeUnmount(()=>s()),(m,d)=>(e.openBlock(),e.createElementBlock("div",null,[d[0]||(d[0]=e.createElementVNode("span",null,"(",-1)),e.createElementVNode("span",fo,[a.day!=="00"?(e.openBlock(),e.createElementBlock("span",ho,e.toDisplayString(a.day)+"天",1)):e.createCommentVNode("",!0),e.createElementVNode("span",null,e.toDisplayString(a.hours)+":"+e.toDisplayString(a.minutes)+":"+e.toDisplayString(a.seconds),1)]),d[1]||(d[1]=e.createElementVNode("span",null,")",-1))]))}}),qn="",go=oe(uo,[["__scopeId","data-v-92288512"]]),_o=()=>{const t=e.ref(!1),o=e.ref("");return{showProof:t,goToShowProof:n=>{var s,m;o.value=((m=(s=n==null?void 0:n.extra)==null?void 0:s.pay)==null?void 0:m.proof)||"",t.value=!0},currentProof:o}},xo=e.defineComponent({__name:"pay-order",props:{price:{default:()=>new v(0)}},setup(t,{expose:o}){const a=t,n=e.reactive({payType:"BALANCE",proof:""}),s={payType:[{required:!0,message:"请选择支付方式",trigger:"blur"}],proof:[{required:!0,message:"请选择支付方式",trigger:"change"},{validator:(c,u,_)=>{n.payType==="OFFLINE"&&(u||_(new Error("请上传凭证"))),_()},trigger:"change"}]},m=e.ref(null);return o({getPayOrderFormModel:()=>new Promise((c,u)=>{m.value?m.value.validate(_=>{_?c(n):u("valid error")}):u("none instance")})}),(c,u)=>{const _=e.resolveComponent("el-form-item"),g=e.resolveComponent("el-radio"),r=e.resolveComponent("el-radio-group"),p=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(p,{ref_key:"formRef",ref:m,model:n,rules:s},{default:e.withCtx(()=>[e.createVNode(_,{label:"应付款(元)"},{default:e.withCtx(()=>[e.createTextVNode("￥"+e.toDisplayString(a.price),1)]),_:1}),e.createVNode(_,{label:"支付方式",prop:"payType"},{default:e.withCtx(()=>[e.createVNode(r,{modelValue:n.payType,"onUpdate:modelValue":u[0]||(u[0]=i=>n.payType=i)},{default:e.withCtx(()=>[e.createVNode(g,{value:"BALANCE"},{default:e.withCtx(()=>u[2]||(u[2]=[e.createTextVNode("店铺余额")])),_:1}),e.createVNode(g,{value:"OFFLINE"},{default:e.withCtx(()=>u[3]||(u[3]=[e.createTextVNode("线下付款")])),_:1})]),_:1},8,["modelValue"])]),_:1}),n.payType==="OFFLINE"?(e.openBlock(),e.createBlock(_,{key:0,label:"付款凭证",prop:"proof"},{default:e.withCtx(()=>[e.createVNode(Re,{src:n.proof,"onUpdate:src":u[1]||(u[1]=i=>n.proof=i),format:{size:1,width:1e4,height:1e4},cropper:!1,height:100,width:100},null,8,["src"])]),_:1})):e.createCommentVNode("",!0)]),_:1},8,["model"])}}}),yo={PERSONAL:"个人",ENTERPRISE:"企业"},No={VAT_GENERAL:"增值税电子普通发票",VAT_SPECIAL:"增值税电子专用发票"},Co={style:{padding:"0 30px"},class:"invoice-dialog"},Vo={class:"invoice-dialog-main"},So={class:"invoice-dialog-main--name"},bo={class:"invoice-dialog-main--price"},ko={class:"invoice-dialog-main--number"},Eo={class:"invoice-dialog-main__note"},To={class:"invoice-dialog-main__type"},Io={key:1},Do={key:0,style:{display:"flex","align-items":"center","margin-top":"10px"}},Po={style:{width:"200px"}},$o={class:"invoice-dialog-main__content"},Bo={style:{flex:"3"}},Lo={style:{flex:"3"}},wo={style:{flex:"3"}},Oo={class:"invoice-dialog-main__content"},Ro={style:{flex:"3"}},Mo={style:{flex:"3"}},Ao={style:{flex:"3"}},zo={class:"invoice-dialog-main__content"},Uo={style:{flex:"3"}},Fo={style:{flex:"6"}},qo={key:0,class:"invoice-dialog-main__content"},Ho=e.defineComponent({__name:"invoice-dialog-main",props:{invoiceDetail:{type:Object,default:()=>({})},invoiceSetType:{type:String,default:""}},emits:["update:invoiceDetail"],setup(t,{emit:o}){const a=t,n=o,s=Me.useVModel(a,"invoiceDetail",n),{divTenThousand:m}=ie(),d=e.ref([]),c=e.computed(()=>a.invoiceDetail.invoiceStatus!=="START"?a.invoiceDetail:d.value.find(_=>_.id===s.value.invoiceHeaderId));e.watchEffect(()=>{s.value.invoiceStatus==="START"&&u()});async function u(){const{data:_,msg:g,code:r}=await et();r===200&&(d.value=_.records)}return(_,g)=>{var P,M,k,R,O,G,Y,D,l,f;const r=e.resolveComponent("el-tooltip"),p=e.resolveComponent("el-input"),i=e.resolveComponent("el-radio"),y=e.resolveComponent("el-radio-group"),h=e.resolveComponent("el-option"),V=e.resolveComponent("el-select");return e.openBlock(),e.createElementBlock("div",Co,[e.createElementVNode("div",Vo,[e.createElementVNode("div",So,[g[3]||(g[3]=e.createTextVNode(" 供应商名称：")),e.createElementVNode("span",null,e.toDisplayString(t.invoiceDetail.shopSupplierName),1)]),e.createElementVNode("div",null,[g[4]||(g[4]=e.createTextVNode(" 开票金额 ")),e.createElementVNode("span",bo,e.toDisplayString(e.unref(m)(t.invoiceDetail.invoiceAmount).toFixed(2)),1),e.createVNode(r,{class:"box-item",effect:"dark",content:"1、开票金额为消费者实付款金额，红包、优惠、购物券、消费返利等不在开票范围 2、如订单发生退货退款、退款，开票金额也将对应退款金额扣除。",placement:"bottom"},{default:e.withCtx(()=>[e.createVNode(me,{name:"icon-wenhao1",color:"#666",style:{"margin-left":"5px"}})]),_:1})]),e.createElementVNode("div",ko,[g[5]||(g[5]=e.createTextVNode(" 订单号： ")),e.createElementVNode("span",null,e.toDisplayString(t.invoiceDetail.orderNo),1)])]),e.createElementVNode("div",Eo,[g[6]||(g[6]=e.createElementVNode("span",{style:{"flex-shrink":"0"}},"开票备注：",-1)),t.invoiceDetail.invoiceStatus!=="START"?(e.openBlock(),e.createBlock(p,{key:0,"model-value":t.invoiceDetail.billingRemarks,rows:2,style:{"margin-left":"10px"},disabled:"",type:"textarea",maxlength:"100"},null,8,["model-value"])):(e.openBlock(),e.createBlock(p,{key:1,modelValue:e.unref(s).billingRemarks,"onUpdate:modelValue":g[0]||(g[0]=x=>e.unref(s).billingRemarks=x),rows:2,style:{"margin-left":"10px"},type:"textarea",maxlength:"100",placeholder:"选填"},null,8,["modelValue"]))]),e.createElementVNode("div",To,[e.createElementVNode("div",null,[g[9]||(g[9]=e.createElementVNode("span",{style:{"margin-right":"10px"}},"发票类型：",-1)),t.invoiceDetail.invoiceStatus==="START"?(e.openBlock(),e.createBlock(y,{key:0,modelValue:e.unref(s).invoiceType,"onUpdate:modelValue":g[1]||(g[1]=x=>e.unref(s).invoiceType=x)},{default:e.withCtx(()=>[t.invoiceSetType==="VAT_GENERAL"||t.invoiceSetType==="VAT_COMBINED"?(e.openBlock(),e.createBlock(i,{key:0,label:"VAT_GENERAL"},{default:e.withCtx(()=>g[7]||(g[7]=[e.createTextVNode("增值税电子普通发票")])),_:1})):e.createCommentVNode("",!0),t.invoiceSetType==="VAT_SPECIAL"||t.invoiceSetType==="VAT_COMBINED"?(e.openBlock(),e.createBlock(i,{key:1,label:"VAT_SPECIAL"},{default:e.withCtx(()=>g[8]||(g[8]=[e.createTextVNode("增值税电子专用发票")])),_:1})):e.createCommentVNode("",!0)]),_:1},8,["modelValue"])):(e.openBlock(),e.createElementBlock("span",Io,e.toDisplayString(e.unref(No)[t.invoiceDetail.invoiceType]),1))]),t.invoiceDetail.invoiceStatus==="START"?(e.openBlock(),e.createElementBlock("div",Do,[g[10]||(g[10]=e.createElementVNode("span",{style:{"margin-right":"12px"}},"抬头选择：",-1)),e.createElementVNode("div",Po,[e.createVNode(V,{modelValue:e.unref(s).invoiceHeaderId,"onUpdate:modelValue":g[2]||(g[2]=x=>e.unref(s).invoiceHeaderId=x),placeholder:"请选择抬头",size:"small"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,x=>(e.openBlock(),e.createBlock(h,{key:x.id,label:x.header,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])):e.createCommentVNode("",!0)]),e.createElementVNode("div",$o,[e.createElementVNode("div",Bo,[g[11]||(g[11]=e.createTextVNode(" 抬头类型： ")),e.createElementVNode("span",null,e.toDisplayString(((P=c.value)==null?void 0:P.invoiceHeaderType)&&e.unref(yo)[c.value.invoiceHeaderType]),1)]),e.createElementVNode("div",Lo,"发票抬头： "+e.toDisplayString((M=c.value)==null?void 0:M.header),1),e.createElementVNode("div",wo,"税号："+e.toDisplayString((k=c.value)==null?void 0:k.taxIdentNo),1)]),e.createElementVNode("div",Oo,[e.createElementVNode("div",Ro,"开户行："+e.toDisplayString((R=c.value)==null?void 0:R.openingBank),1),e.createElementVNode("div",Mo,"银行账号："+e.toDisplayString((O=c.value)==null?void 0:O.bankAccountNo),1),e.createElementVNode("div",Ao,"企业电话："+e.toDisplayString((G=c.value)==null?void 0:G.enterprisePhone),1)]),e.createElementVNode("div",zo,[e.createElementVNode("div",Uo,"邮箱地址："+e.toDisplayString((Y=c.value)==null?void 0:Y.email),1),e.createElementVNode("div",Fo,"企业地址："+e.toDisplayString((D=c.value)==null?void 0:D.enterpriseAddress),1)]),(l=t.invoiceDetail)!=null&&l.denialReason?(e.openBlock(),e.createElementBlock("div",qo,[e.createElementVNode("div",null,"拒绝原因："+e.toDisplayString((f=t.invoiceDetail)==null?void 0:f.denialReason),1)])):e.createCommentVNode("",!0)])}}}),jn="",jo=oe(Ho,[["__scopeId","data-v-21d77e4c"]]),Go=t=>{const o=e.ref(""),a=e.ref([]),n=e.ref([]),s=(r,p)=>{console.log(r,"row.key",n.value.includes(r.productId)),n.value.includes(r.productId)?n.value=n.value.filter(i=>i!==r.productId):n.value.push(r.productId)},m=e.computed(()=>r=>{var i,y,h,V,P;let p=((y=(i=r==null?void 0:r.skus)==null?void 0:i[0])==null?void 0:y.num)-((V=(h=r==null?void 0:r.skus)==null?void 0:h[0])==null?void 0:V.used);return(P=r==null?void 0:r.skus)==null||P.forEach(M=>{const k=(M==null?void 0:M.num)-(M==null?void 0:M.used);k<p&&(p=k)}),p}),d=(r,p)=>{var i;if(typeof p=="number"){const y=a.value.findIndex(h=>h.productId===r);y>-1&&((i=a.value[y])==null||i.skus.forEach(h=>h.inStorageNum=p))}},c=e.computed(()=>r=>{const p=a.value.findIndex(i=>i.productId===r);return p>-1?a.value[p].skus.reduce((y,h)=>y+Number(h.inStorageNum||0),0):0});return{remark:o,tableData:a,expandRowKeys:n,expandOpen:s,maxRowBatchNum:m,changeRowBatchNum:d,computedActualNum:c,handleRemoveCommodity:r=>{B.ElMessageBox.confirm("确认移除当前商品信息").then(()=>{a.value.splice(r,1)})},handleRemoveSku:(r,p)=>{B.ElMessageBox.confirm("确认移除当前规格信息").then(()=>{var i,y,h;(h=(y=(i=a.value)==null?void 0:i[r])==null?void 0:y.skus)==null||h.splice(p,1)})},initialInstorageData:async()=>{var p,i;const r=await Te(t.orderNo);r.data&&(o.value=(p=r==null?void 0:r.data)==null?void 0:p.remark,a.value=(i=r.data)==null?void 0:i.products)}}},Yo={class:"oneClickProcurement"},Wo={class:"oneClickProcurement__form"},Ko={class:"oneClickProcurement__commodity"},Xo=["src"],Jo={class:"oneClickProcurement__commodity--name"},Zo=e.defineComponent({__name:"in-storage",props:{orderNo:{default:""}},setup(t,{expose:o}){const a=t,{remark:n,tableData:s,expandRowKeys:m,expandOpen:d,maxRowBatchNum:c,changeRowBatchNum:u,computedActualNum:_,handleRemoveCommodity:g,handleRemoveSku:r,initialInstorageData:p}=Go(a);e.onMounted(()=>p()),o({tableData:s,remark:n});const i=e.ref(),y=h=>{i.value.toggleRowExpansion(h)};return(h,V)=>{const P=e.resolveComponent("el-input"),M=e.resolveComponent("el-form-item"),k=e.resolveComponent("el-form"),R=e.resolveComponent("el-input-number"),O=e.resolveComponent("el-table-column"),G=e.resolveComponent("el-link"),Y=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Yo,[e.createVNode(k,{"show-message":!1,class:"oneClickProcurement__remark"},{default:e.withCtx(()=>[e.createVNode(M,{label:"入库备注"},{default:e.withCtx(()=>[e.createVNode(P,{modelValue:e.unref(n),"onUpdate:modelValue":V[0]||(V[0]=D=>e.isRef(n)?n.value=D:null),placeholder:"请输入入库备注",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e.createVNode(Y,{ref_key:"expandableTable",ref:i,"max-height":450,data:e.unref(s),"expand-row-keys":e.unref(m),"row-key":"productId",onExpandChange:e.unref(d),onRowClick:y},{default:e.withCtx(()=>[e.createVNode(O,{type:"expand"},{default:e.withCtx(({row:D,$index:l})=>[e.createElementVNode("div",Wo,[e.createVNode(k,null,{default:e.withCtx(()=>[e.createVNode(M,{label:"入库数(批量)"},{default:e.withCtx(()=>[e.createVNode(R,{modelValue:D.batchNum,"onUpdate:modelValue":f=>D.batchNum=f,max:e.unref(c)(D),min:0,precision:0,onChange:f=>e.unref(u)(D.productId,f)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])]),_:2},1024)]),_:2},1024)]),e.createVNode(Y,{data:D.skus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(O,{label:"商品规格"},{default:e.withCtx(({row:f})=>{var x;return[e.createTextVNode(e.toDisplayString((x=f==null?void 0:f.specs)==null?void 0:x.join(";")),1)]}),_:1}),e.createVNode(O,{label:"采购数",prop:"num",width:"80"}),e.createVNode(O,{label:"已入库数",prop:"used",width:"100"}),e.createVNode(O,{label:"剩余入库数",width:"100"},{default:e.withCtx(({row:f})=>[e.createTextVNode(e.toDisplayString((f==null?void 0:f.num)-(f==null?void 0:f.used)),1)]),_:1}),e.createVNode(O,{label:"实际入库数（本次）",prop:"inStorageNum",width:"180"},{default:e.withCtx(({row:f})=>[e.createVNode(R,{modelValue:f.inStorageNum,"onUpdate:modelValue":x=>f.inStorageNum=x,max:(f==null?void 0:f.num)-(f==null?void 0:f.used),min:0,precision:0},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),e.createVNode(O,{fixed:"right",label:"操作",width:"80"},{default:e.withCtx(({$index:f})=>[e.createVNode(G,{type:"danger",onClick:x=>e.unref(r)(l,f)},{default:e.withCtx(()=>V[1]||(V[1]=[e.createTextVNode("移出")])),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1032,["data"])]),_:1}),e.createVNode(O,{label:"商品名称",width:"250"},{default:e.withCtx(({row:D})=>[e.createElementVNode("div",Ko,[e.createElementVNode("img",{src:D.image},null,8,Xo),e.createElementVNode("span",Jo,e.toDisplayString(D.productName),1)])]),_:1}),e.createVNode(O,{label:"采购数"},{default:e.withCtx(({row:D})=>{var l;return[e.createTextVNode(e.toDisplayString((l=D==null?void 0:D.skus)==null?void 0:l.reduce((f,x)=>f+x.num,0)),1)]}),_:1}),e.createVNode(O,{label:"已入库数"},{default:e.withCtx(({row:D})=>{var l;return[e.createTextVNode(e.toDisplayString((l=D==null?void 0:D.skus)==null?void 0:l.reduce((f,x)=>f+x.used,0)),1)]}),_:1}),e.createVNode(O,{label:"剩余入库数"},{default:e.withCtx(({row:D})=>{var l,f;return[e.createTextVNode(e.toDisplayString(((l=D==null?void 0:D.skus)==null?void 0:l.reduce((x,I)=>x+I.num,0))-((f=D==null?void 0:D.skus)==null?void 0:f.reduce((x,I)=>x+I.used,0))),1)]}),_:1}),e.createVNode(O,{label:"实际入库数（本次）"},{default:e.withCtx(({row:D})=>[e.createTextVNode(e.toDisplayString(e.unref(_)(D.productId)),1)]),_:1}),e.createVNode(O,{label:"操作"},{default:e.withCtx(({$index:D})=>[e.createVNode(G,{type:"danger",onClick:l=>e.unref(g)(D)},{default:e.withCtx(()=>V[2]||(V[2]=[e.createTextVNode("移出")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","expand-row-keys","onExpandChange"])])}}}),Yn="",Qo=oe(Zo,[["__scopeId","data-v-bcd44223"]]),vo=t=>{const o=e.reactive({orderNo:"",supplierId:"",remark:""}),a=e.ref([]);return{tableData:a,extraData:o,initialInstorageData:async()=>{var m;const s=await Te(t.orderNo);s.data&&(a.value=(m=s.data)==null?void 0:m.products,Object.keys(o).forEach(d=>{var u;const c=d;o[c]=(u=s==null?void 0:s.data)==null?void 0:u[d]}))}}},ea={class:"batch"},ta={class:"batch__commodity"},oa=["src"],aa={class:"batch__commodity--name"},na=e.defineComponent({__name:"storage-details",props:{orderNo:{default:""}},setup(t){const o=t,{tableData:a,initialInstorageData:n,extraData:s}=vo(o);e.onMounted(()=>n());const m=e.ref(),d=c=>{m.value.toggleRowExpansion(c)};return(c,u)=>{const _=e.resolveComponent("el-form-item"),g=e.resolveComponent("el-form"),r=e.resolveComponent("el-table-column"),p=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",ea,[e.createVNode(g,{"show-message":!1,class:"purchase__remark"},{default:e.withCtx(()=>[e.createVNode(_,{label:"采购备注"},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(s).remark),1)]),_:1})]),_:1}),e.createVNode(p,{ref_key:"expandableTable",ref:m,data:e.unref(a),"row-key":"id","max-height":450,onRowClick:d},{default:e.withCtx(()=>[e.createVNode(r,{type:"expand"},{default:e.withCtx(({row:i})=>[e.createVNode(p,{data:i.skus,"row-key":"id"},{default:e.withCtx(()=>[e.createVNode(r,{label:"商品规格"},{default:e.withCtx(({row:y})=>{var h;return[e.createTextVNode(e.toDisplayString((h=y==null?void 0:y.specs)==null?void 0:h.join(";")),1)]}),_:1}),e.createVNode(r,{label:"采购数",prop:"num",width:"80"}),e.createVNode(r,{label:"已入库数",prop:"used",width:"100"}),e.createVNode(r,{label:"剩余入库数",width:"100"},{default:e.withCtx(({row:y})=>[e.createTextVNode(e.toDisplayString((y==null?void 0:y.num)-(y==null?void 0:y.used)),1)]),_:1})]),_:2},1032,["data"])]),_:1}),e.createVNode(r,{label:"商品名称",width:"250"},{default:e.withCtx(({row:i})=>[e.createElementVNode("div",ta,[e.createElementVNode("img",{src:i.image},null,8,oa),e.createElementVNode("span",aa,e.toDisplayString(i.productName),1)])]),_:1}),e.createVNode(r,{label:"采购数"},{default:e.withCtx(({row:i})=>{var y;return[e.createTextVNode(e.toDisplayString((y=i==null?void 0:i.skus)==null?void 0:y.reduce((h,V)=>h+V.num,0)),1)]}),_:1}),e.createVNode(r,{label:"已入库数"},{default:e.withCtx(({row:i})=>{var y;return[e.createTextVNode(e.toDisplayString((y=i==null?void 0:i.skus)==null?void 0:y.reduce((h,V)=>h+V.used,0)),1)]}),_:1}),e.createVNode(r,{label:"剩余入库数"},{default:e.withCtx(({row:i})=>{var y,h;return[e.createTextVNode(e.toDisplayString(((y=i==null?void 0:i.skus)==null?void 0:y.reduce((V,P)=>V+P.num,0))-((h=i==null?void 0:i.skus)==null?void 0:h.reduce((V,P)=>V+P.used,0))),1)]}),_:1})]),_:1},8,["data"])])}}}),Kn="",la=oe(na,[["__scopeId","data-v-66633c0a"]]),ra=()=>{const t=e.ref(""),o=e.ref(!1),a=e.ref(null);return{inStorageOrderNo:t,showInStorageDialog:o,openInStorageDialog:s=>{t.value=s,o.value=!0},inStorageRefs:a}},sa=()=>{const t=e.ref(""),o=e.ref(!1);return{storageDetailsOrderNo:t,showStorageDetailsDialog:o,openStorageDetailsDialog:n=>{t.value=n,o.value=!0}}},ia=["近一个月订单","近三个月订单","全部订单"],ca={START:{title:"申请开票",btnConfig:{text:"",type:""},describe:""},SUCCESSFULLY_INVOICED:{title:"开票成功",btnConfig:{text:"重新发送发票到邮箱",type:"primary"},describe:"开票成功后，发票将发送至您的邮箱地址，请注意查看邮件~"},REQUEST_IN_PROCESS:{title:"开票中",btnConfig:{text:"撤销开票申请",type:"danger"},describe:"开票成功后，发票将发送至您的邮箱地址，请注意查看邮件~"},FAILED_INVOICE_REQUEST:{title:"开票失败",btnConfig:{text:"重新申请开票",type:"primary"},describe:"开票失败后，您可以重新申请开票~"}},{divTenThousand:da}=ie(),{toClipboard:pa}=ze(),Ne=new Ae,ma=()=>{const{showInStorageDialog:t,inStorageOrderNo:o,openInStorageDialog:a,inStorageRefs:n}=ra(),{storageDetailsOrderNo:s,showStorageDetailsDialog:m,openStorageDetailsDialog:d}=sa(),c={OFFLINE:"线下支付",BALANCE:"余额支付"},u=pe.useRouter(),_=e.ref(!1),g=e.ref(!1),r=e.ref({applicantId:"",applicantShopId:"",invoiceOwnerType:"SHOP",shopSupplierName:"",applicationTime:"",orderNo:"",invoiceAmount:"",invoiceType:"VAT_GENERAL",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""}),p=e.ref("VAT_COMBINED"),i=e.ref(),y=e.ref(null),h=e.reactive({status:"",startTime:"",endTime:"",purchaser:"",no:"",supplierId:""}),V=e.reactive({page:{current:1,size:10},total:0}),P=e.ref(" "),M=e.ref("全部订单"),k=e.ref(),R=e.ref([]),O=e.ref([]),G=E=>{h.status=E,f()},Y=e.computed(()=>E=>ha(E)),D=e.computed(()=>E=>we(E)),l=e.computed(()=>E=>ua(E)),f=async()=>{var A,W;let E=[],C=0;try{const Q=await ve({...V.page,...h});E=(A=Q.data)==null?void 0:A.records,C=((W=Q.data)==null?void 0:W.total)||0}finally{R.value=E,V.total=Number(C)}},x=E=>{if(P.value=" ",M.value=E,M.value==="近一个月订单"){const C=Ne.getLastMonth(new Date);z(C)}else if(M.value==="近三个月订单"){const C=Ne.getLastThreeMonth(new Date);z(C)}else h.startTime="",h.endTime="",f()},I=E=>{h.startTime=E.startTime,h.endTime=E.endTime,h.purchaser=E.purchaser,h.no=E.no,h.supplierId=E.supplierId,V.page.current=1,f()},z=async E=>{const C=Ne.getYMDs(new Date);h.startTime=E,h.endTime=C,f()},w=()=>{switch(r.value.invoiceStatus){case"START":K();break;case"REQUEST_IN_PROCESS":Z();break;case"SUCCESSFULLY_INVOICED":X();break;case"FAILED_INVOICE_REQUEST":T();break}},$=()=>{r.value={applicantId:"",applicantShopId:"",invoiceOwnerType:"SHOP",shopSupplierName:"",applicationTime:"",orderNo:"",invoiceAmount:"",invoiceType:"VAT_GENERAL",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""}},K=async()=>{if(!r.value.invoiceHeaderId)return B.ElMessage.error("请选择抬头");if(!r.value.invoiceType)return B.ElMessage.error("请选择发票类型");const{code:E,data:C,msg:A}=await ot(r.value);if(E!==200)return B.ElMessage.error(A||"申请开票失败");B.ElMessage.success("申请开票成功"),g.value=!1},Z=async()=>{const{data:E,code:C,msg:A}=await lt(r.value.id);if(C!==200)return B.ElMessage.error(A||"撤销开票失败");B.ElMessage.success("撤销开票成功"),g.value=!1},X=async()=>{const{data:E,code:C,msg:A}=await rt({invoiceRequestId:r.value.id,shopId:r.value.applicantShopId});if(C!==200)return B.ElMessage.error(A||"重发发票失败");B.ElMessage.success("重发发票成功"),g.value=!1},T=async()=>{r.value={...r.value,applicantId:"",applicationTime:"",invoiceType:"VAT_GENERAL",invoiceStatus:"START",billingRemarks:"",denialReason:"",invoiceHeaderId:""},$e().then(E=>{r.value.invoiceHeaderId=E.data.id}).catch(()=>{B.ElMessage.error("获取默认抬头失败")}),Pe({invoiceSettingsClientType:"SUPPLIER",shopId:r.value.applicantShopId}).then(E=>{p.value=E.data.invoiceSetupValue[0].invoiceType}).catch(()=>{B.ElMessage.error("获取发票设置失败")})},N=async(E,C)=>{switch(E){case"cancel":B.ElMessageBox.confirm("请确认是否将订单取消？？").then(async()=>{const{code:A,msg:W}=await We(C.no);A===200?(B.ElMessage.success({message:W||"取消成功"}),f()):B.ElMessage.error({message:W||"取消失败"})});break;case"details":u.push({path:"/goods/purchase/detail",query:{orderNo:C.no}});break;case"pay":i.value=C,_.value=!0;break;case"finish":B.ElMessageBox.confirm("完成后将无法入库，请确认订单是否已完成？？").then(async()=>{const{code:A,msg:W}=await Xe(C.no);A===200?(B.ElMessage.success({message:W||"订单入库已完成"}),f()):B.ElMessage.error({message:W||"订单完成失败"})});break;case"inStorage":a(C.no);break;case"storageDetails":d(C.no);break;case"contact":_e(C.shopId,C.supplierId).then(({code:A})=>{A===200&&u.push({path:"/message/customer/supplierService",query:{id:C.supplierId}})});break;case"invoice":U(C);break}},U=async E=>{const{data:C,code:A,msg:W}=await nt({invoiceOwnerType:"SHOP",applicantId:ce.useShopInfoStore().shopInfo.id,applicantShopId:E.supplierId,orderNo:E.no});if(A!==200||!C)return B.ElMessage.error({message:W||"获取开票信息失败"});if(C.invoiceStatus==="REQUEST_HAS_EXPIRED")return B.ElMessage.error({message:W||"已超出可开票时间"});if(C.invoiceStatus==="SERVER_NOT_SUPPORTED")return B.ElMessage.error({message:W||"供应商不支持开票"});g.value=!0,C.invoiceStatus!=="ALLOWED_INVOICING"?at(C.id).then(Q=>{r.value=Q.data}).catch(()=>{B.ElMessage.error("获取发票详情失败")}):(r.value.shopSupplierName=E.extraInfo.supplierName,r.value.applicantShopId=E.supplierId,r.value.orderNo=E.no,r.value.invoiceAmount=C.billMoney,$e().then(Q=>{r.value.invoiceHeaderId=Q.data.id}).catch(()=>{B.ElMessage.error("获取默认抬头失败")}),Pe({invoiceSettingsClientType:"SUPPLIER",shopId:E.supplierId}).then(Q=>{p.value=Q.data.invoiceSetupValue[0].invoiceType}).catch(()=>{B.ElMessage.error("获取发票设置失败")}))},q=async E=>{try{await pa(E),B.ElMessage.success("复制成功")}catch{B.ElMessage.error("复制失败")}},H=async()=>{var W,Q;const E=await((W=y.value)==null?void 0:W.getPayOrderFormModel()),{code:C,msg:A}=await Ye({...E,orderNo:(Q=i.value)==null?void 0:Q.no});C===200?(B.ElMessage.success("支付成功"),f(),_.value=!1):B.ElMessage.error(A||"支付失败")},le=async()=>{const E=n.value;if(E){const C={skuStorages:fa(E.tableData),remark:E.remark,orderNo:o.value},A=await Ke(C);A.code===200?(B.ElMessage.success({message:A.msg||"入库成功"}),t.value=!1,f()):B.ElMessage.error({message:A.msg||"入库失败"})}},F=e.ref([]),L=(E,C)=>{const A=R.value.find(W=>W.no===C);if(A)if(E===!1){const W=F.value.findIndex(Q=>Q.no===C);W>-1&&F.value.splice(W,1)}else F.value.push(A)};function te(E){O.value=E}return{handleTabChange:G,pagination:V,multipleTableRef:k,initOrderList:f,orderDataList:R,chooseList:O,handleQuickSearchCommand:x,handleSearch:I,quickSearchTabName:M,quickSearchTabNames:ia,activeTabName:P,getMainOrderStatusText:D,computedBtnList:Y,handleDispatchEvent:N,copyOrderNo:q,computedCalculateFreight:l,showPayDialog:_,currentRow:i,handlePayOrder:H,payOrderRef:y,payTypeMap:c,showInStorageDialog:t,inStorageOrderNo:o,inStorageRefs:n,storageDetailsOrderNo:s,showStorageDetailsDialog:m,handleConfirmInStorage:le,showInvoiceDialog:g,InvoiceStatusHander:ca,invoiceDetail:r,handleInvoiceConfig:w,handleCloseInvoiceDialog:$,invoiceSetType:p,handleChangeRow:L,multiSelect:F,handleSizeChange:E=>{V.page.size=E,f()},handleCurrentChange:E=>{V.page.current=E,f()},handleSelectionChange:te}},fa=t=>{const o=[];return t.forEach(a=>{const n=a.productId;a.skus.forEach(m=>{const d=m.skuId,c=m.inStorageNum||0;o.push({key:{productId:n,skuId:d},value:c})})}),o},we=t=>{if(t.status==="UNPAID")return"待支付";if(t.status==="PAYMENT_AUDIT")return"待审核";const o=["WAITING_FOR_DELIVER","WAITING_FOR_RECEIVE","COMPLETED"],a={WAITING_FOR_DELIVER:"待发货",WAITING_FOR_RECEIVE:"待入库",COMPLETED:"已完成"};if(t.status==="PAID"){let n="COMPLETED";for(const m of t.orderItems){const d=o.findIndex(c=>c===n);if(m.packageStatus==="WAITING_FOR_DELIVER"&&d>0){n="WAITING_FOR_DELIVER";continue}if(m.packageStatus==="WAITING_FOR_RECEIVE"&&d>1){n="WAITING_FOR_RECEIVE";continue}}let s=a[n];return s==="待发货"&&t.orderItems.find(d=>d.packageStatus!=="WAITING_FOR_DELIVER")&&(s="部分发货"),s}return"已关闭"},ha=t=>{const o=[],a=we(t);return o.push({action:"details",text:"查看",type:"primary"}),a==="待支付"?(o.push({action:"pay",text:"去支付",type:"primary"}),o.push({action:"cancel",text:"取消订单",type:"danger"})):["待入库","部分发货"].includes(a)?(o.push({action:"inStorage",text:"入库",type:"primary"}),o.push({action:"finish",text:"完成",type:"primary"}),o.push({action:"storageDetails",text:"入库详情",type:"primary"})):a==="已完成"&&(o.push({action:"storageDetails",text:"入库详情",type:"primary"}),o.push({action:"invoice",text:"申请开票",type:"primary"})),t.stockInCount===0&&o.forEach((n,s)=>{n.text==="入库详情"&&o.splice(s,1)}),o},ua=t=>t.reduce((o,a)=>o.plus(new v(da(a.freightPrice))),new v(0)),ga={class:"handle_container"},_a={key:0,style:{color:"#999"}},xa={class:"table_container overh"},ya={class:"customer"},Na={class:"customer__name"},Ca={style:{display:"flex","align-items":"center"}},Va={style:{color:"#f54319"}},Sa=["src","onClick"],ba={style:{display:"flex","justify-content":"end"}},ka={class:"dialog-footer"},Ea=["src"],Ta={class:"dialog-footer"},Ia={class:"invoice-title"},Da=["id"],Pa={class:"invoice-desc"},$a={class:"dialog-footer"},Ba=e.defineComponent({__name:"index",setup(t){const{handleTabChange:o,pagination:a,orderDataList:n,chooseList:s,multipleTableRef:m,handleQuickSearchCommand:d,handleSearch:c,quickSearchTabName:u,quickSearchTabNames:_,activeTabName:g,getMainOrderStatusText:r,computedBtnList:p,handleDispatchEvent:i,computedCalculateFreight:y,showPayDialog:h,showInvoiceDialog:V,handlePayOrder:P,payOrderRef:M,payTypeMap:k,currentRow:R,showInStorageDialog:O,inStorageOrderNo:G,storageDetailsOrderNo:Y,showStorageDetailsDialog:D,handleConfirmInStorage:l,inStorageRefs:f,InvoiceStatusHander:x,invoiceDetail:I,handleInvoiceConfig:z,handleCloseInvoiceDialog:w,invoiceSetType:$,multiSelect:K,handleSizeChange:Z,handleCurrentChange:X,handleSelectionChange:T,initOrderList:N}=ma(),{divTenThousand:U}=ie(),{showProof:q,goToShowProof:H,currentProof:le}=_o(),F=[];(()=>{Object.keys(Le).forEach(ee=>{F.push([ee,Le[ee]])})})(),N();const te=async ee=>{var b,E;if(K.value.length){let C=[""];C=K.value.map(Ce=>Ce.mainNo);const{data:A,code:W,msg:Q}=await Be({exportOrderIds:C});return W!==200?B.ElMessage.error(Q||"导出数据失败"):B.ElMessage.success("导出数据成功")}else{let C={no:ee.no,supplierId:ee.supplierId,startTime:(b=ee.date)==null?void 0:b[0],endTime:(E=ee.date)==null?void 0:E[1],status:g.value,exportOrderIds:""};C.status=C.status.trim();const{data:A,code:W,msg:Q}=await Be(C);return W!==200?B.ElMessage.error(Q||"导出数据失败"):B.ElMessage.success("导出数据成功")}};return(ee,b)=>{const E=e.resolveComponent("el-icon"),C=e.resolveComponent("el-dropdown-item"),A=e.resolveComponent("el-dropdown-menu"),W=e.resolveComponent("el-dropdown"),Q=e.resolveComponent("el-tab-pane"),Ce=e.resolveComponent("el-tabs"),ae=e.resolveComponent("el-table-column"),$n=e.resolveComponent("el-link"),Bn=e.resolveComponent("el-table"),se=e.resolveComponent("el-button"),fe=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(io,{onSearch:e.unref(c),onSupplierExport:te},null,8,["onSearch"]),e.createVNode(Ce,{modelValue:e.unref(g),"onUpdate:modelValue":b[0]||(b[0]=S=>e.isRef(g)?g.value=S:null),class:"tab_container",onTabChange:e.unref(o)},{default:e.withCtx(()=>[e.createVNode(Q,{name:" "},{label:e.withCtx(()=>[e.createElementVNode("span",null,e.toDisplayString(e.unref(u)),1),e.createVNode(W,{placement:"bottom-end",trigger:"click",onCommand:e.unref(d)},{dropdown:e.withCtx(()=>[e.createVNode(A,null,{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(_),S=>(e.openBlock(),e.createBlock(C,{key:S,command:S},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(S),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:e.withCtx(()=>[e.createElementVNode("span",{class:"el-dropdown-link",style:{"line-height":"40px"},onClick:e.withModifiers(()=>{},["stop"])},[e.createVNode(E,{class:"el-icon--right"},{default:e.withCtx(()=>[e.createVNode(e.unref(Ue.ArrowDown))]),_:1})])]),_:1},8,["onCommand"])]),_:1}),(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(F,S=>e.createVNode(Q,{key:S[0],label:S[1],name:S[0]},null,8,["label","name"])),64))]),_:1},8,["modelValue","onTabChange"]),e.createElementVNode("div",ga,[e.unref(s).length>=1?(e.openBlock(),e.createElementBlock("span",_a,"已选"+e.toDisplayString(e.unref(s).length)+"条 ",1)):e.createCommentVNode("",!0)]),e.createElementVNode("div",xa,[e.createVNode(Bn,{ref_key:"multipleTableRef",ref:m,"cell-style":{fontSize:"14px",color:"#333333"},data:e.unref(n),"header-cell-style":{background:"#f6f8fa",height:"50px"},"header-row-style":{fontSize:"14px",color:"#000"},onSelectionChange:e.unref(T)},{empty:e.withCtx(()=>[e.createVNode(Ve)]),default:e.withCtx(()=>[e.createVNode(ae,{type:"selection",width:"40",fixed:"left"}),e.createVNode(ae,{label:"订单号",prop:"no",width:"200"}),e.createVNode(ae,{label:"供应商",prop:"extraInfo",width:"160"},{default:e.withCtx(({row:S})=>{var J,re;return[e.createElementVNode("div",ya,[e.createElementVNode("div",null,[e.createElementVNode("span",Na,e.toDisplayString((J=S==null?void 0:S.extraInfo)==null?void 0:J.supplierName),1),e.createVNode(me,{name:"icon-xiaoxi-copy",size:"18px",style:{cursor:"pointer"},svg:"",onClick:Ln=>e.unref(i)("contact",S)},null,8,["onClick"])]),e.createElementVNode("span",null,e.toDisplayString((re=S==null?void 0:S.extraInfo)==null?void 0:re.supplierPhone),1)])]}),_:1}),e.createVNode(ae,{label:"采购数量",prop:"orderItems",width:"90"},{default:e.withCtx(({row:S})=>[e.createTextVNode(e.toDisplayString(S.orderItems.reduce((J,re)=>J+re.num,0)),1)]),_:1}),e.createVNode(ae,{label:"商品总价(元)",prop:"payAmount",width:"110"},{default:e.withCtx(({row:S})=>[e.createTextVNode(e.toDisplayString(e.unref(U)(S.payAmount).toFixed(2)),1)]),_:1}),e.createVNode(ae,{label:"运费(元)",prop:"orderItems",width:"90"},{default:e.withCtx(({row:S})=>[e.createTextVNode(e.toDisplayString(e.unref(y)(S==null?void 0:S.orderItems).toFixed(2)),1)]),_:1}),e.createVNode(ae,{label:"应付金额(元)",prop:"payAmount",width:"120"},{default:e.withCtx(({row:S})=>{var J,re;return[e.createElementVNode("div",Ca,[e.createElementVNode("span",Va,"￥"+e.toDisplayString(e.unref(U)(S.payAmount)),1),(re=(J=S==null?void 0:S.extra)==null?void 0:J.pay)!=null&&re.proof?(e.openBlock(),e.createElementBlock("img",{key:0,src:e.unref(Fe),alt:"",style:{height:"16px",width:"21px","margin-left":"4px"},onClick:Ln=>e.unref(H)(S)},null,8,Sa)):e.createCommentVNode("",!0)])]}),_:1}),e.createVNode(ae,{label:"订单状态",width:"140"},{default:e.withCtx(({row:S})=>{var J;return[e.createElementVNode("div",{class:e.normalizeClass({"text-red":e.unref(r)(S)==="待支付","text-grey":e.unref(r)(S)==="待支付"}),style:{display:"flex","align-items":"center"}},[e.createTextVNode(e.toDisplayString(e.unref(r)(S))+" ",1),e.unref(r)(S)==="待支付"?(e.openBlock(),e.createBlock(go,{key:0,"create-time":S==null?void 0:S.createTime,"pay-timeout":(J=S==null?void 0:S.extra)==null?void 0:J.payTimeout},null,8,["create-time","pay-timeout"])):e.createCommentVNode("",!0)],2)]}),_:1}),e.createVNode(ae,{label:"入库数",prop:"stockInCount",width:"120"}),e.createVNode(ae,{label:"支付方式",width:"120"},{default:e.withCtx(({row:S})=>{var J,re;return[e.createTextVNode(e.toDisplayString(e.unref(k)[(re=(J=S==null?void 0:S.extra)==null?void 0:J.pay)==null?void 0:re.payType]),1)]}),_:1}),e.createVNode(ae,{label:"下单时间",prop:"createTime",width:"180"}),e.createVNode(ae,{label:"支付时间",prop:"payTime",width:"180"},{default:e.withCtx(({row:S})=>{var J;return[e.createTextVNode(e.toDisplayString((J=S==null?void 0:S.timeNodes)==null?void 0:J.payTime),1)]}),_:1}),e.createVNode(ae,{label:"操作",fixed:"right",width:"200",align:"right"},{default:e.withCtx(({row:S})=>[e.createElementVNode("div",ba,[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(e.unref(p)(S),J=>(e.openBlock(),e.createBlock($n,{key:J.action,type:J.type,style:{"margin-left":"8px"},onClick:re=>e.unref(i)(J.action,S)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(J.text),1)]),_:2},1032,["type","onClick"]))),128))])]),_:1})]),_:1},8,["data","onSelectionChange"])]),e.createVNode(ge,{class:"pagination","page-size":e.unref(a).page.size,"page-num":e.unref(a).page.current,total:e.unref(a).total,onHandleSizeChange:e.unref(Z),onHandleCurrentChange:e.unref(X)},null,8,["page-size","page-num","total","onHandleSizeChange","onHandleCurrentChange"]),e.createVNode(fe,{modelValue:e.unref(h),"onUpdate:modelValue":b[2]||(b[2]=S=>e.isRef(h)?h.value=S:null),title:"支付订单",width:"500px","destroy-on-close":""},{footer:e.withCtx(()=>[e.createElementVNode("span",ka,[e.createVNode(se,{onClick:b[1]||(b[1]=S=>h.value=!1)},{default:e.withCtx(()=>b[9]||(b[9]=[e.createTextVNode("取消")])),_:1}),e.createVNode(se,{type:"primary",onClick:e.unref(P)},{default:e.withCtx(()=>b[10]||(b[10]=[e.createTextVNode(" 确认 ")])),_:1},8,["onClick"])])]),default:e.withCtx(()=>{var S;return[e.createVNode(xo,{ref_key:"payOrderRef",ref:M,price:e.unref(U)((S=e.unref(R))==null?void 0:S.payAmount)},null,8,["price"])]}),_:1},8,["modelValue"]),e.createVNode(fe,{modelValue:e.unref(q),"onUpdate:modelValue":b[3]||(b[3]=S=>e.isRef(q)?q.value=S:null),title:"付款凭证",width:"500px"},{default:e.withCtx(()=>[e.createElementVNode("img",{src:e.unref(le),class:"proof-img"},null,8,Ea)]),_:1},8,["modelValue"]),e.createVNode(fe,{modelValue:e.unref(O),"onUpdate:modelValue":b[5]||(b[5]=S=>e.isRef(O)?O.value=S:null),title:"入库",width:"1150px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Ta,[e.createVNode(se,{onClick:b[4]||(b[4]=S=>O.value=!1)},{default:e.withCtx(()=>b[11]||(b[11]=[e.createTextVNode("取消")])),_:1}),e.createVNode(se,{type:"primary",onClick:e.unref(l)},{default:e.withCtx(()=>b[12]||(b[12]=[e.createTextVNode(" 确认 ")])),_:1},8,["onClick"])])]),default:e.withCtx(()=>[e.unref(O)?(e.openBlock(),e.createBlock(Qo,{key:0,ref_key:"inStorageRefs",ref:f,"order-no":e.unref(G)},null,8,["order-no"])):e.createCommentVNode("",!0)]),_:1},8,["modelValue"]),e.createVNode(fe,{modelValue:e.unref(D),"onUpdate:modelValue":b[6]||(b[6]=S=>e.isRef(D)?D.value=S:null),title:"入库详情",width:"1150px"},{default:e.withCtx(()=>[e.createVNode(la,{"order-no":e.unref(Y)},null,8,["order-no"])]),_:1},8,["modelValue"]),e.createVNode(fe,{modelValue:e.unref(V),"onUpdate:modelValue":b[8]||(b[8]=S=>e.isRef(V)?V.value=S:null),width:"1200px",center:"",onClose:e.unref(w)},{header:e.withCtx(({titleId:S,titleClass:J})=>[e.createElementVNode("div",Ia,[e.createElementVNode("h5",{id:S,class:e.normalizeClass(J)},e.toDisplayString(e.unref(x)[e.unref(I).invoiceStatus].title),11,Da)]),e.createElementVNode("div",Pa,e.toDisplayString(e.unref(x)[e.unref(I).invoiceStatus].describe),1)]),footer:e.withCtx(()=>[e.createElementVNode("span",$a,[e.unref(I).invoiceStatus==="START"?(e.openBlock(),e.createElementBlock(e.Fragment,{key:0},[e.createVNode(se,{onClick:b[7]||(b[7]=S=>V.value=!1)},{default:e.withCtx(()=>b[13]||(b[13]=[e.createTextVNode("取消")])),_:1}),e.createVNode(se,{type:"primary",onClick:e.unref(z)},{default:e.withCtx(()=>b[14]||(b[14]=[e.createTextVNode(" 确认 ")])),_:1},8,["onClick"])],64)):(e.openBlock(),e.createBlock(se,{key:1,type:e.unref(x)[e.unref(I).invoiceStatus].btnConfig.type,onClick:e.unref(z)},{default:e.withCtx(()=>[e.createTextVNode(e.toDisplayString(e.unref(x)[e.unref(I).invoiceStatus].btnConfig.text),1)]),_:1},8,["type","onClick"]))])]),default:e.withCtx(()=>[e.createVNode(jo,{"invoice-detail":e.unref(I),"invoice-set-type":e.unref($)},null,8,["invoice-detail","invoice-set-type"])]),_:1},8,["modelValue","onClose"])],64)}}}),Jn="",La=Object.freeze(Object.defineProperty({__proto__:null,default:oe(Ba,[["__scopeId","data-v-436de01a"]])},Symbol.toStringTag,{value:"Module"})),wa=()=>{const t=e.ref([]),o=e.ref([]),a=e.reactive({supplierId:"",productName:"",categoryId:""}),n=[{label:"商品名称",prop:"productName",valueType:"copy",fieldProps:{placeholder:"请输入商品名称"}},{label:"供应商",labelWidth:85,prop:"supplierId",valueType:"select",options:t,fieldProps:{placeholder:"请输入店铺名称",props:{expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:d=>{m(d)}}},{label:"平台类目",valueType:"cascader",options:o,fieldProps:{placeholder:"请选择平台类目",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"},onChange:d=>{d.length>1&&(a.categoryId=d[d.length-1])}}}];async function s(){const{data:d,code:c}=await ye();o.value=d}const m=async(d="")=>{const c=await xe({supplierName:d});t.value=(c==null?void 0:c.data.map(u=>({label:u.name,value:u.id})))||[]};return e.onMounted(()=>{s()}),{searchType:a,supplierList:t,platformCategoryList:o,fetchSupplierList:m,columns:n}},Oa=e.defineComponent({__name:"search",emits:["search"],setup(t,{emit:o}){const a=o,{searchType:n,columns:s}=wa(),m=()=>{const c=he.cloneDeep(n);c.categoryId=Array.isArray(c.categoryId)?c.categoryId.pop():"",a("search",c)},d=()=>{Object.keys(n).forEach(c=>n[c]=""),m()};return(c,u)=>(e.openBlock(),e.createBlock(ue,{modelValue:e.unref(n),"onUpdate:modelValue":u[0]||(u[0]=_=>e.isRef(n)?n.value=_:null),columns:e.unref(s),"show-number":3,onSearchHandle:m,onHandleReset:d},null,8,["modelValue","columns"]))}}),Ra=()=>{const t=e.reactive({supplierId:"",categoryId:"",productName:""}),o=e.reactive({page:{current:1,size:10},total:0}),a=e.ref([]),n=async()=>{const{code:u,data:_}=await Je({...o.page,...t});u===200&&_&&(a.value=(_==null?void 0:_.records)||[],o.total=Number(_==null?void 0:_.total))},s=u=>{Object.keys(u).forEach(_=>{const g=_;t[g]=u[_]}),o.page.current=1,n()},m=e.computed(()=>u=>{const _=((u==null?void 0:u.prices)||[]).map(p=>Number(p)),g=Math.min(..._),r=Math.max(..._);return g===r?r/1e4:`${g/1e4}~￥${r/1e4}`});return{handleSearch:s,waitingPublishData:a,pagination:o,computedSalePrice:m,handleSizeChange:u=>{o.page.size=u,n()},handleCurrentChange:u=>{o.page.current=u,n()},initData:n}},Ma={class:"good"},Aa={class:"good__info"},za={class:"good__info"},Ua={class:"good__img"},Fa=e.defineComponent({__name:"preview",props:{currentProduct:{type:Object,default:()=>({})}},setup(t){const o=t,{divTenThousand:a}=ie(),n=e.ref([]),s=e.ref([]),m=async()=>{const{data:d}=await Ie(ce.useShopInfoStore().shopInfo.id,o.currentProduct.productId);n.value=(d==null?void 0:d.skus)||[],s.value=(d==null?void 0:d.specGroups)||[]};return e.onMounted(()=>m()),(d,c)=>{var r,p;const u=e.resolveComponent("el-image"),_=e.resolveComponent("el-table-column"),g=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",Ma,[e.createElementVNode("div",Aa,"店铺名称："+e.toDisplayString((r=o.currentProduct)==null?void 0:r.supplierName),1),e.createElementVNode("div",za,"商品名称："+e.toDisplayString((p=o.currentProduct)==null?void 0:p.productName),1),e.createElementVNode("div",Ua,[c[0]||(c[0]=e.createElementVNode("text",null,"商品图片：",-1)),e.createVNode(u,{"preview-src-list":[o.currentProduct.image],src:o.currentProduct.image,style:{width:"100px",height:"100px"}},null,8,["preview-src-list","src"])]),c[1]||(c[1]=e.createElementVNode("div",null,"规格：",-1)),e.createVNode(g,{data:n.value,"header-row-style":{"font-size":"12px",color:"#000000"},height:"350",stripe:""},{default:e.withCtx(()=>[s.value.length?(e.openBlock(),e.createBlock(_,{key:0,align:"center",label:"规格"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString((i==null?void 0:i.specs)&&(i==null?void 0:i.specs.join("-"))),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(_,{align:"center",label:"sku图"},{default:e.withCtx(({row:i})=>[e.createVNode(u,{src:i==null?void 0:i.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(_,{align:"center",label:"销售价(元)"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.salePrice&&e.unref(a)(i.salePrice)),1)]),_:1}),e.createVNode(_,{align:"center",label:"划线价(元)",prop:"originalPrice"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.price&&e.unref(a)(i.price)),1)]),_:1}),e.createVNode(_,{align:"center",label:"重量(kg)",prop:"weight"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.weight),1)]),_:1})]),_:1},8,["data"])])}}}),el="",qa=oe(Fa,[["__scopeId","data-v-b5df09d6"]]),Ha=()=>{const t=e.ref(),o=e.ref(!1);return{currentRow:t,showPreviewDialog:o,openPreviewDialog:n=>{t.value=n,o.value=!0}}},ja={class:"commodity-info"},Ga=["src"],Ya={class:"commodity-info-text"},Wa={key:1},Ka={style:{color:"#999"}},Xa={style:{color:"#f54319"}},Ja={class:"customer"},Za={class:"customer__name"};var Oe=(t=>(t.REAL_PRODUCT="实物商品",t.VIRTUAL_PRODUCT="虚拟商品",t))(Oe||{});const Qa=e.defineComponent({__name:"index",setup(t){const{handleSearch:o,waitingPublishData:a,pagination:n,computedSalePrice:s,handleSizeChange:m,handleCurrentChange:d,initData:c}=Ra(),{currentRow:u,showPreviewDialog:_,openPreviewDialog:g}=Ha();c();const r=pe.useRouter(),p=y=>{r.push({path:"/goods/purchase/release",query:{id:y==null?void 0:y.productId,supplierId:y==null?void 0:y.supplierId,type:"ProcurementRelease"}})},i=y=>{_e(ce.useShopInfoStore().shopInfo.id,y.shopId).then(({code:h})=>{h===200&&r.push({path:"/message/customer/supplierService",query:{id:y.supplierId}})})};return(y,h)=>{const V=e.resolveComponent("el-tooltip"),P=e.resolveComponent("el-button"),M=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(Oa,{onSearch:e.unref(o)},null,8,["onSearch"]),e.createVNode(e.unref(Se),{data:e.unref(a),"no-border":"",style:{overflowY:"auto"}},{default:e.withCtx(()=>[e.createVNode(ne,{label:"商品名称",width:"320"},{default:e.withCtx(({row:k})=>[e.createElementVNode("div",ja,[e.createElementVNode("img",{src:k==null?void 0:k.image},null,8,Ga),e.createElementVNode("div",Ya,[(k==null?void 0:k.productName.length)>=30?(e.openBlock(),e.createBlock(V,{key:0,class:"box-item",effect:"dark",content:k==null?void 0:k.productName,placement:"top"},{default:e.withCtx(()=>[e.createElementVNode("p",null,e.toDisplayString(k==null?void 0:k.productName),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("p",Wa,e.toDisplayString(k==null?void 0:k.productName),1)),e.createElementVNode("span",Ka,e.toDisplayString(Oe[k.productType]),1)])])]),_:1}),e.createVNode(ne,{label:"供货价",width:"200"},{default:e.withCtx(({row:k})=>[e.createElementVNode("span",Xa," ￥"+e.toDisplayString(e.unref(s)(k)),1)]),_:1}),e.createVNode(ne,{label:"采购数",prop:"num",width:"120"}),e.createVNode(ne,{label:"入库数",prop:"stockInCount",width:"120"}),e.createVNode(ne,{label:"所属供应商",prop:"supplierName",width:"220"},{default:e.withCtx(({row:k})=>[e.createElementVNode("div",Ja,[e.createElementVNode("div",null,[e.createElementVNode("span",Za,e.toDisplayString(k.supplierName),1),e.createVNode(me,{name:"icon-xiaoxi-copy",size:"18px",style:{cursor:"pointer"},svg:"",onClick:R=>i(k)},null,8,["onClick"])]),e.createElementVNode("span",null,e.toDisplayString(k.supplierContractNumber),1)])]),_:1}),e.createVNode(ne,{align:"right",label:"操作",width:"120",fixed:"right"},{default:e.withCtx(({row:k})=>[e.createElementVNode("div",null,[e.createVNode(P,{link:"",size:"small",type:"primary",onClick:R=>e.unref(g)(k)},{default:e.withCtx(()=>h[1]||(h[1]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),e.createVNode(P,{link:"",size:"small",type:"primary",onClick:R=>p(k)},{default:e.withCtx(()=>h[2]||(h[2]=[e.createTextVNode("发布")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"]),e.createVNode(ge,{"page-size":e.unref(n).page.size,"page-num":e.unref(n).page.current,total:e.unref(n).total,onHandleSizeChange:e.unref(m),onHandleCurrentChange:e.unref(d)},null,8,["page-size","page-num","total","onHandleSizeChange","onHandleCurrentChange"]),e.createVNode(M,{modelValue:e.unref(_),"onUpdate:modelValue":h[0]||(h[0]=k=>e.isRef(_)?_.value=k:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"查看",width:"1000px"},{default:e.withCtx(()=>[e.createVNode(qa,{"current-product":e.unref(u)},null,8,["current-product"])]),_:1},8,["modelValue"])],64)}}}),ol="",va=Object.freeze(Object.defineProperty({__proto__:null,default:oe(Qa,[["__scopeId","data-v-f95351ab"]])},Symbol.toStringTag,{value:"Module"})),en=()=>{const t=e.ref(!1),o=e.ref([]),a=e.ref([]),n=e.reactive({supplierId:"",productName:"",shopCategoryId:""}),s=[{label:"商品名称",prop:"productName",valueType:"copy",fieldProps:{placeholder:"请输入商品名称"}},{label:"供应商",labelWidth:85,prop:"supplierId",valueType:"select",options:o,fieldProps:{placeholder:"请输入店铺名称",props:{expandTrigger:"hover"},filterable:!0,remote:!0,reserveKeyword:!0,remoteMethod:c=>{d(c)}}},{label:"平台类目",valueType:"cascader",options:a,fieldProps:{placeholder:"请选择平台类目",props:{expandTrigger:"hover",label:"name",value:"id",children:"secondCategoryVos"},onChange:c=>{c.length>1&&(n.shopCategoryId=c[c.length-1])}}}];async function m(){const{data:c,code:u}=await ye();a.value=c}const d=async(c="")=>{var _;const u=await xe({supplierName:c});u.data&&((_=u.data)!=null&&_.length)&&(o.value=u.data.map(g=>({label:g.name,value:g.id})))};return e.onMounted(()=>{m()}),{isShow:t,searchType:n,columns:s,supplierList:o,platformCategoryList:a,fetchSupplierList:d}},tn=e.defineComponent({__name:"search",emits:["search"],setup(t,{emit:o}){const a=o,{searchType:n,columns:s}=en(),m=()=>{const c=he.cloneDeep(n);c.shopCategoryId=Array.isArray(c.shopCategoryId)?c.shopCategoryId.pop():"",a("search",c)},d=()=>{Object.keys(n).forEach(c=>n[c]=""),m()};return(c,u)=>(e.openBlock(),e.createBlock(ue,{modelValue:e.unref(n),"onUpdate:modelValue":u[0]||(u[0]=_=>e.isRef(n)?n.value=_:null),columns:e.unref(s),"show-number":3,onSearchHandle:m,onHandleReset:d},null,8,["modelValue","columns"]))}}),on=()=>{const t=e.ref(),o=e.ref(!1);return{currentRow:t,showPreviewDialog:o,openPreviewDialog:n=>{t.value=n,o.value=!0}}},an=()=>{const t=pe.useRouter(),o=e.reactive({page:{current:1,size:10},total:0}),a=e.reactive({supplierId:"",productName:"",shopCategoryId:"",status:""}),n=e.ref([]),s=async()=>{let p=[],i=0;try{const y=await Ze({...o.page,...a});y.code===200&&y.data&&(p=y.data.records,i=Number(y.data.total))}finally{n.value=p,o.total=i}},m=p=>{Object.keys(p).forEach(i=>{const y=i;a[y]=p[y]||""}),o.page.current=1,s()},d=p=>{B.ElMessageBox.confirm("确认上架当前商品").then(()=>{Qe(p).then(({code:i,msg:y})=>{i===200?(B.ElMessage.success({message:y||"上架成功"}),s()):B.ElMessage.error({message:y||"上架失败"})})})},c=e.computed(()=>p=>{const i=((p==null?void 0:p.salePrices)||[]).map(V=>Number(V)),y=Math.min(...i),h=Math.max(...i);return y===h?h/1e4:`${y/1e4}~￥${h/1e4}`});return{handleSearch:m,releaseData:n,pagination:o,initData:s,handleSaleOn:d,searchType:a,changeStatus:p=>{a.status=p,o.page.current=1,s()},handleSizeChange:p=>{o.page.size=p,s()},handleCurrentChange:p=>{o.page.current=p,s()},computedSalePrice:c,handleContact:p=>{_e(ce.useShopInfoStore().shopInfo.id,p.shopId).then(({code:i})=>{i===200&&t.push({path:"/message/customer/supplierService",query:{id:p.supplierId}})})}}},nn={class:"good"},ln={class:"good__info"},rn={class:"good__info"},sn={class:"good__img"},cn=e.defineComponent({__name:"preview",props:{currentProduct:{type:Object,default:()=>({})}},setup(t){const o=t,{divTenThousand:a}=ie(),n=e.ref([]),s=e.ref([]),m=async()=>{const{data:d}=await Ie(ce.useShopInfoStore().shopInfo.id,o.currentProduct.id);n.value=(d==null?void 0:d.skus)||[],s.value=(d==null?void 0:d.specGroups)||[]};return e.onMounted(()=>m()),(d,c)=>{var r,p;const u=e.resolveComponent("el-image"),_=e.resolveComponent("el-table-column"),g=e.resolveComponent("el-table");return e.openBlock(),e.createElementBlock("div",nn,[e.createElementVNode("div",ln,"店铺名称："+e.toDisplayString((r=o.currentProduct)==null?void 0:r.supplierName),1),e.createElementVNode("div",rn,"商品名称："+e.toDisplayString((p=o.currentProduct)==null?void 0:p.name),1),e.createElementVNode("div",sn,[c[0]||(c[0]=e.createElementVNode("text",null,"商品图片：",-1)),e.createVNode(u,{"preview-src-list":[o.currentProduct.pic],src:o.currentProduct.pic,style:{width:"100px",height:"100px"}},null,8,["preview-src-list","src"])]),c[1]||(c[1]=e.createElementVNode("div",null,"规格：",-1)),e.createVNode(g,{data:n.value,"header-row-style":{"font-size":"12px",color:"#000000"},height:"350",stripe:""},{default:e.withCtx(()=>[s.value.length?(e.openBlock(),e.createBlock(_,{key:0,align:"center",label:"规格"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString((i==null?void 0:i.specs)&&(i==null?void 0:i.specs.join("-"))),1)]),_:1})):e.createCommentVNode("",!0),e.createVNode(_,{align:"center",label:"sku图"},{default:e.withCtx(({row:i})=>[e.createVNode(u,{src:i==null?void 0:i.image,style:{width:"50px",height:"50px"}},null,8,["src"])]),_:1}),e.createVNode(_,{align:"center",label:"销售价(元)"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.salePrice&&e.unref(a)(i.salePrice)),1)]),_:1}),e.createVNode(_,{align:"center",label:"划线价(元)",prop:"originalPrice"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.price&&e.unref(a)(i.price)),1)]),_:1}),e.createVNode(_,{align:"center",label:"重量(kg)",prop:"weight"},{default:e.withCtx(({row:i})=>[e.createElementVNode("span",null,e.toDisplayString(i.weight),1)]),_:1})]),_:1},8,["data"])])}}}),sl="",dn=oe(cn,[["__scopeId","data-v-b854e3eb"]]),pn=()=>{const t=e.ref(!1),o=e.ref();return{violationReason:o,showViolationReason:t,openViolationReasonDialog:n=>{o.value=n,t.value=!0}}},mn={style:{"line-height":"30px"}},fn=["src"],hn=e.defineComponent({__name:"violation-reason",props:{productViolation:{type:Object,default:()=>({})}},setup(t){const o={PROHIBITED:"违禁品",COUNTERFEIT:"假冒伪劣",EXCESSIVE_PLATFORM_INTERVENTION:"平台介入率太高",TITLE_IRREGULARITY:"标题有问题",OTHER:"其他"},a=t,n=e.computed(()=>{var s;return{...a.productViolation,violationType:o[a.productViolation.violationType],violationEvidence:(s=a.productViolation.violationEvidence)==null?void 0:s.split(",")}});return(s,m)=>{const d=e.resolveComponent("el-col"),c=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",mn,[e.createVNode(c,{gutter:8},{default:e.withCtx(()=>[e.createVNode(d,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查员："+e.toDisplayString(n.value.rummager),1)]),_:1}),e.createVNode(d,{span:12},{default:e.withCtx(()=>[e.createElementVNode("div",null,"检查时间："+e.toDisplayString(n.value.examineDateTime),1)]),_:1})]),_:1}),e.createElementVNode("div",null,"类型："+e.toDisplayString(n.value.violationType),1),e.createElementVNode("div",null,"原因："+e.toDisplayString(n.value.violationExplain),1),e.createElementVNode("div",null,[m[0]||(m[0]=e.createTextVNode(" 相关证据： ")),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.violationEvidence,(u,_)=>(e.openBlock(),e.createElementBlock("img",{key:_,src:u,class:"violation-evidence"},null,8,fn))),128))])])}}}),cl="",un=oe(hn,[["__scopeId","data-v-cdc1ed69"]]),gn={class:"tab_container"},_n={class:"commodity-info"},xn=["src"],yn={class:"commodity-info-text"},Nn={key:1},Cn={class:"type_tag"},Vn={style:{color:"#f54319"}},Sn={key:0},bn={key:1},kn={key:2,style:{color:"#999"}},En={key:3,style:{color:"#f54319"}},Tn={class:"customer"},In={class:"customer__name"},Dn=e.defineComponent({__name:"index",setup(t){const{handleSearch:o,releaseData:a,pagination:n,initData:s,handleSaleOn:m,searchType:d,changeStatus:c,handleSizeChange:u,handleCurrentChange:_,computedSalePrice:g,handleContact:r}=an(),{currentRow:p,showPreviewDialog:i,openPreviewDialog:y}=on(),{violationReason:h,showViolationReason:V,openViolationReasonDialog:P}=pn();return s(),(M,k)=>{const R=e.resolveComponent("el-tab-pane"),O=e.resolveComponent("el-tabs"),G=e.resolveComponent("el-tooltip"),Y=e.resolveComponent("el-button"),D=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(tn,{onSearch:e.unref(o)},null,8,["onSearch"]),e.createElementVNode("div",gn,[e.createVNode(O,{"model-value":e.unref(d).status,"onUpdate:modelValue":e.unref(c)},{default:e.withCtx(()=>[e.createVNode(R,{label:"全部",name:""}),e.createVNode(R,{label:"已上架",name:"SELL_ON"}),e.createVNode(R,{label:"已下架",name:"SELL_OFF"}),e.createVNode(R,{label:"违规下架",name:"PLATFORM_SELL_OFF"})]),_:1},8,["model-value","onUpdate:modelValue"])]),e.createVNode(e.unref(Se),{data:e.unref(a),style:{overflowY:"auto"},"no-border":""},{default:e.withCtx(()=>[e.createVNode(ne,{label:"商品名称",width:"360"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",_n,[e.createElementVNode("img",{src:l==null?void 0:l.pic},null,8,xn),e.createElementVNode("div",yn,[(l==null?void 0:l.name.length)>=30?(e.openBlock(),e.createBlock(G,{key:0,class:"box-item",effect:"dark",content:l==null?void 0:l.name,placement:"top"},{default:e.withCtx(()=>[e.createElementVNode("p",null,e.toDisplayString(l==null?void 0:l.name),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("p",Nn,e.toDisplayString(l==null?void 0:l.name),1)),e.createElementVNode("div",Cn,e.toDisplayString(l.productType==="REAL_PRODUCT"?"实物商品":"虚拟商品"),1)])])]),_:1}),e.createVNode(ne,{label:"价格",width:"200"},{default:e.withCtx(({row:l})=>[e.createElementVNode("span",Vn," ￥"+e.toDisplayString(e.unref(g)(l)),1)]),_:1}),e.createVNode(ne,{label:"库存",width:"140"},{default:e.withCtx(({row:l})=>{var f;return[e.createTextVNode(e.toDisplayString((f=l==null?void 0:l.storageSkus)==null?void 0:f.reduce((x,I)=>x+Number(I.stock),0)),1)]}),_:1}),e.createVNode(ne,{label:"状态",width:"120"},{default:e.withCtx(({row:l})=>[l!=null&&l.delete?(e.openBlock(),e.createElementBlock("span",Sn,"下架")):(l==null?void 0:l.status)==="SELL_ON"?(e.openBlock(),e.createElementBlock("span",bn,"已上架")):(l==null?void 0:l.status)==="SELL_OFF"?(e.openBlock(),e.createElementBlock("span",kn,"已下架")):(l==null?void 0:l.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createElementBlock("span",En,"违规下架")):e.createCommentVNode("",!0)]),_:1}),e.createVNode(ne,{label:"所属供应商",prop:"supplierName",width:"140"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",Tn,[e.createElementVNode("div",null,[e.createElementVNode("span",In,e.toDisplayString(l.supplierName),1),e.createVNode(me,{name:"icon-xiaoxi-copy",size:"18px",style:{cursor:"pointer"},svg:"",onClick:f=>e.unref(r)(l)},null,8,["onClick"])]),e.createElementVNode("span",null,e.toDisplayString(l.supplierContractNumber),1)])]),_:1}),e.createVNode(ne,{fixed:"right",label:"操作",align:"right",width:"140"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,[e.createVNode(Y,{link:"",size:"small",type:"primary",onClick:f=>e.unref(y)(l)},{default:e.withCtx(()=>k[2]||(k[2]=[e.createTextVNode("查看")])),_:2},1032,["onClick"]),l!=null&&l.delete||(l==null?void 0:l.status)==="SELL_OFF"?(e.openBlock(),e.createBlock(Y,{key:0,link:"",size:"small",type:"primary",onClick:f=>e.unref(m)(l==null?void 0:l.id)},{default:e.withCtx(()=>k[3]||(k[3]=[e.createTextVNode("上架 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),(l==null?void 0:l.status)==="PLATFORM_SELL_OFF"?(e.openBlock(),e.createBlock(Y,{key:1,link:"",size:"small",type:"primary",onClick:f=>{var x;return e.unref(P)((x=l==null?void 0:l.extra)==null?void 0:x.productViolation)}},{default:e.withCtx(()=>k[4]||(k[4]=[e.createTextVNode("违规原因 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)])]),_:1})]),_:1},8,["data"]),e.createVNode(ge,{"page-size":e.unref(n).page.size,"page-num":e.unref(n).page.current,total:e.unref(n).total,onHandleSizeChange:e.unref(u),onHandleCurrentChange:e.unref(_)},null,8,["page-size","page-num","total","onHandleSizeChange","onHandleCurrentChange"]),e.createVNode(D,{modelValue:e.unref(i),"onUpdate:modelValue":k[0]||(k[0]=l=>e.isRef(i)?i.value=l:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"查看",width:"1000px"},{default:e.withCtx(()=>[e.createVNode(dn,{"current-product":e.unref(p)},null,8,["current-product"])]),_:1},8,["modelValue"]),e.createVNode(D,{modelValue:e.unref(V),"onUpdate:modelValue":k[1]||(k[1]=l=>e.isRef(V)?V.value=l:null),"close-on-click-modal":!1,"destroy-on-close":"",title:"违规原因",width:"600px"},{default:e.withCtx(()=>[e.createVNode(un,{"product-violation":e.unref(h)},null,8,["product-violation"])]),_:1},8,["modelValue"])],64)}}}),dl="",Pn=Object.freeze(Object.defineProperty({__proto__:null,default:oe(Dn,[["__scopeId","data-v-7336ff23"]])},Symbol.toStringTag,{value:"Module"}));return He});
