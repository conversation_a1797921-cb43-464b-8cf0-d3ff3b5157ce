package com.medusa.gruul.addon.supplier.modules.order.controller;

import com.medusa.gruul.addon.supplier.model.bo.OrderTimeout;
import com.medusa.gruul.addon.supplier.modules.order.service.SupplierOrderConfigService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * date 2023/7/24
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/supplier/order/config")
public class SupplierOrderConfigController {


    private final SupplierOrderConfigService supplierOrderConfigService;

    /**
     * 查询订单超时时间配置
     *
     * @return 超时时间配置
     */
    @Log("查询订单超时时间配置")
    @GetMapping("/timeout")
    public Result<OrderTimeout> timeout() {
        return Result.ok(supplierOrderConfigService.orderTimeout());
    }

    /**
     * 更新订单超时时间配置
     *
     * @param timeout 超时时间配置
     * @return void
     */
    @Log("更新订单超时时间配置")
    @PreAuthorize("""
            @S.matcher().any(@S.ROLES,@S.R.SUPPLIER_ADMIN,@S.PLATFORM_ADMIN)
            .or(@S.consumer().eq(@S.ROLES,@S.PLATFORM_CUSTOM_ADMIN).eq(@S.PERMS,'generalSet'))
            .match()
                       """)
    @PutMapping("/timeout")
    public Result<Void> updateOrderConfig(@RequestBody @Valid OrderTimeout timeout) {
        supplierOrderConfigService.updateOrderTimeout(timeout);
        return Result.ok();
    }
}
