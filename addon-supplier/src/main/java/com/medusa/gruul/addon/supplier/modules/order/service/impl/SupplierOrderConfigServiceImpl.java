package com.medusa.gruul.addon.supplier.modules.order.service.impl;

import com.medusa.gruul.addon.supplier.model.SupplierConst;
import com.medusa.gruul.addon.supplier.model.bo.OrderTimeout;
import com.medusa.gruul.addon.supplier.modules.order.service.SupplierOrderConfigService;
import com.medusa.gruul.common.redis.util.RedisUtil;
import io.vavr.control.Option;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * date 2023/7/24
 */
@Service
public class SupplierOrderConfigServiceImpl implements SupplierOrderConfigService {

    private OrderTimeout timeout;

    @Override
    public void updateOrderTimeout(OrderTimeout timeout) {
        RedisUtil.setCacheMap(SupplierConst.ORDER_TIMEOUT_CONFIG_KEY, timeout);
        this.timeout = timeout;
    }

    @Override
    public OrderTimeout orderTimeout() {
        return this.timeout = Option.of(this.timeout)
                .orElse(() -> Option.of(RedisUtil.getCacheMap(SupplierConst.ORDER_TIMEOUT_CONFIG_KEY, OrderTimeout.class)))
                .getOrElse(OrderTimeout::new);
    }


}
