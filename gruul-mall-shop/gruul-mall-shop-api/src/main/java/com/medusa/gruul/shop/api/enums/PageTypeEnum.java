package com.medusa.gruul.shop.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 页面类型枚举
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PageTypeEnum {
    /**
     * 商城首页(推荐)
     */
    RECOMMENDED_MALL_HOME_PAGE(1, true, "商城首页(推荐)"),

    /**
     * 商城首页(同城)
     */
    SAME_CITY_MALL_HOME_PAGE(2, true, "商城首页(同城)"),

    /**
     * 商品分类
     */
    PRODUCT_CATEGORY_PAGE(3, true, "商品分类"),

    /**
     * 底部导航
     */
    BOTTOM_NAVIGATION_PAGE(4, true, "底部导航"),

    /**
     * 个人中心
     */
    PERSONAL_CENTER_PAGE(5, true, "个人中心"),

    /**
     * 自定义页面
     */
    CUSTOMIZED_PAGE(6, true, "自定义页面"),

    /**
     * 店铺首页
     */
    SHOP_HOME_PAGE(7, false, "店铺首页"),

    /**
     * 店铺底部导航
     */
    SHOP_BOTTOM_NAVIGATION_PAGE(8, false, "店铺底部导航"),

    /**
     * 店铺分类
     */
    SHOP_CATEGORY_PAGE(9, false, "店铺分类"),

    /**
     * 自定义页面
     */
    SHOP_CUSTOMIZED_PAGE(10, false, "自定义页面"),

    /**
     * 商城首页(关注)
     */
    MY_FOLLOW_MALL_HOME_PAGE(11, true, "商城首页(关注)"),

    /**
     * 扫码点餐
     */
    SCAN_CODE_TO_ORDER(12, true, "扫码点餐"),
    ;

    @EnumValue
    private final Integer value;

    /**
     * 是否是平台页面
     */
    private final boolean platform;

    private final String desc;
}
