
package com.medusa.gruul.shop.service.controller.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.shop.service.model.dto.ShopQueryPageDTO;
import com.medusa.gruul.shop.service.model.vo.ShopListVO;
import com.medusa.gruul.shop.service.mp.service.IShopService;


import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺相关接口
 * <AUTHOR>
 * @version 1.0, 2025/5/21
 */
@Validated
@RestController
@RequestMapping ("/api/shop")
@RequiredArgsConstructor
public class ApiShopController {

	private final IShopService shopService;
	/**
	 * 平台 店铺列表
	 *
	 * @param queryPageDTO 查询条件
	 * @return <店铺基础信息>
	 */
	@Log ("获取平台 店铺列表")
	@PostMapping ("/platform/pageList")
	public Result<Page<ShopListVO>> shopPageList(@RequestBody ShopQueryPageDTO queryPageDTO) {
		queryPageDTO.setPlatformId(ISystem.platformIdMust());

		Page<ShopListVO> page = (Page<ShopListVO>) shopService.pageShopList(queryPageDTO);
		List<ShopListVO> records = page.getRecords().stream().map(
				shopListVO -> {
					if (shopListVO.getLocation() != null) {
						shopListVO.setLongitude(shopListVO.getLocation().getX());
						shopListVO.setLatitude(shopListVO.getLocation().getY());
					}
					return shopListVO;
				}
		).collect(Collectors.toList());
		page.setRecords(records);

		return Result.ok(page);
	}
}
