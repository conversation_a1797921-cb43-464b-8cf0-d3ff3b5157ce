let api = [];
const apiDocListSize = 1
api.push({
    name: 'default',
    order: '1',
    list: []
})
api[0].list.push({
    alias: 'InvoiceAttachmentController',
    order: '1',
    link: '发票附件表_前端控制器',
    desc: '发票附件表 前端控制器',
    list: []
})
api[0].list[0].list.push({
    order: '1',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceAttachment/upload',
    desc: '上传发票附件/重新上传发票附件',
});
api[0].list[0].list.push({
    order: '2',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceAttachment/re-send',
    desc: '重新发送附件',
});
api[0].list[0].list.push({
    order: '3',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceAttachment/downloadInvoiceAttachment',
    desc: '下载发票附件',
});
api[0].list.push({
    alias: 'InvoiceHeaderController',
    order: '2',
    link: '&lt;p&gt;发票抬头控制器&lt;/p&gt;',
    desc: '&lt;p&gt;发票抬头控制器&lt;/p&gt;',
    list: []
})
api[0].list[1].list.push({
    order: '1',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers/invoice-header',
    desc: '创建发票抬头',
});
api[0].list[1].list.push({
    order: '2',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers/pageInvoiceHeader',
    desc: '分页查询发票抬头',
});
api[0].list[1].list.push({
    order: '3',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers/{invoiceHeaderId}',
    desc: '删除发票抬头',
});
api[0].list[1].list.push({
    order: '4',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers/default-invoice-header',
    desc: '设置默认抬头',
});
api[0].list[1].list.push({
    order: '5',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers/getDefaultInvoiceHeader',
    desc: '获取默认抬头',
});
api[0].list[1].list.push({
    order: '6',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoice-headers',
    desc: '获取发票抬头详情',
});
api[0].list.push({
    alias: 'InvoiceRequestController',
    order: '3',
    link: '发票申请表_前端控制器',
    desc: '发票申请表 前端控制器',
    list: []
})
api[0].list[2].list.push({
    order: '1',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest/pre-request',
    desc: '申请开票-前置校验',
});
api[0].list[2].list.push({
    order: '2',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest',
    desc: '申请开票',
});
api[0].list[2].list.push({
    order: '3',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest',
    desc: '分页查询开票申请',
});
api[0].list[2].list.push({
    order: '4',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest/{id}',
    desc: '获取发票申请详情',
});
api[0].list[2].list.push({
    order: '5',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest/{id}',
    desc: '撤销开票',
});
api[0].list[2].list.push({
    order: '6',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceRequest/refuseInvoiceRequest',
    desc: '拒绝开票申请',
});
api[0].list.push({
    alias: 'InvoiceSettingsController',
    order: '4',
    link: '发票设置表_前端控制器',
    desc: '发票设置表 前端控制器',
    list: []
})
api[0].list[3].list.push({
    order: '1',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceSettings',
    desc: '编辑发票设置',
});
api[0].list[3].list.push({
    order: '2',
    deprecated: 'false',
    url: '/addon-invoice/invoice/invoiceSettings',
    desc: '获取发票设置',
});
document.onkeydown = keyDownSearch;
function keyDownSearch(e) {
    const theEvent = e;
    const code = theEvent.keyCode || theEvent.which || theEvent.charCode;
    if (code === 13) {
        const search = document.getElementById('search');
        const searchValue = search.value.toLocaleLowerCase();

        let searchGroup = [];
        for (let i = 0; i < api.length; i++) {

            let apiGroup = api[i];

            let searchArr = [];
            for (let i = 0; i < apiGroup.list.length; i++) {
                let apiData = apiGroup.list[i];
                const desc = apiData.desc;
                if (desc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                    searchArr.push({
                        order: apiData.order,
                        desc: apiData.desc,
                        link: apiData.link,
                        list: apiData.list
                    });
                } else {
                    let methodList = apiData.list || [];
                    let methodListTemp = [];
                    for (let j = 0; j < methodList.length; j++) {
                        const methodData = methodList[j];
                        const methodDesc = methodData.desc;
                        if (methodDesc.toLocaleLowerCase().indexOf(searchValue) > -1) {
                            methodListTemp.push(methodData);
                            break;
                        }
                    }
                    if (methodListTemp.length > 0) {
                        const data = {
                            order: apiData.order,
                            desc: apiData.desc,
                            link: apiData.link,
                            list: methodListTemp
                        };
                        searchArr.push(data);
                    }
                }
            }
            if (apiGroup.name.toLocaleLowerCase().indexOf(searchValue) > -1) {
                searchGroup.push({
                    name: apiGroup.name,
                    order: apiGroup.order,
                    list: searchArr
                });
                continue;
            }
            if (searchArr.length === 0) {
                continue;
            }
            searchGroup.push({
                name: apiGroup.name,
                order: apiGroup.order,
                list: searchArr
            });
        }
        let html;
        if (searchValue === '') {
            const liClass = "";
            const display = "display: none";
            html = buildAccordion(api,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        } else {
            const liClass = "open";
            const display = "display: block";
            html = buildAccordion(searchGroup,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        }
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            let $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
    }
}

function buildAccordion(apiGroups, liClass, display) {
    let html = "";
    if (apiGroups.length > 0) {
        if (apiDocListSize === 1) {
            let apiData = apiGroups[0].list;
            let order = apiGroups[0].order;
            for (let j = 0; j < apiData.length; j++) {
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#_'+order+'_'+apiData[j].order+'_' + apiData[j].link + '">' + apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                html += '<ul class="sectlevel2" style="'+display+'">';
                let doc = apiData[j].list;
                for (let m = 0; m < doc.length; m++) {
                    let spanString;
                    if (doc[m].deprecated === 'true') {
                        spanString='<span class="line-through">';
                    } else {
                        spanString='<span>';
                    }
                    html += '<li><a href="#_'+order+'_' + apiData[j].order + '_' + doc[m].order + '_' + doc[m].desc + '">' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                }
                html += '</ul>';
                html += '</li>';
            }
        } else {
            for (let i = 0; i < apiGroups.length; i++) {
                let apiGroup = apiGroups[i];
                html += '<li class="'+liClass+'">';
                html += '<a class="dd" href="#_'+apiGroup.order+'_' + apiGroup.name + '">' + apiGroup.order + '.&nbsp;' + apiGroup.name + '</a>';
                html += '<ul class="sectlevel1">';

                let apiData = apiGroup.list;
                for (let j = 0; j < apiData.length; j++) {
                    html += '<li class="'+liClass+'">';
                    html += '<a class="dd" href="#_'+apiGroup.order+'_'+ apiData[j].order + '_'+ apiData[j].link + '">' +apiGroup.order+'.'+ apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
                    html += '<ul class="sectlevel2" style="'+display+'">';
                    let doc = apiData[j].list;
                    for (let m = 0; m < doc.length; m++) {
                       let spanString;
                       if (doc[m].deprecated === 'true') {
                           spanString='<span class="line-through">';
                       } else {
                           spanString='<span>';
                       }
                       html += '<li><a href="#_'+apiGroup.order+'_' + apiData[j].order + '_' + doc[m].order + '_' + doc[m].desc + '">'+apiGroup.order+'.' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + spanString + doc[m].desc + '<span></a> </li>';
                   }
                    html += '</ul>';
                    html += '</li>';
                }

                html += '</ul>';
                html += '</li>';
            }
        }
    }
    return html;
}