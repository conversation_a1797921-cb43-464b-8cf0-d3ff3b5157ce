package com.medusa.gruul.addon.coupon.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.unilink.MemberUnilinkSystemConfigVO;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 会员通
 */
@RestController
@RequestMapping("/memberUnilink")
@RequiredArgsConstructor
public class MemberUnilinkSystemConfigController {

    private final MemberApiService memberApiService;

    /**
     * 从会员中台查询系统配置（终端、渠道、业务）（迁移会员中台接口：/member_unilink_system_config/listConfig）
     *
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台查询系统配置（终端、渠道、业务）")
    @GetMapping("/memberUnilinkSystemConfig")
    Result<MemberUnilinkSystemConfigVO> memberUnilinkSystemConfig() {
        return Result.ok(memberApiService.memberUnilinkSystemConfig());
    }

}
