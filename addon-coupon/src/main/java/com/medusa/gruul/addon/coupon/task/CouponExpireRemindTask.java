package com.medusa.gruul.addon.coupon.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.mp.service.ICouponUserService;
import com.medusa.gruul.common.member.dto.MessagesSendDTO;
import com.medusa.gruul.common.member.enums.WechatMsgSendTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优惠券过期提醒定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CouponExpireRemindTask {

    private final ICouponUserService couponUserService;
    private final MemberApiService memberApiService;
    private final UaaRpcService uaaRpcService;
    
    /**
     * Redis缓存key前缀，用于存储已发送过提醒的优惠券ID
     */
    private static final String COUPON_REMIND_CACHE_KEY = "coupon:expire:remind";

    /**
     * 每15分钟执行一次优惠券过期提醒
     */
    @Async("taskScheduler")
    @Scheduled(cron = "0 */15 * * * ?")
    public void remindExpireCoupon() {
        log.info("开始执行优惠券过期提醒任务,当前时间：{}", LocalDateTime.now());
        try {
            TenantShop.disable(() -> {
                List<CouponUser> expireCoupons = getExpireCoupons();
                if (CollUtil.isEmpty(expireCoupons)) {
                    log.info("没有需要提醒的优惠券");
                    return;
                }

                // 过滤掉已经发送过提醒的优惠券
                List<CouponUser> pendingReminderCoupons = filterPendingReminderCoupons(expireCoupons);
                if (CollUtil.isEmpty(pendingReminderCoupons)) {
                    log.info("所有即将过期的优惠券都已发送过提醒");
                    return;
                }

                Map<Long, UserInfoVO> userInfoMap = getUserInfoMap(pendingReminderCoupons);
                // 按platformId分组处理优惠券
                Map<Long, List<CouponUser>> platformCoupons = pendingReminderCoupons.stream()
                        .filter(coupon -> coupon.getPlatformId() != null)
                        .collect(Collectors.groupingBy(CouponUser::getPlatformId));

                processPlatformCoupons(platformCoupons, userInfoMap);
            });
        } catch (Exception e) {
            log.error("执行优惠券过期提醒任务失败", e);
        }
    }

    /**
     * 获取即将过期的优惠券列表
     */
    private List<CouponUser> getExpireCoupons() {
        return couponUserService.lambdaQuery()
                .eq(CouponUser::getUsed, false)  // 未使用的优惠券
                .eq(CouponUser::getDeleted, false)  // 未删除的优惠券
                .apply("DATE(end_date) <= DATE_ADD(CURDATE(), INTERVAL 3 DAY)")  // 3天内过期
                .apply("DATE(end_date) >= CURDATE()")  // 还未过期
                .list();
    }

    /**
     * 过滤掉已经发送过提醒的优惠券
     * 
     * @param expireCoupons 即将过期的优惠券列表
     * @return 未发送过提醒的优惠券列表
     */
    private List<CouponUser> filterPendingReminderCoupons(List<CouponUser> expireCoupons) {
        return expireCoupons.stream()
                .filter(coupon -> !isReminderSent(coupon))
                .toList();
    }
    
    /**
     * 检查优惠券是否已经发送过提醒
     * 
     * @param coupon 优惠券对象
     * @return 是否已经发送过提醒
     */
    private boolean isReminderSent(CouponUser coupon) {
        String cacheKey = RedisUtil.key(COUPON_REMIND_CACHE_KEY, coupon.getPlatformId(), coupon.getId());
        Boolean hasKey = RedisUtil.getRedisTemplate().hasKey(cacheKey);
        return Boolean.TRUE.equals(hasKey);
    }
    
    /**
     * 标记优惠券已发送提醒
     * 
     * @param coupon 优惠券对象
     */
    private void markReminderSent(CouponUser coupon) {
        String cacheKey = RedisUtil.key(COUPON_REMIND_CACHE_KEY, coupon.getPlatformId(), coupon.getId());
        // 设置3天的过期时间，因为只会提醒3天内过期的券
        RedisUtil.setCacheObject(cacheKey, true, 3, TimeUnit.DAYS);
    }

    /**
     * 获取用户信息映射
     */
    private Map<Long, UserInfoVO> getUserInfoMap(List<CouponUser> expireCoupons) {
        Set<Long> userIds = expireCoupons.stream()
                .map(CouponUser::getUserId)
                .filter(userId -> userId != null)
                .collect(Collectors.toSet());

        if (userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, UserInfoVO> userMap = uaaRpcService.getUserDataBatchByUserIds(userIds);
        return userMap != null ? userMap : new HashMap<>();
    }

    /**
     * 按平台分组处理优惠券
     */
    private void processPlatformCoupons(Map<Long, List<CouponUser>> platformCoupons, Map<Long, UserInfoVO> userInfoMap) {
        for (Map.Entry<Long, List<CouponUser>> entry : platformCoupons.entrySet()) {
            Long platformId = entry.getKey();
            List<CouponUser> coupons = entry.getValue();

            try {
                setupSystemContext(platformId);
                try {
                    processSinglePlatform(platformId, coupons, userInfoMap);
                } finally {
                    // 清理系统上下文
                    SystemContextHolder.clear();
                }
            } catch (Exception e) {
                log.error("发送平台{}的优惠券过期提醒失败: {}", platformId, e.getMessage(), e);
            }
        }
    }
    
    /**
     * 设置系统上下文
     * 
     * @param platformId 平台ID
     */
    private void setupSystemContext(Long platformId) {
        Systems systems = new Systems();
        systems.setPlatformId(platformId);
        systems.setEnterpriseId(platformId);
        SystemContextHolder.set(systems);
    }
    
    /**
     * 处理单个平台的优惠券提醒
     * 
     * @param platformId 平台ID
     * @param coupons 该平台的优惠券列表
     * @param userInfoMap 用户信息映射
     */
    private void processSinglePlatform(Long platformId, List<CouponUser> coupons, Map<Long, UserInfoVO> userInfoMap) {
        // 按用户ID分组统计优惠券数量
        Map<Long, List<CouponUser>> userCoupons = coupons.stream()
                .filter(coupon -> coupon.getUserId() != null)
                .collect(Collectors.groupingBy(CouponUser::getUserId));
        
        // 生成所有用户的消息
        List<MessagesSendDTO> messages = generateUserMessages(userCoupons, userInfoMap);
        
        // 发送消息
        sendMessages(platformId, messages);
    }
    
    /**
     * 为每个用户生成消息
     * 
     * @param userCoupons 按用户ID分组的优惠券
     * @param userInfoMap 用户信息映射
     * @return 生成的消息列表
     */
    private List<MessagesSendDTO> generateUserMessages(Map<Long, List<CouponUser>> userCoupons, Map<Long, UserInfoVO> userInfoMap) {
        // 使用ArrayList创建可修改的列表，因为后续需要添加元素
        List<MessagesSendDTO> messages = new ArrayList<>();
        
        for (Map.Entry<Long, List<CouponUser>> userEntry : userCoupons.entrySet()) {
            UserInfoVO userInfo = userInfoMap.get(userEntry.getKey());
            if (userInfo != null) {
                List<CouponUser> userCouponList = userEntry.getValue();
                // 使用第一张优惠券的信息作为模板，并传入总数量
                CouponUser firstCoupon = userCouponList.get(0);
                messages.add(buildMessage(firstCoupon, userInfo, userCouponList.size()));
                
                // 标记所有优惠券为已发送提醒
                markCouponsAsReminded(userCouponList);
            }
        }
        
        return messages;
    }
    
    /**
     * 标记所有优惠券为已发送提醒
     * 
     * @param coupons 优惠券列表
     */
    private void markCouponsAsReminded(List<CouponUser> coupons) {
        for (CouponUser coupon : coupons) {
            markReminderSent(coupon);
        }
    }
    
    /**
     * 发送消息
     * 
     * @param platformId 平台ID
     * @param messages 消息列表
     */
    private void sendMessages(Long platformId, List<MessagesSendDTO> messages) {
        if (!messages.isEmpty()) {
            log.info("批量发送优惠券过期提醒消息，平台ID：{}，消息数量：{}", platformId, messages.size());
            for (MessagesSendDTO message : messages) {
                try {
                    message.setOperSubjectGuid(String.valueOf(platformId));
                    memberApiService.wechatMessageSendBatch(message);
                } catch (Exception e) {
                    log.error("发送优惠券过期提醒消息失败，平台ID：{}，用户手机：{}, 错误：{}", 
                            platformId, message.getPhone(), e.getMessage(), e);
                    // 继续处理下一条消息，不抛出异常
                }
            }
        }
    }

    /**
     * 构建消息对象
     */
    private MessagesSendDTO buildMessage(CouponUser coupon, UserInfoVO userInfo, int couponCount) {
        MessagesSendDTO messagesSendQO = new MessagesSendDTO();
        messagesSendQO.setTemplateName(WechatMsgSendTypeEnum.COUPON_EXPIRE.getMsgTitle());

        // 生成小程序参数
        Map<String, String> params = generateExpireRemindParam(
                coupon.getName(),
                coupon.getEndDate().toString(),
                String.valueOf(couponCount)
        );

        messagesSendQO.setPrams(params);
        messagesSendQO.setOrderNum(String.valueOf(coupon.getId()));
        messagesSendQO.setPhone(userInfo.getMobile());
        messagesSendQO.setMemberName(userInfo.getNickname());

        return messagesSendQO;
    }

    /**
     * 生成过期提醒的小程序参数
     */
    private Map<String, String> generateExpireRemindParam(String couponName, String expireDate, String number7) {
        Map<String, String> params = new HashMap<>();
        params.put("thing2", couponName);        // 优惠券名称
        params.put("number7", number7);          // 券数量
        params.put("time3", expireDate);         // 过期时间
        params.put("thing1", "您的优惠券即将过期"); // 温馨提示
        return params;
    }
} 