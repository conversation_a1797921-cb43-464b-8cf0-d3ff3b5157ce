package com.medusa.gruul.addon.coupon.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.coupon.model.CouponConstant;
import com.medusa.gruul.addon.coupon.model.CouponErrorCode;
import com.medusa.gruul.addon.coupon.model.dto.ConsumerCouponQueryDTO;
import com.medusa.gruul.addon.coupon.model.dto.OrderCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.enums.CouponStatus;
import com.medusa.gruul.addon.coupon.model.vo.CouponVO;
import com.medusa.gruul.addon.coupon.mp.entity.Coupon;
import com.medusa.gruul.addon.coupon.mp.entity.CouponCalculate;
import com.medusa.gruul.addon.coupon.mp.entity.CouponUser;
import com.medusa.gruul.addon.coupon.mp.service.ICouponCalculateService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponService;
import com.medusa.gruul.addon.coupon.mp.service.ICouponUserService;
import com.medusa.gruul.addon.coupon.service.ConsumerCouponService;
import com.medusa.gruul.addon.coupon.service.CouponPlusService;
import com.medusa.gruul.common.member.dto.MessagesSendDTO;
import com.medusa.gruul.common.member.enums.WechatMsgSendTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.shop.api.model.vo.ShopInfoVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2022/11/3
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConsumerCouponServiceImpl implements ConsumerCouponService {

    private final ShopRpcService shopRpcService;
    private final ICouponService couponService;
    private final ICouponUserService couponUserService;
    private final CouponPlusService couponPlusService;
    private final ICouponCalculateService couponCalculateService;

    private final MemberApiService memberApiService;


    @Override
    public IPage<CouponVO> consumerCouponPage(Option<Long> userIdOpt, ConsumerCouponQueryDTO query) {
        query.validParam();
        IPage<CouponVO> page = couponService.consumerCouponPage(userIdOpt.getOrNull(), query);
        List<CouponVO> coupons = page.getRecords();
        //当查询平台券 或 指定了店铺id 都不用去查询店铺名称
        if (query.getIsPlatform() || query.getShopId() != null || CollUtil.isEmpty(coupons)) {
            return page;
        }
        Map<Long, String> shopNameMap = shopRpcService.getShopInfoByShopIdList(
                        coupons.stream().map(CouponVO::getShopId).collect(Collectors.toSet())
                ).stream()
                .collect(Collectors.toMap(ShopInfoVO::getId, ShopInfoVO::getName));
        coupons.forEach(coupon -> coupon.setShopName(shopNameMap.get(coupon.getShopId())));
        return page;
    }

    @Override
    @Redisson(value = CouponConstant.COUPON_USER_COLLECT_LOCK, key = "#userId+':'+#shopId+':'+#couponId")
    public Boolean collectCoupon(Long userId, Long shopId, Long couponId) {
        //查询优惠券详情
        Coupon coupon = couponPlusService.getCoupon(shopId, couponId).getOrElseThrow(() -> new GlobalException(SystemCode.DATA_NOT_EXIST));
        //校验是否不可用
        if (coupon.getStatus() == CouponStatus.BANED ||
                CouponStatus.SHOP_BANED.equals(coupon.getStatus())) {
            //平台下架 或者店铺下架 都不可用
            throw new GlobalException(CouponErrorCode.COUPON_INVALID, "当前优惠券不可用");
        }
        //检查是否库存不足
        Long stock = coupon.getStock();
        Integer num = CommonPool.NUMBER_ONE;
        if (stock < num) {
            throw new GlobalException(CouponErrorCode.COUPON_OUT_STOCK, ("优惠券库存不足"));
        }
        //校验已领取的优惠券数量是否超额
        Long count = couponUserService.lambdaQuery()
                .eq(CouponUser::getUserId, userId)
                .eq(CouponUser::getShopId, shopId)
                .eq(CouponUser::getCouponId, couponId)
                .count();
        if (count >= coupon.getLimit()) {
            throw new GlobalException(CouponErrorCode.COUPON_OUT_LIMIT, "超过了每人限领数量");
        }
        // redis 减库存 -> 异步db减库存
        coupon = couponPlusService.couponStockReduce(shopId, couponId, num);
        boolean save = couponUserService.save(coupon.newCouponUser(userId, couponPlusService::getProductIds));
        if (!save) {
            throw new GlobalException(SystemCode.DATA_ADD_FAILED);
        }
        try {
            sendCouponMessage(shopId, coupon, num);
        } catch (Exception e) {
            log.info("发送优惠券领取成功小程序订阅消息失败，参数：{}", JSON.toJSONString(coupon), e);
        }
        // 领取的限制 减去 已经领取的数量 这里判断大于1 是因为会再次领取一张  所以count+1
        return coupon.getLimit() - count > 1;
    }

    private void sendCouponMessage(Long shopId, Coupon coupon, Integer num) {
        MessagesSendDTO messagesSendQO = new MessagesSendDTO();
        messagesSendQO.setTemplateName(WechatMsgSendTypeEnum.COUPON_ARRIVAL.getMsgTitle());
        // 获取店铺信息
        ShopInfoVO shopInfo = shopRpcService.getShopInfoByShopId(shopId);
        String shopName = shopInfo != null ? shopInfo.getName() : "";
        
        // 计算优惠券的实际过期时间
        String expireDate = null;
        try {
            // 使用calcStartAnEndDate方法获取实际的开始和结束时间
            var dateRange = coupon.calcStartAnEndDate();
            expireDate = dateRange._2().toString(); // 获取结束时间
        } catch (Exception e) {
            log.warn("计算优惠券过期时间失败", e);
        }
        
        // 生成小程序参数
        Map<String, String> params = generateCouponAppletsParam(
                coupon.getName(),                // 券名称
                String.valueOf(num),             // 优惠券数量
                expireDate,                      // 过期日期
                shopName                         // 商家名称
        );
        messagesSendQO.setPrams(params);
        messagesSendQO.setOrderNum(String.valueOf(coupon.getId()));
        SecureUser<?> user = ISecurity.userMust();
        messagesSendQO.setPhone(user.getMobile());
        messagesSendQO.setOperSubjectGuid(ISystem.platformIdMust().toString());
        messagesSendQO.setMemberName(user.getUsername());
        
        log.info("发送优惠券领取成功小程序订阅消息，参数：{}", JSON.toJSONString(messagesSendQO));
        memberApiService.wechatMessageSendBatch(messagesSendQO);
    }

    private Map<String, String> generateCouponAppletsParam(String thing1,
                                                           String number6,
                                                           String time4,
                                                           String thing10) {
        Map<String, String> params = new HashMap<>();
        //券名称
        params.put("thing1", thing1);
        //优惠券数量
        params.put("number6", number6);
        //过期日期
        params.put("time4", time4);
        //商家名称
        params.put("thing10", thing10);
        //温馨提示
        params.put("thing5", "领券成功！请立即使用");
        return params;
    }

    @Override
    public List<CouponVO> platformCouponAvailable(Long userId) {
        //满折
        //满减
        //无门槛 折扣/优惠
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IPage<CouponVO> orderShopCouponPage(Long userId, OrderCouponPageDTO orderCouponPage) {
        Long shopId = orderCouponPage.getShopId();
        return couponCalculateService.todo(
                orderCouponPage.getProductAmounts()
                        .stream()
                        .map(productAmount -> new CouponCalculate().setShopId(shopId).setProductId(productAmount.getProductId()).setAmount(productAmount.getAmount()))
                        .collect(Collectors.toList()),
                bid -> couponService.orderShopCouponPage(bid, userId, orderCouponPage)
        );

    }

    @Override
    public IPage<CouponVO> productShopCouponPage(ProductCouponPageDTO query) {
        return couponService.productShopCouponPage(query, ISecurity.userOpt().map(SecureUser::getId).getOrNull());
    }


}
