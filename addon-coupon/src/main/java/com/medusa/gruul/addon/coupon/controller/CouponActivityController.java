package com.medusa.gruul.addon.coupon.controller;

import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.dto.SelfReceiveCouponPackageDTO;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.coupon.CouponPackageSelfDetailsVO;
import com.medusa.gruul.common.member.vo.coupon.SelfReceiveCouponPackageVO;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import io.vavr.control.Option;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 券获取
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@Valid
@RequestMapping("/coupon/activity")
@RequiredArgsConstructor
public class CouponActivityController {

    private final MemberApiService memberApiService;

    /**
     * 从会员中台查询自助领券活动详情（迁移会员中台接口：marketing/coupon_package/self_details）
     *
     * @param guid 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台查询自助领券活动详情")
    @GetMapping("/details")
    Result<CouponPackageSelfDetailsVO> getCouponPackageSelfDetails(@RequestParam("guid") String guid) {
        Option<SecureUser<Object>> secureUser = ISecurity.userOpt();
        String memberGuid = secureUser.map(SecureUser::getId).map(String::valueOf).getOrNull();
        return Result.ok(memberApiService.getCouponPackageSelfDetails(guid, memberGuid));
    }

    /**
     * 用户主动领券接口
     *
     * @param dto 领券请求参数
     * @return 领券结果
     */
    @PostMapping(value = "/receive")
    @PreAuthorize("@S.matcher().role(@S.USER).match()")
    Result<SelfReceiveCouponPackageVO> selfReceiveCouponPackage(@RequestBody SelfReceiveCouponPackageDTO dto) {
        dto.setMemberGuid(ISecurity.userMust().getId().toString());
        return Result.ok(memberApiService.selfReceiveCouponPackage(dto));
    }

}
