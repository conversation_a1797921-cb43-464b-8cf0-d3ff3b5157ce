package com.medusa.gruul.addon.coupon.model.enums;


/**
 * <AUTHOR>
 * @since 2024/3/13
 */

import com.medusa.gruul.global.model.exception.Error;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 优惠券错误
 *
 * <AUTHOR>
 * @since 2024/3/13
 */
@Getter
@RequiredArgsConstructor
public enum CouponErrorEnum implements Error {

    CAN_NOT_DELETE(33310, "未开始,进行中的活动不能删除"),
    CAN_NOT_REMOVE(33311, "进行中的活动不能删除"),
    SHOP_NOT_MATCH(33312, "店铺id不匹配"),
    COUPON_STATE_ERROR(33313, "业务状态不正确"),

    ;
    private final int code;
    private final String msgCode;

    @Override
    public int code() {
        return getCode();
    }

    @Override
    public String msg() {
        return getMsgCode();
    }
}
