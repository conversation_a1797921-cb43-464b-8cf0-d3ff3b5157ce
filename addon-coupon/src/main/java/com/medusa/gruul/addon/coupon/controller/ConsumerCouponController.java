package com.medusa.gruul.addon.coupon.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.addon.coupon.model.dto.ConsumerCouponQueryDTO;
import com.medusa.gruul.addon.coupon.model.dto.OrderCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.dto.ProductCouponPageDTO;
import com.medusa.gruul.addon.coupon.model.vo.CouponVO;
import com.medusa.gruul.addon.coupon.service.ConsumerCouponService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.member.dto.*;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.PageResult;
import com.medusa.gruul.common.member.vo.coupon.*;
import com.medusa.gruul.common.member.vo.unilink.MemberUnilinkSystemConfigVO;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 消费者优惠券控制器
 *
 * <AUTHOR>
 * date 2022/11/3
 */
@RestController
@Valid
@RequestMapping("/coupon/consumer")
@PreAuthorize("@S.matcher().role(@S.USER).match()")
@RequiredArgsConstructor
public class ConsumerCouponController {

    private final ConsumerCouponService consumerCouponService;
    private final MemberApiService memberApiService;

    /**
     * 从会员中台查询已领券（迁移会员中台接口：base/applets/coupon/pageMemberCouponByTime）
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台查询已领券")
    @PostMapping("/pageMemberCouponByTime")
    Result<List<MemberCouponVO>> pageMemberCouponByTime(@RequestBody MemberCouponQueryDTO dto) {
        return Result.ok(memberApiService.pageMemberCouponByTime(dto));
    }

    /**
     * 从会员中台查询优惠卷详情（迁移会员中台接口：base/applets/coupon/detail）
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台优惠卷详情")
    @PostMapping("/memberCouponDetail")
    Result<MemberCouponDetailVO> memberCouponDetail(@RequestBody MemberCouponDetailDTO dto) {
        return Result.ok(memberApiService.memberCouponDetail(dto));
    }

    /**
     * 从会员中台查询优惠卷二维码（迁移会员中台接口：base/applets/coupon/getQrcode）
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台查询优惠卷二维码")
    @PostMapping("/memberCouponQrcode")
    Result<MemberCouponQrCodeVO> memberCouponQrcode(@RequestBody MemberCouponQrcodeDTO dto) {
        return Result.ok(memberApiService.memberCouponQrcode(dto));
    }

    /**
     * 从会员中台统计优惠卷（迁移会员中台接口：base/applets/coupon/countMemberNum）
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台统计优惠卷")
    @PostMapping("/memberCouponCount")
    Result<MemberCouponWxCountVO> memberCouponCount(@RequestBody MemberCouponCountDTO dto) {
        return Result.ok(memberApiService.memberCouponCount(dto));
    }

    /**
     * 从会员中台查询适用门店（迁移会员中台接口：applets/get_applet_store_list_by_ids）
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PreAuthorize("permitAll()")
    @Log("从会员中台查询适用门店")
    @PostMapping("/memberCouponStoreList")
    Result<PageResult> memberCouponStoreList(@RequestBody MemberCouponStoreListDTO dto) {
        return Result.ok(memberApiService.memberCouponStoreList(dto));
    }

    /**
     * 领券中心 优惠券分页查询
     *
     * @param query 查询条件
     * @return 分页查询结果
     */
    @GetMapping
    @PreAuthorize("permitAll()")
    @Log("领券中心/我的优惠券/ 可以领取的店铺优惠券查询 分页查询")
    public Result<IPage<CouponVO>> consumerCouponPage(@Valid ConsumerCouponQueryDTO query) {
        return Result.ok(
                consumerCouponService.consumerCouponPage(ISecurity.userOpt().map(SecureUser::getId), query)
        );
    }

    /**
     * 结算页分页查询优惠券
     *
     * @param orderCouponPage 订单优惠券分页参数
     * @return 分页查询结果
     */
    @PostMapping("/order")
    @Log("结算页选择优惠券")
    public Result<IPage<CouponVO>> orderShopCouponPage(@RequestBody @Valid OrderCouponPageDTO orderCouponPage) {
        return Result.ok(
                consumerCouponService.orderShopCouponPage(ISecurity.userMust().getId(), orderCouponPage)
        );
    }

    /**
     * 分页查询指定商品详情优惠券
     *
     * @param productCouponPage 分页查询参数
     * @return 分页查询结果
     */
    @PostMapping("/product")
    @Log("商品详情优惠券优惠")
    @PreAuthorize("permitAll()")
    public Result<IPage<CouponVO>> productShopCouponPage(@Valid @RequestBody ProductCouponPageDTO productCouponPage) {
        return Result.ok(
                consumerCouponService.productShopCouponPage(productCouponPage)
        );
    }

    /**
     * 领取优惠券
     *
     * @param shopId   店铺id
     * @param couponId 优惠券id
     * @return 领取结果 true 可以继续再领  false  不可以继续再领取
     */
    @Log("领取优惠券")
    @PostMapping("/collect/shop/{shopId}/{couponId}")
    public Result<Boolean> collectCoupon(@PathVariable Long shopId, @PathVariable Long couponId) {
        Boolean flag = consumerCouponService.collectCoupon(ISecurity.userMust().getId(), shopId, couponId);
        return Result.ok(flag);
    }
}
