

ALTER TABLE `pro-order`.t_order_discount ADD COLUMN source_origin tinyint DEFAULT 0 COMMENT '来源系统:0-内部系统 1-会员中台' after source_desc;


-- 新增退款方式表
create table `pro-afs`.t_refund_method_config
(
    id          bigint                             not null comment '主键ID'
        primary key,
    platform_id bigint                             not null comment '平台ID',
    method_name varchar(100)                       not null comment '退款方式名称',
    method_desc varchar(500)                       null comment '退款方式描述',
    is_enabled  tinyint  default 1                 not null comment '是否启用: 0-禁用, 1-启用',
    is_default  tinyint  default 0                 not null comment '是否默认: 0-非默认, 1-默认',
    create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted     tinyint  default 0                 null comment '删除标记: 0-未删除, 1-已删除',
    version     int                                null
)
    comment '退款方式配置表';

create index idx_default
    on `pro-afs`.t_refund_method_config (is_default, deleted);

create index idx_enabled
    on `pro-afs`.t_refund_method_config (is_enabled, deleted);

create index idx_platform
    on `pro-afs`.t_refund_method_config (platform_id);

create index idx_platform_default
    on `pro-afs`.t_refund_method_config (platform_id, is_default, deleted);

create index idx_platform_enabled
    on `pro-afs`.t_refund_method_config (platform_id, is_enabled);

CREATE INDEX `idx_platform_create_time` ON `pro-afs`.`t_refund_method_config` (`platform_id`, `create_time`);




-- 新增退款原因模板表
create table `pro-afs`.t_refund_reason_template
(
    id          bigint                             not null comment '主键ID'
        primary key,
    platform_id            bigint                             not null comment '平台ID',
    template_type          tinyint                            not null comment '模板类型: 1-系统默认, 2-自定义',
    reason_text            varchar(200)                       not null comment '退款原因文案',
    scenario_refund_only   tinyint  default 0                 not null comment '仅退款场景: 0-不适用, 1-适用',
    scenario_return_refund tinyint  default 0                 not null comment '退货退款场景: 0-不适用, 1-适用',
    is_enabled             tinyint  default 1                 not null comment '是否启用: 0-禁用, 1-启用',
    create_time            datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_time            datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                tinyint  default 0                 null comment '删除标记: 0-未删除, 1-已删除',
    version     int                                null
)
    comment '退款原因模板配置表';

create index idx_enabled
    on `pro-afs`.t_refund_reason_template (is_enabled, deleted);

create index idx_platform
    on `pro-afs`.t_refund_reason_template (platform_id);

create index idx_scenario
    on `pro-afs`.t_refund_reason_template (scenario_refund_only, scenario_return_refund);

