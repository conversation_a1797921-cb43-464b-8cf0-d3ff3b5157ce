-- 修改菜单表中名称包含'会员'的记录，将deleted字段设置为1（逻辑删除）

UPDATE `pro-security`.t_menu 
SET deleted = 1, 
    update_time = NOW(), 
    version = version + 1 
WHERE name LIKE '会员%' 
  AND deleted = 0;

-- 屏蔽储值流水菜单
UPDATE `pro-security`.`t_menu`
SET deleted = 1
WHERE name = '储值流水';


-- 查询确认修改结果
SELECT id, name, deleted, update_time 
FROM `pro-security`.t_menu 
WHERE name LIKE '会员%' 
ORDER BY id;


-- 更新商家端经营概况菜单支持权限管理分配
UPDATE `pro-security`.t_menu
SET unshared = 0
WHERE name = '经营概况' AND deleted = 0;

## 注意nacos platform服务配置文件打开租户过滤
-- 更新配置表索引
alter table `pro-security`.t_web_param_config
    drop key idx_param_key;

alter table `pro-security`.t_web_param_config
    add constraint idx_param_key
        unique (param_key, platform_id);

