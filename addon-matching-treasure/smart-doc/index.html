<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta content="width=device-width, initial-scale=1.0" name="viewport"><meta content="smart-doc" name="generator"><title>addon-matching-treasure</title><link href="font.css" rel="stylesheet"><link href="AllInOne.css?v=1694497978042" rel="stylesheet"/><style>.literalblock pre,.listingblock pre:not(.highlight),.listingblock pre[class="highlight"],.listingblock pre[class^="highlight "],.listingblock pre.CodeRay,.listingblock pre.prettyprint{background:#f7f7f8}.hljs{padding:0}</style><script src="highlight.min.js"></script><script src="jquery.min.js"></script></head><body class="book toc2 toc-left"><div id="header"><h1>addon-matching-treasure</h1><div class="toc2" id="toc"><div id="book-search-input"><input id="search" placeholder="Type to search" type="text"></div><div id="toctitle"><span>API Reference</span></div><ul class="sectlevel1" id="accordion"><li class="open"><a class="dd" href="#_1_套餐店铺平台端 前端控制器">1.套餐店铺平台端 前端控制器</a><ul class="sectlevel2"><li><a href="#_1_1_1_新增套餐">1.1.新增套餐</a></li><li><a href="#_1_1_2_分页查询套餐活动">1.2.分页查询套餐活动</a></li><li><a href="#_1_1_3_查询套餐活动详情">1.3.查询套餐活动详情</a></li><li><a href="#_1_1_4_删除套餐活动">1.4.删除套餐活动</a></li><li><a href="#_1_1_5_批量删除套餐活动">1.5.批量删除套餐活动</a></li><li><a href="#_1_1_6_下架套餐活动">1.6.下架套餐活动</a></li></ul></li><li class="open"><a class="dd" href="#_2_套餐用户端">2.套餐用户端</a><ul class="sectlevel2"><li><a href="#_1_2_1_商品详情页套餐基本信息">2.1.商品详情页套餐基本信息</a></li><li><a href="#_1_2_2_套餐详情">2.2.套餐详情</a></li></ul></li></ul></div></div><div id="content"><div id="preamble"><div class="sectionbody"><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Version</th><th class="tableblock halign-left valign-top">Update Time</th><th class="tableblock halign-left valign-top">Status</th><th class="tableblock halign-left valign-top">Author</th><th class="tableblock halign-left valign-top">Description</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">v2023-09-12 13:52:58</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">2023-09-12 13:52:58</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">auto</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">@yusi</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">Created by smart-doc</p></td></tr></tbody></table></div></div><div class="sect1"><h2 id="_1_1_套餐店铺平台端 前端控制器"><a class="anchor" href="#_1_1_套餐店铺平台端 前端控制器"></a><a class="link" href="#_1_1_套餐店铺平台端 前端控制器">1.套餐店铺平台端 前端控制器</a></h2><div class="sectionbody"><div class="sect2" id="0cc04ad5515065c851a94041c7e8cc50"><h3 id="_1_1_1_新增套餐"><a class="anchor" href="#_1_1_1_新增套餐"></a><a class="link" href="#_1_1_1_新增套餐">1.1.新增套餐</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal" id="0cc04ad5515065c851a94041c7e8cc50-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal">&nbsp;/addon-matching-treasure/setMeal</a></p></div><div class="paragraph" data-method="POST" id="0cc04ad5515065c851a94041c7e8cc50-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/json" id="0cc04ad5515065c851a94041c7e8cc50-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>新增套餐</p></div><div class="paragraph"><p><strong>Body-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称
Validate[max: 15; ]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealDescription</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐描述
Validate[max: 40; ]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐类型 [0:自选商品套餐 1:固定组合套餐]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealStatus</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动状态 [0:未开始 1:进行中 2:已结束 3:违规下架]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">startTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开始时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">peopleNum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">参与人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">amountReceivable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">应收金额</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">stackable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否可叠加优惠（会员，优惠券，满减）</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─payTimeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">支付超时时间 为空使用订单默认的超时时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─units</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─seconds</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nanos</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─coupon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否使用优惠券 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─vip</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用会员价 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─full</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用满减 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">mainProduct</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">matchingProducts</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -H 'Content-Type: application/json' -i /addon-matching-treasure/setMeal --data '{
  "setMealId": 0,
  "shopId": 0,
  "shopName": "",
  "setMealName": "",
  "setMealDescription": "",
  "setMealMainPicture": "",
  "setMealType": "OPTIONAL_PRODUCT",
  "setMealStatus": "NOT_STARTED",
  "startTime": "yyyy-MM-dd HH:mm:ss",
  "endTime": "yyyy-MM-dd HH:mm:ss",
  "peopleNum": 0,
  "amountReceivable": 0,
  "stackable": {
    "payTimeout": {
      "units": [
        {
          "object": "any object"
        }
      ],
      "seconds": 0,
      "nanos": 0
    },
    "coupon": true,
    "vip": true,
    "full": true
  },
  "mainProduct": [
    {
      "setMealId": 0,
      "shopId": 0,
      "productId": 0,
      "productPic": "",
      "productName": "",
      "productAttributes": "MAIN_PRODUCT",
      "skuId": 0,
      "skuName": "",
      "stockType": "UNLIMITED",
      "skuStock": 0,
      "skuPrice": 0,
      "matchingPrice": 0,
      "matchingStock": 0
    }
  ],
  "matchingProducts": [
    {
      "setMealId": 0,
      "shopId": 0,
      "productId": 0,
      "productPic": "",
      "productName": "",
      "productAttributes": "MAIN_PRODUCT",
      "skuId": 0,
      "skuName": "",
      "stockType": "UNLIMITED",
      "skuStock": 0,
      "skuPrice": 0,
      "matchingPrice": 0,
      "matchingStock": 0
    }
  ]
}'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="8ca658c37b9eeb7a7543509d939b9dfe"><h3 id="_1_1_2_分页查询套餐活动"><a class="anchor" href="#_1_1_2_分页查询套餐活动"></a><a class="link" href="#_1_1_2_分页查询套餐活动">1.2.分页查询套餐活动</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal" id="8ca658c37b9eeb7a7543509d939b9dfe-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal">&nbsp;/addon-matching-treasure/setMeal</a></p></div><div class="paragraph" data-method="GET" id="8ca658c37b9eeb7a7543509d939b9dfe-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="8ca658c37b9eeb7a7543509d939b9dfe-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>分页查询套餐活动</p></div><div class="paragraph"><p><strong>Query-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">查询数据列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐类型 [0:自选商品套餐 1:固定组合套餐]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealStatus</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动状态 [0:未开始 1:进行中 2:已结束 3:违规下架]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─startTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开始时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─stackable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否可叠加优惠（会员，优惠券，满减）</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─payTimeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">支付超时时间 为空使用订单默认的超时时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─units</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─seconds</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nanos</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─coupon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否使用优惠券 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─vip</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用会员价 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─full</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用满减 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─peopleNum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">参与人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─amountReceivable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">应收金额</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─mainProduct</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingProducts</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">总数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">每页显示条数，默认 10</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">orders</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">排序字段信息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─column</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">需要进行排序的字段</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─asc</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否正序排列，默认 true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">自动优化 COUNT SQL</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">searchCount</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否进行 count 查询</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">optimizeJoinOfCountSql</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">{@link #optimizeJoinOfCountSql()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">maxLimit</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">countId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">keyword</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">关键词</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealStatus</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动状态</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-matching-treasure/setMeal?pages=0&id=0&shopId=0&setMealType=OPTIONAL_PRODUCT&setMealStatus=NOT_STARTED&startTime=yyyy-MM-dd HH:mm:ss&endTime=yyyy-MM-dd HH:mm:ss&seconds=0&nanos=0&coupon=true&vip=true&full=true&peopleNum=0&amountReceivable=0&setMealId=0&productId=0&productAttributes=MAIN_PRODUCT&skuId=0&stockType=UNLIMITED&skuStock=0&skuPrice=0&matchingPrice=0&matchingStock=0&total=0&size=0&current=0&asc=true&optimizeCountSql=true&searchCount=true&optimizeJoinOfCountSql=true&maxLimit=0&orders[0].column=&orders[0].asc=true&countId=&keyword=</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─pages</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前分页总页数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─records</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">分页记录列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐类型 [0:自选商品套餐 1:固定组合套餐]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealStatus</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动状态 [0:未开始 1:进行中 2:已结束 3:违规下架]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─startTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开始时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stackable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否可叠加优惠（会员，优惠券，满减）</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─payTimeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">支付超时时间 为空使用订单默认的超时时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─units</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─seconds</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nanos</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─coupon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否使用优惠券 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─vip</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用会员价 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─full</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用满减 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─peopleNum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">参与人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─amountReceivable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">应收金额</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─mainProduct</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingProducts</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─total</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前满足条件总行数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─size</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">获取每页显示条数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─current</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">当前页</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "pages": 0,
    "records": [
      {
        "id": 0,
        "shopId": 0,
        "shopName": "",
        "setMealName": "",
        "setMealMainPicture": "",
        "setMealType": "OPTIONAL_PRODUCT",
        "setMealStatus": "NOT_STARTED",
        "startTime": "yyyy-MM-dd HH:mm:ss",
        "endTime": "yyyy-MM-dd HH:mm:ss",
        "stackable": {
          "payTimeout": {
            "units": [
              {
                "object": "any object"
              }
            ],
            "seconds": 0,
            "nanos": 0
          },
          "coupon": true,
          "vip": true,
          "full": true
        },
        "peopleNum": 0,
        "amountReceivable": 0,
        "mainProduct": [
          {
            "setMealId": 0,
            "shopId": 0,
            "productId": 0,
            "productPic": "",
            "productName": "",
            "productAttributes": "MAIN_PRODUCT",
            "skuId": 0,
            "skuName": "",
            "stockType": "UNLIMITED",
            "skuStock": 0,
            "skuPrice": 0,
            "matchingPrice": 0,
            "matchingStock": 0
          }
        ],
        "matchingProducts": [
          {
            "setMealId": 0,
            "shopId": 0,
            "productId": 0,
            "productPic": "",
            "productName": "",
            "productAttributes": "MAIN_PRODUCT",
            "skuId": 0,
            "skuName": "",
            "stockType": "UNLIMITED",
            "skuStock": 0,
            "skuPrice": 0,
            "matchingPrice": 0,
            "matchingStock": 0
          }
        ]
      }
    ],
    "total": 0,
    "size": 0,
    "current": 0
  }
}</code></pre></div></div></div><div class="sect2" id="eadbfd5a36dcef39b096a3ae96f465df"><h3 id="_1_1_3_查询套餐活动详情"><a class="anchor" href="#_1_1_3_查询套餐活动详情"></a><a class="link" href="#_1_1_3_查询套餐活动详情">1.3.查询套餐活动详情</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal/{shopId}/{setMealId}" id="eadbfd5a36dcef39b096a3ae96f465df-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal/{shopId}/{setMealId}">&nbsp;/addon-matching-treasure/setMeal/{shopId}/{setMealId}</a></p></div><div class="paragraph" data-method="GET" id="eadbfd5a36dcef39b096a3ae96f465df-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="eadbfd5a36dcef39b096a3ae96f465df-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>查询套餐活动详情</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">   店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-matching-treasure/setMeal/{shopId}/{setMealId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐类型 [0:自选商品套餐 1:固定组合套餐]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealStatus</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动状态 [0:未开始 1:进行中 2:已结束 3:违规下架]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─startTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">开始时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─stackable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否可叠加优惠（会员，优惠券，满减）</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─payTimeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">支付超时时间 为空使用订单默认的超时时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─units</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─seconds</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nanos</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─coupon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否使用优惠券 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─vip</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用会员价 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─full</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用满减 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─peopleNum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">参与人数</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─amountReceivable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">应收金额</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─mainProduct</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">主商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─matchingProducts</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "id": 0,
    "shopId": 0,
    "shopName": "",
    "setMealName": "",
    "setMealMainPicture": "",
    "setMealType": "OPTIONAL_PRODUCT",
    "setMealStatus": "NOT_STARTED",
    "startTime": "yyyy-MM-dd HH:mm:ss",
    "endTime": "yyyy-MM-dd HH:mm:ss",
    "stackable": {
      "payTimeout": {
        "units": [
          {
            "object": "any object"
          }
        ],
        "seconds": 0,
        "nanos": 0
      },
      "coupon": true,
      "vip": true,
      "full": true
    },
    "peopleNum": 0,
    "amountReceivable": 0,
    "mainProduct": [
      {
        "setMealId": 0,
        "shopId": 0,
        "productId": 0,
        "productPic": "",
        "productName": "",
        "productAttributes": "MAIN_PRODUCT",
        "skuId": 0,
        "skuName": "",
        "stockType": "UNLIMITED",
        "skuStock": 0,
        "skuPrice": 0,
        "matchingPrice": 0,
        "matchingStock": 0
      }
    ],
    "matchingProducts": [
      {
        "setMealId": 0,
        "shopId": 0,
        "productId": 0,
        "productPic": "",
        "productName": "",
        "productAttributes": "MAIN_PRODUCT",
        "skuId": 0,
        "skuName": "",
        "stockType": "UNLIMITED",
        "skuStock": 0,
        "skuPrice": 0,
        "matchingPrice": 0,
        "matchingStock": 0
      }
    ]
  }
}</code></pre></div></div></div><div class="sect2" id="e54fadf6129aea309dab60f1bea02865"><h3 id="_1_1_4_删除套餐活动"><a class="anchor" href="#_1_1_4_删除套餐活动"></a><a class="link" href="#_1_1_4_删除套餐活动">1.4.删除套餐活动</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal/{shopId}/{setMealId}" id="e54fadf6129aea309dab60f1bea02865-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal/{shopId}/{setMealId}">&nbsp;/addon-matching-treasure/setMeal/{shopId}/{setMealId}</a></p></div><div class="paragraph" data-method="DELETE" id="e54fadf6129aea309dab60f1bea02865-method"><p><strong>Type:&nbsp;</strong>DELETE</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="e54fadf6129aea309dab60f1bea02865-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>删除套餐活动</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">   店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X DELETE -i /addon-matching-treasure/setMeal/{shopId}/{setMealId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="72cc6950394845b9655d7c2bfad3173b"><h3 id="_1_1_5_批量删除套餐活动"><a class="anchor" href="#_1_1_5_批量删除套餐活动"></a><a class="link" href="#_1_1_5_批量删除套餐活动">1.5.批量删除套餐活动</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal" id="72cc6950394845b9655d7c2bfad3173b-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal">&nbsp;/addon-matching-treasure/setMeal</a></p></div><div class="paragraph" data-method="DELETE" id="72cc6950394845b9655d7c2bfad3173b-method"><p><strong>Type:&nbsp;</strong>DELETE</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/json" id="72cc6950394845b9655d7c2bfad3173b-content-type"><p><strong>Content-Type:&nbsp;</strong>application/json</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>批量删除套餐活动</p></div><div class="paragraph"><p><strong>Body-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">false</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X DELETE -H 'Content-Type: application/json' -i /addon-matching-treasure/setMeal --data '[
  {
    "shopId": 0,
    "setMealId": 0
  }
]'</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div><div class="sect2" id="2388c4ee8c948b0a2a44bca6b4d77c99"><h3 id="_1_1_6_下架套餐活动"><a class="anchor" href="#_1_1_6_下架套餐活动"></a><a class="link" href="#_1_1_6_下架套餐活动">1.6.下架套餐活动</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/setMeal/{shopId}/{setMealId}" id="2388c4ee8c948b0a2a44bca6b4d77c99-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/setMeal/{shopId}/{setMealId}">&nbsp;/addon-matching-treasure/setMeal/{shopId}/{setMealId}</a></p></div><div class="paragraph" data-method="POST" id="2388c4ee8c948b0a2a44bca6b4d77c99-method"><p><strong>Type:&nbsp;</strong>POST</p></div><div class="paragraph"><p><strong>Author:&nbsp;</strong>WuDi</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="2388c4ee8c948b0a2a44bca6b4d77c99-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>下架套餐活动</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">   店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X POST -i /addon-matching-treasure/setMeal/{shopId}/{setMealId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": ""
}</code></pre></div></div></div></div></div><div class="sect1"><h2 id="_1_2_套餐用户端"><a class="anchor" href="#_1_2_套餐用户端"></a><a class="link" href="#_1_2_套餐用户端">2.套餐用户端</a></h2><div class="sectionbody"><div class="sect2" id="eb4accfa58c6b0b19b6cb1dc9821f1e7"><h3 id="_1_2_1_商品详情页套餐基本信息"><a class="anchor" href="#_1_2_1_商品详情页套餐基本信息"></a><a class="link" href="#_1_2_1_商品详情页套餐基本信息">2.1.商品详情页套餐基本信息</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/consumer/setMealBasicInfo/{shopId}/{productId}" id="eb4accfa58c6b0b19b6cb1dc9821f1e7-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/consumer/setMealBasicInfo/{shopId}/{productId}">&nbsp;/addon-matching-treasure/consumer/setMealBasicInfo/{shopId}/{productId}</a></p></div><div class="paragraph" data-method="GET" id="eb4accfa58c6b0b19b6cb1dc9821f1e7-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="eb4accfa58c6b0b19b6cb1dc9821f1e7-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>商品详情页套餐基本信息</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">   店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-matching-treasure/consumer/setMealBasicInfo/{shopId}/{productId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─saveAtLeast</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">最少可省</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": [
    {
      "setMealId": 0,
      "setMealMainPicture": "",
      "setMealName": "",
      "endTime": "yyyy-MM-dd",
      "saveAtLeast": 0
    }
  ]
}</code></pre></div></div></div><div class="sect2" id="65ab4566a4e72b1ade866678fd803874"><h3 id="_1_2_2_套餐详情"><a class="anchor" href="#_1_2_2_套餐详情"></a><a class="link" href="#_1_2_2_套餐详情">2.2.套餐详情</a></h3><div class="paragraph" data-download="false" data-page="" data-url="/addon-matching-treasure/consumer/setMealDetail/{shopId}/{setMealId}" id="65ab4566a4e72b1ade866678fd803874-url"><p><strong>URL:</strong><a class="bare" href="/addon-matching-treasure/consumer/setMealDetail/{shopId}/{setMealId}">&nbsp;/addon-matching-treasure/consumer/setMealDetail/{shopId}/{setMealId}</a></p></div><div class="paragraph" data-method="GET" id="65ab4566a4e72b1ade866678fd803874-method"><p><strong>Type:&nbsp;</strong>GET</p></div><div class="paragraph" data-content-type="application/x-www-form-urlencoded;charset=UTF-8" id="65ab4566a4e72b1ade866678fd803874-content-type"><p><strong>Content-Type:&nbsp;</strong>application/x-www-form-urlencoded;charset=UTF-8</p></div><div class="paragraph"><p><strong>Description:&nbsp;</strong>套餐详情</p></div><div class="paragraph"><p><strong>Path-parameters:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"><col style="width: 20%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Parameter</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Required</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Request-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="bash">curl -X GET -i /addon-matching-treasure/consumer/setMealDetail/{shopId}/{setMealId}</code></pre></div></div><div class="paragraph"><p><strong>Response-fields:</strong></p></div><table class="tableblock frame-all grid-all spread"><colgroup><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"><col style="width: 25%;"></colgroup><thead><tr><th class="tableblock halign-left valign-top">Field</th><th class="tableblock halign-left valign-top">Type</th><th class="tableblock halign-left valign-top">Description</th><th class="tableblock halign-left valign-top">Since</th></tr></thead><tbody><tr><td class="tableblock halign-left valign-top"><p class="tableblock">code</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应状态码</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">msg</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应消息</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">data</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">响应数据</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealMainPicture</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐主图</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealDescription</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐描述</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐类型 [0:自选商品套餐 1:固定组合套餐]</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─saveAtLeast</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">最少可省</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─endTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">结束时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─setMealProductDetails</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐关联商品</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">产品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productPic</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productAttributes</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品属性(0:主商品 1:搭配商品)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─saveAtLeast</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">最多可省</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─setMealProductSkuDetails</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">套餐商品规格</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku_id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─skuName</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingStock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">搭配库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型 1 无限库存 2 有限<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─saveAtLeast</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">最多可省</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─matchingPrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku原价</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─storageSku</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">id
{@link com.medusa.gruul.common.mp.config.MybatisPlusConfig#identifierGenerator()}</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─createTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">创建时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─updateTime</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">更新时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─version</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">乐观锁版本号</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─deleted</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">逻辑删除标记</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─activityType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型<br/>(See: 订单类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─activityId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">活动id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─shopId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">店铺id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─productId</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品id</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stockType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">库存类型 1 无限库存 2 有限<br/>(See: 库存类型

无限个)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─stock</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">商品库存</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─salesVolume</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">销量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─initSalesVolume</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">初始销量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─limitType</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">enum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">限购类型 1 不限购 , 2 商品限购 , 3 规格限购<br/>(See: 限购类型)</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─limitNum</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">限购数量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─specs</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">规格名称列表</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─image</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">string</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">sku图片</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─price</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">原价 单位 豪 1豪 = 0.01分</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─salePrice</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">真实销售价 单位豪 1豪 = 0.01分</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─weight</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">number</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">重量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─sort</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">排序</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─minimumPurchase</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">最低购买量</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">└─stackable</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否可叠加优惠（会员，优惠券，满减）</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─payTimeout</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">object</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">支付超时时间 为空使用订单默认的超时时间</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─units</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">array</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─seconds</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int64</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─nanos</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">int32</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">No comments found.</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─coupon</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否使用优惠券 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─vip</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用会员价 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr><tr><td class="tableblock halign-left valign-top"><p class="tableblock">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└─full</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">boolean</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">是否能使用满减 默认为true</p></td><td class="tableblock halign-left valign-top"><p class="tableblock">-</p></td></tr></tbody></table><div class="paragraph"><p><strong>Response-example:</strong></p></div><div class="listingblock"><div class="content"><pre><code class="json">{
  "code": 0,
  "msg": "",
  "data": {
    "setMealId": 0,
    "shopId": 0,
    "setMealName": "",
    "setMealMainPicture": "",
    "setMealDescription": "",
    "setMealType": "OPTIONAL_PRODUCT",
    "saveAtLeast": 0,
    "endTime": "yyyy-MM-dd HH:mm:ss",
    "setMealProductDetails": [
      {
        "productId": 0,
        "productPic": "",
        "productName": "",
        "productAttributes": "MAIN_PRODUCT",
        "saveAtLeast": 0,
        "setMealProductSkuDetails": [
          {
            "skuId": 0,
            "skuName": [
              ""
            ],
            "matchingStock": 0,
            "stockType": "UNLIMITED",
            "saveAtLeast": 0,
            "matchingPrice": 0,
            "storageSku": {
              "id": 0,
              "createTime": "yyyy-MM-dd HH:mm:ss",
              "updateTime": "yyyy-MM-dd HH:mm:ss",
              "version": 0,
              "deleted": true,
              "activityType": "COMMON",
              "activityId": 0,
              "shopId": 0,
              "productId": 0,
              "stockType": "UNLIMITED",
              "stock": 0,
              "salesVolume": 0,
              "initSalesVolume": 0,
              "limitType": "UNLIMITED",
              "limitNum": 0,
              "specs": [
                ""
              ],
              "image": "",
              "price": 0,
              "salePrice": 0,
              "weight": 0,
              "sort": 0,
              "minimumPurchase": 0
            }
          }
        ]
      }
    ],
    "stackable": {
      "payTimeout": {
        "units": [
          {
            "object": "any object"
          }
        ],
        "seconds": 0,
        "nanos": 0
      },
      "coupon": true,
      "vip": true,
      "full": true
    }
  }
}</code></pre></div></div></div></div></div><footer class="page-footer"><span class="copyright">Generated by smart-doc at 2023-09-12 13:52:58</span><span class="footer-modification">Suggestions,contact,support and error reporting on<a href="https://gitee.com/smart-doc-team/smart-doc" target="_blank">&nbsp;Gitee&nbsp;</a>or<a href="https://github.com/smart-doc-group/smart-doc.git" target="_blank">&nbsp;Github</a></span></footer><div href="javascript:void(0)" id="toTop"><img id="upArrow" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABlUlEQVRIS+2UvUvDQBiH398Rly4udnARwUXs4qAIOigI4iL30dTZ2T9AcNPVvUsXF7uYttdScNDFRRAnB11cFFwKxcXBJTQnJ6lEbRI/CIiY9e6e5/e+9+ZAGX/ImE9/QKCU2jfGbGTQqq4xZgtSyisiKmQgIAAVCCFWAGxnIOhqrdd/xyUrpRZsP40xSwA6AI57vd5eq9W6T6s8tQIppSKi+gDQNREprfVNkiRRwDlfY4xZ+FAIuSOi8Qjw0nEc5XnebZwkViClXA2T5+xhY8xus9ncEUJMAziITN5FEARuXLsGCoQQywBs8uEovJ+Scz7FGDuMSM4cx3E9z+u8r+SDQEq5SEQ1IhoZBE+QnBKRq7V+iEreCDjn84wxCx9NgidITnK5nFutVh/7e14FSqnZIAhqAMY+A4+TADjyfb/Ubref7J4XQXhxNvnEV+AJlbTy+XypUqn4KBaLBZuciCa/A0+opN5oNFz7FpUBbP4EHicxxsyAcz7HGDvvz3nar5+2Ho5wOQwsU5+KNGDa+r8grUP0DBLjtRtNKEliAAAAAElFTkSuQmCC"><span id="upText">Top</span></div></div><script src="search.js?v=1694497978042"></script><script>$(function(){const Accordion=function(el,multiple){this.el=el||{};this.multiple=multiple||false;const links=this.el.find(".dd");links.on("click",{el:this.el,multiple:this.multiple},this.dropdown)};Accordion.prototype.dropdown=function(e){const $el=e.data.el;const $this=$(this),$next=$this.next();$next.slideToggle();$this.parent().toggleClass("open");if(!e.data.multiple){$el.find(".submenu").not($next).slideUp("20").parent().removeClass("open")}};new Accordion($("#accordion"),false);hljs.highlightAll();$(window).scroll(function(){if($(window).scrollTop()>100){let $toTop=$("#toTop");$toTop.fadeIn(1500);$toTop.hover(function(){$("#upArrow").hide();$("#upText").show()},function(){$("#upArrow").show();$("#upText").hide()})}else{$("#toTop").fadeOut(1500)}});$("#toTop").click(function(){$("body, html").animate({scrollTop:0},1000);return false})});</script></body></html>