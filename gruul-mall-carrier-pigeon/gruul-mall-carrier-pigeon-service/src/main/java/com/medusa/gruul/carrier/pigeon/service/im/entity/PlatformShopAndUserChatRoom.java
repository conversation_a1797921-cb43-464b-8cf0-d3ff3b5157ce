package com.medusa.gruul.carrier.pigeon.service.im.entity;

import com.medusa.gruul.carrier.pigeon.service.im.constants.IMRedisConstant;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <p>平台/店铺-用户聊天室</p>
 * <AUTHOR>
 */
@Getter
@Setter
public class PlatformShopAndUserChatRoom {

    private Long platformId;

    private Long shopId;

    private Long userId;

    private final String roomKey;

    private final String roomValue;

    private final String roomId;

    private Long lastUpdateTime;

    public PlatformShopAndUserChatRoom(Long platformId, Long shopId, Long userId, Long lastUpdateTime) {
        this.platformId = Objects.requireNonNull(platformId, "platformId不能为空");
        this.shopId = shopId;
        this.userId = userId;
        this.lastUpdateTime = lastUpdateTime;
        this.roomKey = String.format(IMRedisConstant.IM_PLATFORM_CHATROOM_KEY, platformId, shopId);
        if (shopId >= userId) {
            this.roomValue = userId + ":" + shopId;
            this.roomId = String.format(IMRedisConstant.IM_PLATFORM_ROOM_KEY, platformId, userId, shopId);
        } else {
            this.roomValue = shopId + ":" + userId;
            this.roomId = String.format(IMRedisConstant.IM_PLATFORM_ROOM_KEY, platformId, shopId, userId);
        }

    }

    public PlatformShopAndUserChatRoom(Long platformId, Long shopId) {
        this.platformId = Objects.requireNonNull(platformId, "platformId不能为空");
        this.shopId = shopId;
        this.roomKey = String.format(IMRedisConstant.IM_PLATFORM_CHATROOM_KEY, platformId, shopId);
        this.roomValue = null;
        this.roomId = null;
    }

    @Deprecated
    public PlatformShopAndUserChatRoom(Long shopId, Long userId, Long lastUpdateTime) {
        throw new IllegalArgumentException("此构造函数已废弃，请使用带platformId的构造函数以确保租户隔离: PlatformShopAndUserChatRoom(Long platformId, Long shopId, Long userId, Long lastUpdateTime)");
    }

    @Deprecated
    public PlatformShopAndUserChatRoom(Long shopId) {
        throw new IllegalArgumentException("此构造函数已废弃，请使用带platformId的构造函数以确保租户隔离: PlatformShopAndUserChatRoom(Long platformId, Long shopId)");
    }

    public String getRoomKey() {
        return this.roomKey;
    }

    public String getRoomValue() {
        return this.roomValue;
    }

    public Long getLastUpdateTime() {
        return this.lastUpdateTime;
    }

    public String getRoomId() {
        return this.roomId;
    }

    public PlatformShopAndUserChatRoom reverse() {
        return new PlatformShopAndUserChatRoom(this.platformId, this.userId, this.shopId, this.lastUpdateTime);
    }
}
