<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.storage.service.mp.mapper.SkuMessageLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.storage.service.mp.entity.SkuMessageLog">
        <id column="id" property="id" />
        <result column="message_id" property="messageId" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="store_id" property="storeId" />
        <result column="shop_id" property="shopId" />
        <result column="channel_id" property="channelId" />
        <result column="message_content" property="messageContent" />
        <result column="sku_count" property="skuCount" />
        <result column="sku_ids" property="skuIds" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler" />
        <result column="process_status" property="processStatus" />
        <result column="process_start_time" property="processStartTime" />
        <result column="process_end_time" property="processEndTime" />
        <result column="process_duration" property="processDuration" />
        <result column="success_count" property="successCount" />
        <result column="failed_count" property="failedCount" />
        <result column="error_message" property="errorMessage" />
        <result column="message_timestamp" property="messageTimestamp" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, enterprise_id, store_id, shop_id, channel_id, message_content, 
        sku_count, sku_ids, process_status, process_start_time, process_end_time, 
        process_duration, success_count, failed_count, error_message, message_timestamp,
        create_time, update_time, deleted
    </sql>

    <!-- 根据消息ID查询日志 -->
    <select id="selectByMessageId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sku_message_log
        WHERE message_id = #{messageId}
        AND deleted = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据时间范围查询日志统计 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sku_message_log
        WHERE deleted = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper> 