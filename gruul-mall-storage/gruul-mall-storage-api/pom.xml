<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>gruul-mall-storage</artifactId>
    <groupId>com.medusa.gruul</groupId>
    <version>1.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>gruul-mall-storage-api</artifactId>
  <dependencies>
    <dependency>
      <groupId>com.medusa.gruul</groupId>
      <artifactId>gruul-common-mybatis-plus-model</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.medusa.gruul</groupId>
      <artifactId>gruul-common-module-api</artifactId>
      <scope>provided</scope>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.codehaus.groovy</groupId>-->
<!--      <artifactId>groovy</artifactId>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
  </dependencies>


</project>