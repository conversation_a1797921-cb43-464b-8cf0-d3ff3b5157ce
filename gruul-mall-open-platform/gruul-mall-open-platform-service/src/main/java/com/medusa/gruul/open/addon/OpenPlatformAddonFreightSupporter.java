package com.medusa.gruul.open.addon;

import com.medusa.gruul.common.addon.supporter.annotation.AddonMethod;
import com.medusa.gruul.common.addon.supporter.annotation.AddonSupporter;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.open.api.addon.OpenPlatformAddonConstant;
import com.medusa.gruul.open.api.addon.freight.LogisticsTemplateVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 */
@AddonSupporter(id = OpenPlatformAddonConstant.OPEN_PLATFORM_FREIGHT_SUPPORT_ID)
public interface OpenPlatformAddonFreightSupporter {

    /**
     * 获取模板列表
     *
     * @param shopId 门店id
     * @return 模板列表
     * 插件实现服务  addon-freight {@link com.medusa.gruul.addon.freight.addon.impl.AddonFreightProviderImpl#queryLogisticsTemplateList}
     */
    @AddonMethod(returnType = LogisticsTemplateVO.class)
    List<LogisticsTemplateVO> queryLogisticsTemplateList(Long shopId);

    /**
     * 获取物流模板 By id
     *
     * @param id  物流模板id
     * @return LogisticsTemplateVO
     * 插件实现服务  addon-freight {@link com.medusa.gruul.addon.freight.addon.impl.AddonFreightProviderImpl#getTemplateInfo}
     */
    @AddonMethod(returnType = LogisticsTemplateVO.class)
    LogisticsTemplateVO getTemplateInfo(Long id);

}
