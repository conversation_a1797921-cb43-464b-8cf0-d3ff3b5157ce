<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.medusa.gruul</groupId>
        <artifactId>gruul-mall-open-platform</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-mall-open-platform-service</artifactId>
    <version>1.0</version>

    <dependencies>
        <!-- open-platform api-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-open-platform-api</artifactId>
            <version>1.0</version>
        </dependency>

        <!--shop api-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-shop-api</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- service-->
        <dependency>
            <artifactId>gruul-common-module-service</artifactId>
            <groupId>com.medusa.gruul</groupId>
            <exclusions>
                <!-- datasource  -->
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                </exclusion>

                <!-- mybatis plus -->
                <exclusion>
                    <groupId>com.medusa.gruul</groupId>
                    <artifactId>gruul-common-mybatis-plus-config</artifactId>
                </exclusion>

                <!-- mysql -->
                <exclusion>
                    <groupId>com.mysql</groupId>
                    <artifactId>mysql-connector-j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>config/</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>com.medusa.gruul.open.OpenPlatformApplication</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>config/*</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>