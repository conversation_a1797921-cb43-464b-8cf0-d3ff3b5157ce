package com.medusa.gruul.goods.api.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.storage.api.entity.StorageSku;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: 店铺商品基础Vo
 * @Author: xiaoq
 * @Date : 2022-05-19
 */
@Data
public class ApiProductVO implements Serializable {

    private Long id;

    private Long shopId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 供应商名称
     */
    private String providerName;


    /**
     * 配送类型
     */
    @TableField(value = "distribution_mode", typeHandler = Fastjson2TypeHandler.class)
    private List<DistributionMode> distributionMode;

    /**
     * "展示图片"
     */
    private String pic;
    /**
     * 画册图片，连产品图片限制为6张，以逗号分割
     */
    private String albumPics;

    @TableField(value = "sale_prices", typeHandler = Fastjson2TypeHandler.class)
    private List<Long> salePrices;

    /**
     * 商品 仓储
     */
    private List<StorageSku> storageSkus;


}
