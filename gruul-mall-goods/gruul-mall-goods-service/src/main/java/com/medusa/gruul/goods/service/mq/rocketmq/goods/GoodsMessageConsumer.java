package com.medusa.gruul.goods.service.mq.rocketmq.goods;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.goods.service.service.GoodsMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 商品消息消费者
 *
 * <AUTHOR>
 * @date 2024/5/28
 */
@Slf4j
@RequiredArgsConstructor
@Component
@RocketMQMessageListener(
        topic = "${rocketmq.topic}",
        consumerGroup = "${rocketmq.consumer.group}",
        consumeMode = ConsumeMode.CONCURRENTLY
)
public class GoodsMessageConsumer implements RocketMQListener<GoodsMessage> {
    private final GoodsMessageService goodsMessageService;

    @Override
    public void onMessage(GoodsMessage message) {
        try {
            // 使用ObjectMapper配置下划线命名
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
            log.info("【商品消息同步】=======开始，收到商品同步消息: {}", objectMapper.writeValueAsString(message));
            goodsMessageService.processGoodsMessage(message);
            log.info("【商品消息同步】=======结束");
        } catch (Exception e) {
            log.error("【商品消息同步】=======处理商品同步消息失败: message={}, error={}", message, e.getMessage(), e);
        } finally {
            SystemContextHolder.clear();
        }
    }
}