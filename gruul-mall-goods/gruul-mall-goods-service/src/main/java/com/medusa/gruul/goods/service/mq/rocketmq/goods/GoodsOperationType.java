package com.medusa.gruul.goods.service.mq.rocketmq.goods;

import lombok.Getter;

/**
 * 商品操作类型枚举
 *
 * <AUTHOR>
 * @date 2024/5/28
 */
@Getter
public enum GoodsOperationType {
    /**
     * 其他
     */
    Other(0, "其他"),

    /**
     * 新增
     */
    CREATE(1, "新增"),

    /**
     * 更新
     */
    UPDATE(2, "更新"),

    /**
     * 删除
     */
    DELETE(3, "删除");

    private final Integer code;
    private final String desc;

    GoodsOperationType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GoodsOperationType fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GoodsOperationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 