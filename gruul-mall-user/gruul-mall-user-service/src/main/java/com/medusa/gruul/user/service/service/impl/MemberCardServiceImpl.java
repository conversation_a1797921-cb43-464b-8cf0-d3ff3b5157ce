package com.medusa.gruul.user.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.member.vo.EquitiesInfoVO;
import com.medusa.gruul.common.member.vo.GradeEquitiesInfoVO;
import com.medusa.gruul.user.api.enums.converter.RightsTypeConverter;
import com.medusa.gruul.common.member.dto.*;
import com.medusa.gruul.common.member.enums.SourceTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.vo.MemberAccountInfoVO;
import com.medusa.gruul.common.member.vo.MemberCardOpenVO;
import com.medusa.gruul.common.member.vo.MemberCardRechargeVO;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.*;
import com.medusa.gruul.common.model.message.DataChangeMessage;
import com.medusa.gruul.common.model.message.OverviewStatementDTO;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.global.model.constant.SecurityConst;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.global.model.helper.CompletableTask;
import com.medusa.gruul.payment.api.enums.FeeType;
import com.medusa.gruul.payment.api.model.dto.PayNotifyResultDTO;
import com.medusa.gruul.payment.api.model.dto.PaymentInfoDTO;
import com.medusa.gruul.payment.api.model.pay.PayResult;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import com.medusa.gruul.user.api.constant.UserConstant;
import com.medusa.gruul.user.api.enums.*;
import com.medusa.gruul.user.api.model.dto.BalanceChangeDTO;
import com.medusa.gruul.user.api.model.dto.MemberLabelDTO;
import com.medusa.gruul.user.api.model.dto.paid.PaidMemberDealDTO;
import com.medusa.gruul.user.api.model.vo.*;
import com.medusa.gruul.user.service.model.dto.MemberCardOpenCardDealDTO;
import com.medusa.gruul.user.service.model.dto.MemberCardRechargeDealDTO;
import com.medusa.gruul.user.service.model.dto.UserVirtualDeliverDTO;
import com.medusa.gruul.user.service.model.vo.RankedMemberVO;
import com.medusa.gruul.user.service.mp.entity.*;
import com.medusa.gruul.user.service.mp.service.*;
import com.medusa.gruul.user.service.mp.service.impl.MemberPurchaseHistoryService;
import com.medusa.gruul.user.service.service.IUserBalanceHistoryManageService;
import com.medusa.gruul.user.service.service.MemberCardService;
import com.medusa.gruul.user.service.service.addon.UserAddonSupporter;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;

import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Description MemberCardServiceImpl.java
 * date 2022-11-17 10:59
 */
@Service
@RequiredArgsConstructor
public class MemberCardServiceImpl implements MemberCardService {

    private final IUserMemberCardService userMemberCardService;

    private final IUserAccountService userAccountService;

    private final IUserFreeMemberService userFreeMemberService;

    private final RabbitTemplate rabbitTemplate;

    private final UserAddonSupporter userAddonSupporter;

    private final Executor globalExecutor;

    private final IMemberRightsService memberRightsService;

    private final MemberPurchaseHistoryService memberPurchaseHistoryService;

    private final MemberApiService memberApiService;

    private final IUserBalanceHistoryManageService userBalanceHistoryManageService;

    private final IUserMemberRelevancyRightsService userMemberRelevancyRightsService;

    private final PaymentRpcService paymentRpcService;

    @Override
    public MemberAggregationInfoVO getMemberCentre(Long userId) {
        userId = Option.of(userId).getOrElse(() -> ISecurity.userMust().getId());
        MemberAccountInfoVO user = memberApiService.getMemberAccountInfo(new MemberQueryDTO().setMemberGuid(String.valueOf(userId)));
        if (user == null) {
            throw new GlobalException("当前用户信息不存在");
        }
        MemberGradePaidInfoDTO memberGradePaidInfoDTO = memberApiService.getGradePaidInfoByMemberGuid(userId);
        List<UserMemberCard> memberCards = getUserMemberCard(memberGradePaidInfoDTO, userId);
        // 用户不存在付费会员卡信息 返回免费会员信息
        if (CollUtil.isEmpty(memberCards)) {
            return wrapFreeMemberGradeInfo(userId, user);
        }
        Map<Long, UserMemberCard> memberIdCardMap = memberCards.stream().collect(Collectors.toMap(UserMemberCard::getMemberId, v -> v));
        //获取正在使用的用户付费会员卡 (用户会员卡中最高等级的会员级别)
        PaidMemberInfoVO paidMember = getPaidMemberInfo(memberGradePaidInfoDTO);
        // 用户付费会员卡因异常原因到期 返回免费会员信息
        if (paidMember == null) {
            return wrapFreeMemberGradeInfo(userId, user);
        }
        UserMemberCard userMemberCard = memberIdCardMap.get(paidMember.getMemberId());

        //会员权益
        List<MemberBasicsRelevancyRightsVO> basicMemberRights = paidMember.getMemberBasicsRelevancyRightsList();
        //付费会员权益为空 使用免费会员权益 但是会员卡显示为付费会员
        if (CollUtil.isEmpty(basicMemberRights)) {
            MemberGradeFreeInfoDTO freeMemberGradeInfoDTO = memberApiService.getGradeFreeInfoByMemberGuid(userId);
            MemberAggregationInfoVO freeMemberRight = freeMemberAggregationAssemble(user, freeMemberGradeInfoDTO);
            freeMemberRight.getCurrentMemberVO()
                    .setMemberGradeIcon(memberGradePaidInfoDTO.getGradeIcon())
                    .setMemberGradeId(userMemberCard.getMemberId())
                    .setMemberName(paidMember.getPaidMemberName())
                    .setMemberCardId(userMemberCard.getId())
                    .setRankCode(userMemberCard.getRankCode())
                    .setMemberCardValidTime(userMemberCard.getMemberCardValidTime());
            freeMemberRight.setMemberLabel(paidMember.getMemberLabel());
            return freeMemberRight.setMemberType(MemberType.PAID_MEMBER);
        }

        Map<Long, MemberBasicsRelevancyRightsVO> basicRightMap = basicMemberRights.stream().collect(Collectors.toMap(MemberBasicsRelevancyRightsVO::getMemberRightsId, v -> v));
        //权益列表
        List<MemberRights> memberRights = basicMemberRights.stream()
                                                  .map(vo -> {
                                                      MemberRights memberRight = new MemberRights()
                                                                                         .setRightsName(vo.getRightsName())
                                                                                         .setRightsType(vo.getRightsType())
                                                                                         .setRightsExplain(vo.getRightsExplain())
                                                                                         .setRightsIcon(vo.getRightsIcon())
                                                                                         .setRightsSwitch(true);
                                                      memberRight.setId(vo.getMemberRightsId());
                                                      return memberRight;
                                                  })
                                                  .toList();

        // 处理付费会员到期会员卡信息
//        disposeExpireMemberCard(paidMember, userId);
        return new MemberAggregationInfoVO()
                .setUserNickname(user.getNickName())
                .setUserHeadPortrait(user.getHeadImgUrl())
                .setGrowthValue(Optional.ofNullable(Long.valueOf(user.getMemberGrowthValue())).orElse(0L))
                .setMemberType(MemberType.PAID_MEMBER)
                .setMemberLabel(paidMember.getMemberLabel())
                .setCurrentMemberVO(
                        new CurrentMemberVO()
                                .setMemberGradeIcon(memberGradePaidInfoDTO.getGradeIcon())
                                .setMemberGradeId(userMemberCard.getMemberId())
                                .setMemberCardId(userMemberCard.getId())
                                .setMemberCardValidTime(userMemberCard.getMemberCardValidTime())
                                .setRankCode(userMemberCard.getRankCode())
                                .setRelevancyRights(
                                        memberRights.stream()
                                                .filter(memberRight -> basicRightMap.containsKey(memberRight.getId()))
                                                .filter(memberRight -> memberRight.getRightsType() != null)
                                                .collect(
                                                        Collectors.toMap(
                                                                MemberRights::getRightsType,
                                                                memberRight -> new RelevancyRightsVO()
                                                                        .setExtendValue(basicRightMap.get(memberRight.getId()).getExtendValue())
                                                                        .setRightsName(memberRight.getRightsName())
                                                                        .setRightsType(memberRight.getRightsType())
                                                                        .setMemberRightsId(memberRight.getId())
                                                        )
                                                )
                                )
                                .setMemberName(paidMember.getPaidMemberName())
                );

    }

    @NotNull
    private MemberAggregationInfoVO wrapFreeMemberGradeInfo (Long userId, MemberAccountInfoVO user) {
        MemberGradeFreeInfoDTO freeMemberGradeInfoDTO = memberApiService.getGradeFreeInfoByMemberGuid(userId);
        MemberAggregationInfoVO memberAggregationInfoVO = freeMemberAggregationAssemble(user, freeMemberGradeInfoDTO);
        memberAggregationInfoVO.setSubordinateGrowthValue(
                Option.of(freeMemberGradeInfoDTO).map(MemberGradeFreeInfoDTO::getNextGrowthValue).getOrElse(0L)
        );
        memberAggregationInfoVO.setGrowthValue(Optional.ofNullable(Long.valueOf(user.getMemberGrowthValue())).orElse(0L));
        MemberLabelDTO memberLabelDTO = new MemberLabelDTO();
        memberLabelDTO.setLabelColor(Option.of(freeMemberGradeInfoDTO).map(MemberGradeFreeInfoDTO::getBackgroundColor).getOrNull());
        memberLabelDTO.setName(freeMemberGradeInfoDTO.getMemberInfoGradeName());
        memberAggregationInfoVO.setMemberLabel(memberLabelDTO);
        return memberAggregationInfoVO;
    }

    private PaidMemberInfoVO getPaidMemberInfo (MemberGradePaidInfoDTO memberGradePaidInfoDTO) {
        if (memberGradePaidInfoDTO == null) {
            return null;
        } else {
            List<MemberBasicsRelevancyRightsVO> rightsVOList = Option.of(memberGradePaidInfoDTO.getMemberGradeEquitiesInfos())
                                                                       .getOrElse(List.of())
                                                                       .stream()
                                                                       .map(equitiesInfoVO -> new MemberBasicsRelevancyRightsVO()
                                                                                                      .setMemberRightsId(Long.valueOf(equitiesInfoVO.getEquitiesGuid()))
                                                                                                      .setRightsName(equitiesInfoVO.getName())
                                                                                                      .setRightsIcon(equitiesInfoVO.getEquitiesImg())
                                                                                                      .setRightsExplain(equitiesInfoVO.getEquitiesExplain())
                                                                                                      .setRightsType(RightsTypeConverter.fromEquitiesRuleTypeCode(equitiesInfoVO.getEquitiesRuleType()))
                                                                                                      .setExtendValue(processExtendValue(equitiesInfoVO)))
                                                                       .filter(rights -> rights.getRightsType() != null)
                                                                       .toList();
			return new PaidMemberInfoVO()
														.setMemberId(Long.valueOf(memberGradePaidInfoDTO.getMemberInfoGradeGuid()))
														.setPaidMemberName(memberGradePaidInfoDTO.getMemberInfoGradeName())
														.setMemberBasicsRelevancyRightsList(Optional.of(rightsVOList).orElse(Collections.emptyList()));
        }
    }

    /**
     * 处理扩展字段取值 要显示到文案上
     * 会员折扣有*100 积分翻倍直接取
     * @param equitiesInfoVO 权益信息
     * @return 扩展字段取值
     */
    private long processExtendValue(GradeEquitiesInfoVO equitiesInfoVO) {
        return Optional.ofNullable(equitiesInfoVO.getExtendValue())
                       .map(value ->new BigDecimal(value).multiply(CommonPool.BIG_DECIMAL_UNIT_CONVERSION_HUNDRED).longValue())
                       .orElse(0L);
    }
    private List<UserMemberCard> getUserMemberCard (MemberGradePaidInfoDTO memberGradePaidInfoDTO, Long userId) {
        if (memberGradePaidInfoDTO == null) {
            return List.of();
        }else{
            UserMemberCard userMemberCard = new UserMemberCard()
                                                    .setMemberCardStatus(MemberCardStatus.getByValue(memberGradePaidInfoDTO.getMemberCardStatus()))
                                                    .setMemberId(Long.valueOf(memberGradePaidInfoDTO.getMemberInfoGradeGuid()))
                                                    .setMemberName(memberGradePaidInfoDTO.getMemberInfoGradeName())
                                                    .setMemberCardValidTime(memberGradePaidInfoDTO.getMemberCardValidTime())
                                                    .setUserId(userId)
                                                    .setRankCode(memberGradePaidInfoDTO.getVipGrade() == null ? CommonPool.NUMBER_ZERO : Integer.parseInt(memberGradePaidInfoDTO.getVipGrade()));
            userMemberCard.setId(Long.valueOf(memberGradePaidInfoDTO.getMemberCardGuid()));

            return List.of(userMemberCard);
        }
    }


    /**
     * 用户开通付费会员
     */
    @Override
    @Redisson(name = UserConstant.USER_OPEN_PAID_MEMBER_CARD, key = "#paidMemberDeal.userId")
    @Transactional(rollbackFor = Exception.class)
    public void userPayPaidMember(String transactionId, PaidMemberDealDTO paidMemberDeal, PaymentInfoDTO businessParams) {
        //获取续费有效时长 /天
        Integer durationDays = paidMemberDeal.getPaidRuleJson().getEffectiveDurationType().getValue();
        // 查看当前用户开通得会员卡是否存在
        Long userId = paidMemberDeal.getUserId();
        List<MemberGradeCardDTO> memberGradeCardDTOS = memberApiService.getGradeCardList(
                new GradeCardQueryDTO().setMemberInfoGuid(String.valueOf(userId))
                        .setGradeType(MemberType.PAID_MEMBER.getValue())
                        .setMemberCardStatus(MemberCardStatus.NORMAL.getValue())
                        .setVipGrade(paidMemberDeal.getRankCode())
                        .setGradeGuid(String.valueOf(paidMemberDeal.getPaidMemberId()))
        );

        MemberGradeCardDTO memberCard = memberGradeCardDTOS.stream()
               .filter(memberGradeCardDTO1 -> memberGradeCardDTO1.getGradeGuid().equals(String.valueOf(paidMemberDeal.getPaidMemberId())))
               .findFirst()
               .orElse(null);
        //交易类型
        TransactionType transactionType;
        //用于计算会员过期时间
        LocalDate memberExpiredDate;
        if (memberCard == null || memberCard.getMemberCardValidTime().isBefore(LocalDate.now())) {
            // 新增会员卡
            // 会员已过期
            if (memberCard != null && memberCard.getMemberCardValidTime().isBefore(LocalDate.now())) {
                //更新为已过期状态
                memberApiService.updateGradeCard(new GradeCardQueryDTO().setGuid(memberCard.getGuid())
                                                         .setMemberInfoGuid(String.valueOf(userId))
                                                         .setMemberCardStatus(MemberCardStatus.NORMAL.getValue()));
            }
            transactionType = TransactionType.PAID_MEMBER_OPEN;
            memberExpiredDate = LocalDate.now().plusDays(durationDays);
            memberApiService.addGradeCard(new GradeCardQueryDTO()
                                                  .setMemberCardStatus(MemberCardStatus.NORMAL.getValue())
                                                  .setGradeType(MemberType.PAID_MEMBER.getValue())
                                                  .setMemberCardValidTime(memberExpiredDate)
                                                  .setGradeGuid(String.valueOf(paidMemberDeal.getPaidMemberId()))
                                                  .setOperSubjectGuid(String.valueOf(ISystem.platformIdMust()))
                                                  .setOpenType(OpenType.PAID_BUY.getValue())
                                                  .setVipGrade(paidMemberDeal.getRankCode())
                                                  .setMemberInfoGuid(String.valueOf(userId))
                                                  .setEffectiveDurationType(durationDays));

        } else {
            //原有会员卡续费
            transactionType = TransactionType.PAID_MEMBER_RENEW;
            memberExpiredDate = memberCard.getMemberCardValidTime().plusDays(durationDays);
            memberApiService.updateGradeCard(new GradeCardQueryDTO().setGuid(memberCard.getGuid())
                                                     .setMemberInfoGuid(String.valueOf(userId))
                                                     .setMemberCardValidTime(memberExpiredDate)
                                                     .setMemberCardStatus(MemberCardStatus.NORMAL.getValue())
                                                     .setEffectiveDurationType(durationDays)
                                                     .setOpenType(OpenType.PAID_BUY.getValue())
                                                     .setVipGrade(paidMemberDeal.getRankCode())
                                                     .setGradeGuid(String.valueOf(paidMemberDeal.getPaidMemberId()))
                                                     .setOperSubjectGuid(String.valueOf(ISystem.platformIdMust())));
        }
        UserAccount user = userAccountService.lambdaQuery()
                .select(UserAccount::getUserNickname, UserAccount::getUserPhone, UserAccount::getUserHeadPortrait)
                .eq(UserAccount::getUserId, userId)
                .one();
        //更新用户最后交易时间
        userAccountService.lambdaUpdate()
                .set(UserAccount::getLastDealTime, LocalDateTime.now())
                .eq(UserAccount::getUserId, userId)
                .update();
        //记录会员流水
        String memberPurchaseNo = UserConstant.MEMBER_PURCHASE_HISTORY_NO_PREFIX + IdUtil.getSnowflakeNextIdStr();
        //会员中台同步写入购买记录数据
        MemberGradePurchaseHistoryDTO gradeHistory = new MemberGradePurchaseHistoryDTO();
        gradeHistory.setNo(memberPurchaseNo)
                .setMemberInfoGuid(String.valueOf(userId))
                .setMemberInfoName(user.getUserNickname())
                .setUserPhone(user.getUserPhone())
                .setMemberInfoGradeGuid(String.valueOf(paidMemberDeal.getPaidMemberId()))
                .setMemberInfoGradeName(paidMemberDeal.getPaidMemberName())
                .setEffectiveDurationType(paidMemberDeal.getPaidRuleJson().getEffectiveDurationType().getValue())
                .setVipGrade(paidMemberDeal.getRankCode())
                .setPayAmount(BigDecimal.valueOf(paidMemberDeal.getPayAmount()).divide(BigDecimal.valueOf(10000), 2, java.math.RoundingMode.HALF_UP))
                .setPayType(paidMemberDeal.getPayType().getPlatform())
                .setRemark(paidMemberDeal.getPayType().getDesc())
                .setOrderNo(businessParams.getOrderNum())
                .setType(TransactionType.PAID_MEMBER_OPEN == transactionType ? MemberPurchaseType.PAID_MEMBER_OPEN.getValue() : MemberPurchaseType.PAID_MEMBER_RENEW.getValue())
                .setExpireTime(memberExpiredDate.atTime(LocalTime.MAX))
                .setSource(paidMemberDeal.getPlatform()== Platform.H5 ? SourceTypeEnum.MALL_H5.getCode():SourceTypeEnum.MALL_WECHAT_APPLET.getCode())
                .setOperatorName(user.getUserNickname()+"/"+user.getUserPhone());
        memberApiService.saveMemberGradePurchaseHistory(gradeHistory);

        //只有用用户储值购买会员才记录储值流水
        if (PayType.BALANCE.equals(paidMemberDeal.getPayType())) {
            //保存会员储值流水信息
            BalanceChangeDTO balanceChangeDTO = new BalanceChangeDTO();
            DataChangeMessage dataChangeMessage = new DataChangeMessage();
            dataChangeMessage.setUserId(userId);
            dataChangeMessage.setChangeType(ChangeType.REDUCE);
            dataChangeMessage.setOperatorType(TransactionType.PAID_MEMBER_OPEN.equals(transactionType) ?
                    BalanceHistoryOperatorType.PURCHASE_MEMBERSHIP : BalanceHistoryOperatorType.RENEWAL_MEMBERSHIP);
            dataChangeMessage.setValue(paidMemberDeal.getPayAmount());
            dataChangeMessage.setOrderNo(gradeHistory.getNo());
            dataChangeMessage.setOperatorUserId(userId);
            balanceChangeDTO.setPersonDataChangeMessage(dataChangeMessage);
            userBalanceHistoryManageService.asyncSaveUserBalanceHistory(balanceChangeDTO, null, null);
        }
        //异步发送 mq 消息
        globalExecutor.execute(
                () -> {
                    // 生成对账单
                    rabbitTemplate.convertAndSend(
                            StatementRabbit.OVERVIEW_STATEMENT.exchange(),
                            StatementRabbit.OVERVIEW_STATEMENT.routingKey(),
                            new OverviewStatementDTO()
                                    .setTransactionSerialNumber(transactionId)
                                    .setUserNickname(user.getUserNickname())
                                    .setUserAvatar(user.getUserHeadPortrait())
                                    .setAccount(paidMemberDeal.getPayAmount())
                                    .setOrderNo(memberPurchaseNo)
                                    .setUserId(userId)
                                    .setShopId(SecurityConst.NO_SHOP_ID_CLIENT_DEFAULT_SHOP_ID)
                                    .setTransactionTime(LocalDateTime.now())
                                    .setTransactionType(transactionType)
                                    .setChangeType(ChangeType.INCREASE));
                    if (paidMemberDeal.getPayType() != PayType.WECHAT) {
                        return;
                    }
                    // 延迟队列 小程序发货 立即发可能会出现支付单不存在
                    rabbitTemplate.convertAndSend(
                            UserRabbit.USER_VIRTUAL_DELIVER.exchange(),
                            UserRabbit.USER_VIRTUAL_DELIVER.routingKey(),
                            new UserVirtualDeliverDTO()
                                    .setTransactionId(transactionId)
                                    .setOpenId(businessParams.getOpenId())
                                    .setDesc(transactionType == TransactionType.PAID_MEMBER_RENEW ? "付费会员续费" : "付费会员开通")
                                    .setPlatform(businessParams.getPayPlatform()),
                            message -> {
                                //延迟俩分钟
                                message.getMessageProperties().setHeader(MessageProperties.X_DELAY, 2 * 60 * 1000);
                                return message;
                            });
                }
        );
    }


    @Override
    public Option<UserMemberCard> checkFreeMemberCardInfo(MemberAccountInfoVO user,MemberGradeFreeInfoDTO freeMemberGradeInfoDTO) {
        return Option.of(
                freeMemberGradeInfoDTO
        ).map(
                userFreeMember -> new UserMemberCard()
                        .setMemberName(userFreeMember.getMemberInfoGradeName())
                                          .setRankCode(userFreeMember.getVipGrade() == null ? null : Integer.valueOf(userFreeMember.getVipGrade()))
                        .setMemberCardStatus(MemberCardStatus.NORMAL)
                        .setMemberCardValidTime(LocalDate.now())
                        .setUserId(Long.valueOf(user.getGuid()))
                        .setMemberId(userFreeMember.getMemberInfoGradeGuid() == null?null:Long.valueOf(userFreeMember.getMemberInfoGradeGuid()))
                        .setOpenType(OpenType.GROWTH_VALUE_REACH)
                        .setMemberType(MemberType.FREE_MEMBER)
        );
    }

    @Override
    public Map<Long, Boolean> getUserRights(Set<Long> userIds, RightsType rightsType) {
        //初始数据默认是所有用户都没有这项权益
        Map<Long, Boolean> userRightsMap = userIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(), userId -> false
                ));
        // 查询权益类型是否开启
        MemberRights activeRights = memberRightsService.lambdaQuery()
                .eq(MemberRights::getRightsType, rightsType)
                .eq(MemberRights::getRightsSwitch, Boolean.TRUE)
                .one();
        if (activeRights == null) {
            // 权益未开启，所有用户都无该权益
            return userRightsMap;
        }

        Long activeRightsId = activeRights.getId();

        // 获取用户中是付费会员的用户ids
        List<UserMemberCard> paidMemberCards = userMemberCardService.lambdaQuery()
                .eq(UserMemberCard::getMemberCardStatus, MemberCardStatus.NORMAL)
//                .groupBy(UserMemberCard::getUserId)
                .in(UserMemberCard::getUserId, userIds)
                .list();
        Set<Long> paidMemberUserIds = paidMemberCards.stream().map(UserMemberCard::getUserId).collect(Collectors.toSet());

        // 排除付费会员 ids   拿到免费会员信息
        Set<Long> freeMemberUserIds = new HashSet<>(userIds);
        freeMemberUserIds.removeAll(paidMemberUserIds);
        if (rightsType == RightsType.EXCLUSIVE_SERVICE) {
            /*
             *   1. 检查用户是否是付费会员
             *   2.付费会员是否包含该权益
             *   3.免费会员是否包含该权益
             */

            if (!freeMemberUserIds.isEmpty()) {

                List<RankedMemberVO> rankedFreeMembers = TenantShop.disable(()->{
                    return userAccountService.getRankedMember(freeMemberUserIds);

                });
                Set<Long> freeMemberRankIds = rankedFreeMembers.stream()
                        .map(RankedMemberVO::getFreeMemberId)
                        .collect(Collectors.toSet());

                Map<Long, Long> collect = rankedFreeMembers.stream().collect(Collectors.toMap(RankedMemberVO::getUserId, RankedMemberVO::getFreeMemberId));

                // 获取用户当前会员级别是否有相关权益
                Set<Long> memberIdsWithRights = Optional.ofNullable(userMemberRelevancyRightsService.lambdaQuery()
                                .in(UserMemberRelevancyRights::getMemberId, freeMemberRankIds)
                                .eq(UserMemberRelevancyRights::getMemberRightsId, activeRightsId)
                                .list())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(UserMemberRelevancyRights::getMemberId)
                        .collect(Collectors.toSet());

                freeMemberUserIds.forEach(userId -> userRightsMap.put(userId, memberIdsWithRights.contains(collect.get(userId))));
            }
            //付费会员
            if (CollUtil.isNotEmpty(paidMemberCards)) {
                paidMemberCards = paidMemberCards.stream()
                        .collect(Collectors.toMap(
                                UserMemberCard::getUserId,
                                user -> user,
                                (u1, u2) -> u1.getRankCode() > u2.getRankCode() ? u1 : u2
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
                List<Long> paidMemberIds = userAddonSupporter.queryHasRightsMemberId(activeRightsId);
                paidMemberCards.forEach(paidMemberCard -> {
                    Long memberId = paidMemberCard.getMemberId();
                    if (paidMemberIds.contains(memberId)) {
                        userRightsMap.put(paidMemberCard.getUserId(), true);
                    }
                });

            }

        }

        return userRightsMap;
    }

    @Override
    public boolean queryMemberInUse(Long memberId) {
        Long count = userMemberCardService.lambdaQuery()
                .eq(UserMemberCard::getMemberId, memberId)
                .eq(UserMemberCard::getDeleted, Boolean.FALSE)
                .eq(UserMemberCard::getMemberCardStatus, MemberCardStatus.NORMAL)
                .gt(UserMemberCard::getMemberCardValidTime, LocalDate.now())
                .count();
        return count>0;
    }

    @Override
    public PayResult recharge(MemberCardRechargeDealDTO dto) {
        //生成记录到会员系统
        MemberCardRechargeVO memberCardRechargeVO = memberApiService.memberCardRecharge(new MemberCardRechargeDTO()
                .setMemberInfoGuid(dto.getMemberInfoGuid())
                .setMemberInfoCardGuid(dto.getMemberInfoCardGuid())
                .setRechargeMoney(dto.getPayAmount()));

        Long userId = ISecurity.userMust().getId();
        PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setOrderNum(memberCardRechargeVO.getOrderNumber());
        paymentInfo.setTotalFee(dto.getPayAmount().multiply(BigDecimal.valueOf(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)).longValue());
        paymentInfo.setPayType(dto.getPayType());
        paymentInfo.setUserId(userId);
        paymentInfo.setSubject("会员卡充值");
        paymentInfo.setBody("会员卡充值{}".concat(paymentInfo.getOrderNum()));
        //附加所使用规则json
        paymentInfo.setAttach(JSON.toJSONString(dto));
        paymentInfo.setExchange(UserRabbit.EXCHANGE);
        paymentInfo.setRouteKey(UserRabbit.USER_MEMBER_CARD_RECHARGE_OK.routingKey());

        paymentInfo.setSeconds(30 * 60L);
        paymentInfo.setFeeType(FeeType.CNY);
        paymentInfo.setTerminalIp(ISystem.ipMust());
        paymentInfo.setShopId(ISystem.shopIdMust());
        paymentInfo.setPlatformId(ISystem.platformIdMust());
        paymentInfo.setPayPlatform(ISystem.platformMust());
        paymentInfo.setOpenId(ISecurity.userMust().getOpenid());
        paymentInfo.setAuthCode(null);
        paymentInfo.setWapUrl(null);
        paymentInfo.setWapName(null);
        return paymentRpcService.payRequest(paymentInfo);
    }

    @Override
    @Redisson(name = UserConstant.USER_OPEN_MEMBER_CARD, key = "#dto.memberInfoGuid +':'+#dto.cardGuid")
    public MemberCardOpenVO openCard(MemberCardOpenDTO dto) {
        return memberApiService.openCard(dto);
    }

    @Override
    @Redisson(name = UserConstant.USER_OPEN_MEMBER_CARD, key = "#dto.memberInfoGuid +':'+#dto.cardGuid")
    public PayResult openCardPay(MemberCardOpenCardDealDTO dto) {
        //生成记录到会员系统
        String orderNumber = memberApiService.openCardPay(new MemberCardOpenCardPayDTO()
                .setCardGuid(dto.getCardGuid())
                .setMemberInfoGuid(dto.getMemberInfoGuid())
                .setPayMoney(dto.getPayAmount())
                .setBusinessType(dto.getBusinessType())
                .setPayWay(dto.getPayType() == PayType.WECHAT ? CommonPool.NUMBER_ZERO : CommonPool.NUMBER_ONE));

        Long userId = ISecurity.userMust().getId();
        PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setOrderNum(orderNumber);
        paymentInfo.setTotalFee(dto.getPayAmount().multiply(BigDecimal.valueOf(CommonPool.UNIT_CONVERSION_TEN_THOUSAND)).longValue());
        paymentInfo.setPayType(dto.getPayType());
        paymentInfo.setUserId(userId);
        paymentInfo.setSubject("会员卡开卡");
        paymentInfo.setBody("会员卡开卡{}".concat(paymentInfo.getOrderNum()));
        //附加所使用规则json
        paymentInfo.setAttach(JSON.toJSONString(dto));
        paymentInfo.setExchange(UserRabbit.EXCHANGE);
        paymentInfo.setRouteKey(UserRabbit.USER_MEMBER_CARD_OPEN_CARD_PAY_OK.routingKey());

        paymentInfo.setSeconds(30 * 60L);
        paymentInfo.setFeeType(FeeType.CNY);
        paymentInfo.setTerminalIp(ISystem.ipMust());
        paymentInfo.setShopId(ISystem.shopIdMust());
        paymentInfo.setPlatformId(ISystem.platformIdMust());
        paymentInfo.setPayPlatform(ISystem.platformMust());
        paymentInfo.setOpenId(ISecurity.userMust().getOpenid());
        paymentInfo.setAuthCode(null);
        paymentInfo.setWapUrl(null);
        paymentInfo.setWapName(null);
        return paymentRpcService.payRequest(paymentInfo);
    }

    @Override
    public void rechargeResult(PayNotifyResultDTO payNotifyResultDTO) {
        PaymentInfoDTO businessParams = payNotifyResultDTO.getBusinessParams();
        MemberCardRechargeDealDTO dealDTO = JSON.parseObject(businessParams.getAttach(), MemberCardRechargeDealDTO.class);

        //生成记录到会员系统
        memberApiService.memberCardRecharge(new MemberCardRechargeDTO()
                .setOrderNumber(businessParams.getOrderNum())
                .setMemberInfoGuid(dealDTO.getMemberInfoGuid())
                .setMemberInfoCardGuid(dealDTO.getMemberInfoCardGuid())
                .setRechargeMoney(dealDTO.getPayAmount()));

        String transactionId = payNotifyResultDTO.getShopIdTransactionMap().values().iterator().next().getTransactionId();
        CompletableTask.allOf(globalExecutor,
                // 延迟队列 小程序发货 立即发可能会出现支付单不存在
                () -> rabbitTemplate.convertAndSend(
                        UserRabbit.USER_MEMBER_CARD_RECHARGE_VIRTUAL_DELIVER.exchange(),
                        UserRabbit.USER_MEMBER_CARD_RECHARGE_VIRTUAL_DELIVER.routingKey(),
                        new UserVirtualDeliverDTO()
                                .setTransactionId(transactionId)
                                .setOpenId(businessParams.getOpenId())
                                .setDesc("会员卡充值")
                                .setPlatform(businessParams.getPayPlatform()),
                        message -> {
                            //延迟俩分钟
                            message.getMessageProperties().setHeader(MessageProperties.X_DELAY, 2 * 60 * 1000);
                            return message;
                        })
        );
    }

    @Override
    @Redisson(name = UserConstant.USER_OPEN_MEMBER_CARD_CALLBACK, key = "#payNotifyResultDTO.businessParams.orderNum")
    public void openCardPayResult(PayNotifyResultDTO payNotifyResultDTO) {
        PaymentInfoDTO businessParams = payNotifyResultDTO.getBusinessParams();
        MemberCardOpenCardDealDTO dealDTO = JSON.parseObject(businessParams.getAttach(), MemberCardOpenCardDealDTO.class);

        //生成记录到会员系统
        memberApiService.openCardPay(new MemberCardOpenCardPayDTO()
                .setOrderNumber(businessParams.getOrderNum())
                .setCardGuid(dealDTO.getCardGuid())
                .setMemberInfoGuid(dealDTO.getMemberInfoGuid())
                .setPayMoney(dealDTO.getPayAmount())
                .setBusinessType(dealDTO.getBusinessType()));
    }

    /**
     * 免费会员聚合数据组装
     *
     * @param user 用户信息
     * @return 会员信息
     */
    private MemberAggregationInfoVO freeMemberAggregationAssemble(MemberAccountInfoVO user, MemberGradeFreeInfoDTO freeMemberGradeInfoDTO) {
        MemberAggregationInfoVO memberInfo = new MemberAggregationInfoVO()
                .setUserNickname(user.getNickName())
                .setUserHeadPortrait(user.getHeadImgUrl())
                                                     .setGrowthValue(user.getMemberGrowthValue() == null ? 0L : Long.valueOf(user.getMemberGrowthValue()))
                .setMemberType(MemberType.FREE_MEMBER);

        //免费会员权益
        List<RelevancyRightsVO> relevancyRights = Option.of(freeMemberGradeInfoDTO.getMemberGradeEquitiesInfos())
                                                                   .getOrElse(List.of())
                                                                   .stream()
                                                                   .map(equitiesInfoVO -> new RelevancyRightsVO()
                                                                                                  .setMemberRightsId(Long.valueOf(equitiesInfoVO.getEquitiesGuid()))
                                                                                                  .setRightsName(equitiesInfoVO.getName())
                                                                                                  .setRightsIcon(equitiesInfoVO.getEquitiesImg())
                                                                                                  .setRightsExplain(equitiesInfoVO.getEquitiesExplain())
                                                                                                  .setRightsType(RightsTypeConverter.fromEquitiesRuleTypeCode(equitiesInfoVO.getEquitiesRuleType()))
                                                                                                  .setExtendValue(processExtendValue(equitiesInfoVO)))
                                                                   .filter(rights -> rights.getRightsType() != null)
                                                                   .toList();

        return checkFreeMemberCardInfo(user, freeMemberGradeInfoDTO)
                .map(
                        (userMemberCard) -> {
                            return memberInfo.setCurrentMemberVO(
                                    new CurrentMemberVO()
                                            .setMemberGradeIcon(freeMemberGradeInfoDTO.getGradeIcon())
                                            .setMemberGradeId(userMemberCard.getMemberId())
                                            .setMemberCardId(userMemberCard.getId())
                                            .setRankCode(userMemberCard.getRankCode() == null ? CommonPool.NUMBER_ZERO : userMemberCard.getRankCode())
                                            .setMemberName(userMemberCard.getMemberName())
                                            .setRelevancyRights(CollUtil.isEmpty(relevancyRights) ? Collections.emptyMap() : relevancyRights.stream().collect(Collectors.toMap(RelevancyRightsVO::getRightsType, bean -> bean)))
                            );
                        }
                ).getOrElse(() -> memberInfo.setCurrentMemberVO(new CurrentMemberVO().setRankCode(CommonPool.NUMBER_ZERO)));

    }

    /**
     * todo 处理用户到期会员卡 这里要会员中台提供接口过期卡
     *
     * @param paidMemberInfo 付费会员卡信息
     * @param userId         用户id
     */
    private void disposeExpireMemberCard(PaidMemberInfoVO paidMemberInfo, Long userId) {
        CompletableTask.allOf(globalExecutor,
                () -> {
                    //更新付费会员卡过期数据
                    if (CollUtil.isNotEmpty(paidMemberInfo.getMemberCloseIds())) {
                        userMemberCardService.lambdaUpdate()
                                .set(UserMemberCard::getMemberCardStatus, MemberCardStatus.ABNORMAL)
                                .in(UserMemberCard::getMemberId, paidMemberInfo.getMemberCloseIds())
                                .eq(UserMemberCard::getUserId, userId)
                                .update();
                    }
                },
                () -> {
                    // 更新会员正常到期的用户会员卡
                    userMemberCardService.lambdaUpdate()
                            .set(UserMemberCard::getMemberCardStatus, MemberCardStatus.EXPIRE)
                            .lt(UserMemberCard::getMemberCardValidTime, LocalDate.now())
                            .eq(UserMemberCard::getUserId, userId)
                            .eq(UserMemberCard::getMemberCardStatus, MemberCardStatus.NORMAL)
                            .update();
                }
        );
    }

}