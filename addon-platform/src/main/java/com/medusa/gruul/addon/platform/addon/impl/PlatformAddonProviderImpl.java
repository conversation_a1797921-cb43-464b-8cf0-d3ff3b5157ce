package com.medusa.gruul.addon.platform.addon.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.medusa.gruul.addon.platform.addon.PlatformAddonProvider;
import com.medusa.gruul.addon.platform.addon.PlatformAddonSupporter;
import com.medusa.gruul.addon.platform.model.enums.PlatformError;
import com.medusa.gruul.addon.platform.model.enums.TemplateTypeEnum;
import com.medusa.gruul.addon.platform.model.enums.WebParamKeyEnum;
import com.medusa.gruul.addon.platform.model.enums.WebParamModuleEnum;
import com.medusa.gruul.addon.platform.model.vo.CategoryVO;
import com.medusa.gruul.addon.platform.mp.entity.DecorationPage;
import com.medusa.gruul.addon.platform.mp.entity.DecorationTemplate;
import com.medusa.gruul.addon.platform.mp.entity.PlatformCategory;
import com.medusa.gruul.addon.platform.mp.entity.PlatformShopSigningCategory;
import com.medusa.gruul.addon.platform.mp.service.IDecorationPageService;
import com.medusa.gruul.addon.platform.mp.service.IDecorationTemplateService;
import com.medusa.gruul.addon.platform.mp.service.IPlatformCategoryService;
import com.medusa.gruul.addon.platform.mp.service.IPlatformShopSigningCategoryService;
import com.medusa.gruul.addon.platform.service.PlatformCategoryService;
import com.medusa.gruul.addon.platform.service.PlatformConfigService;
import com.medusa.gruul.addon.platform.service.SigningCategoryService;
import com.medusa.gruul.common.addon.provider.AddonProvider;
import com.medusa.gruul.common.addon.provider.AddonProviders;
import com.medusa.gruul.common.ipaas.model.category.PlatformCategoryTreeVO;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.mp.model.SqlHelper;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.global.model.constant.Services;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.goods.api.enums.GoodsRabbit;
import com.medusa.gruul.goods.api.model.CategoryLevel;
import com.medusa.gruul.goods.api.model.dto.CategorySigningCustomDeductionRationMqDto;
import com.medusa.gruul.goods.api.model.enums.ProductStatus;
import com.medusa.gruul.goods.api.model.vo.CategoryLevelName;
import com.medusa.gruul.goods.api.model.vo.PlatformCategoryVo;
import com.medusa.gruul.goods.api.rpc.GoodsRpcService;
import com.medusa.gruul.open.api.addon.OpenPlatformAddonConstant;
import com.medusa.gruul.open.api.addon.platform.CategoryResponse;
import com.medusa.gruul.order.api.addon.OrderAddonConstant;
import com.medusa.gruul.service.uaa.api.constant.UaaConstant;
import com.medusa.gruul.shop.api.enums.PageTypeEnum;
import com.medusa.gruul.shop.api.model.bo.PageReference;
import com.medusa.gruul.shop.api.model.dto.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@AddonProviders
@DubboService
@Service
@RequiredArgsConstructor
public class PlatformAddonProviderImpl implements PlatformAddonProvider {

    private final IPlatformCategoryService platformCategoryService;
    private final IPlatformShopSigningCategoryService platformShopSigningCategoryService;
    private final GoodsRpcService goodsRpcService;
    private final PlatformAddonSupporter platformAddonSupporter;
    private final IDecorationPageService decorationPageService;
    private final RabbitTemplate rabbitTemplate;
    private final IDecorationTemplateService decorationTemplateService;
    private final PlatformConfigService platformConfigService;
    private final SigningCategoryService signingCategoryService;
    private final PlatformCategoryService platformCategoryServiceBusiness;


    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getProductStatus")
    public ProductStatus getProductStatus(ProductStatus status) {
        log.debug("PlatformAddonProviderImpl invoking " + status.name());
        return ProductStatus.UNDER_REVIEW;
    }

    /**
     * 根据三级类目id获取对应的一级、二级类目id
     *
     * @param platformCategoryIdSet 三级类目id
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getPlatformCategoryVoByLevel3Id", order = 1)
    public PlatformCategoryVo getPlatformCategoryVoByLevel3Id(Set<Long> platformCategoryIdSet) {
        Map<Long, Long> platformCategories = getPlatformCategories(platformCategoryIdSet);
        if (platformCategories == null) {
            return null;
        }
        PlatformCategoryVo platformCategoryVo = new PlatformCategoryVo();
        platformCategoryVo.setThirdSecondMap(platformCategories);
        Map<Long, Long> secondFirstMap = getPlatformCategories(platformCategories.values());
        platformCategoryVo.setSecondFirstMap(secondFirstMap);
        return platformCategoryVo;

    }


    /**
     * 签约类目信息保存
     *
     * @param signingCategories 签约类目
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @AddonProvider(service = Services.GRUUL_MALL_SHOP, supporterId = "shopSigningCategory", methodName = "editSingingCategory", order = 1)
    public Boolean editPlatformShopSigningCategory(List<SigningCategoryDTO> signingCategories, Long shopId) {

        List<PlatformShopSigningCategory> existingSigningCategories = platformShopSigningCategoryService.lambdaQuery()
                .eq(PlatformShopSigningCategory::getShopId, shopId)
                .list();
        if (CollUtil.isNotEmpty(existingSigningCategories)) {
            //获取更新后 未删除的的签约类目id
            List<Long> existingSigningCategoryIds = Lists.newArrayList();
            if (CollectionUtil.isNotEmpty(signingCategories)) {
                existingSigningCategoryIds = signingCategories.stream()
                        .map(SigningCategoryDTO::getId)
                        .filter(Objects::nonNull)
                        .toList();
            }
            // 过滤出数据库中还有 但是已经被前端删除的签约类目
            List<Long> finalExistingSigningCategoryIds = existingSigningCategoryIds;

            List<PlatformShopSigningCategory> platformShopSigningCategories = existingSigningCategories.stream()
                    .filter(dbCategory -> !finalExistingSigningCategoryIds.contains(dbCategory.getId())).toList();

            if (CollUtil.isNotEmpty(platformShopSigningCategories)) {
                Set<Long> deleteSigningCategoryIds = platformShopSigningCategories.stream()
                        .map(PlatformShopSigningCategory::getCurrentCategoryId).collect(Collectors.toSet());

                boolean signingCategoryProduct = goodsRpcService.getSigningCategoryProduct(deleteSigningCategoryIds,
                        shopId);
                if (signingCategoryProduct) {
                    throw new GlobalException("当前删除签约类目下存在商品请联系商家删除");
                }
                Boolean supplierSigningCategoryProduct = platformAddonSupporter.getSupplierSigningCategoryProduct(
                        deleteSigningCategoryIds, shopId);
                if (supplierSigningCategoryProduct != null && supplierSigningCategoryProduct) {
                    throw new GlobalException("当前删除签约类目下存在商品请联系供应商删除");
                }
                List<Long> needDeleteIds = platformShopSigningCategories.stream()
                        .map(PlatformShopSigningCategory::getId).collect(
                                Collectors.toList());
                platformShopSigningCategoryService.removeByIds(needDeleteIds);
            }
        }

        //新增
        List<PlatformShopSigningCategory> addSigningCategory = signingCategories.stream()
                .filter(bean -> bean.getId() == null)
                .map(bean -> convertToPlatformShopSigningCategory(bean, shopId))
                .collect(Collectors.toList());
        platformShopSigningCategoryService.saveBatch(addSigningCategory);
        //获取数据库中店铺签约类目信息
        List<SigningCategoryDTO> updateSignCategoryDtos = signingCategories.stream()
                .filter(x -> Objects.nonNull(x.getId())).toList();
        List<CategorySigningCustomDeductionRationMqDto> signingCategoryCustomDeductionRationMqDtos = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(updateSignCategoryDtos)) {
            List<Long> shopSigningCategoryIds = updateSignCategoryDtos.stream().map(SigningCategoryDTO::getId).collect(Collectors.toList());
            List<PlatformShopSigningCategory> list = platformShopSigningCategoryService.lambdaQuery()
                    .in(PlatformShopSigningCategory::getId, shopSigningCategoryIds)
                    .list();
            Map<Long, Long> dbCategoryCustomDeductionRatioMap = Maps.newHashMap();
            for (PlatformShopSigningCategory platformShopSigningCategory : list) {
                dbCategoryCustomDeductionRatioMap.put(platformShopSigningCategory.getId(),
                        platformShopSigningCategory.getCustomDeductionRatio());
            }
            CategorySigningCustomDeductionRationMqDto dto = new CategorySigningCustomDeductionRationMqDto();
            //比较数据库中签约类目信息和前端传入的签约类目信息 获取出扣率变化的类目
            for (SigningCategoryDTO updateSignCategoryDto : updateSignCategoryDtos) {
                if (!Objects.equals(updateSignCategoryDto.getCustomDeductionRatio(),
                        dbCategoryCustomDeductionRatioMap.get(updateSignCategoryDto.getId()))) {
                    dto.setSecondCategoryId(updateSignCategoryDto.getCurrentCategoryId());
                    dto.setDeductionRation(updateSignCategoryDto.getCustomDeductionRatio());
                    dto.setShopId(shopId);
                    signingCategoryCustomDeductionRationMqDtos.add(dto);
                }
            }


        }

        //修改
        List<PlatformShopSigningCategory> updateSigningCategory = signingCategories.stream()
                .filter(bean -> bean.getId() != null)
                .map(bean -> {
                    PlatformShopSigningCategory platformShopSigningCategory =
                            convertToPlatformShopSigningCategory(bean, shopId);
                    platformShopSigningCategory.setId(bean.getId());
                    return platformShopSigningCategory;
                })
                .collect(Collectors.toList());

        platformShopSigningCategoryService.updateBatchById(updateSigningCategory);
        if (CollUtil.isNotEmpty(updateSigningCategory)) {
            // 修改签约类目 应通知商品修改类目信息
            log.error("修改签约类目");
        }

        for (CategorySigningCustomDeductionRationMqDto dto : signingCategoryCustomDeductionRationMqDtos) {
            //发送更新商品类目扣率的MQ
            rabbitTemplate.convertAndSend(GoodsRabbit.EXCHANGE,
                    GoodsRabbit.GOODS_CUSTOM_DEDUCTION_RATIO_CHANGE.routingKey(),
                    dto);
        }

        return Boolean.TRUE;
    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getCustomDeductionRatio")
    public Long getCustomDeductionRatio(Long platformTwoCategory, Long shopId) {
        PlatformShopSigningCategory shopSigningCategory = platformShopSigningCategoryService.lambdaQuery()
                .select(PlatformShopSigningCategory::getCustomDeductionRatio, BaseEntity::getId, PlatformShopSigningCategory::getEnterpriseId)
                .eq(PlatformShopSigningCategory::getCurrentCategoryId, platformTwoCategory)
                .eq(PlatformShopSigningCategory::getShopId, shopId).one();
        if (shopSigningCategory == null) {
            throw new GlobalException("当前类目暂未无签约,请刷新后选择");
        }
        //先获取店铺的类目签约比例 如果店铺没有设置类目签约比例 则获取平台的签约比例
        if (Objects.nonNull(shopSigningCategory.getCustomDeductionRatio())) {
            return shopSigningCategory.getCustomDeductionRatio();
        }
        // 平台类目
        PlatformCategoryTreeVO platformCategory = platformCategoryServiceBusiness.getMdmPlatformCategoryById(shopSigningCategory.getEnterpriseId(), platformTwoCategory);
        SystemCode.DATA_NOT_EXIST.trueThrow(Objects.isNull(platformCategory));
        return platformCategory.getDeductionRatio();
    }

    /**
     * 获取店铺签约类目ids
     *
     * @param shopId 店铺id
     * @return 二级类目ids
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getSigningCategoryIds")
    public Set<Long> getSigningCategoryIds(Long shopId) {
        List<PlatformShopSigningCategory> platformShopSigningCategories =
                platformShopSigningCategoryService.lambdaQuery()
                        .eq(PlatformShopSigningCategory::getShopId, shopId)
                        .list();
        if (CollUtil.isNotEmpty(platformShopSigningCategories)) {
            return platformShopSigningCategories.stream()
                    .map(PlatformShopSigningCategory::getCurrentCategoryId)
                    .collect(Collectors.toSet());
        }
        return Collections.emptySet();
    }

    /**
     * 获取平台类目级别名称
     *
     * @param platformCategory 平台类目级别id
     * @return 平台类目级别名称
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_GOODS, supporterId = "goodsAddonSupporter", methodName = "getPlatformCategoryLevelName")
    public CategoryLevelName getPlatformCategoryLevelName(CategoryLevel platformCategory) {
        return platformCategoryService.getPlatformCategoryLevelName(platformCategory);
    }


    @Override
    @AddonProvider(service = Services.GRUUL_MALL_SHOP, supporterId = "shopSigningCategory", methodName = "platformTemplate")
    public PlatformShopDecorationTemplate platformTemplate(@NotNull @Valid DecorationCopyTemplateParamDTO param) {
        DecorationTemplate template = decorationTemplateService.lambdaQuery()
                .eq(DecorationTemplate::getBusinessType, param.getBusiness())
                .eq(DecorationTemplate::getEndpointType, param.getEndpoint())
                .eq(DecorationTemplate::getTemplateType, TemplateTypeEnum.SHOP)
                .eq(param.getId() != null, BaseEntity::getId, param.getId())
                .last(SqlHelper.SQL_LIMIT_1)
                .one();
        if (template == null) {
            throw PlatformError.TEMPLATE_NOT_EXIST.exception();
        }
        List<Long> pageIds = template.getPages()
                .stream()
                .filter(e -> e.getPageType() != PageTypeEnum.SAME_CITY_MALL_HOME_PAGE).map(PageReference::getPageId)
                .collect(Collectors.toList());
        List<DecorationPage> pages = this.decorationPageService.listPageByIds(pageIds);
        return new PlatformShopDecorationTemplate()
                .setName(template.getName())
                .setDescription(template.getDescription())
                .setPages(
                        pages.stream()
                                .map(
                                        page -> {
                                            DecorationCopyPageDTO result = new DecorationCopyPageDTO();
                                            result.setName(page.getName());
                                            result.setType(page.getType());
                                            result.setProperties(page.getProperties());
                                            return result;
                                        }
                                )
                                .collect(Collectors.toList())
                );
    }


    /**
     * 根据装修页面参数复制平台装修页面
     *
     * @param param 页面参数
     * @return {@link DecorationCopyPageDTO} 装修页面数据
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_SHOP, supporterId = "shopSigningCategory", methodName = "platformPage")
    public DecorationCopyPageDTO platformPage(@NotNull @Valid DecorationCopyPageParamDTO param) {
        DecorationPage page = decorationPageService.lambdaQuery()
                .select(DecorationPage::getName, DecorationPage::getType, DecorationPage::getProperties)
                .eq(param.getId() != null, DecorationPage::getId, param.getId())
                .eq(DecorationPage::getBusinessType, param.getBusiness())
                .eq(DecorationPage::getEndpointType, param.getEndpoint())
                .eq(DecorationPage::getType, param.getPageType())
                .eq(DecorationPage::getTemplateType, TemplateTypeEnum.SHOP)
                .last(SqlHelper.SQL_LIMIT_1)
                .one();
        if (page == null) {
            throw PlatformError.PAGE_NOT_EXIST.exception();
        }
        return new DecorationCopyPageDTO()
                .setName(page.getName())
                .setType(page.getType())
                .setProperties(page.getProperties());
    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = "orderPlatform",
            methodName = "platformName")
    public String getPlatformName() {
        Map<String, String> stringStringMap = platformConfigService.queryConfigParamByModuleList(List.of(WebParamModuleEnum.PUBLIC_SETTING));
        return stringStringMap.get(WebParamKeyEnum.PLATFORM_NAME.name());

    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_OPEN_PLATFORM, supporterId = OpenPlatformAddonConstant.OPEN_PLATFORM_PLATFORM_SUPPORT_ID, methodName = "getChoosableCategoryInfo")
    public List<CategoryResponse> getChoosableCategoryInfo(Long shopId) {
        List<CategoryVO> categoryVOList = Tenant.disable(() ->signingCategoryService.getChoosableCategoryInfoFromMdm(shopId));

        return categoryVOList.stream().map(categoryVO -> {
            CategoryResponse categoryResponse = new CategoryResponse();
            BeanUtil.copyProperties(categoryVO, categoryResponse);
            return categoryResponse;
        }).collect(Collectors.toList());
    }

    @Override
    @AddonProvider(service = Services.GRUUL_MALL_OPEN_PLATFORM, supporterId = OpenPlatformAddonConstant.OPEN_PLATFORM_PLATFORM_SUPPORT_ID, methodName = "cheackCategorySigned")
    public Boolean cheackCategorySigned(Long enterpriseId, Long platformCategoryId) {
        // 获取平台类目列表
        List<PlatformCategoryTreeVO> platformCategoryList = platformCategoryServiceBusiness.getMdmPlatformCategoryList(enterpriseId, Boolean.FALSE);
        if (CollUtil.isEmpty(platformCategoryList)) {
            throw new GlobalException(SystemCode.DATA_EXISTED.getCode(), "当前分类不存在");
        }

        // 获取目标类目
        PlatformCategoryTreeVO platformCategory = platformCategoryList.stream()
                .filter(vo -> Objects.equals(vo.getId(), platformCategoryId))
                .findFirst()
                .orElseThrow(() -> new GlobalException(SystemCode.DATA_EXISTED.getCode(), "当前分类不存在"));

        // 根据类目级别处理
        return switch (platformCategory.getLevel()) {
            case 1 -> handleFirstLevelCategory(enterpriseId, platformCategoryList, platformCategory);
            case 2 -> isCategorySigned(enterpriseId, List.of(platformCategoryId));
            default -> isCategorySigned(enterpriseId, List.of(platformCategory.getParentId()));
        };
    }

    /**
     * 处理一级类目
     */
    private Boolean handleFirstLevelCategory(Long enterpriseId, List<PlatformCategoryTreeVO> platformCategoryList, PlatformCategoryTreeVO platformCategory) {
        List<Long> childIds = platformCategoryList.stream()
                .filter(vo -> Objects.equals(vo.getParentId(), platformCategory.getId()))
                .map(PlatformCategoryTreeVO::getId)
                .collect(Collectors.toList());

        return CollUtil.isEmpty(childIds) ? Boolean.FALSE : isCategorySigned(enterpriseId, childIds);
    }

    /**
     * 检查类目是否已签约
     */
    private Boolean isCategorySigned(Long enterpriseId, List<Long> ids) {
        return Tenant.disable(() -> platformShopSigningCategoryService.lambdaQuery()
                .in(PlatformShopSigningCategory::getCurrentCategoryId, ids)
                .eq(PlatformShopSigningCategory::getEnterpriseId, enterpriseId)
                .exists());
    }

    private PlatformShopSigningCategory convertToPlatformShopSigningCategory(SigningCategoryDTO bean, Long shopId) {
        return new PlatformShopSigningCategory()
                .setShopId(shopId)
                .setParentId(bean.getParentId())
                .setCurrentCategoryId(bean.getCurrentCategoryId())
                .setCustomDeductionRatio(bean.getCustomDeductionRatio())
                .setEnterpriseId(ISystem.enterpriseGuidMust());
    }


    private Map<Long, Long> getPlatformCategories(Collection<Long> platformCategoryIdSet) {
        return CollectionUtil.emptyIfNull(platformCategoryService.lambdaQuery()
                .select(PlatformCategory::getId, PlatformCategory::getParentId)
                .in(PlatformCategory::getId, platformCategoryIdSet).
                list()).stream().collect(Collectors.toMap(PlatformCategory::getId, PlatformCategory::getParentId));
    }

    /**
     * 获取微信配置 - 订单服务
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_ORDER, supporterId = OrderAddonConstant.ORDER_DISCOUNT_SUPPORT_ID, methodName = "getWechatConfig")
    public Map<String, String> getWechatConfig(Long platformId) {
        Map<WebParamKeyEnum, String> configMap = platformConfigService.queryConfigByKeys(
            Arrays.asList(WebParamKeyEnum.WECHAT_APP_ID,
                    WebParamKeyEnum.WECHAT_APP_SEC,
                    WebParamKeyEnum.WECHAT_APP_DELIVER,
                    WebParamKeyEnum.MP_APP_ID,
                    WebParamKeyEnum.MP_APP_SEC)
        );
        
        Map<String, String> result = new HashMap<>();
        result.put("appId", configMap.get(WebParamKeyEnum.WECHAT_APP_ID));
        result.put("appSecret", configMap.get(WebParamKeyEnum.WECHAT_APP_SEC));
        result.put("miniAppDeliver", configMap.get(WebParamKeyEnum.WECHAT_APP_DELIVER));
        result.put("mpAppId", configMap.get(WebParamKeyEnum.MP_APP_ID));
        result.put("mpAppSec", configMap.get(WebParamKeyEnum.MP_APP_SEC));

        return result;
    }

    /**
     * 获取微信配置 - 用户服务
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_USER, supporterId = "userPaidMember", methodName = "getWechatConfig")
    public Map<String, String> getWechatConfigForUser(Long platformId) {
        return getWechatConfig(platformId);
    }

    /**
     * 获取微信配置 - 售后服务
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_AFS, supporterId = "afsAddonSupporter", methodName = "getWechatConfig")
    public Map<String, String> getWechatConfigForAfs(Long platformId) {
        return getWechatConfig(platformId);
    }

    /**
     * 获取微信配置 - 积分插件
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.ADDON_INTEGRAL, supporterId = "integralAddonSupporter", methodName = "getWechatConfig")
    public Map<String, String> getWechatConfigForIntegral(Long platformId) {
        return getWechatConfig(platformId);
    }

    /**
     * 获取微信配置 - 消息服务
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_CARRIER_PIGEON, supporterId = "carrierPigeonAddonSupporter", methodName = "getWechatConfig")
    public Map<String, String> getWechatConfigForCarrierPigeon(Long platformId) {
        return getWechatConfig(platformId);
    }

    /**
     * 获取微信配置 - 认证服务
     *
     * @param platformId 平台ID
     * @return 微信配置信息
     */
    @Override
    @AddonProvider(service = Services.GRUUL_MALL_UAA, supporterId = UaaConstant.UAA_SUPPORT_ID, methodName = "getWechatConfig")
    public Map<String, String> getWechatConfigForUaa(Long platformId) {
        return getWechatConfig(platformId);
    }
}
