package com.medusa.gruul.addon.platform.service.impl;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Maps;
import com.medusa.gruul.addon.platform.model.enums.WebParamKeyEnum;
import com.medusa.gruul.addon.platform.model.enums.WebParamModuleEnum;
import com.medusa.gruul.addon.platform.mp.entity.WebParamConfig;
import com.medusa.gruul.addon.platform.mp.service.IWebParamConfigService;
import com.medusa.gruul.addon.platform.service.PlatformConfigService;
import com.medusa.gruul.addon.platform.util.PlatformUtil;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.addon.supporter.helper.AddonSupporterHelper;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformConfigServiceImpl implements PlatformConfigService {

  private final IWebParamConfigService webParamConfigService;
  public static final String EMPTY_KEY="EMPTY_KEY";

  // 注入WechatServicePreloader用于缓存刷新
  @Autowired(required = false)
  private com.medusa.gruul.common.wechat.WechatServicePreloader wechatServicePreloader;

  /**
   * 保存或者更新配置
   * @param configMap 配置Map
   */
  @Override
  public void saveOrUpdateConfig(Map<WebParamKeyEnum, String> configMap) {
    Set<WebParamModuleEnum> moduleEnums = configMap.keySet().stream()
        .map(WebParamKeyEnum::getModule)
        .collect(Collectors.toSet());
    //根据模块查询已经存在的key
    Map<WebParamKeyEnum, WebParamConfig> existConfigMap = webParamConfigService.lambdaQuery()
        .in(WebParamConfig::getModule, moduleEnums)
        .list()
        .stream().filter(config -> config.getParamKey() != null)
                                                                  .collect(Collectors.toMap(WebParamConfig::getParamKey, x -> x));
    //新增
    List<WebParamConfig> saveList = Lists.newArrayList();
    //修改
    List<WebParamConfig> updateList = Lists.newArrayList();
    configMap.forEach((key, value) -> {
      WebParamConfig webParamConfig = new WebParamConfig();
      webParamConfig.setParamKey(key);
      webParamConfig.setParamValue(value);
      webParamConfig.setModule(key.getModule());
      WebParamConfig dbParamConfig = existConfigMap.get(key);
      if (Objects.isNull(dbParamConfig)) {
        saveList.add(webParamConfig);
      } else {
        webParamConfig.setId(dbParamConfig.getId());

        updateList.add(webParamConfig);
      }
    });
    
    // 检查是否有微信相关配置更新
    boolean hasWechatConfigUpdate = configMap.keySet().stream()
        .anyMatch(this::isWechatRelatedConfig);
    
    if (CollectionUtil.isNotEmpty(saveList)) {
      RedisUtil.doubleDeletion(()->{
        webParamConfigService.saveBatch(saveList);
      },()->{
        //删除缓存
        Set<String> keySet = configMap.keySet().stream()
            .map(x -> PlatformUtil.paramConfigModuleKey(x.getModule(), ISystem.platformIdMust())).collect(Collectors.toSet());
        RedisUtil.delete(keySet);
      });

    }
    if (CollectionUtil.isNotEmpty(updateList)) {
     RedisUtil.doubleDeletion(()->{
       webParamConfigService.updateBatchById(updateList);
     },()->{
       //删除缓存
       Set<String> keySet = configMap.keySet().stream()
           .map(x -> PlatformUtil.paramConfigModuleKey(x.getModule(),ISystem.platformIdMust())).collect(Collectors.toSet());
       RedisUtil.delete(keySet);
     });
    }

    // 如果有微信相关配置更新，刷新WechatServicePreloader缓存
    if (hasWechatConfigUpdate) {
      refreshWechatServiceCache();
    }
  }

  /**
   * 判断是否为微信相关配置
   * @param key 配置key
   * @return 是否为微信相关配置
   */
  private boolean isWechatRelatedConfig(WebParamKeyEnum key) {
    return key == WebParamKeyEnum.WECHAT_APP_ID 
        || key == WebParamKeyEnum.WECHAT_APP_SEC 
        || key == WebParamKeyEnum.WECHAT_APP_DELIVER
        || key == WebParamKeyEnum.MP_APP_ID 
        || key == WebParamKeyEnum.MP_APP_SEC;
  }

  /**
   * 刷新微信服务缓存
   */
  private void refreshWechatServiceCache() {
    if (wechatServicePreloader != null) {
      try {
        Long platformId = ISystem.platformIdMust();
        log.info("刷新平台 {} 微信服务缓存", platformId);
        
        // 清除插件机制的内存缓存
        try {
          AddonSupporterHelper.clearAllAddonCache();
          log.debug("插件机制缓存已清除");
        } catch (Exception e) {
          log.error("清除插件机制缓存异常: {}", e.getMessage(), e);
        }
        
        // 清除WechatServicePreloader的服务实例缓存
        try {
          wechatServicePreloader.refreshPlatformServices(platformId);
          log.debug("微信服务实例缓存已清除");
        } catch (Exception e) {
          log.error("清除微信服务缓存异常: {}", e.getMessage(), e);
        }
        
        log.info("平台 {} 微信服务缓存刷新完成", platformId);
        
      } catch (Exception e) {
        log.error("刷新平台 {} 微信服务缓存异常: {}", ISystem.platformIdMust(), e.getMessage(), e);
      }
    } else {
      log.warn("WechatServicePreloader 未注入，跳过微信服务缓存刷新");
    }
  }


  @Override
  public Map<String, String> queryConfigParamByModuleList(List<WebParamModuleEnum> modules) {
    Map<String, String> result = Maps.newHashMap();
    modules.forEach(x -> {
      Map<String, String> map = queryParamByModule(x);
      if (CollectionUtil.isNotEmpty(map)) {
        result.putAll(map);
      }
    });
    return result;
  }

  @Override
  public Map<WebParamKeyEnum, String> queryConfigByKeys(List<WebParamKeyEnum> keys) {
    //对keys进行分组
    Map<WebParamModuleEnum, List<WebParamKeyEnum>> groupMap = keys.stream()
        .collect(Collectors.groupingBy(WebParamKeyEnum::getModule));
    Map<WebParamKeyEnum, String> result = Maps.newHashMap();
    groupMap.forEach((key, value) -> {
      Map<String, String> map = queryParamByModule(key);
      if (CollectionUtil.isNotEmpty(map)) {
        for (WebParamKeyEnum webParamKeyEnum : value) {
          result.put(webParamKeyEnum, map.get(webParamKeyEnum.name()));
        }
      }
    });
    return result;
  }

  /**
   * 根据模块获取配置Map
   * @param webParamModuleEnum
   * @return
   */
  private Map<String,String> queryParamByModule(WebParamModuleEnum webParamModuleEnum) {
    Map<String,String> cacheMap = RedisUtil.getCacheMap(Map.class, () -> {
          List<WebParamConfig> list = webParamConfigService.lambdaQuery()
              .select(WebParamConfig::getParamValue,WebParamConfig::getParamKey)
              .eq(WebParamConfig::getModule, webParamModuleEnum)
              .list();
          if (CollectionUtil.isEmpty(list)) {
            HashMap<String, String> emptyMap = new HashMap<>();
            emptyMap.put(EMPTY_KEY,StringUtils.EMPTY);
            return emptyMap;
          }
          Map<String, String> map = Maps.newHashMap();
          for (WebParamConfig webParamConfig : list) {
            map.put(webParamConfig.getParamKey().name(), webParamConfig.getParamValue());
          }
          return map;
        }
        , Duration.ofDays(CommonPool.NUMBER_ONE),
        PlatformUtil.paramConfigModuleKey(webParamModuleEnum,ISystem.platformIdMust()));
    if (CollectionUtil.isEmpty(cacheMap)||cacheMap.containsKey(EMPTY_KEY)) {
      return Maps.newHashMap();
    }

    return cacheMap;
  }
}
