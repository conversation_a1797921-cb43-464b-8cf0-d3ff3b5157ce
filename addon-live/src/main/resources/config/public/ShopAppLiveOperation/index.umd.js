(function(e,g){typeof exports=="object"&&typeof module<"u"?module.exports=g(require("vue"),require("@element-plus/icons-vue"),require("element-plus")):typeof define=="function"&&define.amd?define(["vue","@element-plus/icons-vue","element-plus"],g):(e=typeof globalThis<"u"?globalThis:e||self,e.ShopAppLiveOperation=g(e.ShopAppLiveOperationContext.Vue,e.ShopAppLiveOperationContext.ElementPlusIconsVue,e.ShopAppLiveOperationContext.ElementPlus))})(this,function(e,g,C){"use strict";var h=document.createElement("style");h.textContent=`@charset "UTF-8";.barrage[data-v-4b5646b7]{position:absolute;bottom:50px;left:15px}.barrage__notice[data-v-4b5646b7]{font-size:13px;color:#999;padding:0 10px;line-height:1.3}.barrage__main[data-v-4b5646b7]{height:300px;overflow:hidden;width:180px;position:relative;margin-top:15px;font-size:12px;padding-left:10px;padding-bottom:10px}.barrage__list[data-v-4b5646b7]{width:100%;height:100%;overflow-y:auto}.barrage__list[data-v-4b5646b7]::-webkit-scrollbar{width:2px}.barrage__list--main[data-v-4b5646b7]{line-height:20px;margin-top:10px;padding:8px;border-radius:10px;background-color:#0000004d;color:#fff}.barrage__list--main .nickname[data-v-4b5646b7]{color:#f90}.commodity[data-v-7f045ce2]{position:absolute;bottom:60px;right:10px;width:100px;background-color:#ffffffb3;overflow:hidden;border-radius:10px}.commodity>img[data-v-7f045ce2]{width:100px;height:100px}.commodity__title[data-v-7f045ce2]{font-size:13px;margin-top:12px;line-height:18px;padding:0 5px}.commodity__price[data-v-7f045ce2]{color:red}.commodity__price .currency[data-v-7f045ce2]{font-size:16px}.commodity__btn[data-v-7f045ce2]{text-align:center;margin-top:5px;margin-bottom:10px}.live[data-v-a1dc2885]{border:1px solid #efefef;padding:10px;position:relative;height:100%;background-color:#666}.live__room[data-v-a1dc2885]{position:absolute;top:15px;left:15px;background-color:#0000004d;border-radius:15px;display:flex;padding:10px}.live__room--avatar[data-v-a1dc2885]{width:50px;height:50px;border-radius:50%;flex-shrink:0;margin-right:10px}.live__room--info[data-v-a1dc2885]{display:flex;height:50px;flex-direction:column;justify-content:space-around}.live__form[data-v-a1dc2885]{position:absolute;left:0;bottom:0;right:0;height:50px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-top:1px solid #efefef}.live__form .icon[data-v-a1dc2885]{font-size:30px}[data-v-a1dc2885] .el-form--inline .el-form-item{margin-right:0;margin-bottom:0}[data-v-a1dc2885] .el-form-item+.el-form-item{margin-left:5px}.audience[data-v-4a2e5fb1]{border:1px solid #efefef;padding:10px}.audience__title[data-v-4a2e5fb1]{font-size:20px;line-height:30px;font-weight:600;text-align:center}.audience .el-form--inline .el-form-item[data-v-4a2e5fb1]{margin-right:15px;margin-bottom:0}.audience .el-form[data-v-4a2e5fb1]{padding:15px 0;border-bottom:1px solid #efefef}.audience[data-v-4a2e5fb1] .el-checkbox-group{font-size:14px}.audience__checkbox[data-v-4a2e5fb1]{display:flex;width:100%;justify-content:space-between;align-items:center}.audience__checkbox[data-v-4a2e5fb1] .el-checkbox__label{display:none}.audience__checkbox span[data-v-4a2e5fb1]{flex:1;color:#000;margin-left:8px}.audience__checkbox--icons .el-icon[data-v-4a2e5fb1]{color:#000;font-size:18px}.audience__checkbox--icons .el-icon+.el-icon[data-v-4a2e5fb1]{margin-left:5px}.audience__bottom[data-v-4a2e5fb1]{display:flex;justify-content:space-between;align-items:center;border-top:1px solid #efefef;padding:10px 0 0}.table-info[data-v-89650a4e]{display:flex;overflow:hidden}.table-info img[data-v-89650a4e]{width:70px;height:70px;border-radius:10px}.table-info__content[data-v-89650a4e]{display:flex;flex-direction:column;justify-content:space-between;margin-left:12px}.table-info__content .title[data-v-89650a4e]{text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical;font-size:14px;line-height:20px}.table-info__content .price[data-v-89650a4e]{color:red;font-weight:600}.table-info__content .price .currency[data-v-89650a4e]{font-size:1.2em}.pagination[data-v-89650a4e]{text-align:right;padding-top:15px;display:flex;justify-content:flex-end}.commodity[data-v-d1342d33]{border:1px solid #efefef;padding:10px}.commodity__title[data-v-d1342d33]{font-size:20px;line-height:30px;font-weight:600;text-align:center}.commodity .el-form--inline .el-form-item[data-v-d1342d33]{margin-right:15px;margin-bottom:0}.commodity .el-form[data-v-d1342d33],.commodity .commodity-list[data-v-d1342d33]{padding:15px 0;border-bottom:1px solid #efefef}.commodity-list__container[data-v-d1342d33]{display:flex;overflow:hidden}.commodity-list__container--img[data-v-d1342d33]{width:70px;height:70px;position:relative}.commodity-list__container--img .count[data-v-d1342d33]{width:60px;position:absolute;bottom:10px;left:5px;right:5px;background-color:#fff9;height:22px;line-height:22px;text-align:center;color:red;border-radius:10px}.commodity-list__container img[data-v-d1342d33]{width:70px;height:70px;flex-shrink:0;border-radius:10px}.commodity-list__container--info[data-v-d1342d33]{margin-left:8px;flex:1;display:flex;flex-direction:column;justify-content:space-between}.commodity-list__container--info .title[data-v-d1342d33]{text-overflow:-o-ellipsis-lastline;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;line-clamp:2;-webkit-box-orient:vertical;font-size:14px;line-height:20px}.commodity-list__container--info .price[data-v-d1342d33]{color:red;font-weight:600}.commodity-list__container--info .price .currency[data-v-d1342d33]{font-size:1.2em}.commodity-list__operation[data-v-d1342d33]{margin-top:15px;text-align:right}.commodity .commodity-add[data-v-d1342d33]{padding:15px}.commodity .commodity-add button[data-v-d1342d33]{width:100%}.el-row[data-v-205a65a7]{height:600px}
`,document.head.appendChild(h);const k={class:"barrage"},u={class:"barrage__main"},E=e.defineComponent({__name:"barrage",setup(f,{expose:n}){const d=e.ref([]),a=e.ref(null);return n({addBarrage:r=>{const t={content:r};d.value.push(t),e.nextTick(()=>{a.value&&(a.value.scrollTop=a.value.scrollHeight)})}}),(r,t)=>(e.openBlock(),e.createElementBlock("div",k,[t[1]||(t[1]=e.createElementVNode("div",{class:"barrage__notice"}," 直播间严禁出现危害国家安全，破坏政治和社会稳定的言论，严禁出现低俗色情、售假售劣、售卖违禁品，引导私下交易等行为，不要在直播中从事挂机、录播、盗播等行为。若违反，平台有权依据规则对您采取相关管控。 ",-1)),e.createElementVNode("div",u,[e.createElementVNode("div",{ref_key:"barrageListRef",ref:a,class:"barrage__list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(d.value,(i,_)=>(e.openBlock(),e.createElementBlock("div",{key:_,class:"barrage__list--main"},[t[0]||(t[0]=e.createElementVNode("span",{class:"nickname"},"取个名字好难：",-1)),e.createElementVNode("span",null,e.toDisplayString(i.content),1)]))),128))],512)])]))}}),ce="",V=(f,n)=>{const d=f.__vccOpts||f;for(const[a,l]of n)d[a]=l;return d},$=V(E,[["__scopeId","data-v-4b5646b7"]]),pe="",B={},T={class:"commodity"},z={class:"commodity__btn"};function S(f,n){const d=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock("div",T,[n[1]||(n[1]=e.createElementVNode("img",{src:""},null,-1)),n[2]||(n[2]=e.createElementVNode("div",{class:"commodity__title"},"商品名称",-1)),n[3]||(n[3]=e.createElementVNode("div",{class:"commodity__price"},[e.createElementVNode("span",{class:"currency"},"￥"),e.createElementVNode("span",null,"5.78")],-1)),e.createElementVNode("div",z,[e.createVNode(d,{type:"primary"},{default:e.withCtx(()=>n[0]||(n[0]=[e.createTextVNode("讲解中")])),_:1})])])}const A=V(B,[["render",S],["__scopeId","data-v-7f045ce2"]]),L={class:"live"},U={class:"live__form"},I=e.defineComponent({__name:"index",setup(f){const n=e.reactive({comments:""}),d=e.ref(null),a=()=>{if(d.value){if(!n.comments)return C.ElMessage.error("请输入消息");d.value.addBarrage(n.comments),n.comments=""}};return(l,r)=>{const t=e.resolveComponent("el-input"),i=e.resolveComponent("el-form-item"),_=e.resolveComponent("el-icon"),s=e.resolveComponent("el-button"),b=e.resolveComponent("el-form");return e.openBlock(),e.createElementBlock("div",L,[r[2]||(r[2]=e.createElementVNode("div",{class:"live__room"},[e.createElementVNode("img",{class:"live__room--avatar",src:""}),e.createElementVNode("div",{class:"live__room--info"},[e.createElementVNode("span",null,"直播间名称"),e.createElementVNode("span",null,"400 观看")])],-1)),e.createVNode($,{ref_key:"barrageRef",ref:d},null,512),e.createVNode(A),e.createElementVNode("div",U,[e.createVNode(b,{model:n,"show-message":!1,inline:""},{default:e.withCtx(()=>[e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(t,{modelValue:n.comments,"onUpdate:modelValue":r[0]||(r[0]=m=>n.comments=m)},null,8,["modelValue"])]),_:1}),e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(_,{class:"icon"},{default:e.withCtx(()=>[e.createVNode(e.unref(g.InfoFilled))]),_:1})]),_:1}),e.createVNode(i,null,{default:e.withCtx(()=>[e.createVNode(s,{type:"primary",onClick:a},{default:e.withCtx(()=>r[1]||(r[1]=[e.createTextVNode("发送")])),_:1})]),_:1})]),_:1},8,["model"])])])}}}),me="",O=V(I,[["__scopeId","data-v-a1dc2885"]]),j={class:"audience"},D={class:"audience__checkbox--icons"},F={class:"audience__bottom"},M={class:"dialog-footer"},P=e.defineComponent({__name:"index",setup(f){const n=e.ref(!1),d=e.reactive({keyword:""}),a=e.ref([]),l=e.ref([]),r=e.ref(!1),t=e.ref(!1),i=m=>{l.value=m?new Array(10).fill(0).map((o,c)=>(c+1).toString()):[],t.value=!1},_=m=>{const o=m.length;r.value=o===10,t.value=o>0&&o<10},s=m=>{a.value=m,n.value=!0},b=m=>{C.ElMessageBox.confirm("请确认是否对用户禁言？？？").then(async()=>{})};return(m,o)=>{const c=e.resolveComponent("el-input"),y=e.resolveComponent("el-form-item"),x=e.resolveComponent("el-button"),le=e.resolveComponent("el-form"),w=e.resolveComponent("el-checkbox"),N=e.resolveComponent("el-icon"),de=e.resolveComponent("el-checkbox-group"),ie=e.resolveComponent("el-button-group"),re=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",j,[o[13]||(o[13]=e.createElementVNode("div",{class:"audience__title"},"观众",-1)),e.createVNode(le,{model:d,"show-message":!1,inline:""},{default:e.withCtx(()=>[e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(c,{modelValue:d.keyword,"onUpdate:modelValue":o[0]||(o[0]=p=>d.keyword=p),placeholder:"请输入关键词"},null,8,["modelValue"])]),_:1}),e.createVNode(y,null,{default:e.withCtx(()=>[e.createVNode(x,{type:"primary"},{default:e.withCtx(()=>o[8]||(o[8]=[e.createTextVNode("搜索")])),_:1})]),_:1})]),_:1},8,["model"]),e.createVNode(de,{modelValue:l.value,"onUpdate:modelValue":o[1]||(o[1]=p=>l.value=p),onChange:_},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(10,p=>e.createElementVNode("div",{key:p,class:"audience__checkbox"},[e.createVNode(w,{label:p.toString()},null,8,["label"]),o[9]||(o[9]=e.createElementVNode("span",null,"用户昵称",-1)),e.createElementVNode("div",D,[e.createVNode(N,null,{default:e.withCtx(()=>[e.createVNode(e.unref(g.Microphone))]),_:1}),e.createVNode(N,{onClick:se=>b([p.toString()])},{default:e.withCtx(()=>[e.createVNode(e.unref(g.Mute))]),_:2},1032,["onClick"]),e.createVNode(N,{onClick:se=>s([p.toString()])},{default:e.withCtx(()=>[e.createVNode(e.unref(g.Wallet))]),_:2},1032,["onClick"])])])),64))]),_:1},8,["modelValue"]),e.createElementVNode("div",F,[e.createVNode(w,{modelValue:r.value,"onUpdate:modelValue":o[2]||(o[2]=p=>r.value=p),indeterminate:t.value,onChange:i},{default:e.withCtx(()=>o[10]||(o[10]=[e.createTextVNode("全选")])),_:1},8,["modelValue","indeterminate"]),e.createVNode(ie,null,{default:e.withCtx(()=>[e.createVNode(x,{type:"primary",onClick:o[3]||(o[3]=p=>b(l.value))},{default:e.withCtx(()=>o[11]||(o[11]=[e.createTextVNode("批量禁言")])),_:1}),e.createVNode(x,{type:"primary",onClick:o[4]||(o[4]=p=>s(l.value))},{default:e.withCtx(()=>o[12]||(o[12]=[e.createTextVNode("批量送优惠券")])),_:1})]),_:1})])]),e.createVNode(re,{modelValue:n.value,"onUpdate:modelValue":o[7]||(o[7]=p=>n.value=p),"close-on-click-modal":!1,"destroy-on-close":"",title:"送优惠券",width:"850px"},{footer:e.withCtx(()=>[e.createElementVNode("span",M,[e.createVNode(x,{onClick:o[5]||(o[5]=p=>n.value=!1)},{default:e.withCtx(()=>o[14]||(o[14]=[e.createTextVNode("取消")])),_:1}),e.createVNode(x,{type:"primary",onClick:o[6]||(o[6]=p=>n.value=!1)},{default:e.withCtx(()=>o[15]||(o[15]=[e.createTextVNode("确认")])),_:1})])]),default:e.withCtx(()=>[o[16]||(o[16]=e.createElementVNode("div",null,null,-1))]),_:1},8,["modelValue"])],64)}}}),_e="",fe="",R=V(P,[["__scopeId","data-v-4a2e5fb1"]]),q=e.defineComponent({__name:"modify-sku",setup(f){const n=e.ref([{name:"规格1",price:100,salePrice:""}]);return(d,a)=>{const l=e.resolveComponent("el-table-column"),r=e.resolveComponent("el-input"),t=e.resolveComponent("el-table");return e.openBlock(),e.createBlock(t,{data:n.value},{default:e.withCtx(()=>[e.createVNode(l,{prop:"name",label:"规格名称"}),e.createVNode(l,{prop:"price",label:"指导价"}),e.createVNode(l,{prop:"salePrice",label:"销售价"},{default:e.withCtx(({row:i})=>[e.createVNode(r,{modelValue:i.salePrice,"onUpdate:modelValue":_=>i.salePrice=_,placeholder:"请输入销售价"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])}}}),H={class:"table-info"},W={class:"table-info__content"},G={class:"title"},J={class:"pagination"},K=e.defineComponent({__name:"add-commodity",setup(f){const n=e.reactive({keyword:""}),d=e.ref([{id:1},{id:2}]),a=e.reactive({page:1,pageSize:10,total:0}),l=t=>{a.pageSize=t},r=t=>{a.page=t};return(t,i)=>{const _=e.resolveComponent("el-input"),s=e.resolveComponent("el-form-item"),b=e.resolveComponent("el-button"),m=e.resolveComponent("el-form"),o=e.resolveComponent("el-table-column"),c=e.resolveComponent("el-table"),y=e.resolveComponent("el-pagination");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(m,{model:n,inline:""},{default:e.withCtx(()=>[e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(_,{modelValue:n.keyword,"onUpdate:modelValue":i[0]||(i[0]=x=>n.keyword=x),placeholder:"请输入关键词"},null,8,["modelValue"])]),_:1}),e.createVNode(s,null,{default:e.withCtx(()=>[e.createVNode(b,{type:"primary"},{default:e.withCtx(()=>i[1]||(i[1]=[e.createTextVNode("搜索")])),_:1})]),_:1})]),_:1},8,["model"]),e.createVNode(c,{data:d.value},{default:e.withCtx(()=>[e.createVNode(o,{fixed:"left",type:"selection",width:"55"}),e.createVNode(o,{label:"商品名称"},{default:e.withCtx(({row:x})=>[e.createElementVNode("div",H,[i[3]||(i[3]=e.createElementVNode("img",{src:""},null,-1)),e.createElementVNode("div",W,[e.createElementVNode("span",G,e.toDisplayString(x.name||"商品民称商品民称商品民称商品民称商品民称商品民称"),1),i[2]||(i[2]=e.createElementVNode("span",{class:"price"},[e.createElementVNode("span",{class:"currency"},"￥"),e.createElementVNode("span",null,"200.09- 210.00")],-1))])])]),_:1}),e.createVNode(o,{label:"库存",prop:"count",width:"120"})]),_:1},8,["data"]),e.createElementVNode("div",J,[e.createVNode(y,{"current-page":a.page,"page-size":a.pageSize,"page-sizes":[10,20,30,40],total:a.total,layout:"total, prev, pager, next, sizes",onSizeChange:l,onCurrentChange:r},null,8,["current-page","page-size","total"])])],64)}}}),xe="",Q=V(K,[["__scopeId","data-v-89650a4e"]]),X={class:"commodity"},Y={class:"commodity-list"},Z={class:"commodity-list__operation"},v={class:"commodity-add"},ee={class:"dialog-footer"},te={class:"dialog-footer"},oe=e.defineComponent({__name:"index",setup(f){const n=e.reactive({keyword:""}),d=r=>{C.ElMessageBox.confirm("请确认是否从直播商品中移出？？？")},a=e.ref(!1),l=e.ref(!1);return(r,t)=>{const i=e.resolveComponent("el-input"),_=e.resolveComponent("el-form-item"),s=e.resolveComponent("el-button"),b=e.resolveComponent("el-form"),m=e.resolveComponent("el-button-group"),o=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("div",X,[t[15]||(t[15]=e.createElementVNode("div",{class:"commodity__title"},"直播宝贝",-1)),e.createVNode(b,{model:n,"show-message":!1,inline:""},{default:e.withCtx(()=>[e.createVNode(_,null,{default:e.withCtx(()=>[e.createVNode(i,{modelValue:n.keyword,"onUpdate:modelValue":t[0]||(t[0]=c=>n.keyword=c),placeholder:"请输入关键词"},null,8,["modelValue"])]),_:1}),e.createVNode(_,null,{default:e.withCtx(()=>[e.createVNode(s,{type:"primary"},{default:e.withCtx(()=>t[9]||(t[9]=[e.createTextVNode("搜索")])),_:1})]),_:1})]),_:1},8,["model"]),e.createElementVNode("div",Y,[t[13]||(t[13]=e.createStaticVNode('<div class="commodity-list__container" data-v-d1342d33><div class="commodity-list__container--img" data-v-d1342d33><img src="" data-v-d1342d33><div class="count" data-v-d1342d33>500</div></div><div class="commodity-list__container--info" data-v-d1342d33><div class="title" data-v-d1342d33>商品名称商品名称商品名称商品商品商品商品商品商品商品名称称称...</div><div class="price" data-v-d1342d33><span class="currency" data-v-d1342d33>￥</span><span data-v-d1342d33>5.80</span></div></div></div>',1)),e.createElementVNode("div",Z,[e.createVNode(m,null,{default:e.withCtx(()=>[e.createVNode(s,{type:"primary",onClick:t[1]||(t[1]=c=>a.value=!0)},{default:e.withCtx(()=>t[10]||(t[10]=[e.createTextVNode("改价")])),_:1}),e.createVNode(s,{type:"danger",onClick:d},{default:e.withCtx(()=>t[11]||(t[11]=[e.createTextVNode("移出")])),_:1}),e.createVNode(s,{type:"warning"},{default:e.withCtx(()=>t[12]||(t[12]=[e.createTextVNode("讲解")])),_:1})]),_:1})])]),e.createElementVNode("div",v,[e.createVNode(s,{type:"primary",onClick:t[2]||(t[2]=c=>l.value=!0)},{default:e.withCtx(()=>t[14]||(t[14]=[e.createTextVNode("添加商品")])),_:1})])]),e.createVNode(o,{modelValue:a.value,"onUpdate:modelValue":t[5]||(t[5]=c=>a.value=c),"close-on-click-modal":!1,"destroy-on-close":"",title:"改价格"},{footer:e.withCtx(()=>[e.createElementVNode("span",ee,[e.createVNode(s,{onClick:t[3]||(t[3]=c=>a.value=!1)},{default:e.withCtx(()=>t[16]||(t[16]=[e.createTextVNode("取消")])),_:1}),e.createVNode(s,{type:"primary",onClick:t[4]||(t[4]=c=>a.value=!1)},{default:e.withCtx(()=>t[17]||(t[17]=[e.createTextVNode("确认")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(q)]),_:1},8,["modelValue"]),e.createVNode(o,{modelValue:l.value,"onUpdate:modelValue":t[8]||(t[8]=c=>l.value=c),"close-on-click-modal":!1,"destroy-on-close":"",title:"选择商品"},{footer:e.withCtx(()=>[e.createElementVNode("span",te,[e.createVNode(s,{onClick:t[6]||(t[6]=c=>l.value=!1)},{default:e.withCtx(()=>t[18]||(t[18]=[e.createTextVNode("取消")])),_:1}),e.createVNode(s,{type:"primary",onClick:t[7]||(t[7]=c=>l.value=!1)},{default:e.withCtx(()=>t[19]||(t[19]=[e.createTextVNode("确认")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(Q)]),_:1},8,["modelValue"])],64)}}}),ge="",ne=V(oe,[["__scopeId","data-v-d1342d33"]]),ae=e.defineComponent({__name:"ShopAppLiveOperation",setup(f){return(n,d)=>{const a=e.resolveComponent("el-col"),l=e.resolveComponent("el-row");return e.openBlock(),e.createBlock(l,{gutter:8},{default:e.withCtx(()=>[e.createVNode(a,{span:8},{default:e.withCtx(()=>[e.createVNode(O)]),_:1}),e.createVNode(a,{span:8},{default:e.withCtx(()=>[e.createVNode(R)]),_:1}),e.createVNode(a,{span:8},{default:e.withCtx(()=>[e.createVNode(ne)]),_:1})]),_:1})}}}),be="";return V(ae,[["__scopeId","data-v-205a65a7"]])});
