(function(e,T){typeof exports=="object"&&typeof module<"u"?module.exports=T(require("vue"),require("@/components/chrome-tab/index.vue"),require("lodash-es"),require("@/components/SchemaForm.vue"),require("@/components/qszr-core/packages/q-table/QTable"),require("@/components/qszr-core/packages/q-table/q-table-column.vue"),require("element-plus"),require("@/components/BetterPageManage/BetterPageManage.vue"),require("@/apis/http"),require("@/components/q-upload/q-upload.vue"),require("@element-plus/icons-vue")):typeof define=="function"&&define.amd?define(["vue","@/components/chrome-tab/index.vue","lodash-es","@/components/SchemaForm.vue","@/components/qszr-core/packages/q-table/QTable","@/components/qszr-core/packages/q-table/q-table-column.vue","element-plus","@/components/BetterPageManage/BetterPageManage.vue","@/apis/http","@/components/q-upload/q-upload.vue","@element-plus/icons-vue"],T):(e=typeof globalThis<"u"?globalThis:e||self,e.PlatformAppLiveList=T(e.PlatformAppLiveListContext.Vue,e.PlatformAppLiveListContext.ChromeTab,e.PlatformAppLiveListContext.Lodash,e.PlatformAppLiveListContext.SchemaForm,e.PlatformAppLiveListContext.QTable,e.PlatformAppLiveListContext.QTableColumn,e.PlatformAppLiveListContext.ElementPlus,e.PlatformAppLiveListContext.PageManage,e.PlatformAppLiveListContext.Request,e.PlatformAppLiveListContext.QUpload,e.PlatformAppLiveListContext.ElementPlusIconsVue))})(this,function(e,T,D,$,q,V,N,z,I,M,G){"use strict";var F=document.createElement("style");F.textContent=`@charset "UTF-8";.el-row[data-v-5bf44f34]{margin-bottom:8px}.el-row img+img[data-v-5bf44f34]{margin-left:10px;width:50px;height:50px;vertical-align:top;object-fit:cover}[data-v-3dbee6cf]{font-size:14px}.table[data-v-3dbee6cf]{overflow:auto}.table-info[data-v-3dbee6cf]{display:flex;align-items:center;width:350px;overflow:hidden}.table-info img[data-v-3dbee6cf]{width:56px;height:56px;flex-shrink:0}.table-info__wrapper[data-v-3dbee6cf]{margin-left:15px;overflow:hidden;flex:1}.table-info__wrapper--name[data-v-3dbee6cf]{display:flex;align-items:center}.table-info__wrapper--name .label[data-v-3dbee6cf]{display:inline-block;line-height:22px;padding:0 3px;border-radius:4px;color:#fff;margin-right:5px;font-size:12px}.table-info__wrapper--name .label.in-live[data-v-3dbee6cf]{background-color:#555cfd}.table-info__wrapper--name .label.foretell[data-v-3dbee6cf]{background-color:#00bb2c}.table-info__wrapper--name .label.finish[data-v-3dbee6cf]{background-color:#c7c7c7}.table-info__wrapper--name .label.violation[data-v-3dbee6cf]{background-color:#f54319}.table-performance[data-v-3dbee6cf]{width:100%;line-height:22px}.ellipsis[data-v-3dbee6cf]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ellipsis2[data-v-3dbee6cf]{width:270px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}.el-link+.el-link[data-v-3dbee6cf]{margin-left:5px}.table[data-v-7b4c89cb]{overflow:auto}[data-v-7b4c89cb]{font-size:14px}.table-info[data-v-7b4c89cb]{display:flex;width:350px;overflow:hidden;padding:8px 0}.table-info img[data-v-7b4c89cb]{width:40px;height:40px;flex-shrink:0}.table-info__wrapper[data-v-7b4c89cb]{margin-left:15px;overflow:hidden;height:40px;display:flex;flex-direction:column;justify-content:space-around}.table-info__wrapper--name span+span[data-v-7b4c89cb]{margin-left:5px}.table-performance[data-v-7b4c89cb]{width:100%;line-height:22px}.ellipsis[data-v-7b4c89cb]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ellipsis2[data-v-7b4c89cb]{width:349px;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden}
`,document.head.appendChild(F);const Y={class:"q_plugin_container"},W=e.defineComponent({__name:"PlatformAppLiveList",setup(b){const i=e.ref("room"),o={room:e.defineAsyncComponent(()=>Promise.resolve().then(()=>he)),anchor:e.defineAsyncComponent(()=>Promise.resolve().then(()=>Se))},n=[{label:"直播间",name:"room"},{label:"主播管理",name:"anchor"}],s=c=>{c&&(i.value=c)};return(c,_)=>(e.openBlock(),e.createElementBlock("div",Y,[e.createVNode(T,{"tab-list":n,value:i.value,onHandleTabs:s},null,8,["value"]),(e.openBlock(),e.createBlock(e.resolveDynamicComponent(o[i.value])))]))}}),J=e.defineComponent({__name:"room-search",emits:["onSearchParams"],setup(b,{emit:i}){const o=e.reactive({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:"",shopName:""}),n=[{label:"直播主题",prop:"liveTitle",valueType:"copy",fieldProps:{placeholder:"请输入直播主题"}},{label:"主播昵称",prop:"anchorNickname",valueType:"copy",fieldProps:{placeholder:"请输入主播昵称"}},{label:"直播时间",prop:"createTime",valueType:"date-picker",fieldProps:{type:"daterange",startPlaceholder:"开始时间",endPlaceholder:"结束时间",valueFormat:"YYYY-MM-DD"}},{label:"直播ID",prop:"liveId",valueType:"copy",fieldProps:{placeholder:"请输入直播ID"}},{label:"手机号",prop:"phone",valueType:"copy",fieldProps:{placeholder:"请输入手机号"}},{label:"店铺名称",prop:"shopName",valueType:"copy",fieldProps:{placeholder:"请输入店铺名称"}}],s=i,c=()=>{const d=D.cloneDeep(o);s("onSearchParams",d)},_=()=>{Object.keys(o).forEach(d=>o[d]=""),c()};return(d,g)=>(e.openBlock(),e.createElementBlock("div",null,[e.createVNode($,{modelValue:o,"onUpdate:modelValue":g[0]||(g[0]=p=>o=p),columns:n,onSearchHandle:c,onHandleReset:_},null,8,["modelValue"])]))}}),K=b=>I.get({url:"addon-live/manager/live/list",params:b}),X=(b,i)=>I.get({url:`addon-live/manager/ban/reason/${b}/${i}`}),Z=b=>I.get({url:"addon-live/manager/anchor/list",params:b}),R=b=>I.put({url:"addon-live/manager/platform/update/anchor",data:b}),Q={class:"reason"},v=["src"],ee=e.defineComponent({__name:"violation-reason",props:{id:{type:String,default:""},type:{type:String,default:""}},setup(b){const i={YELLOW_INVOLVEMENT:"涉黄",DRUG_RELATED:"涉毒",SENSITIVE_TOPIC:"敏感话题",OTHER:"其它"},o=b,n=e.ref({qualityInspector:"",sourceId:"",shopId:"",type:"",categoryTypes:"",reason:"",relevantEvidence:"",createTime:""});e.watch(o,c=>{s()}),s();async function s(){const{code:c,data:_}=await X(o.id,o.type);if(c!==200)return N.ElMessage.error("获取直播间列表失败");_.relevantEvidence=_.relevantEvidence.split(","),_.categoryTypes=_.categoryTypes.map(d=>i[d]).join(","),n.value=_}return(c,_)=>{const d=e.resolveComponent("el-col"),g=e.resolveComponent("el-row");return e.openBlock(),e.createElementBlock("div",Q,[e.createVNode(g,{gutter:8},{default:e.withCtx(()=>[e.createVNode(d,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查员："+e.toDisplayString(n.value.qualityInspector),1)]),_:1}),e.createVNode(d,{span:12},{default:e.withCtx(()=>[e.createTextVNode("检查时间："+e.toDisplayString(n.value.createTime),1)]),_:1})]),_:1}),e.createVNode(g,{gutter:8},{default:e.withCtx(()=>[e.createVNode(d,{span:24},{default:e.withCtx(()=>[e.createTextVNode("类型："+e.toDisplayString(n.value.categoryTypes),1)]),_:1})]),_:1}),e.createVNode(g,{gutter:8},{default:e.withCtx(()=>[e.createVNode(d,{span:24},{default:e.withCtx(()=>[e.createTextVNode("原因："+e.toDisplayString(n.value.reason),1)]),_:1})]),_:1}),e.createVNode(g,{gutter:8},{default:e.withCtx(()=>[_[0]||(_[0]=e.createTextVNode(" 相关证据： ")),e.createVNode(d,{span:24},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value.relevantEvidence,p=>(e.openBlock(),e.createElementBlock("img",{key:p,src:p,style:{width:"60px",height:"60px"}},null,8,v))),128))]),_:1})]),_:1})])}}}),Ie="",P=(b,i)=>{const o=b.__vccOpts||b;for(const[n,s]of i)o[n]=s;return o},H=P(ee,[["__scopeId","data-v-5bf44f34"]]),U=e.defineComponent({__name:"prohibitLive",setup(b,{expose:i}){const o=e.reactive({categoryTypes:[],reason:"",relevantEvidence:""}),n=e.ref([]),s={categoryTypes:[{required:!0,message:"请选择类型",trigger:"change"}],reason:[{required:!0,message:"请输入原因",trigger:"change"}],relevantEvidence:[{required:!0,message:"请选择证据",trigger:"change"}]},c=e.ref(null),_=()=>new Promise((p,m)=>{c.value?c.value.validate(f=>{f?p(o):m("valid error")}):m("form instance not found")}),d=p=>{n.value.splice(p,1),o.relevantEvidence=n.value.join(",")},g=(p,m)=>{n.value.push(p),o.relevantEvidence=n.value.join(",")};return i({getprohibitModel:_}),(p,m)=>{const f=e.resolveComponent("el-checkbox"),u=e.resolveComponent("el-checkbox-group"),k=e.resolveComponent("el-form-item"),L=e.resolveComponent("el-input"),B=e.resolveComponent("el-icon"),S=e.resolveComponent("el-form");return e.openBlock(),e.createBlock(S,{ref_key:"formRef",ref:c,model:o,rules:s},{default:e.withCtx(()=>[e.createVNode(k,{label:"类型(多选)",prop:"categoryTypes"},{default:e.withCtx(()=>[e.createVNode(u,{modelValue:o.categoryTypes,"onUpdate:modelValue":m[0]||(m[0]=y=>o.categoryTypes=y)},{default:e.withCtx(()=>[e.createVNode(f,{label:"YELLOW_INVOLVEMENT"},{default:e.withCtx(()=>m[2]||(m[2]=[e.createTextVNode("涉黄")])),_:1}),e.createVNode(f,{label:"DRUG_RELATED"},{default:e.withCtx(()=>m[3]||(m[3]=[e.createTextVNode("涉毒")])),_:1}),e.createVNode(f,{label:"SENSITIVE_TOPIC"},{default:e.withCtx(()=>m[4]||(m[4]=[e.createTextVNode("敏感话题")])),_:1}),e.createVNode(f,{label:"OTHER"},{default:e.withCtx(()=>m[5]||(m[5]=[e.createTextVNode("其它")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e.createVNode(k,{label:"原因",prop:"reason"},{default:e.withCtx(()=>[e.createVNode(L,{modelValue:o.reason,"onUpdate:modelValue":m[1]||(m[1]=y=>o.reason=y),placeholder:"20字以内"},null,8,["modelValue"])]),_:1}),e.createVNode(k,{label:"相关证据（最多5张图片）","label-width":"120px",prop:"relevantEvidence"},{default:e.withCtx(()=>[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,(y,C)=>(e.openBlock(),e.createElementBlock("div",{key:y,style:{position:"relative","margin-right":"20px"}},[e.createVNode(M,{src:n.value[C],"onUpdate:src":h=>n.value[C]=h,format:{size:1},height:100,width:100},null,8,["src","onUpdate:src"]),y?(e.openBlock(),e.createBlock(B,{key:0,color:"#7f7f7f",size:"20px",style:{position:"absolute",right:"-5px",top:"-5px",background:"#fff","border-radius":"50%"},onClick:h=>d(C)},{default:e.withCtx(()=>[e.createVNode(e.unref(G.CircleClose))]),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]))),128)),e.withDirectives(e.createVNode(M,{format:{size:1},height:100,width:100,onChange:g},null,512),[[e.vShow,n.value.length<=4]])]),_:1})]),_:1},8,["model"])}}}),te={class:"table-info"},oe=["src"],ae={class:"table-info__wrapper"},ne={class:"table-info__wrapper--name"},le={key:0,class:"label in-live"},re={key:1,class:"label foretell"},ie={key:2,class:"label finish"},se={key:3,class:"label violation"},ce={class:"ellipsis",style:{display:"inline-block",width:"180px","line-height":"14px"}},de={key:5,class:"ellipsis",style:{display:"inline-block",width:"180px","line-height":"14px"}},pe={class:"table-info__wrapper--description"},me={class:"ellipsis2"},fe={class:"dialog-footer"},_e=e.defineComponent({__name:"index",setup(b){const i=e.ref([]),o=e.ref(""),n=e.ref({liveTitle:"",anchorNickname:"",createTime:"",liveId:"",phone:"",shopName:"",beginTime:"",endTime:""}),s=e.reactive({size:10,current:1,total:0}),c=e.ref(!1),_=[{name:"",label:"全部"},{name:"PROCESSING",label:"直播中"},{name:"NOT_STARTED",label:"预告"},{name:"OVER",label:"已结束"},{name:"ILLEGAL_SELL_OFF",label:"违规下播"}],d=e.ref({id:""}),g=e.ref({id:""}),p=e.ref(!1),m=e.ref();f();async function f(){Array.isArray(n.value.createTime)&&(n.value.beginTime=n.value.createTime[0],n.value.endTime=n.value.createTime[1],n.value.createTime="");const h={...s,...n.value,status:o.value},{code:t,data:a}=await K(h);if(t!==200)return N.ElMessage.error("获取直播间列表失败");i.value=a.records,s.current=a.current,s.size=a.size,s.total=a.total}const u=h=>{s.size=h,f()},k=h=>{s.current=h,f()},L=h=>{n.value=h,s.current=1,f()},B=()=>{s.current=1,f()},S=h=>{c.value=!0,d.value.id=h},y=h=>{p.value=!0,g.value.id=h},C=async()=>{var x;const h=await((x=m.value)==null?void 0:x.getprohibitModel()),{code:t,data:a}=await R({...h,type:"LIVE",isEnable:!1,sourceId:g.value.id});if(t!==200)return N.ElMessage.error("违规下播失败");f(),p.value=!1};return(h,t)=>{const a=e.resolveComponent("el-tab-pane"),x=e.resolveComponent("el-tabs"),A=e.resolveComponent("el-tooltip"),w=e.resolveComponent("el-link"),O=e.resolveComponent("el-dialog"),E=e.resolveComponent("el-button");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(J,{onOnSearchParams:L}),e.createVNode(x,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=l=>o.value=l),class:"tab_container",onTabChange:B},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(_,l=>e.createVNode(a,{key:l.name,label:l.label,name:l.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createVNode(e.unref(q),{data:i.value,class:"table","no-border":""},{default:e.withCtx(()=>[e.createVNode(V,{label:"直播信息",align:"left",width:"370"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",te,[e.createElementVNode("img",{src:l.pic},null,8,oe),e.createElementVNode("div",ae,[e.createElementVNode("div",ne,[l.status==="PROCESSING"?(e.openBlock(),e.createElementBlock("span",le,"直播中")):l.status==="NOT_STARTED"?(e.openBlock(),e.createElementBlock("span",re,"预告")):l.status==="OVER"?(e.openBlock(),e.createElementBlock("span",ie,"已结束")):l.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createElementBlock("span",se,"违规下播")):e.createCommentVNode("",!0),l.liveTitle.length>=15?(e.openBlock(),e.createBlock(A,{key:4,class:"box-item",effect:"dark",content:l.liveTitle,placement:"top"},{default:e.withCtx(()=>[e.createElementVNode("span",ce,e.toDisplayString(l.liveTitle),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("span",de,e.toDisplayString(l.liveTitle),1))]),e.createElementVNode("div",pe,"直播ID："+e.toDisplayString(l.id),1)])])]),_:1}),e.createVNode(V,{align:"left",label:"主播简介",width:"310"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",me,e.toDisplayString(l.liveSynopsis),1)]),_:1}),e.createVNode(V,{align:"left",label:"主播昵称",width:"270"},{default:e.withCtx(({row:l})=>[e.createElementVNode("div",null,e.toDisplayString(l.anchor.anchorNickname)+"("+e.toDisplayString(l.anchor.phone)+")",1)]),_:1}),e.createVNode(V,{label:"操作",prop:"action",width:"150",fixed:"right"},{default:e.withCtx(({row:l})=>[l.status==="PROCESSING"?(e.openBlock(),e.createBlock(w,{key:0,type:"danger",onClick:r=>y(l.id)},{default:e.withCtx(()=>t[4]||(t[4]=[e.createTextVNode("违规下播")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),l.status==="ILLEGAL_SELL_OFF"?(e.openBlock(),e.createBlock(w,{key:1,type:"primary",onClick:r=>S(l.id)},{default:e.withCtx(()=>t[5]||(t[5]=[e.createTextVNode("违禁原因 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(z,{"page-num":s.current,"page-size":s.size,total:s.total,onHandleSizeChange:u,onHandleCurrentChange:k},null,8,["page-num","page-size","total"]),e.createVNode(O,{modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=l=>c.value=l),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode(H,{id:d.value.id,type:"LIVE"},null,8,["id"])]),_:1},8,["modelValue"]),e.createVNode(O,{modelValue:p.value,"onUpdate:modelValue":t[3]||(t[3]=l=>p.value=l),"close-on-click-modal":!1,"destroy-on-close":"",title:"违禁下播",width:"550px"},{footer:e.withCtx(()=>[e.createElementVNode("span",fe,[e.createVNode(E,{onClick:t[2]||(t[2]=l=>p.value=!1)},{default:e.withCtx(()=>t[6]||(t[6]=[e.createTextVNode("取消")])),_:1}),e.createVNode(E,{type:"primary",onClick:C},{default:e.withCtx(()=>t[7]||(t[7]=[e.createTextVNode("确认")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(U,{ref_key:"prohibitLiveRef",ref:m},null,512)]),_:1},8,["modelValue"]),t[8]||(t[8]=e.createElementVNode("text",null,null,-1))],64)}}}),Oe="",he=Object.freeze(Object.defineProperty({__proto__:null,default:P(_e,[["__scopeId","data-v-3dbee6cf"]])},Symbol.toStringTag,{value:"Module"})),be=e.defineComponent({__name:"anchor-search",emits:["onSearchParams"],setup(b,{emit:i}){const o=e.reactive({anchorNickname:"",id:"",phone:""}),n=[{label:"主播昵称",prop:"anchorNickname",valueType:"copy",fieldProps:{placeholder:"请输入主播昵称"}},{label:"直播ID",prop:"id",valueType:"copy",fieldProps:{placeholder:"请输入直播ID"}},{label:"手机号",prop:"phone",valueType:"copy",fieldProps:{placeholder:"请输入手机号"}}],s=i,c=()=>{const d=D.cloneDeep(o);s("onSearchParams",d)},_=()=>{Object.keys(o).forEach(d=>o[d]=""),c()};return(d,g)=>(e.openBlock(),e.createBlock($,{modelValue:o,"onUpdate:modelValue":g[0]||(g[0]=p=>o=p),columns:n,onSearchHandle:c,onHandleReset:_},null,8,["modelValue"]))}}),ge={class:"handle_container"},xe={class:"table-info"},ye=["src"],Ve={class:"table-info__wrapper"},ke={class:"table-info__wrapper--name"},Ce={class:"table-info__wrapper--description"},Ne={class:"table-info__wrapper--description ellipsis2"},we={key:1,class:"table-info__wrapper--description ellipsis2"},Ee={key:0,style:{color:"#00bb2c"}},Te={key:1,style:{color:"#999"}},ue={key:2,style:{color:"#f54319"}},Le={class:"dialog-footer"},Be=e.defineComponent({__name:"index",setup(b){const i=e.reactive({size:10,current:1,total:0}),o=e.ref(""),n=[{name:"",label:"全部"},{name:"NORMAL",label:"启用中"},{name:"FORBIDDEN",label:"已禁用"},{name:"VIOLATION",label:"违规禁播"}],s=e.ref([]),c=e.ref(!1),_=e.ref({id:""}),d=e.ref({id:""}),g=e.ref({id:"",anchorNickname:"",phone:""}),p=e.ref(!1),m=e.ref();f();async function f(){const t={...i,...g.value,status:o.value},{code:a,data:x}=await Z(t);if(a!==200)return N.ElMessage.error("获取直播间列表失败");s.value=x.records,i.current=x.current,i.size=x.size,i.total=x.total}const u=t=>{i.size=t,f()},k=t=>{i.current=t,f()},L=t=>{g.value=t,i.current=1,f()},B=()=>{i.current=1,f()},S=t=>{c.value=!0,_.value.id=t},y=t=>{p.value=!0,d.value.id=t},C=async()=>{var x;const t=await((x=m.value)==null?void 0:x.getprohibitModel()),{code:a}=await R({...t,type:"ANCHOR",isEnable:!1,sourceId:d.value.id});if(a!==200)return N.ElMessage.error("禁播失败");f(),p.value=!1},h=async t=>{const{code:a,data:x}=await R({type:"ANCHOR",isEnable:!0,sourceId:t});if(a!==200)return N.ElMessage.error("恢复直播失败");f()};return(t,a)=>{const x=e.resolveComponent("el-tab-pane"),A=e.resolveComponent("el-tabs"),w=e.resolveComponent("el-button"),O=e.resolveComponent("el-tooltip"),E=e.resolveComponent("el-link"),l=e.resolveComponent("el-dialog");return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createVNode(be,{onOnSearchParams:L}),e.createVNode(A,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=r=>o.value=r),class:"tab_container",onTabChange:B},{default:e.withCtx(()=>[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(n,r=>e.createVNode(x,{key:r.name,label:r.label,name:r.name},null,8,["label","name"])),64))]),_:1},8,["modelValue"]),e.createElementVNode("div",ge,[e.createVNode(w,{type:"primary"},{default:e.withCtx(()=>a[4]||(a[4]=[e.createTextVNode("新增主播")])),_:1})]),e.createVNode(e.unref(q),{data:s.value,class:"table"},{default:e.withCtx(()=>[e.createVNode(V,{label:"主播信息",width:"410",align:"left"},{default:e.withCtx(({row:r})=>[e.createElementVNode("div",xe,[e.createElementVNode("img",{src:r.anchorIcon},null,8,ye),e.createElementVNode("div",Ve,[e.createElementVNode("div",ke,[e.createElementVNode("span",null,e.toDisplayString(r.anchorNickname),1),e.createElementVNode("span",null,"("+e.toDisplayString(r.phone)+")",1)]),e.createElementVNode("div",Ce,"主播ID："+e.toDisplayString(r.id),1)])])]),_:1}),e.createVNode(V,{align:"left",label:"主播简介",width:"420"},{default:e.withCtx(({row:r})=>[r.anchorSynopsis.length>=40?(e.openBlock(),e.createBlock(O,{key:0,class:"box-item",effect:"dark",content:r.anchorSynopsis,placement:"top"},{default:e.withCtx(()=>[e.createElementVNode("div",Ne,e.toDisplayString(r.anchorSynopsis),1)]),_:2},1032,["content"])):(e.openBlock(),e.createElementBlock("div",we,e.toDisplayString(r.anchorSynopsis),1))]),_:1}),e.createVNode(V,{align:"left",label:"状态",width:"120"},{default:e.withCtx(({row:r})=>[r.status==="NORMAL"?(e.openBlock(),e.createElementBlock("span",Ee,"启用中")):r.status==="FORBIDDEN"?(e.openBlock(),e.createElementBlock("span",Te,"已禁用")):r.status==="VIOLATION"?(e.openBlock(),e.createElementBlock("span",ue,"违规禁播")):e.createCommentVNode("",!0)]),_:1}),e.createVNode(V,{label:"操作",prop:"action",align:"right",fixed:"right",width:"165"},{default:e.withCtx(({row:r})=>[r.status==="VIOLATION"?(e.openBlock(),e.createBlock(E,{key:0,style:{"margin-right":"20px"},type:"primary",onClick:j=>h(r.id)},{default:e.withCtx(()=>a[5]||(a[5]=[e.createTextVNode("恢复直播 ")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),r.status==="VIOLATION"?(e.openBlock(),e.createBlock(E,{key:1,type:"primary",onClick:j=>S(r.id)},{default:e.withCtx(()=>a[6]||(a[6]=[e.createTextVNode("禁播原因")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0),r.status!=="VIOLATION"?(e.openBlock(),e.createBlock(E,{key:2,type:"danger",onClick:j=>y(r.id)},{default:e.withCtx(()=>a[7]||(a[7]=[e.createTextVNode("违规禁播")])),_:2},1032,["onClick"])):e.createCommentVNode("",!0)]),_:1})]),_:1},8,["data"]),e.createVNode(z,{"page-num":i.current,"page-size":i.size,total:i.total,onHandleSizeChange:u,onHandleCurrentChange:k},null,8,["page-num","page-size","total"]),e.createVNode(l,{modelValue:c.value,"onUpdate:modelValue":a[1]||(a[1]=r=>c.value=r),title:"违禁原因",width:"550px"},{default:e.withCtx(()=>[e.createVNode(H,{id:_.value.id,type:"ANCHOR"},null,8,["id"])]),_:1},8,["modelValue"]),e.createVNode(l,{modelValue:p.value,"onUpdate:modelValue":a[3]||(a[3]=r=>p.value=r),"close-on-click-modal":!1,"destroy-on-close":"",title:"违禁下播",width:"550px"},{footer:e.withCtx(()=>[e.createElementVNode("span",Le,[e.createVNode(w,{onClick:a[2]||(a[2]=r=>p.value=!1)},{default:e.withCtx(()=>a[8]||(a[8]=[e.createTextVNode("取消")])),_:1}),e.createVNode(w,{type:"primary",onClick:C},{default:e.withCtx(()=>a[9]||(a[9]=[e.createTextVNode("确认")])),_:1})])]),default:e.withCtx(()=>[e.createVNode(U,{ref_key:"prohibitLiveRef",ref:m},null,512)]),_:1},8,["modelValue"]),a[10]||(a[10]=e.createElementVNode("text",null,null,-1))],64)}}}),Pe="",Se=Object.freeze(Object.defineProperty({__proto__:null,default:P(Be,[["__scopeId","data-v-7b4c89cb"]])},Symbol.toStringTag,{value:"Module"}));return W});
