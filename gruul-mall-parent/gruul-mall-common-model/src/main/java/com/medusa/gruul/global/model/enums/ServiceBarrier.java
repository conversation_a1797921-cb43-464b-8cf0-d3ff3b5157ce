package com.medusa.gruul.global.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Description: 服务保障枚举
 * @Author: xiaoq
 * @Date : 2022-05-06 16:07
 */
@Getter
@RequiredArgsConstructor
public enum ServiceBarrier {

    /**
     * 全场包邮
     */
    NO_FREIGHT(1),
    /**
     * 7天退换
     */
    SEVEN_END_BACK(2),

    /**
     * 48小时发货
     */
    TWO_DAY_SEND(3),

    /**
     * 假一赔十
     */
    FAKE_COMPENSATE(4),

    /**
     * 正品保证
     */
    ALL_ENSURE(5),

    ;

    @EnumValue
    private final Integer value;

    /**
     * 根据value获取枚举值
     *
     * @param value 状态值
     * @return 对应的枚举值
     */
    public static ServiceBarrier getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ServiceBarrier serviceBarrier : values()) {
            if (serviceBarrier.getValue().equals(value)) {
                return serviceBarrier;
            }
        }
        return null;
    }
}
