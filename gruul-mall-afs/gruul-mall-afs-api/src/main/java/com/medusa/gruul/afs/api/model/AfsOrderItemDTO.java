package com.medusa.gruul.afs.api.model;
import com.medusa.gruul.common.model.enums.SellType;
import com.medusa.gruul.global.model.enums.ServiceBarrier;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class AfsOrderItemDTO implements Serializable {

    /**
     * 售后工单号
     */
    private String afsNo;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * sku规格
     */
    private List<String> specs;

    /**
     * sku图片
     */
    private String image;

    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 销售类型
     */
    private SellType sellType;

    /**
     * 商品保障服务
     */
    private List<ServiceBarrier> services;

    /**
     * 销售单价
     */
    private Long salePrice;

    /**
     * 成交价单价
     */
    private Long dealPrice;
    
    /**
     * 订单商品项ID
     */
    private Long orderItemId;

}
