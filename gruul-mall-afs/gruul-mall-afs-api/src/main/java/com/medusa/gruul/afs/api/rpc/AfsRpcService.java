package com.medusa.gruul.afs.api.rpc;

import com.medusa.gruul.afs.api.model.AfsCloseDTO;
import com.medusa.gruul.afs.api.model.AfsOrderItemDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Set;

/**
 * <p></p>
 *
 * <AUTHOR>
 * date 2022/8/16
 */
public interface AfsRpcService {

    /**
     * 买家操作订单导致的售后工单号关闭
     *
     * @param afsClose 售后工单号 与 关闭原因
     */
    void closeAfsByAfsNo(@Valid AfsCloseDTO afsClose);


    /**
     * 批量关闭售后
     *
     * @param afsCloses 关闭详情
     */
    void closeAfsBatch(@NotNull @Size(min = 1) List<AfsCloseDTO> afsCloses);
    
    /**
     * 根据售后工单号查询售后商品项列表
     *
     * @param afsNo 售后工单号
     * @return 售后商品项列表
     */
    List<AfsOrderItemDTO> getAfsOrderItemsByAfsNo(String afsNo);
    
    /**
     * 根据售后工单号列表批量查询售后商品项列表
     *
     * @param afsNos 售后工单号列表
     * @return 售后商品项列表
     */
    List<AfsOrderItemDTO> listAfsOrderItemsByAfsNos(List<String> afsNos);
}
