<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.afs.service.mp.mapper.RefundReasonTemplateMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.afs.service.mp.entity.RefundReasonTemplate">
        <id column="id" property="id" />
        <result column="platform_id" property="platformId" />
        <result column="template_type" property="templateType" />
        <result column="reason_text" property="reasonText" />
        <result column="scenario_refund_only" property="scenarioRefundOnly" />
        <result column="scenario_return_refund" property="scenarioReturnRefund" />
        <result column="is_enabled" property="isEnabled" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
        id, platform_id, template_type, reason_text, scenario_refund_only, 
        scenario_return_refund, is_enabled, version, deleted, create_time, update_time
    </sql>

    <!-- 场景条件SQL片段 -->
    <sql id="Scenario_Condition">
        <if test="scenarioRefundOnly != null and scenarioRefundOnly == true">
            AND scenario_refund_only = 1
        </if>
        <if test="scenarioReturnRefund != null and scenarioReturnRefund == true">
            AND scenario_return_refund = 1
        </if>
    </sql>

    <!-- 根据平台ID和场景查询启用的退款原因模板列表 -->
    <select id="selectEnabledByPlatformAndScenario" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_refund_reason_template
        WHERE platform_id = #{platformId}
          AND is_enabled = 1
          AND deleted = 0
        <include refid="Scenario_Condition" />
        ORDER BY create_time ASC
    </select>

    <!-- 根据平台ID查询所有退款原因模板列表 -->
    <select id="selectAllByPlatformId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_refund_reason_template
        WHERE platform_id = #{platformId}
          AND deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 检查退款原因文案是否重复 -->
    <select id="countByReasonText" resultType="int">
        SELECT COUNT(*)
        FROM t_refund_reason_template
        WHERE platform_id = #{platformId}
          AND reason_text = #{reasonText}
          AND deleted = 0
        <if test="excludeId != null">
          AND id != #{excludeId}
        </if>
    </select>

    <!-- 统计平台下指定场景的启用模板数量 -->
    <select id="countEnabledByScenario" resultType="int">
        SELECT COUNT(*)
        FROM t_refund_reason_template
        WHERE platform_id = #{platformId}
          AND is_enabled = 1
          AND deleted = 0
        <include refid="Scenario_Condition" />
    </select>

    <!-- 统计平台下指定场景的启用模板数量（排除指定ID） -->
    <select id="countEnabledByScenarioExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM t_refund_reason_template
        WHERE platform_id = #{platformId}
          AND is_enabled = 1
          AND deleted = 0
          AND id != #{excludeId}
        <include refid="Scenario_Condition" />
    </select>

</mapper>