<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.afs.service.mp.mapper.RefundMethodConfigMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.afs.service.mp.entity.RefundMethodConfig">
        <id column="id" property="id" />
        <result column="platform_id" property="platformId" />
        <result column="method_name" property="methodName" />
        <result column="method_desc" property="methodDesc" />
        <result column="is_enabled" property="isEnabled" />
        <result column="is_default" property="isDefault" />
        <result column="version" property="version" />
        <result column="deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用字段 -->
    <sql id="Base_Column_List">
        id, platform_id, method_name, method_desc, 
        is_enabled, is_default, version, deleted, create_time, update_time
    </sql>

    <!-- 根据平台ID查询启用的退款方式列表 -->
    <select id="selectEnabledByPlatformId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_refund_method_config
        WHERE platform_id = #{platformId}
          AND is_enabled = 1
          AND deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据平台ID查询所有退款方式列表 -->
    <select id="selectAllByPlatformId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_refund_method_config
        WHERE platform_id = #{platformId}
          AND deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据平台ID查询默认退款方式 -->
    <select id="selectDefaultByPlatformId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_refund_method_config
        WHERE platform_id = #{platformId}
          AND is_default = 1
          AND is_enabled = 1
          AND deleted = 0
        LIMIT 1
    </select>

    <!-- 检查退款方式名称是否重复 -->
    <select id="countByMethodName" resultType="int">
        SELECT COUNT(*)
        FROM t_refund_method_config
        WHERE platform_id = #{platformId}
          AND method_name = #{methodName}
          AND deleted = 0
        <if test="excludeId != null">
          AND id != #{excludeId}
        </if>
    </select>

    <!-- 清除平台的默认退款方式标记 -->
    <update id="clearDefaultFlag">
        UPDATE t_refund_method_config
        SET is_default = 0,
            update_time = NOW()
        WHERE platform_id = #{platformId}
          AND is_default = 1
          AND deleted = 0
    </update>

    <!-- 设置默认退款方式 -->
    <update id="setAsDefault">
        UPDATE t_refund_method_config
        SET is_default = 1,
            update_time = NOW()
        WHERE id = #{id}
          AND deleted = 0
    </update>

</mapper> 