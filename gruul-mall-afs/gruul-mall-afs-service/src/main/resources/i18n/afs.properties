afs.express.company.not.null=The express company cannot be empty
afs.item.cannot.afs.status=This order item is in a non applicable after-sales status
afs.item.closed=order item closed
afs.item.not.exist=This order does not exist
afs.next.status.not.null=The next after-sales status cannot be empty
afs.not.default.return.address=No default return address set
afs.not.delivered.type.error=The order has not been shipped, and the selected after-sales type is incorrect
afs.not.support.approval.status=Unable to approve current after-sales status
afs.not.support.close.status=Not in a switchable after-sales state
afs.not.support.deliver.type=Unsupported shipping method
afs.not.support.refuse.return.status=Not in an after-sales state where returns can be refused
afs.not.support.return.status=Not in a returnable after-sales state
afs.refund.amount.cannot.greater.than.paid.amount=The requested refund amount cannot be greater than the actual payment amount of the order
afs.status.not.match.order.status=The after-sales work order status does not match the order status
afs.type.reason.not.match=Mismatch between after-sales type and after-sales reason

# ======================== Refund Configuration Messages ========================

# Refund Method Related
refund.method.not.found=Refund method not found
refund.method.name.exists=Refund method name already exists
refund.method.default.cannot.delete=Default refund method cannot be deleted
refund.method.default.cannot.disable=Default refund method cannot be disabled
refund.method.system.cannot.delete=System default refund method cannot be deleted
refund.method.system.cannot.modify=System default refund method cannot be modified
refund.method.not.belong.platform=Refund method does not belong to current platform
refund.method.not.enabled=Refund method is not enabled
refund.method.cannot.disable.last.enabled=Cannot disable the last enabled refund method

# Refund Reason Template Related
refund.reason.not.found=Refund reason template not found
refund.reason.text.exists=Refund reason text already exists
refund.reason.system.cannot.delete=System default refund reason template cannot be deleted
refund.reason.system.cannot.modify=System default refund reason template cannot be modified
refund.reason.not.belong.platform=Refund reason template does not belong to current platform

# Scenario Validation Related
refund.scenario.refund.only.need.enabled.template=At least one enabled template is required for refund-only scenario
refund.scenario.return.refund.need.enabled.template=At least one enabled template is required for return-refund scenario
refund.scenario.must.have.enabled.template=Scenario must have enabled templates
refund.template.last.enabled.cannot.delete=This template is the last enabled template in the scenario and cannot be deleted
refund.template.last.enabled.cannot.disable=This template is the last enabled template in the scenario and cannot be disabled

# General Messages
refund.param.validation.failed=Parameter validation failed
refund.operation.failed=Operation failed
refund.permission.denied=Permission denied

# ======================== 退款配置相关消息 ========================

# 通用错误
refund.param.validation.failed=参数校验失败

# 退款方式相关
refund.method.not.found=退款方式不存在
refund.method.name.exists=退款方式名称已存在
refund.method.default.cannot.delete=默认退款方式不能删除
refund.method.not.belong.platform=退款方式不属于当前平台
refund.method.not.enabled=退款方式未启用
refund.method.cannot.disable.last.enabled=不能禁用最后一个启用的退款方式

# 退款原因相关
refund.reason.not.found=退款原因模板不存在
refund.reason.text.exists=退款原因文案已存在
refund.reason.not.belong.platform=退款原因模板不属于当前平台

# 业务规则相关
refund.scenario.must.have.enabled.template=每个场景下必须有启用的模板
