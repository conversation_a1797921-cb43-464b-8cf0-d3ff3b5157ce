afs.express.company.not.null=\ 物流公司不能为空
afs.item.cannot.afs.status=该订单项处于不可申请售后状态
afs.item.closed=该订单项已关闭
afs.item.not.exist=该比订单不存在
afs.next.status.not.null=下一个售后状态不能为空
afs.not.default.return.address=未设置默认退货地址
afs.not.delivered.type.error=订单未发货，选择的售后类型不正确
afs.not.support.approval.status=无法审批当前售后状态
afs.not.support.close.status=不处于可关闭的售后状态
afs.not.support.deliver.type=不支持的发货方式
afs.not.support.refuse.return.status=不处于可拒绝退货的售后状态
afs.not.support.return.status=不处于可退货的售后状态
afs.refund.amount.cannot.greater.than.paid.amount=申请的退款金额不能大于订单实际支付金额
afs.status.not.match.order.status=售后工单状态和订单状态不匹配
afs.type.reason.not.match=售后类型和售后原因不匹配

# ======================== 退款配置相关消息 ========================

# 通用错误
refund.param.validation.failed=参数校验失败

# 退款方式相关
refund.method.not.found=退款方式不存在
refund.method.name.exists=退款方式名称已存在
refund.method.default.cannot.delete=默认退款方式不能删除
refund.method.default.cannot.disable=默认退款方式不能禁用
refund.method.system.cannot.delete=系统默认退款方式不能删除
refund.method.system.cannot.modify=系统默认退款方式不能修改
refund.method.not.belong.platform=退款方式不属于当前平台
refund.method.not.enabled=退款方式未启用
refund.method.cannot.disable.last.enabled=不能禁用最后一个启用的退款方式

# 退款原因相关
refund.reason.not.found=退款原因模板不存在
refund.reason.text.exists=退款原因文案已存在
refund.reason.system.cannot.delete=系统默认退款原因模板不能删除
refund.reason.system.cannot.modify=系统默认退款原因模板不能修改
refund.reason.not.belong.platform=退款原因模板不属于当前平台

# 业务规则相关
refund.scenario.must.have.enabled.template=每个场景下必须有启用的模板

# 场景校验相关
refund.scenario.refund.only.need.enabled.template=仅退款场景下至少需要一个启用的模板
refund.scenario.return.refund.need.enabled.template=退货退款场景下至少需要一个启用的模板
refund.template.last.enabled.cannot.delete=该模板是场景下最后一个启用模板，不能删除
refund.template.last.enabled.cannot.disable=该模板是场景下最后一个启用模板，不能禁用

# 通用消息
refund.operation.failed=操作失败
refund.permission.denied=权限不足