package com.medusa.gruul.afs.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板类型枚举
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum TemplateType {
    
    /**
     * 系统默认
     */
    SYSTEM_DEFAULT(1, "系统默认"),
    
    /**
     * 自定义
     */
    CUSTOM(2, "自定义");
    
    /**
     * 类型编码
     */
    @EnumValue
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String desc;
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static TemplateType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TemplateType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 