package com.medusa.gruul.afs.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.medusa.gruul.afs.api.constant.AfsConst;
import com.medusa.gruul.afs.api.enums.AfsRabbit;
import com.medusa.gruul.afs.api.enums.AfsType;
import com.medusa.gruul.afs.api.enums.RefundType;
import com.medusa.gruul.afs.api.model.AfsCloseDTO;
import com.medusa.gruul.afs.service.model.bo.AfsUpdateBO;
import com.medusa.gruul.afs.service.model.dto.*;
import com.medusa.gruul.afs.service.model.enums.AfsError;
import com.medusa.gruul.afs.service.mp.entity.AfsHistory;
import com.medusa.gruul.afs.service.mp.entity.AfsOrder;
import com.medusa.gruul.afs.service.mp.entity.AfsOrderItem;
import com.medusa.gruul.afs.service.mp.entity.AfsPackage;
import com.medusa.gruul.afs.service.mp.service.IAfsHistoryService;
import com.medusa.gruul.afs.service.mp.service.IAfsOrderService;
import com.medusa.gruul.afs.service.mp.service.IAfsPackageService;
import com.medusa.gruul.afs.service.service.AfsQueryService;
import com.medusa.gruul.afs.service.service.AfsService;
import com.medusa.gruul.afs.service.service.SyncMdmRefundOrderService;
import com.medusa.gruul.afs.service.task.AfsOrderExportTask;
import com.medusa.gruul.carrier.pigeon.api.rpc.PigeonChatStatisticsRpcService;
import com.medusa.gruul.common.member.dto.MessagesSendDTO;
import com.medusa.gruul.common.member.enums.WechatMsgSendTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.dto.settlement.BarkOrderDiscountCallbackQO;
import com.medusa.gruul.common.member.dto.settlement.OrderCommodityQO;
import com.medusa.gruul.common.member.dto.settlement.SettlementUnLockedDiscountDTO;
import com.medusa.gruul.common.member.service.SettlementApiService;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.module.app.afs.AfsStatus;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.annotation.Redisson;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.resource.helper.ISecurity;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.wechat.WechatServicePreloader;
import com.medusa.gruul.global.model.express.UserAddressDTO;
import com.medusa.gruul.global.model.filter.IFilter;
import com.medusa.gruul.global.model.filter.IFilterPipeline;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.order.api.entity.OrderDiscount;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.DeliverType;
import com.medusa.gruul.order.api.enums.DiscountSourceOrigin;
import com.medusa.gruul.order.api.enums.DiscountSourceType;
import com.medusa.gruul.order.api.enums.OrderRabbit;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.overview.api.enums.ExportDataType;
import com.medusa.gruul.overview.api.model.DataExportRecordDTO;
import com.medusa.gruul.overview.api.rpc.DataExportRecordRpcService;
import com.medusa.gruul.payment.api.model.dto.RefundRequestDTO;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import com.medusa.gruul.shop.api.model.vo.ShopLogisticsAddressVO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import io.vavr.control.Option;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

import java.math.BigDecimal;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <p></p>
 *
 * <AUTHOR>
 * date 2022/8/6
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AfsServiceImpl implements AfsService {

    private final IAfsHistoryService afsHistoryService;
    private final IAfsPackageService afsPackageService;
    private final IAfsOrderService afsOrderService;
    private final PaymentRpcService paymentRpcService;
    private final SyncMdmRefundOrderService syncMdmRefundOrderService;

    private final SettlementApiService settlementApiService;

    private final ShopRpcService shopRpcService;

    private final IFilter<AfsUpdateBO> getAfsOrderFilter;
    private final RabbitTemplate rabbitTemplate;
    private final IFilter<AfsUpdateBO> getAndCheckShopOrderItemFilter;
    private final IFilter<AfsUpdateBO> updateAfsOrderStatusFilter;
    private final IFilter<AfsUpdateBO> updateShopOrderItemStatus;
    private final WechatServicePreloader wechatServicePreloader;

    private final OrderRpcService orderRpcService;

    private final DataExportRecordRpcService dataExportRecordRpcService;

    private final PigeonChatStatisticsRpcService pigeonChatStatisticsRpcService;
    private final UaaRpcService uaaRpcService;

    private final Executor afsExecutor;

    @Resource
    private AfsQueryService afsQueryService;

    private final MemberApiService memberApiService;


    @Override
    @Redisson(value = AfsConst.AFS_AGREE_LOCK, key = "#afsNo")
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void afsRequestAgreeReject(String afsNo, AfsAuditDTO afsAudit, boolean isSystem) {
        Boolean isAgree = afsAudit.getAgree();
        IFilterPipeline.build(Option.of(new AfsUpdateBO().setAfsNo(afsNo).setIsSystem(isSystem)))
                //获取售后工单信息
                .addFilter(getAfsOrderFilter)
                //检查目标状态是否重复消费 状态
                .addFilter(context -> {
                    //商家管理员不可操作供应商售后
                    ISecurity.match().ifAnyShopAdmin(secureUser -> SecureCodes.PERMISSION_DENIED.falseThrow(
                            context.getData().getAfsOrder().getSupplierId() == null));
                    AfsStatus currentStatus = afsAudit.getCurrentStatus();
                    context.setBreakIt(
                            currentStatus != null && currentStatus != context.getData().getAfsOrder()
                                    .getStatus());
                })
                //检查售后工单状态
                .addFilter(context -> AfsError.NOT_SUPPORT_APPROVAL_STATUS.falseThrow(
                        context.getData().getAfsOrder().getStatus().isCanAgreeOrReject()))
                //获取订单商品项 并检查数据
                .addFilter(getAndCheckShopOrderItemFilter)
                //获取售后下一步状态
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    data.setNextStatus(AfsStatus.REFUND_REQUEST == data.getAfsOrder().getStatus() ?
                            (isAgree ? (isSystem ? AfsStatus.SYSTEM_REFUND_AGREE : AfsStatus.REFUND_AGREE)
                                    : AfsStatus.REFUND_REJECT) :
                            (isAgree ? (isSystem ? AfsStatus.SYSTEM_RETURN_REFUND_AGREE
                                    : AfsStatus.RETURN_REFUND_AGREE) : AfsStatus.RETURN_REFUND_REJECT));
                })
                //更新售后工单状态
                .addFilter(updateAfsOrderStatusFilter)
                //生成并保存售后历史
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    saveHistory(new AfsHistory().setAfsNo(afsNo).setAfsStatus(data.getNextStatus())
                            .setPackageStatus(data.getShopOrderItem().getPackageStatus())
                            .setRemark(afsAudit.getRemark()));
                })
                //如果是同意退款申请 则 调起退款
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    if (!data.getNextStatus().isCanRefunded()) {
                        //发送微信订阅消息
                        sendAfsMessage(afsNo, data.getAfsOrder(), "退款失败", afsAudit.getRemark());
                        return;
                    }
                    AfsOrder afsOrder = data.getAfsOrder();
                    paymentRpcService.refundRequest(new RefundRequestDTO()
                            .setOrderNum(afsOrder.getOrderNo())
                            .setShopId(afsOrder.getShopId())
                            .setAfsNum(afsNo)
                            .setExchange(AfsRabbit.AFS_REFUND_CALLBACK.exchange())
                            .setRouteKey(AfsRabbit.AFS_REFUND_CALLBACK.routingKey())
                            .setRefundFee(afsOrder.getRefundAmount())
                    );

                    // 同意退款后同步订单到MDM，异常不影响主流程
                    try {
                        log.info("[MDM退款订单同步] 开始同步订单，afsNo={}", afsNo);
                        syncMdmRefundOrderService.syncOrder(afsNo);
                        log.info("[MDM退款订单同步] 同步完成，afsNo={}", afsNo);
                    } catch (Exception e) {
                        log.error("[MDM退款订单同步] 同步失败，afsNo={}", afsNo, e);
                    }
                    //发送微信订阅消息
                    sendAfsMessage(afsNo, afsOrder, "退款成功", "退款将原路返回您的支付账户，请注意查收~");
                    sendBarkOrderDiscountCallback(afsOrder.getOrderNo(), afsOrder);
                })
                //如果是同意退货退换 则 获取默认退货地址 保存退款地址数据
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    if (AfsStatus.SYSTEM_RETURN_REFUND_AGREE != data.getNextStatus()
                            && AfsStatus.RETURN_REFUND_AGREE != data.getNextStatus()) {
                        return;
                    }
                    AfsOrder afsOrder = data.getAfsOrder();
                    UserAddressDTO receiver = Option.of(afsAudit.getReceiver())
                            .getOrElse(() -> {
                                //查询默认退货地址
                                ShopLogisticsAddressVO receiveAddress = Option.of(
                                                shopRpcService.getSendOrReceiveAddress(afsOrder.getShopId(), Boolean.FALSE))
                                        .getOrElseThrow(AfsError.NOT_DEFAULT_RETURN_ADDRESS::exception);
                                return new UserAddressDTO()
                                        .setName(receiveAddress.getContactName())
                                        .setMobile(receiveAddress.getContactPhone())
                                        .setAddress(
                                                CollUtil.join(receiveAddress.getArea(), StrUtil.SPACE) + StrUtil.SPACE
                                                        + receiveAddress.getAddress()
                                        );
                            });
                    boolean success = afsPackageService.save(
                            new AfsPackage()
                                    .setAfsNo(afsNo)
                                    .setType(DeliverType.EXPRESS)
                                    .setReceiverName(receiver.getName())
                                    .setReceiverMobile(receiver.getMobile())
                                    .setReceiverAddress(receiver.getAddress())
                    );
                    SystemCode.DATA_ADD_FAILED.falseThrow(success);
                }).addFilter(updateShopOrderItemStatus)
                //发送自动关闭mq
                .addFilter(context -> {
                    AfsOrder afsOrder = context.getData().getAfsOrder();
                    if (!isAgree || AfsType.RETURN_REFUND == afsOrder.getType()) {
                        return;
                    }
                    rabbitTemplate.convertAndSend(
                            AfsRabbit.AFS_AUTO_CLOSE.exchange(),
                            AfsRabbit.AFS_AUTO_CLOSE.routingKey(),
                            new AfsMqMessageDTO().setAfsNo(afsNo)
                                    .setCurrentStatus(context.getData().getNextStatus()),
                            message -> {
                                message.getMessageProperties().setHeader(MessageProperties.X_DELAY,
                                        afsOrder.getKeyNodeTimeout().getReturnedTimeoutMills());
                                return message;
                            }

                    );
                })//发送微信发货信息录入
                .addFilter(context -> wxOrderShipping(afsNo, isAgree)).flush();
    }

    private void sendAfsMessage(String afsNo, AfsOrder afsOrder, String afsStatus, String afsType) {
        try {
            log.info("[微信小程序] 发送售后消息，afsNo={}, afsStatus={}, afsType={}", afsNo, afsStatus, afsType);
            wechatAfsMessageSend(afsNo, afsOrder, afsStatus, afsType);
        } catch (Exception e) {
            log.error("[微信小程序] 发送售后消息失败，afsNo={}", afsNo, e);
        }
    }

    private void wechatAfsMessageSend(String afsNo, AfsOrder afsOrder, String afsStatus, String afsType) {
        AfsOrder asOrder = afsQueryService.getCurrentAfsOrderDetail(afsNo).getOrNull();
        if (afsOrder == null) {
            return;
        }
        String orderNo = afsOrder.getOrderNo();
        Long shopId = afsOrder.getShopId();
        ShopOrder targetShopOrder = orderRpcService.getShopOrderList(orderNo).stream()
                .filter(shopOrder -> shopOrder.getShopId().equals(shopId))
                .findFirst()
                .orElse(null);
        if (targetShopOrder == null) {
            return;
        }
        //退款成功
        MessagesSendDTO messagesSendQO = new MessagesSendDTO();
        StringBuilder messageItem = new StringBuilder();
        AfsOrderItem afsOrderItem = asOrder.getAfsOrderItem();
        log.info("[微信小程序] asOrder={}", JSON.toJSONString(asOrder));
        messageItem.append(afsOrderItem.getProductName()).append("*").append(afsOrderItem.getNum()).append(";");
        messagesSendQO.setTemplateName(WechatMsgSendTypeEnum.ORDER_REFUND.getMsgTitle());
        messagesSendQO.setPrams(
                generateAfsOrderAppletsParam(
                        afsStatus,
                        messageItem.toString(),
                        AmountCalculateHelper.toYuan(asOrder.getRefundAmount()).toString(),
                        targetShopOrder.getShopName(),
                        afsType));
        messagesSendQO.setOrderNo(orderNo);
        messagesSendQO.setPackageId(Objects.nonNull(asOrder.getPackageId()) ? asOrder.getPackageId().toString() : null);
        messagesSendQO.setShopId(shopId.toString());
        messagesSendQO.setOrderNum(Strings.EMPTY);

        UserInfoVO userInfo = uaaRpcService.getUserDataByUserId(asOrder.getBuyerId()).get();
        log.info("[微信小程序] 获取售后用户信息:{}", JSON.toJSONString(userInfo));
        // 获取退款用户信息
        messagesSendQO.setPhone(userInfo.getMobile());
        messagesSendQO.setOperSubjectGuid(ISystem.platformIdMust().toString());
        messagesSendQO.setMemberName(userInfo.getUsername());
        log.info("[微信小程序] 发送售后消息:{}", JSON.toJSONString(messagesSendQO));
        memberApiService.wechatMessageSendBatch(messagesSendQO);
    }

    private Map<String, String> generateAfsOrderAppletsParam(String thing1,
                                                             String thing2,
                                                             String amount3,
                                                             String thing12,
                                                             String thing13) {
        Map<String, String> params = new HashMap<>();
        //退款状态
        params.put("thing1", thing1);
        //商品名称
        params.put("thing2", thing2);
        //退款金额
        params.put("amount3", amount3);
        //门店名称
        params.put("thing12", thing12);
        //温馨提示
        params.put("thing13", thing13);
        return params;
    }

    private void sendBarkOrderDiscountCallback(String orderNo, AfsOrder afsOrder) {
        try {
            // 获取shopOrder对象
            List<ShopOrderItem> allItems = Option.of(orderRpcService.getShopOrderItemList(orderNo))
                    .getOrElse(Collections.emptyList());

            if (CollUtil.isEmpty(allItems)) {
                log.info("订单退款 优惠活动记录回调失败, 未找到订单商品项, orderNo: {}", orderNo);
                return;
            }

            // 发送退款优惠活动记录回调
            doBarkOrderDiscountCallback(orderNo, allItems, afsOrder);
        } catch (Exception e) {
            // 发送失败不影响业务
            log.info("订单退款 优惠活动记录回调失败, orderNo: {}, error: {}", orderNo, e.getMessage());
        }
    }

    /**
     * 检查订单中所有商品是否都已退款（包含历史退款）
     *
     * @param orderNo 订单号
     * @param orderItems 订单商品项列表
     * @return 所有商品是否已退款
     */
    /**
     * 检查订单中所有商品是否都已退款（包含历史退款）
     *
     * @param orderNo 订单号
     * @param orderItems 订单商品项列表
     * @return 所有商品是否已退款
     */
    private boolean checkAllOrderItemsRefunded(String orderNo, List<ShopOrderItem> orderItems, Long shopId) {
        log.info("checkAllOrderItemsRefunded, orderNo: {}, shopId: {}, 商品数: {}", orderNo, shopId, orderItems.size());
        // 快速检查：如果没有商品，无法判断
        if (CollUtil.isEmpty(orderItems)) {
            log.info("订单商品项为空，无法判断退款状态，orderNo: {}", orderNo);
            return false;
        }
        try {
            return TenantShop.disable(() -> {
                // 1. 获取所有有效商品ID
                Set<Long> orderItemIds = orderItems.stream()
                        .map(ShopOrderItem::getId)
                        .collect(Collectors.toSet());

                // 2. 获取所有已退款或正在退款的商品项ID
                Set<Long> refundedItemIds = afsOrderService.lambdaQuery()
                        .eq(AfsOrder::getOrderNo, orderNo)
                        .eq(shopId != null, AfsOrder::getShopId, shopId)
                        .select(AfsOrder::getShopOrderItemId, AfsOrder::getStatus)
                        .list()
                        .stream()
                        .filter(afs -> isRefundedStatus(afs.getStatus()))
                        .map(AfsOrder::getShopOrderItemId)
                        .collect(Collectors.toSet());

                // 3. 记录日志
                if (log.isInfoEnabled()) {
                    log.info("订单退款状态检查, orderNo: {}, 总商品数: {}, 已退款商品数: {}, 商品IDs: {}, 退款IDs: {}",
                            orderNo, orderItemIds.size(), refundedItemIds.size(), orderItemIds, refundedItemIds);
                }

                // 4. 检查是否所有商品都已退款 (orderItemIds是否是refundedItemIds的子集)
                boolean allRefunded = refundedItemIds.containsAll(orderItemIds);

                log.info("订单{}商品均已退款, orderNo: {}", allRefunded ? "所有" : "部分", orderNo);
                return allRefunded;
            });
        } catch (Exception e) {
            log.error("检查订单商品退款状态失败, orderNo: {}, error: {}", orderNo, e.getMessage());
            return false;
        }
    }

    /**
     * 判断售后状态是否为已退款或正在退款
     */
    private boolean isRefundedStatus(AfsStatus status) {
        return status == AfsStatus.REFUNDED ||
                status == AfsStatus.RETURNED_REFUNDED ||
                status == AfsStatus.REFUND_AGREE ||
                status == AfsStatus.SYSTEM_REFUND_AGREE ||
                status == AfsStatus.RETURN_REFUND_AGREE ||
                status == AfsStatus.SYSTEM_RETURN_REFUND_AGREE ||
                status == AfsStatus.RETURNED_REFUND_CONFIRM;
    }

    private void doBarkOrderDiscountCallback(String orderNo, List<ShopOrderItem> allItems, AfsOrder afsOrder) {
        try {
            // 确保获取到了售后工单
            if (afsOrder == null) {
                log.error("订单退款 优惠活动记录回调失败, 未找到售后工单, orderNo: {}", orderNo);
                return;
            }

            // 记录日志
            log.info("订单退款 获取到售后工单, afsNo: {}, orderNo: {}, status: {}",
                    afsOrder.getNo(), orderNo, afsOrder.getStatus());

            // 构建回调参数
            BarkOrderDiscountCallbackQO callbackQO = buildOrderDiscountCallback(orderNo, afsOrder, allItems);

            // 处理店铺优惠券回退
            tryProcessShopCouponReturn(afsOrder, orderNo, allItems);

            // 调用结算服务
            settlementApiService.barkOrderDiscountCallback(callbackQO);
        } catch (Exception e) {
            log.error("订单退款 优惠活动记录回调失败, orderNo: {}, error: {}", orderNo, e.getMessage());
        }
    }

    /**
     * 尝试处理店铺优惠券回退
     *
     * @param afsOrder 售后工单
     * @param orderNo 订单号
     * @param allItems 所有订单商品项
     */
    private void tryProcessShopCouponReturn(AfsOrder afsOrder, String orderNo, List<ShopOrderItem> allItems) {
        try {
            // 检查私域商城店铺券回退
            checkBarkShopCoupons(afsOrder.getNo(), orderNo, afsOrder.getShopId(), allItems);
        } catch (Exception e) {
            log.info("私域商城店铺券回退失败, orderNo: {}, error: {}", orderNo, e.getMessage());
        }
    }

    private void checkBarkShopCoupons(String afsNum, String orderNum, Long shopId, List<ShopOrderItem> allItems) {
        log.info("开始检查私域商城店铺券回退, afsNum: {}, orderNum: {}, shopId: {}, allItems: {}",
                afsNum, orderNum, shopId, JSON.toJSONString(allItems));
        // 如果参数为空，直接返回零
        if (CharSequenceUtil.isBlank(afsNum) || CharSequenceUtil.isBlank(orderNum) || shopId == null) {
            log.info("参数不完整，无法计算退款优惠金额");
            return;
        }
        // 获取订单优惠信息
        List<OrderDiscount> orderDiscounts = orderRpcService.getOrderDiscountsByOrderNo(orderNum);
        log.info("获取订单优惠信息: {}", JSON.toJSONString(orderDiscounts));
        if (CollUtil.isEmpty(orderDiscounts)) {
            return;
        }
        List<ShopOrderItem> shopOrderItems = allItems.stream().filter(in -> in.getShopId().equals(shopId)).toList();
        if (CollUtil.isEmpty(shopOrderItems)) {
            log.info("没有找到对应店铺的订单商品项, orderNum: {}, shopId: {}", orderNum, shopId);
            return;
        }
        // 检查是否所有商品都已退款
        boolean allItemsRefunded = checkAllOrderItemsRefunded(orderNum, shopOrderItems, shopId);
        log.info("检查所有店铺商品是否已退款: orderNum: {}, allItemsRefunded: {}", orderNum, allItemsRefunded);
        //私域商城店铺券回退
        if (allItemsRefunded) {
            doBarkShopCoupons(orderDiscounts, shopId);
        }

    }

    private void doBarkShopCoupons(List<OrderDiscount> orderDiscounts, Long shopId) {
        try {
            List<OrderDiscount> orderDiscountList = orderDiscounts.stream()
                    .filter(orderDiscount -> orderDiscount.getSourceType().equals(DiscountSourceType.SHOP_COUPON)
                            && orderDiscount.getSourceOrigin().equals(DiscountSourceOrigin.INTERNAL))
                    .toList();
            log.info("私域商城店铺券回退优惠信息: {}", JSON.toJSONString(orderDiscountList));
            if (CollUtil.isNotEmpty(orderDiscountList)) {
                // 如果是私域商城店铺券，执行回退逻辑
                List<Long> sourceIdList = orderDiscountList.stream().map(OrderDiscount::getSourceId).toList();
                log.info("私域商城店铺券回退优惠ID: {}", JSON.toJSONString(sourceIdList));
                // 添加服务调用超时处理
                int num = orderRpcService.updateCouponsToUnused(sourceIdList, shopId);
                log.info("私域商城店铺券回退数量: {}", num);
            }
        } catch (Exception e) {
            log.error("私域商城店铺券回退逻辑执行异常: {}", e.getMessage(), e);
            // 记录异常堆栈，方便排查问题
            log.error("异常堆栈:", e);
        }
    }

    /**
     * 构建优惠活动回调参数
     */
    private BarkOrderDiscountCallbackQO buildOrderDiscountCallback(String orderNo, AfsOrder afsOrder, List<ShopOrderItem> orderItems) {
        BarkOrderDiscountCallbackQO callbackQO = new BarkOrderDiscountCallbackQO();
        callbackQO.setOrderNo(orderNo);
        callbackQO.setRefundAmount(AmountCalculateHelper.toYuan(afsOrder.getRefundAmount()));

        // 检查是否所有商品都已退款
        boolean allItemsRefunded = checkAllOrderItemsRefunded(orderNo, orderItems, null);

        // 根据所有商品是否退完来判断退款类型
        // 如果所有商品都退完了，则为整单退款(1)，否则为部分退款(0)
        boolean isPartialRefund = !allItemsRefunded;
        callbackQO.setRefundType(isPartialRefund ? 0 : 1);

        // 所有商品退完才回退积分(1)，否则不回退(0)
        callbackQO.setIsIntegralBack(allItemsRefunded ? 1 : 0);

        // 添加日志，方便排查问题
        log.info("优惠回调参数, orderNo: {}, 是否全部退款: {}, refundType: {}, isIntegralBack: {}",
                orderNo, allItemsRefunded, callbackQO.getRefundType(), callbackQO.getIsIntegralBack());

        // 部分退款时，添加退款商品明细
        if (isPartialRefund) {
            callbackQO.setOrderCommodityQOList(buildRefundCommodityList(afsOrder, orderItems));
        }

        return callbackQO;
    }

    /**
     * 构建退款商品明细
     */
    private List<OrderCommodityQO> buildRefundCommodityList(AfsOrder afsOrder, List<ShopOrderItem> orderItems) {
        List<OrderCommodityQO> commodityList = new ArrayList<>();

        // 查找当前退款的商品项
        orderItems.stream()
                .filter(item -> item.getId().equals(afsOrder.getShopOrderItemId()))
                .findFirst()
                .ifPresent(refundItem -> {
                    OrderCommodityQO commodityQO = new OrderCommodityQO()
                            .setCommodityCode(String.valueOf(refundItem.getSkuId()))
                            .setCommodityName(refundItem.getProductName())
                            .setCommodityNum(new BigDecimal(refundItem.getNum()));
                    commodityList.add(commodityQO);
                });

        return commodityList;
    }

    public void doUnLockedDiscount(String orderNo) {
        try {
            SettlementUnLockedDiscountDTO dto = new SettlementUnLockedDiscountDTO();
            dto.setOrderNo(orderNo);
            settlementApiService.lockedDiscount(dto);
        } catch (Exception e) {
            log.error("订单退款 释放优惠活动失败, orderNo: {}, error: {}", orderNo, e.getMessage());
        }
    }

    @Override
    @Redisson(value = AfsConst.AFS_AGREE_LOCK, key = "#afsNo")
    @Transactional(rollbackFor = Exception.class)
    public void refundedNotify(String afsNo, String refundNum) {
        IFilterPipeline.build(Option.of(
                        new AfsUpdateBO().setAfsNo(afsNo).setAfsTradeNo(refundNum).setIsSystem(Boolean.TRUE)))
                //获取售后工单
                .addFilter(getAfsOrderFilter)
                //检查当前售后状态
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    AfsStatus currentStatus = data.getAfsOrder().getStatus();
                    if (!currentStatus.isCanRefunded()) {
                        context.setBreakIt(Boolean.TRUE);
                        return;
                    }
                    //已退款 直接中断执行
                    if (currentStatus.isRefunded()) {
                        context.setBreakIt(Boolean.TRUE);
                        return;
                    }
                    data.setNextStatus((AfsStatus.REFUND_AGREE == currentStatus
                            || AfsStatus.SYSTEM_REFUND_AGREE == currentStatus) ? AfsStatus.REFUNDED
                            : AfsStatus.RETURNED_REFUNDED);
                })
                //获取订单商品项 并检查数据
                .addFilter(getAndCheckShopOrderItemFilter)
                //更新售后工单状态
                .addFilter(updateAfsOrderStatusFilter)
                //生成并保存售后历史
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    saveHistory(
                            new AfsHistory()
                                    .setAfsNo(afsNo)
                                    .setAfsStatus(data.getNextStatus())
                                    .setPackageStatus(data.getShopOrderItem().getPackageStatus())
                    );
                })
                //更新订单商品项状态
                .addFilter(updateShopOrderItemStatus)
                .flush();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afsBuyerReturned(String afsNo, RefundType type, BuyerReturnedDTO buyerReturned) {
        final AtomicReference<String> remarkHolder = new AtomicReference<>(null);
        if (type == RefundType.EXPRESS_REFUND) {
            buyerReturned.getExpressRefund().validParam();
            remarkHolder.set(buyerReturned.getExpressRefund().getRemark());
        } else {
            remarkHolder.set(buyerReturned.getGoStoreRefund().getExplain());
        }

        IFilterPipeline.build(Option.of(new AfsUpdateBO().setAfsNo(afsNo).setIsSystem(Boolean.FALSE)))
                //获取售后工单
                .addFilter(getAfsOrderFilter)
                //检查当前售后状态
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    AfsError.NOT_SUPPORT_RETURN_STATUS.trueThrow(
                            AfsStatus.RETURN_REFUND_AGREE != data.getAfsOrder().getStatus()
                                    && AfsStatus.SYSTEM_RETURN_REFUND_AGREE != data.getAfsOrder().getStatus());
                    data.setNextStatus(AfsStatus.RETURNED_REFUND);
                })
                //获取订单商品项 并检查数据
                .addFilter(getAndCheckShopOrderItemFilter)
                //更新售后工单状态
                .addFilter(updateAfsOrderStatusFilter)
                //生成并保存售后历史 并保存用户发货数据
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    saveHistory(new AfsHistory()
                            .setAfsNo(afsNo)
                            .setAfsStatus(data.getNextStatus())
                            .setPackageStatus(data.getShopOrderItem().getPackageStatus())
                            .setRemark(remarkHolder.get()));
                    boolean success = afsPackageService.lambdaUpdate()
                            .set(AfsPackage::getRefundType, type)
                            .set(AfsPackage::getBuyerReturnedInfo, JSON.toJSONString(buyerReturned))
                            .eq(AfsPackage::getAfsNo, afsNo)
                            .update();
                    SystemCode.DATA_UPDATE_FAILED.falseThrow(success);
                })
                //更新订单商品项状态
                .addFilter(updateShopOrderItemStatus)
                //发送mq
                .addFilter(context ->
                        rabbitTemplate.convertAndSend(
                                AfsRabbit.AFS_AUTO_CONFIRM_RETURNED.exchange(),
                                AfsRabbit.AFS_AUTO_CONFIRM_RETURNED.routingKey(),
                                new AfsMqMessageDTO().setAfsNo(afsNo)
                                        .setCurrentStatus(AfsStatus.RETURNED_REFUND_CONFIRM),
                                message -> {
                                    message.getMessageProperties().setHeader(MessageProperties.X_DELAY,
                                            context.getData().getAfsOrder().getKeyNodeTimeout()
                                                    .getConfirmReturnedTimeoutMills());
                                    return message;
                                }
                        )
                )
                //渲染
                .flush();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Redisson(value = AfsConst.AFS_AGREE_LOCK, key = "#afsNo")
    public void afsReturnedConfirmReject(String afsNo, AfsAuditDTO afsAudit, boolean isSystem) {
        Boolean agree = afsAudit.getAgree();
        IFilterPipeline.build(Option.of(new AfsUpdateBO().setAfsNo(afsNo).setIsSystem(isSystem)))
                //获取售后工单
                .addFilter(getAfsOrderFilter)
                //检查是否可以直接跳过
                .addFilter(context -> {
                    //商家管理员不可操作供应商售后
                    ISecurity.match().ifAnyShopAdmin(secureUser -> SecureCodes.PERMISSION_DENIED.falseThrow(context.getData().getAfsOrder().getSupplierId() == null));
                    AfsStatus currentStatus = afsAudit.getCurrentStatus();
                    context.setBreakIt(currentStatus != null && currentStatus != context.getData().getAfsOrder().getStatus());
                })
                //检查当前售后状态
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    AfsStatus status = data.getAfsOrder().getStatus();
                    AfsError.NOT_SUPPORT_REFUSE_RETURN_STATUS.falseThrow(AfsStatus.RETURNED_REFUND == status);
                    data.setNextStatus(agree ? (isSystem ? AfsStatus.SYSTEM_RETURN_REFUND_AGREE : AfsStatus.RETURNED_REFUND_CONFIRM) : AfsStatus.RETURNED_REFUND_REJECT);
                })
                //获取订单商品项 并检查数据
                .addFilter(getAndCheckShopOrderItemFilter)
                //更新售后工单状态
                .addFilter(updateAfsOrderStatusFilter)
                //生成并保存售后历史 若是商家确认收货 则 调起退款
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    saveHistory(new AfsHistory()
                            .setAfsNo(afsNo)
                            .setAfsStatus(data.getNextStatus())
                            .setPackageStatus(data.getShopOrderItem().getPackageStatus())
                            .setRemark(afsAudit.getRemark()));
                    if (agree) {
                        AfsOrder afsOrder = data.getAfsOrder();
                        paymentRpcService.refundRequest(
                                new RefundRequestDTO()
                                        .setOrderNum(afsOrder.getOrderNo())
                                        .setShopId(afsOrder.getShopId())
                                        .setAfsNum(afsNo)
                                        .setExchange(AfsRabbit.AFS_REFUND_CALLBACK.exchange())
                                        .setRouteKey(AfsRabbit.AFS_REFUND_CALLBACK.routingKey())
                                        .setRefundFee(afsOrder.getRefundAmount())
                        );
                    }
                })
                //更新订单商品项状态
                .addFilter(updateShopOrderItemStatus)
                //渲染
                .flush();
    }

    /**
     * 发起微信订单物流录入
     *
     * @param afsNo 售后工单
     * @param agree 是否同意
     */
    private void wxOrderShipping(String afsNo, Boolean agree) {
        if (!wechatServicePreloader.getMiniAppDeliver(ISystem.platformIdMust()) || !agree) {
            return;
        }
        //判断该笔订单下是否还有待发货不处于售后、正在售后的明细
        Boolean exists = orderRpcService.existsDeliverShopOrderItem(afsNo);
        if (!exists) {
            //发送微信小程序退货发货信息录入 MQ
            rabbitTemplate.convertAndSend(
                    OrderRabbit.MINI_APP_ORDER_RETURN_GOODS.exchange(),
                    OrderRabbit.MINI_APP_ORDER_RETURN_GOODS.routingKey(),
                    new ShopOrderItem().setAfsNo(afsNo)
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void afsClose(AfsCloseDTO afsClose) {
        AfsStatus currentStatus = afsClose.getCurrentStatus();
        String afsNo = afsClose.getAfsNo();
        Boolean isSystem = afsClose.getIsSystem();
        IFilterPipeline<AfsUpdateBO> pipeline = IFilterPipeline.build(Option.of(
                        new AfsUpdateBO()
                                .setAfsNo(afsNo)
                                .setIsSystem(isSystem)
                                .setNextStatus(isSystem ? AfsStatus.SYSTEM_CLOSED : AfsStatus.BUYER_CLOSED)
                ))
                //获取售后工单
                .addFilter(getAfsOrderFilter)
                //1.检查是否可以直接跳过 2.检查当前售后状态
                .addFilter(context -> {
                    AfsStatus afsOrderStatus = context.getData().getAfsOrder().getStatus();
                    boolean breakIt = currentStatus != null && currentStatus != afsOrderStatus;
                    context.setBreakIt(breakIt);
                    //不能跳过 但是售后不能关闭
                    AfsError.NOT_SUPPORT_CLOSE_STATUS.falseThrow(breakIt || afsOrderStatus.isCanClose());
                })
                //获取订单商品项 并检查数据
                .addFilter(getAndCheckShopOrderItemFilter)
                //更新售后工单状态
                .addFilter(updateAfsOrderStatusFilter)
                //生成并保存售后历史 若是商家确认收货 则 调起退款
                .addFilter(context -> {
                    AfsUpdateBO data = context.getData();
                    saveHistory(new AfsHistory()
                            .setAfsNo(afsNo)
                            .setAfsStatus(data.getNextStatus())
                            .setPackageStatus(data.getShopOrderItem().getPackageStatus())
                            .setRemark(afsClose.getReason())
                    );
                });
        //更新订单商品项状态
        if (BooleanUtil.isTrue(afsClose.getUpdateShopOrderItem())) {
            pipeline.addFilter(updateShopOrderItemStatus);
        }
        pipeline.flush();
    }


    private void saveHistory(AfsHistory history) {
        boolean success = afsHistoryService.save(history);
        SystemCode.DATA_ADD_FAILED.falseThrow(success);
    }

    @Override
    public void afsOrderBatchRemark(AfsRemarkDTO afsRemark) {
        LambdaUpdateChainWrapper<AfsOrder> updateWrapper = afsOrderService.lambdaUpdate();
        updateWrapper.in(AfsOrder::getNo, afsRemark.getNos())
                .set(AfsOrder::getRemark, afsRemark.getRemark());
        TenantShop.disable(
                () -> {
                    ISecurity.match()
                            .ifAnyShopAdmin(
                                    secureUser -> updateWrapper.eq(AfsOrder::getShopId, secureUser.getShopId()))
                            .ifAnySupplierAdmin(
                                    secureUser -> updateWrapper.eq(AfsOrder::getSupplierId, secureUser.getShopId()));
                    updateWrapper.update();
                }
        );
    }

    @Override
    public Long export(AfsPageDTO afsPage) {
        DataExportRecordDTO dto = new DataExportRecordDTO();
        dto.setExportUserId(ISecurity.userMust().getId())
                .setDataType(ExportDataType.AFTER_SALES_WORK_ORDER)
                .setShopId(ISystem.shopIdMust())
                .setPlatformId(ISystem.platformIdMust())
                .setUserPhone(ISecurity.userMust().getMobile());
        //RPC保存导出记录
        Long exportRecordId = dataExportRecordRpcService.saveExportRecord(dto);
        ISecurity.match()
                .ifAnySupplierAdmin(secureUser -> afsPage.setSupplierId(secureUser.getShopId()))
                .ifAnyShopAdmin(secureUser -> afsPage.setShopId(secureUser.getShopId()))
                .ifUser(secureUser -> afsPage.setBuyerId(secureUser.getId()));
        AfsOrderExportTask task = AfsOrderExportTask.builder()
                .exportRecordId(exportRecordId)
                .afsPageDTO(afsPage)
                .uaaRpcService(uaaRpcService)
                .afsQueryService(afsQueryService)
                .afsPageDTO(afsPage)
                .dataExportRecordRpcService(dataExportRecordRpcService)
                .pigeonChatStatisticsRpcService(pigeonChatStatisticsRpcService)
                .build();
        afsExecutor.execute(task);
        return exportRecordId;
    }


}
