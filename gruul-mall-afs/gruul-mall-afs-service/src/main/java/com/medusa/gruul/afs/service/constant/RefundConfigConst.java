package com.medusa.gruul.afs.service.constant;

/**
 * 退款配置常量
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface RefundConfigConst {

    // ======================== 缓存键常量 ========================
    
    /**
     * 启用的退款方式缓存键
     */
    String REFUND_METHODS_ENABLED_CACHE_KEY = "afs:refund:methods:enabled";
    
    /**
     * 所有退款方式缓存键
     */
    String REFUND_METHODS_ALL_CACHE_KEY = "afs:refund:methods:all";
    
    /**
     * 默认退款方式缓存键
     */
    String REFUND_METHODS_DEFAULT_CACHE_KEY = "afs:refund:methods:default";
    
    /**
     * 按场景查询的退款原因模板缓存键
     */
    String REFUND_REASONS_SCENARIO_CACHE_KEY = "afs:refund:reasons:scenario";
    
    /**
     * 所有退款原因模板缓存键
     */
    String REFUND_REASONS_ALL_CACHE_KEY = "afs:refund:reasons:all";
    
    /**
     * 所有启用的退款原因模板缓存键
     */
    String REFUND_REASONS_ALL_ENABLED_CACHE_KEY = "afs:refund:reasons:all:enabled";

    // ======================== 分布式锁键常量 ========================
    
    /**
     * 退款方式保存分布式锁键
     */
    String REFUND_METHOD_SAVE_LOCK_KEY = "afs:refund:method:save:lock";
    
    /**
     * 退款方式查询分布式锁键
     */
    String REFUND_METHOD_QUERY_LOCK_KEY = "afs:refund:method:query:lock";
    
    /**
     * 退款原因模板保存分布式锁键
     */
    String REFUND_REASON_SAVE_LOCK_KEY = "afs:refund:reason:save:lock";
    
    /**
     * 退款原因模板查询分布式锁键
     */
    String REFUND_REASON_QUERY_LOCK_KEY = "afs:refund:reason:query:lock";
    
    // ======================== 场景化缓存键常量 ========================
    
    /**
     * 按场景启用的退款原因模板缓存键前缀
     */
    String REFUND_REASONS_ENABLED_BY_SCENARIO_KEY = "afs:refund:reasons:enabled";
    
    /**
     * 按场景查询的分布式锁键前缀
     */
    String REFUND_REASON_QUERY_LOCK_BY_SCENARIO_KEY = "afs:refund:reason:query:lock";
} 