package com.medusa.gruul.afs.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.afs.service.constant.RefundConfigConst;
import com.medusa.gruul.afs.service.model.dto.RefundMethodConfigDTO;
import com.medusa.gruul.afs.service.model.dto.RefundReasonTemplateDTO;
import com.medusa.gruul.afs.service.model.enums.RefundConfigError;
import com.medusa.gruul.afs.service.model.enums.RefundScenario;
import com.medusa.gruul.afs.service.model.vo.RefundMethodVO;
import com.medusa.gruul.afs.service.model.vo.RefundReasonVO;
import com.medusa.gruul.afs.service.mp.entity.RefundMethodConfig;
import com.medusa.gruul.afs.service.mp.entity.RefundReasonTemplate;
import com.medusa.gruul.afs.service.mp.mapper.RefundMethodConfigMapper;
import com.medusa.gruul.afs.service.mp.mapper.RefundReasonTemplateMapper;
import com.medusa.gruul.afs.service.service.RefundConfigService;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 退款配置服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefundConfigServiceImpl implements RefundConfigService {

    private final RefundMethodConfigMapper refundMethodConfigMapper;
    private final RefundReasonTemplateMapper refundReasonTemplateMapper;

    // ======================== 退款方式相关方法 ========================

    @Override
    public List<RefundMethodVO> getEnabledRefundMethods() {
        Long platformId = getPlatformId();
        String cacheKey = RedisUtil.key(RefundConfigConst.REFUND_METHODS_ENABLED_CACHE_KEY, platformId);
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_METHOD_QUERY_LOCK_KEY, platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据，明确处理空列表的情况
                () -> {
                    List<RefundMethodVO> cacheList = RedisUtil.getCacheList(cacheKey, RefundMethodVO.class);
                    if (CollUtil.isEmpty(cacheList)) {
                        return null;
                    }
                    return cacheList;
                },
                // 从数据库获取数据
                () -> {
                    List<RefundMethodConfig> configs = refundMethodConfigMapper.selectEnabledByPlatformId(platformId);
                    if (CollUtil.isEmpty(configs)) {
                        return new ArrayList<>();
                    }
                    return configs.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                },
                // 设置缓存
                (methods) -> RedisUtil.setCacheList(cacheKey, methods),
                lockKey
        );
    }

    @Override
    public List<RefundMethodVO> getAllRefundMethods() {
        Long platformId = getPlatformId();
        String cacheKey = RedisUtil.key(RefundConfigConst.REFUND_METHODS_ALL_CACHE_KEY, platformId);
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_METHOD_QUERY_LOCK_KEY, "all", platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据，明确处理空列表的情况
                () -> {
                    List<RefundMethodVO> cacheList = RedisUtil.getCacheList(cacheKey, RefundMethodVO.class);
                    if (CollUtil.isEmpty(cacheList)) {
                        return null;
                    }
                    return cacheList;
                },
                // 从数据库获取数据
                () -> {
                    List<RefundMethodConfig> configs = refundMethodConfigMapper.selectAllByPlatformId(platformId);
                    if (CollUtil.isEmpty(configs)) {
                        return new ArrayList<>();
                    }
                    return configs.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                },
                // 设置缓存
                (methods) -> RedisUtil.setCacheList(cacheKey, methods),
                lockKey
        );
    }

    @Override
    public RefundMethodVO getDefaultRefundMethod() {
        Long platformId = getPlatformId();
        String cacheKey = RedisUtil.key(RefundConfigConst.REFUND_METHODS_DEFAULT_CACHE_KEY, platformId);
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_METHOD_QUERY_LOCK_KEY, "default", platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据
                () -> {
					// 这里不需要检查是否为空列表，因为返回的是单个对象
                    return RedisUtil.getCacheObject(cacheKey);
                },
                // 从数据库获取数据
                () -> {
                    RefundMethodConfig config = refundMethodConfigMapper.selectDefaultByPlatformId(platformId);
                    // 如果没有默认退款方式，返回null
                    return config != null ? convertToVO(config) : null;
                },
                // 设置缓存
                (method) -> RedisUtil.setCacheObject(cacheKey, method),
                lockKey
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRefundMethods(List<RefundMethodConfigDTO> dtoList) {
        Long platformId = getPlatformId();
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_METHOD_SAVE_LOCK_KEY, platformId);
        return RedisUtil.lockRun(lockKey, () -> {
            // 获取所有相关的缓存键
            Set<String> cacheKeys = buildRefundMethodCacheKeys(platformId);
            
            // 执行双删逻辑：先删除缓存，执行业务，再延迟删除缓存
            return RedisUtil.doubleDeletion(() -> {
                // 1. 验证数据
                validateRefundMethodList(dtoList);
                validateMethodNameUnique(dtoList);
                validateHasDefaultMethod(dtoList);

                // 2. 删除原数据
                refundMethodConfigMapper.delete(new LambdaQueryWrapper<RefundMethodConfig>()
                        .eq(RefundMethodConfig::getPlatformId, platformId));

                // 3. 创建新数据
                List<RefundMethodConfig> configs = dtoList.stream().map(dto -> {
                    RefundMethodConfig config = new RefundMethodConfig();
                    config.setMethodName(dto.getMethodName());
                    config.setMethodDesc(dto.getMethodDesc());
                    config.setIsEnabled(dto.getIsEnabled() != null ? dto.getIsEnabled() : true);
                    config.setIsDefault(dto.getIsDefault() != null ? dto.getIsDefault() : false);
                    config.setPlatformId(platformId);
                    return config;
                }).toList();

                // 4. 保存新数据
                if (!configs.isEmpty()) {
                    configs.forEach(refundMethodConfigMapper::insert);
                }
                
                // 5. 对所有缓存键执行删除（确保彻底清除旧缓存）
                for (String cacheKey : cacheKeys) {
                    RedisUtil.delete(cacheKey);
                }
                
                log.info("保存退款方式配置成功, platformId: {}, 数量: {}, 已清除缓存键: {}", 
                         platformId, configs.size(), cacheKeys);
                return Boolean.TRUE;
            }, cacheKeys);
        });
    }

    // ======================== 退款原因模板相关方法 ========================

    @Override
    public List<RefundReasonVO> getEnabledRefundReasonsByScenario(Boolean scenarioRefundOnly, Boolean scenarioReturnRefund) {
        Long platformId = getPlatformId();
        
        // 构建缓存键
        String scenarioPart = "";
        if (scenarioRefundOnly != null) {
            scenarioPart += "refundOnly=" + scenarioRefundOnly;
        }
        if (scenarioReturnRefund != null) {
            scenarioPart += (scenarioPart.isEmpty() ? "" : "_") + "returnRefund=" + scenarioReturnRefund;
        }
        
        // 根据场景参数生成不同的缓存键
        String cacheKey;
        if (scenarioRefundOnly == null && scenarioReturnRefund == null) {
            // 无场景参数时使用所有启用模板的缓存键
            cacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ALL_ENABLED_CACHE_KEY, platformId);
        } else {
            // 有场景参数时使用带场景参数的缓存键
            cacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, scenarioPart, platformId);
        }
        
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_REASON_QUERY_LOCK_KEY, scenarioPart, platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据，明确处理空列表的情况
                () -> {
                    List<RefundReasonVO> cacheList = RedisUtil.getCacheList(cacheKey, RefundReasonVO.class);
                    if (CollUtil.isEmpty(cacheList)) {
                        return null;  // 如果缓存为空或不存在，返回null触发数据库查询
                    }
                    return cacheList;
                },
                // 从数据库获取数据
                () -> {
                    List<RefundReasonTemplate> templates = refundReasonTemplateMapper
                            .selectEnabledByPlatformAndScenario(platformId, scenarioRefundOnly, scenarioReturnRefund);
                    if (CollUtil.isEmpty(templates)) {
                        return new ArrayList<>();  // 返回空列表而不是null，避免缓存穿透
                    }
                    return templates.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                },
                // 设置缓存
                (reasons) -> RedisUtil.setCacheList(cacheKey, reasons),
                lockKey
        );
    }

    @Override
    public List<RefundReasonVO> getAllRefundReasons() {
        Long platformId = getPlatformId();
        String cacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ALL_CACHE_KEY, platformId);
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_REASON_QUERY_LOCK_KEY, "all", platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据，明确处理空列表的情况
                () -> {
                    List<RefundReasonVO> cacheList = RedisUtil.getCacheList(cacheKey, RefundReasonVO.class);
                    if (CollUtil.isEmpty(cacheList)) {
                        return null;  // 如果缓存为空或不存在，返回null触发数据库查询
                    }
                    return cacheList;
                },
                // 从数据库获取数据
                () -> {
                    List<RefundReasonTemplate> templates = refundReasonTemplateMapper.selectAllByPlatformId(platformId);
                    if (CollUtil.isEmpty(templates)) {
                        return new ArrayList<>();  // 返回空列表而不是null，避免缓存穿透
                    }
                    return templates.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                },
                // 设置缓存
                (reasons) -> RedisUtil.setCacheList(cacheKey, reasons),
                lockKey
        );
    }

    @Override
    public List<RefundReasonVO> getEnabledReasonsByScenario(RefundScenario scenario) {
        Long platformId = getPlatformId();
        String cacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ENABLED_BY_SCENARIO_KEY, scenario.name(), platformId);
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_REASON_QUERY_LOCK_BY_SCENARIO_KEY, scenario.name(), platformId);
        
        return RedisUtil.getCache(
                // 从缓存获取数据，明确处理空列表的情况
                () -> {
                    List<RefundReasonVO> cacheList = RedisUtil.getCacheList(cacheKey, RefundReasonVO.class);
                    if (CollUtil.isEmpty(cacheList)) {
                        return null;  // 如果缓存为空或不存在，返回null触发数据库查询
                    }
                    return cacheList;
                },
                // 从数据库获取数据
                () -> {
                    // 根据枚举转换查询条件
                    Boolean refundOnly = (scenario == RefundScenario.REFUND_ONLY) ? true : null;
                    Boolean returnRefund = (scenario == RefundScenario.RETURN_REFUND) ? true : null;
                    
                    List<RefundReasonTemplate> templates = refundReasonTemplateMapper
                            .selectEnabledByPlatformAndScenario(platformId, refundOnly, returnRefund);
                    if (CollUtil.isEmpty(templates)) {
                        return new ArrayList<>();  // 返回空列表而不是null，避免缓存穿透
                    }
                    return templates.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList());
                },
                // 设置缓存
                (reasons) -> RedisUtil.setCacheList(cacheKey, reasons),
                lockKey
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRefundReasons(List<RefundReasonTemplateDTO> dtoList) {
        Long platformId = getPlatformId();
        String lockKey = RedisUtil.key(RefundConfigConst.REFUND_REASON_SAVE_LOCK_KEY, platformId);
        return RedisUtil.lockRun(lockKey, () -> {
            // 获取所有相关的缓存键
            Set<String> cacheKeys = buildRefundReasonCacheKeys(platformId);
            
            // 执行双删逻辑：先删除缓存，执行业务，再延迟删除缓存
            return RedisUtil.doubleDeletion(() -> {
                // 1. 验证数据
                validateRefundReasonList(dtoList);
                validateReasonTextUnique(dtoList);
                validateScenarioHasEnabledTemplate(dtoList);

                // 2. 删除原数据
                refundReasonTemplateMapper.delete(new LambdaQueryWrapper<RefundReasonTemplate>()
                        .eq(RefundReasonTemplate::getPlatformId, platformId));

                // 3. 创建新数据
                List<RefundReasonTemplate> templates = dtoList.stream().map(dto -> {
                    RefundReasonTemplate template = new RefundReasonTemplate();
                    template.setReasonText(dto.getReasonText());
                    template.setTemplateType(dto.getTemplateType());
                    template.setScenarioRefundOnly(dto.getScenarioRefundOnly());
                    template.setScenarioReturnRefund(dto.getScenarioReturnRefund());
                    template.setIsEnabled(dto.getIsEnabled() != null ? dto.getIsEnabled() : true);
                    template.setPlatformId(platformId);
                    return template;
                }).toList();

                // 4. 保存新数据
                if (!templates.isEmpty()) {
                    templates.forEach(refundReasonTemplateMapper::insert);
                }
                
                // 5. 对所有缓存键执行删除（确保彻底清除旧缓存）
                for (String cacheKey : cacheKeys) {
                    RedisUtil.delete(cacheKey);
                }
                
                log.info("保存退款原因模板配置成功, platformId: {}, 数量: {}, 已清除缓存键: {}", 
                         platformId, templates.size(), cacheKeys);
                return Boolean.TRUE;
            }, cacheKeys);
        });
    }

    // ======================== 私有方法 ========================

    /**
     * 获取当前平台ID
     */
    private Long getPlatformId() {
        return ISystem.platformIdMust();
    }

    /**
     * 校验退款方式列表基础信息
     */
    private void validateRefundMethodList(List<RefundMethodConfigDTO> dtoList) {
        RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(CollUtil.isEmpty(dtoList));
        
        for (RefundMethodConfigDTO dto : dtoList) {
            RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(StrUtil.isBlank(dto.getMethodName()));
        }
    }

    /**
     * 校验退款方式名称唯一性
     */
    private void validateMethodNameUnique(List<RefundMethodConfigDTO> dtoList) {
        List<String> methodNames = dtoList.stream()
                .map(RefundMethodConfigDTO::getMethodName)
                .filter(StrUtil::isNotBlank)
                .toList();
        
        Set<String> uniqueNames = new HashSet<>(methodNames);
        RefundConfigError.REFUND_METHOD_NAME_EXISTS.trueThrow(methodNames.size() != uniqueNames.size());
    }

    /**
     * 校验至少有一个默认退款方式
     */
    private void validateHasDefaultMethod(List<RefundMethodConfigDTO> dtoList) {
        long defaultCount = dtoList.stream()
                .filter(dto -> Boolean.TRUE.equals(dto.getIsDefault()))
                .count();
        RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(defaultCount != 1);
    }

    /**
     * 校验退款原因模板列表基础信息
     */
    private void validateRefundReasonList(List<RefundReasonTemplateDTO> dtoList) {
        RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(CollUtil.isEmpty(dtoList));
        
        for (RefundReasonTemplateDTO dto : dtoList) {
            RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(StrUtil.isBlank(dto.getReasonText()));
            RefundConfigError.PARAM_VALIDATION_FAILED.trueThrow(dto.getTemplateType() == null);
        }
    }

    /**
     * 校验退款原因文案唯一性
     */
    private void validateReasonTextUnique(List<RefundReasonTemplateDTO> dtoList) {
        List<String> reasonTexts = dtoList.stream()
                .map(RefundReasonTemplateDTO::getReasonText)
                .filter(StrUtil::isNotBlank)
                .toList();
        
        Set<String> uniqueTexts = new HashSet<>(reasonTexts);
        RefundConfigError.REFUND_REASON_TEXT_EXISTS.trueThrow(reasonTexts.size() != uniqueTexts.size());
    }

    /**
     * 校验每个场景至少有一个启用的模板
     */
    private void validateScenarioHasEnabledTemplate(List<RefundReasonTemplateDTO> dtoList) {
        // 按场景分组统计启用的模板数量
        Map<Boolean, Long> refundOnlyCount = dtoList.stream()
                .filter(dto -> Boolean.TRUE.equals(dto.getScenarioRefundOnly()) && Boolean.TRUE.equals(dto.getIsEnabled()))
                .collect(Collectors.groupingBy(dto -> true, Collectors.counting()));
        
        Map<Boolean, Long> returnRefundCount = dtoList.stream()
                .filter(dto -> Boolean.TRUE.equals(dto.getScenarioReturnRefund()) && Boolean.TRUE.equals(dto.getIsEnabled()))
                .collect(Collectors.groupingBy(dto -> true, Collectors.counting()));
        
        // 检查仅退款场景
        boolean hasRefundOnlyEnabled = refundOnlyCount.getOrDefault(true, 0L) > 0;
        RefundConfigError.SCENARIO_MUST_HAVE_ENABLED_TEMPLATE.falseThrow(hasRefundOnlyEnabled);
        
        // 检查退货退款场景
        boolean hasReturnRefundEnabled = returnRefundCount.getOrDefault(true, 0L) > 0;
        RefundConfigError.SCENARIO_MUST_HAVE_ENABLED_TEMPLATE.falseThrow(hasReturnRefundEnabled);
    }

    /**
     * 构建退款方式缓存键集合
     */
    private Set<String> buildRefundMethodCacheKeys(Long platformId) {
        Set<String> cacheKeys = new HashSet<>();
        
        // 添加标准缓存键
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_METHODS_ENABLED_CACHE_KEY, platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_METHODS_ALL_CACHE_KEY, platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_METHODS_DEFAULT_CACHE_KEY, platformId));
        
        return cacheKeys;
    }

    /**
     * 构建退款原因模板缓存键集合
     */
    private Set<String> buildRefundReasonCacheKeys(Long platformId) {
        Set<String> cacheKeys = new HashSet<>();
        
        // 添加标准缓存键
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_ALL_ENABLED_CACHE_KEY, platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_ALL_CACHE_KEY, platformId));
        
        // 添加场景化缓存键
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_ENABLED_BY_SCENARIO_KEY, RefundScenario.REFUND_ONLY.name(), platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_ENABLED_BY_SCENARIO_KEY, RefundScenario.RETURN_REFUND.name(), platformId));
        
        // 添加可能的场景组合参数缓存键
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=true", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "returnRefund=true", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=true_returnRefund=true", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=false", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "returnRefund=false", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=false_returnRefund=false", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=true_returnRefund=false", platformId));
        cacheKeys.add(RedisUtil.key(RefundConfigConst.REFUND_REASONS_SCENARIO_CACHE_KEY, "refundOnly=false_returnRefund=true", platformId));
        
        return cacheKeys;
    }

    /**
     * 转换退款方式配置为VO
     */
    private RefundMethodVO convertToVO(RefundMethodConfig config) {
        RefundMethodVO vo = new RefundMethodVO();
        vo.setId(config.getId());
        vo.setMethodName(config.getMethodName());
        vo.setMethodDesc(config.getMethodDesc());
        vo.setIsEnabled(config.getIsEnabled());
        vo.setIsDefault(config.getIsDefault());
        vo.setCreateTime(config.getCreateTime());
        vo.setUpdateTime(config.getUpdateTime());
        return vo;
    }

    /**
     * 转换退款原因模板为VO
     */
    private RefundReasonVO convertToVO(RefundReasonTemplate template) {
        RefundReasonVO vo = new RefundReasonVO();
        vo.setId(template.getId());
        vo.setReasonText(template.getReasonText());
        vo.setTemplateType(template.getTemplateType());
        vo.setScenarioRefundOnly(template.getScenarioRefundOnly());
        vo.setScenarioReturnRefund(template.getScenarioReturnRefund());
        vo.setIsEnabled(template.getIsEnabled());
        vo.setCreateTime(template.getCreateTime());
        vo.setUpdateTime(template.getUpdateTime());
        return vo;
    }

    /**
     * 按场景缓存退款原因模板数据
     */
    private void cacheRefundReasonsByScenario(Long platformId, List<RefundReasonTemplate> templates) {
        if (CollUtil.isEmpty(templates)) {
            return;
        }

        // 转换所有模板为VO
        List<RefundReasonVO> allVos = templates.stream()
                .map(this::convertToVO)
                .toList();

        // 缓存所有启用模板
        List<RefundReasonVO> enabledVos = allVos.stream()
                .filter(RefundReasonVO::getIsEnabled)
                .collect(Collectors.toList());

        String allEnabledCacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ALL_ENABLED_CACHE_KEY, platformId);
        RedisUtil.setCacheList(allEnabledCacheKey, enabledVos);

        // 按场景分组缓存
        Map<Boolean, List<RefundReasonVO>> refundOnlyMap = enabledVos.stream()
                .collect(Collectors.groupingBy(RefundReasonVO::getScenarioRefundOnly));

        Map<Boolean, List<RefundReasonVO>> returnRefundMap = enabledVos.stream()
                .collect(Collectors.groupingBy(RefundReasonVO::getScenarioReturnRefund));

        // 缓存仅退款场景
        List<RefundReasonVO> refundOnlyVos = refundOnlyMap.getOrDefault(true, new ArrayList<>());
        if (!refundOnlyVos.isEmpty()) {
            String refundOnlyCacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ENABLED_BY_SCENARIO_KEY, 
                    RefundScenario.REFUND_ONLY.name(), platformId);
            RedisUtil.setCacheList(refundOnlyCacheKey, refundOnlyVos);
        }

        // 缓存退货退款场景
        List<RefundReasonVO> returnRefundVos = returnRefundMap.getOrDefault(true, new ArrayList<>());
        if (!returnRefundVos.isEmpty()) {
            String returnRefundCacheKey = RedisUtil.key(RefundConfigConst.REFUND_REASONS_ENABLED_BY_SCENARIO_KEY, 
                    RefundScenario.RETURN_REFUND.name(), platformId);
            RedisUtil.setCacheList(returnRefundCacheKey, returnRefundVos);
        }

        log.debug("缓存退款原因模板数据成功, platformId: {}, 总数量: {}, 仅退款: {}, 退货退款: {}", 
                platformId, enabledVos.size(), refundOnlyVos.size(), returnRefundVos.size());
    }
} 