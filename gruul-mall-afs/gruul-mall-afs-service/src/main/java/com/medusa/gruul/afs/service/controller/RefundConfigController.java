package com.medusa.gruul.afs.service.controller;

import com.medusa.gruul.afs.service.model.dto.RefundMethodConfigDTO;
import com.medusa.gruul.afs.service.model.dto.RefundReasonTemplateDTO;
import com.medusa.gruul.afs.service.model.enums.RefundScenario;
import com.medusa.gruul.afs.service.model.vo.RefundMethodVO;
import com.medusa.gruul.afs.service.model.vo.RefundReasonVO;
import com.medusa.gruul.afs.service.service.RefundConfigService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 退款配置控制器
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/afs/refund-config")
@RequiredArgsConstructor
public class RefundConfigController {

    private final RefundConfigService refundConfigService;

    // ======================== 退款方式相关接口 ========================

    /**
     * 获取启用的退款方式列表
     */
    @GetMapping("/methods/enabled")
    @Log("获取启用的退款方式列表")
    @PreAuthorize("permitAll()")
    public Result<List<RefundMethodVO>> getEnabledRefundMethods() {
        List<RefundMethodVO> methods = refundConfigService.getEnabledRefundMethods();
        return Result.ok(methods);
    }

    /**
     * 获取所有退款方式列表
     */
    @GetMapping("/methods")
    @Log("获取所有退款方式列表")
    @PreAuthorize("@S.platformPerm({'platformConfig','generalSet'})")
    public Result<List<RefundMethodVO>> getAllRefundMethods() {
        List<RefundMethodVO> methods = refundConfigService.getAllRefundMethods();
        return Result.ok(methods);
    }

    /**
     * 获取默认退款方式
     */
    @GetMapping("/methods/default")
    @Log("获取默认退款方式")
    @PreAuthorize("@S.platformPerm({'platformConfig','generalSet'})")
    public Result<RefundMethodVO> getDefaultRefundMethod() {
        RefundMethodVO method = refundConfigService.getDefaultRefundMethod();
        return Result.ok(method);
    }

    /**
     * 保存退款方式配置（覆盖更新）
     */
    @PostMapping("/methods/save")
    @Log("保存退款方式配置")
    @PreAuthorize("@S.platformPerm({'platformConfig','generalSet'})")
    public Result<Boolean> saveRefundMethods(@Valid @RequestBody List<RefundMethodConfigDTO> dtoList) {
        Boolean result = refundConfigService.saveRefundMethods(dtoList);
        return Result.ok(result);
    }

    // ======================== 退款原因模板相关接口 ========================

    /**
     * 根据场景获取启用的退款原因列表
     */
    @GetMapping("/reasons/enabled")
    @Log("根据场景获取启用的退款原因列表")
    @PreAuthorize("permitAll()")
    public Result<List<RefundReasonVO>> getEnabledRefundReasonsByScenario(
            @RequestParam(required = false) Boolean scenarioRefundOnly,
            @RequestParam(required = false) Boolean scenarioReturnRefund) {
        List<RefundReasonVO> reasons = refundConfigService.getEnabledRefundReasonsByScenario(
                scenarioRefundOnly, scenarioReturnRefund);
        return Result.ok(reasons);
    }

    /**
     * 根据场景获取启用的退款原因列表（简化接口）
     */
    @GetMapping("/reasons/enabled/{scenario}")
    @Log("根据场景获取启用的退款原因列表")
    @PreAuthorize("permitAll()")
    public Result<List<RefundReasonVO>> getEnabledReasonsByScenario(@PathVariable RefundScenario scenario) {
        List<RefundReasonVO> reasons = refundConfigService.getEnabledReasonsByScenario(scenario);
        return Result.ok(reasons);
    }

    /**
     * 获取所有退款原因列表
     */
    @GetMapping("/reasons")
    @Log("获取所有退款原因列表")
    @PreAuthorize("@S.platformPerm({'platformConfig','generalSet'})")
    public Result<List<RefundReasonVO>> getAllRefundReasons() {
        List<RefundReasonVO> reasons = refundConfigService.getAllRefundReasons();
        return Result.ok(reasons);
    }

    /**
     * 保存退款原因模板配置（覆盖更新）
     */
    @PostMapping("/reasons/save")
    @Log("保存退款原因模板配置")
    @PreAuthorize("@S.platformPerm({'platformConfig','generalSet'})")
    public Result<Boolean> saveRefundReasons(@Valid @RequestBody List<RefundReasonTemplateDTO> dtoList) {
        Boolean result = refundConfigService.saveRefundReasons(dtoList);
        return Result.ok(result);
    }
} 