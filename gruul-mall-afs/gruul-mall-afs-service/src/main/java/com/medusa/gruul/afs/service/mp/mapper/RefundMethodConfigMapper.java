package com.medusa.gruul.afs.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.afs.service.mp.entity.RefundMethodConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退款方式配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Mapper
public interface RefundMethodConfigMapper extends BaseMapper<RefundMethodConfig> {

    /**
     * 根据平台ID查询启用的退款方式列表
     *
     * @param platformId 平台ID
     * @return 启用的退款方式列表
     */
    List<RefundMethodConfig> selectEnabledByPlatformId(@Param("platformId") Long platformId);

    /**
     * 根据平台ID查询所有退款方式列表
     *
     * @param platformId 平台ID
     * @return 所有退款方式列表
     */
    List<RefundMethodConfig> selectAllByPlatformId(@Param("platformId") Long platformId);

    /**
     * 根据平台ID查询默认退款方式
     *
     * @param platformId 平台ID
     * @return 默认退款方式
     */
    RefundMethodConfig selectDefaultByPlatformId(@Param("platformId") Long platformId);

    /**
     * 检查退款方式名称是否重复
     *
     * @param platformId 平台ID
     * @param methodName 退款方式名称
     * @param excludeId  排除的ID（用于更新时检查）
     * @return 重复的记录数
     */
    int countByMethodName(@Param("platformId") Long platformId, 
                         @Param("methodName") String methodName, 
                         @Param("excludeId") Long excludeId);

    /**
     * 清除平台的默认退款方式标记
     *
     * @param platformId 平台ID
     * @return 更新的记录数
     */
    int clearDefaultFlag(@Param("platformId") Long platformId);

    /**
     * 设置默认退款方式
     *
     * @param id 退款方式ID
     * @return 更新的记录数
     */
    int setAsDefault(@Param("id") Long id);
} 