package com.medusa.gruul.afs.service.model.enums;

import com.medusa.gruul.global.i18n.I18N;
import com.medusa.gruul.global.model.exception.Error;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 退款配置相关错误枚举
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Getter
@RequiredArgsConstructor
public enum RefundConfigError implements Error {

    // ======================== 通用错误 ========================
    
    /**
     * 参数校验失败
     */
    PARAM_VALIDATION_FAILED(80000, "refund.param.validation.failed"),

    // ======================== 退款方式相关错误 ========================
    
    /**
     * 退款方式不存在
     */
    REFUND_METHOD_NOT_FOUND(80001, "refund.method.not.found"),
    
    /**
     * 退款方式名称已存在
     */
    REFUND_METHOD_NAME_EXISTS(80002, "refund.method.name.exists"),
    
    /**
     * 默认退款方式不能删除
     */
    DEFAULT_REFUND_METHOD_CANNOT_DELETE(80003, "refund.method.default.cannot.delete"),
    
    /**
     * 退款方式不属于当前平台
     */
    REFUND_METHOD_NOT_BELONG_PLATFORM(80007, "refund.method.not.belong.platform"),
    
    /**
     * 退款方式未启用
     */
    REFUND_METHOD_NOT_ENABLED(80008, "refund.method.not.enabled"),
    
    /**
     * 不能禁用最后一个启用的退款方式
     */
    CANNOT_DISABLE_LAST_ENABLED_METHOD(80009, "refund.method.cannot.disable.last.enabled"),

    // ======================== 退款原因模板相关错误 ========================
    
    /**
     * 退款原因模板不存在
     */
    REFUND_REASON_NOT_FOUND(80101, "refund.reason.not.found"),
    
    /**
     * 退款原因文案已存在
     */
    REFUND_REASON_TEXT_EXISTS(80102, "refund.reason.text.exists"),
    
    /**
     * 退款原因模板不属于当前平台
     */
    REFUND_REASON_NOT_BELONG_PLATFORM(80105, "refund.reason.not.belong.platform"),

    // ======================== 业务规则相关错误 ========================
    
    /**
     * 场景下必须有启用的模板
     */
    SCENARIO_MUST_HAVE_ENABLED_TEMPLATE(80203, "refund.scenario.must.have.enabled.template");

    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 错误消息key
     */
    private final String msgCode;

    @Override
    public int code() {
        return getCode();
    }

    @Override
    public String msg() {
        return I18N.msg(getMsgCode());
    }
} 