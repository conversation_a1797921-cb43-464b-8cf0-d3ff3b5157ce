package com.medusa.gruul.afs.service.service.rpc;

import com.medusa.gruul.afs.api.model.AfsCloseDTO;
import com.medusa.gruul.afs.api.model.AfsOrderItemDTO;
import com.medusa.gruul.afs.api.rpc.AfsRpcService;
import com.medusa.gruul.afs.service.mp.entity.AfsOrderItem;
import com.medusa.gruul.afs.service.mp.service.IAfsOrderItemService;
import com.medusa.gruul.afs.service.service.AfsQueryService;
import com.medusa.gruul.afs.service.service.AfsService;
import com.medusa.gruul.common.mp.model.TenantShop;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * date 2022/8/16
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class AfsRpcServiceImpl implements AfsRpcService {

    private final AfsService afsService;
    private final AfsQueryService afsQueryService;
    private final IAfsOrderItemService afsOrderItemService;

    @Override
    public void closeAfsByAfsNo(AfsCloseDTO afsClose) {
        afsService.afsClose(afsClose);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeAfsBatch(List<AfsCloseDTO> afsCloses) {
        afsCloses.forEach(afsService::afsClose);
    }
    
    @Override
    public List<AfsOrderItemDTO> getAfsOrderItemsByAfsNo(String afsNo) {
        return TenantShop.disable(() -> {
            try {
                List<AfsOrderItem> items = afsQueryService.afsOrderItems(afsNo);
                if (items == null || items.isEmpty()) {
                    log.warn("未找到售后工单商品项, afsNo: {}", afsNo);
                    return Collections.emptyList();
                }
                
                return convertToDto(items);
            } catch (Exception e) {
                log.error("获取售后工单商品项失败, afsNo: {}", afsNo, e);
                return Collections.emptyList();
            }
        });
    }
    
    @Override
    public List<AfsOrderItemDTO> listAfsOrderItemsByAfsNos(List<String> afsNos) {
        return TenantShop.disable(() -> {
            try {
                // 构建查询条件并批量获取商品项
                List<AfsOrderItem> items = afsOrderItemService.lambdaQuery()
                        .in(AfsOrderItem::getAfsNo, afsNos)
                        .list();
                
                if (items == null || items.isEmpty()) {
                    log.warn("未找到售后工单商品项, afsNos: {}", afsNos);
                    return Collections.emptyList();
                }
                
                return convertToDto(items);
            } catch (Exception e) {
                log.error("批量获取售后工单商品项失败, afsNos: {}", afsNos, e);
                return Collections.emptyList();
            }
        });
    }
    
    /**
     * 将内部实体转换为DTO对象
     *
     * @param items 内部实体列表
     * @return DTO对象列表
     */
    private List<AfsOrderItemDTO> convertToDto(List<AfsOrderItem> items) {
        return items.stream().map(item -> {
            AfsOrderItemDTO dto = new AfsOrderItemDTO();
            dto.setAfsNo(item.getAfsNo())
                    .setProductId(item.getProductId())
                    .setProductName(item.getProductName())
                    .setSkuId(item.getSkuId())
                    .setSpecs(item.getSpecs())
                    .setNum(item.getNum())
                    .setImage(item.getImage())
                    .setSalePrice(item.getSalePrice())
                    .setDealPrice(item.getDealPrice())
                    .setSellType(item.getSellType())
                    .setServices(item.getServices());
            // 注意：AfsOrderItem没有orderItemId字段，需要在使用处自行查询关联
            return dto;
        }).collect(Collectors.toList());
    }
}
