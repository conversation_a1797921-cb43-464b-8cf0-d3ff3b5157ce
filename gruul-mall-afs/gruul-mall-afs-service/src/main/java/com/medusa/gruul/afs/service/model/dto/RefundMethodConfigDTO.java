package com.medusa.gruul.afs.service.model.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 退款方式配置DTO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class RefundMethodConfigDTO {
    
    /**
     * 主键ID（更新时使用）
     */
    private Long id;
    
    /**
     * 退款方式名称
     */
    @NotBlank(message = "退款方式名称不能为空")
    private String methodName;
    
    /**
     * 退款方式描述
     */
    private String methodDesc;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
} 