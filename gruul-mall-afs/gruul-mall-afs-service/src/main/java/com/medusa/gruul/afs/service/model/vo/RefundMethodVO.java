package com.medusa.gruul.afs.service.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 退款方式VO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class RefundMethodVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 退款方式名称
     */
    private String methodName;
    
    /**
     * 退款方式描述
     */
    private String methodDesc;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 是否默认
     */
    private Boolean isDefault;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 