package com.medusa.gruul.afs.service.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款场景枚举
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum RefundScenario {
    
    /**
     * 仅退款
     */
    REFUND_ONLY(1, "仅退款"),
    
    /**
     * 退货退款
     */
    RETURN_REFUND(2, "退货退款");
    
    /**
     * 场景编码
     */
    @EnumValue
    private final Integer code;
    
    /**
     * 场景描述
     */
    private final String desc;
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static RefundScenario getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RefundScenario scenario : values()) {
            if (scenario.getCode().equals(code)) {
                return scenario;
            }
        }
        return null;
    }
} 