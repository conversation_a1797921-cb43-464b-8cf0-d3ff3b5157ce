package com.medusa.gruul.afs.service.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 退款方式配置实体
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_refund_method_config")
public class RefundMethodConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1642215769547595415L;
    /**
     * 平台ID
     */
    @TableField("platform_id")
    private Long platformId;
    
    /**
     * 退款方式名称
     */
    @TableField("method_name")
    private String methodName;
    
    /**
     * 退款方式描述
     */
    @TableField("method_desc")
    private String methodDesc;
    
    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;
    
    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean isDefault;
} 