package com.medusa.gruul.afs.service.service;

import com.medusa.gruul.afs.service.model.dto.RefundMethodConfigDTO;
import com.medusa.gruul.afs.service.model.dto.RefundReasonTemplateDTO;
import com.medusa.gruul.afs.service.model.enums.RefundScenario;
import com.medusa.gruul.afs.service.model.vo.RefundMethodVO;
import com.medusa.gruul.afs.service.model.vo.RefundReasonVO;

import java.util.List;

/**
 * 退款配置服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface RefundConfigService {

    // ======================== 退款方式相关方法 ========================

    /**
     * 获取启用的退款方式列表
     *
     * @return 启用的退款方式列表
     */
    List<RefundMethodVO> getEnabledRefundMethods();

    /**
     * 获取所有退款方式列表
     *
     * @return 所有退款方式列表
     */
    List<RefundMethodVO> getAllRefundMethods();

    /**
     * 获取默认退款方式
     *
     * @return 默认退款方式
     */
    RefundMethodVO getDefaultRefundMethod();

    /**
     * 保存退款方式配置（覆盖更新）
     *
     * @param dtoList 退款方式配置列表
     * @return 保存结果
     */
    Boolean saveRefundMethods(List<RefundMethodConfigDTO> dtoList);

    // ======================== 退款原因模板相关方法 ========================

    /**
     * 根据场景获取启用的退款原因列表
     *
     * @param scenarioRefundOnly 仅退款场景
     * @param scenarioReturnRefund 退货退款场景
     * @return 启用的退款原因列表
     */
    List<RefundReasonVO> getEnabledRefundReasonsByScenario(Boolean scenarioRefundOnly, Boolean scenarioReturnRefund);

    /**
     * 根据场景获取启用的退款原因列表（简化接口）
     *
     * @param scenario 退款场景
     * @return 启用的退款原因列表
     */
    List<RefundReasonVO> getEnabledReasonsByScenario(RefundScenario scenario);

    /**
     * 获取所有退款原因列表
     *
     * @return 所有退款原因列表
     */
    List<RefundReasonVO> getAllRefundReasons();

    /**
     * 保存退款原因模板配置（覆盖更新）
     *
     * @param dtoList 退款原因模板配置列表
     * @return 保存结果
     */
    Boolean saveRefundReasons(List<RefundReasonTemplateDTO> dtoList);
} 