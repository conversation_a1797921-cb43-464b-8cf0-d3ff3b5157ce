package com.medusa.gruul.afs.service.model.dto;

import com.medusa.gruul.afs.service.model.enums.TemplateType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 退款原因模板DTO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class RefundReasonTemplateDTO {
    
    /**
     * 模板类型
     */
    @NotNull(message = "模板类型不能为空")
    private TemplateType templateType;
    
    /**
     * 退款原因文案
     */
    @NotBlank(message = "退款原因文案不能为空")
    private String reasonText;
    
    /**
     * 仅退款场景
     */
    private Boolean scenarioRefundOnly;
    
    /**
     * 退货退款场景
     */
    private Boolean scenarioReturnRefund;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
} 