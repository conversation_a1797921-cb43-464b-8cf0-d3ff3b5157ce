package com.medusa.gruul.afs.service.model.bo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.afs.service.mp.entity.AfsOrder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.util.Map;

/**
 * 售后工单分页包装类，包含状态统计信息
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public class AfsOrderPage extends Page<AfsOrder> {

    @Serial
    private static final long serialVersionUID = 6301664126371991181L;
    /**
     * 各状态工单数量
     */
    @Getter
    @Setter
    private Map<String, Integer> statusCount;
    
    public AfsOrderPage(IPage<AfsOrder> page) {
        super(page.getCurrent(), page.getSize(), page.getTotal());
        this.setRecords(page.getRecords());
        this.setPages(page.getPages());
    }
} 