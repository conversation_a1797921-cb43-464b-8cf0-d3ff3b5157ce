package com.medusa.gruul.afs.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.medusa.gruul.afs.api.enums.AfsType;
import com.medusa.gruul.afs.service.model.bo.AfsOrderQueryBO;
import com.medusa.gruul.afs.service.mp.entity.AfsOrder;
import com.medusa.gruul.afs.service.mp.entity.AfsOrderItem;
import com.medusa.gruul.afs.service.mp.entity.RefundMethodConfig;
import com.medusa.gruul.afs.service.mp.service.IAfsOrderService;
import com.medusa.gruul.afs.service.mp.service.IRefundMethodConfigService;
import com.medusa.gruul.afs.service.service.AfsQueryService;
import com.medusa.gruul.afs.service.service.SyncMdmRefundOrderService;
import com.medusa.gruul.common.ipaas.model.order.*;
import com.medusa.gruul.common.module.app.afs.AfsStatus;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.order.api.entity.OrderDiscountItem;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.PackageStatus;
import com.medusa.gruul.order.api.enums.mdm.MdmOrderRefundTypeEnum;
import com.medusa.gruul.order.api.enums.mdm.MdmOrderStateEnum;
import com.medusa.gruul.order.api.enums.mdm.MdmPaymentMethodDetailEnum;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.payment.api.entity.PaymentRefund;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * MDM订单同步服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SyncMdmRefundOrderServiceImpl implements SyncMdmRefundOrderService {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final OrderRpcService orderRpcService;
    private final AfsQueryService afsQueryService;
    private final PaymentRpcService paymentRpcService;
    private final IAfsOrderService afsOrderService;
    private final IRefundMethodConfigService refundMethodConfigService;

    @Override
    public void syncOrder(String afsNo) {
        AfsOrder currentAfsOrder = afsQueryService.getCurrentAfsOrderDetail(afsNo).getOrNull();
        if (currentAfsOrder == null) {
            return;
        }
        String orderNo = currentAfsOrder.getOrderNo();
        AfsOrderQueryBO query = new AfsOrderQueryBO().setOrderNo(orderNo);
        List<AfsOrder> afsOrders = TenantShop.disable(() -> afsOrderService.getAfsOrderDetail(query));
        afsOrders = afsOrders.stream().filter(ao -> List.of(AfsStatus.REFUNDED, AfsStatus.RETURNED_REFUNDED).contains(ao.getStatus())).collect(Collectors.toList());
        if (CollUtil.isEmpty(afsOrders) || afsOrders.stream().noneMatch(ao -> ao.getNo().equals(currentAfsOrder.getNo()))) {
            afsOrders.add(0, currentAfsOrder);
        }

        Long shopId = currentAfsOrder.getShopId();
        Optional<ShopOrder> targetShopOrderOptional = orderRpcService.getShopOrderList(orderNo).stream()
                .filter(shopOrder -> shopOrder.getShopId().equals(shopId))
                .findFirst();
        if (targetShopOrderOptional.isEmpty()) {
            return;
        }

        List<MdmOrderSyncDTO> mdmOrderSyncDTOS = orderRpcService.buildSyncDTOS(orderNo);
        if (CollUtil.isEmpty(mdmOrderSyncDTOS)) {
            return;
        }

        Optional<MdmOrderSyncDTO> syncDTOOptional = mdmOrderSyncDTOS.stream()
                .filter(dto -> dto.getOrder() != null && dto.getOrder().getId().equals(targetShopOrderOptional.get().getId())).findFirst();
        if (syncDTOOptional.isEmpty()) {
            return;
        }

        orderRpcService.sendSyncRequest(buildRefundSyncDTO(syncDTOOptional.get(), afsOrders));
    }

    private MdmOrderSyncDTO buildRefundSyncDTO(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {

        // 设置订单退款相关信息
        syncDTO.setOrderRefund(buildOrderRefund(syncDTO, afsOrders));

        // 设置订单退款支付信息
        syncDTO.setOrderRefundPayment(buildOrderRefundPayment(syncDTO, afsOrders));

        // 设置订单退款优惠信息
        syncDTO.setOrderRefundDiscount(buildOrderRefundDiscount(syncDTO, afsOrders));

        // 设置订单退款商品信息
        syncDTO.setOrderRefundItem(buildOrderRefundItem(syncDTO, afsOrders));

        // 设置订单退款商品优惠信息
        syncDTO.setOrderRefundItemDiscount(buildOrderRefundItemDiscount(syncDTO, afsOrders));

        return syncDTO;
    }

    private List<MdmOrderRefund> buildOrderRefund(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {
        List<MdmOrderRefund> result = new ArrayList<>();
        MdmOrder order = syncDTO.getOrder();
        List<MdmOrderItem> orderItems = syncDTO.getOrderItem();
        AfsOrder currentAfsOrder = afsOrders.get(0);
        //退款方式
        MdmOrderRefundTypeEnum orderRefundTypeEnum = orderItems.size() == 1 && orderItems.get(0).getPurchaseQuantity().equals(String.valueOf(currentAfsOrder.getAfsOrderItem().getNum()))
                ? MdmOrderRefundTypeEnum.FULL : MdmOrderRefundTypeEnum.PART;
        //订单状态
        MdmOrderStateEnum orderStateEnum = orderItems.size() == 1 && orderItems.get(0).getPurchaseQuantity().equals(String.valueOf(currentAfsOrder.getAfsOrderItem().getNum()))
                ? MdmOrderStateEnum.FULL_REFUNDED : MdmOrderStateEnum.PART_REFUNDED;
        for (AfsOrder afsOrder : afsOrders) {
            MdmOrderRefund refund = new MdmOrderRefund()
                    .setId(afsOrder.getId())
                    .setOrderId(order.getId())
                    .setOrderNumber(order.getOrderNumber())
                    .setRefundNumber(afsOrder.getNo())
                    .setRefundAmount(AmountCalculateHelper.toYuan(afsOrder.getRefundAmount()).toString())
                    .setOperaterId(order.getOperaterId())
                    .setOperaterPhone(order.getOperaterPhone())
                    .setOperaterName(order.getOperaterName())
                    .setStoreId(order.getStoreId())
                    .setRefundReason(afsOrder.getReason() != null ? afsOrder.getReason().getDesc() : null)
                    .setRefundType(orderRefundTypeEnum.getValue())
                    .setRefundTypeAlias(orderRefundTypeEnum.getDescription())
                    .setDeviceNumber(order.getDeviceNumber())
                    .setRefundPaymentMethod(null)
                    .setActuallyRefundAmount(AmountCalculateHelper.toYuan(afsOrder.getRefundAmount()).toString())
                    .setRefundOriginHeader(new HashMap<>())
                    .setMemberRechargeRefundNumber(null)
                    .setDisabled(false)
                    .setCreateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                    .setUpdateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER));
            result.add(refund);
        }

        // 设置订单状态
        order.setBeforeState(order.getState());
        order.setState(orderStateEnum.getValue());

        return result;
    }

    private List<MdmOrderRefundPayment> buildOrderRefundPayment(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {
        List<MdmOrderRefundPayment> orderRefundPayment = new ArrayList<>();
        MdmOrder order = syncDTO.getOrder();
        MdmOrderPayment orderPayment = syncDTO.getOrderPayment().get(0);

        for (AfsOrder afsOrder : afsOrders) {
            Long refundMethodConfigId = afsOrder.getRefundMethodConfigId();
            // 默认原路退
            Integer paymentType = orderPayment.getPaymentType();
            Integer paymentMethodId = orderPayment.getPaymentMethodId();
            String paymentMethodName = orderPayment.getPaymentMethodName();
            if (refundMethodConfigId != null && refundMethodConfigId != 0L) {
                RefundMethodConfig refundMethodConfig = refundMethodConfigService.lambdaQuery()
                        .select(RefundMethodConfig::getCode, RefundMethodConfig::getMethodName, RefundMethodConfig::getIsDefault)
                        .eq(RefundMethodConfig::getId, refundMethodConfigId)
                        .one();
                // 不是原路退
                if (refundMethodConfig != null && refundMethodConfig.getIsDefault() == Boolean.FALSE) {
                    paymentType = MdmPaymentMethodDetailEnum.PRIVATE_MALL_PAYMENT.getValue();
                    paymentMethodId = refundMethodConfig.getCode();
                    paymentMethodName = refundMethodConfig.getMethodName();
                }
            }
            PaymentRefund paymentRefund = paymentRpcService.getPaymentRefundByAfsNum(afsOrder.getNo());
            MdmOrderRefundPayment refundPayment = new MdmOrderRefundPayment()
                    .setId(paymentRefund.getId())
                    .setOrderRefundId(afsOrder.getId())
                    .setOrderNumber(order.getOrderNumber())
                    .setRefundNumber(afsOrder.getNo())
                    .setRefundPaymentType(paymentType)
                    .setRefundPaymentId(null)
                    .setRefundPaymentMethodId(paymentMethodId)
                    .setRefundPaymentName(paymentMethodName)
                    .setRefundPaymentNumber(orderPayment.getPaymentNumber())
                    .setRefundPaymentAmount(AmountCalculateHelper.toYuan(afsOrder.getRefundAmount()).toString())
                    .setIsRefundSuccess(Boolean.TRUE)
                    .setIsPreRefundSuccess(Boolean.TRUE)
                    .setRefundSuccessTime(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                    .setRefundOriginHeader(new HashMap<>())
                    .setIsMemberRechargeRefundSuccess(false)
                    .setMemberRechargeRefundSuccessTime(null)
                    .setSort(0)
                    .setDisabled(false)
                    .setCreateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                    .setUpdateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER));
            orderRefundPayment.add(refundPayment);
        }

        return orderRefundPayment;
    }

    private List<MdmOrderRefundDiscount> buildOrderRefundDiscount(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {
        List<MdmOrderRefundDiscount> result = new ArrayList<>();
        MdmOrder order = syncDTO.getOrder();
        List<MdmOrderDiscount> orderDiscounts = syncDTO.getOrderDiscount();
        List<Long> discountIds = orderDiscounts.stream().map(MdmOrderDiscount::getId).toList();
        if (CollUtil.isEmpty(discountIds)) {
            return result;
        }
        List<OrderDiscountItem> orderDiscountItems = orderRpcService.getOrderDiscountItemsByDiscountIds(discountIds);
        if (CollUtil.isEmpty(orderDiscountItems)) {
            return result;
        }

        AfsOrder currentAfsOrder = afsOrders.get(0);
        for (OrderDiscountItem discountItem : orderDiscountItems) {
            if (currentAfsOrder.getShopOrderItemId().equals(discountItem.getItemId())) {
                Optional<MdmOrderDiscount> orderDiscountOptional = orderDiscounts.stream()
                        .filter(orderDiscount -> orderDiscount.getId().equals(discountItem.getDiscountId())).findFirst();
                if (orderDiscountOptional.isEmpty()) {
                    continue;
                }
                MdmOrderDiscount orderDiscount = orderDiscountOptional.get();

                MdmOrderRefundDiscount refundDiscount = new MdmOrderRefundDiscount()
                        .setId(discountItem.getId())
                        .setOrderRefundId(currentAfsOrder.getId())
                        .setOrderNumber(order.getOrderNumber())
                        .setRefundNumber(currentAfsOrder.getNo())
                        .setOrderDiscountId(orderDiscount.getId())
                        .setDiscountAmount(AmountCalculateHelper.toYuan(discountItem.getDiscountAmount()).toString())
                        .setDiscountName(orderDiscount.getDiscountName())
                        .setIsRefundSuccess(Boolean.TRUE)
                        .setDisabled(false)
                        .setCreateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                        .setUpdateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER));
                result.add(refundDiscount);
            }
        }

        if (CollUtil.isEmpty(orderDiscountItems)) {
            return result;
        }

        return result;
    }

    private List<MdmOrderRefundItem> buildOrderRefundItem(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {
        List<MdmOrderRefundItem> result = new ArrayList<>();
        MdmOrder order = syncDTO.getOrder();

        for (AfsOrder afsOrder : afsOrders) {
            AfsOrderItem afsOrderItem = afsOrder.getAfsOrderItem();
            List<ShopOrderItem> shopOrderItems = orderRpcService.getShopOrderItemList(afsOrder.getOrderNo());
            if (CollUtil.isEmpty(shopOrderItems)) {
                return result;
            }

            for (ShopOrderItem item : shopOrderItems) {
                // 只处理与本次售后相关的商品
                if (afsOrder.getShopOrderItemId().equals(item.getId())) {
                    MdmOrderRefundItem refundItem = new MdmOrderRefundItem()
                            .setId(item.getId())
                            .setOrderRefundId(afsOrder.getId())
                            .setOrderNumber(order.getOrderNumber())
                            .setRefundNumber(afsOrder.getNo())
                            .setOrderItemId(item.getId())
                            // 已发货仅退款，设置为0
                            .setRefundQuantity(afsOrder.getType() == AfsType.REFUND && afsOrder.getPackageStatus() != PackageStatus.WAITING_FOR_DELIVER ? "0" : String.valueOf(afsOrderItem.getNum()))
                            .setRefundOriginPrice(AmountCalculateHelper.toYuan(afsOrderItem.getDealPrice()).toString())
                            .setRefundPrice(AmountCalculateHelper.toYuan(afsOrderItem.getDealPrice()).toString())
                            .setActuallyRefundItemAmount(AmountCalculateHelper.toYuan(afsOrderItem.getDealPrice()).toString())
                            .setIsRefundSuccess(Boolean.TRUE)
                            .setSellingPrice(AmountCalculateHelper.toYuan(afsOrderItem.getSalePrice()).toString())
                            .setDisabled(false)
                            .setCreateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                            .setUpdateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER));
                    result.add(refundItem);
                }
            }
        }

        return result;
    }

    private List<MdmOrderRefundItemDiscount> buildOrderRefundItemDiscount(MdmOrderSyncDTO syncDTO, List<AfsOrder> afsOrders) {
        List<MdmOrderRefundItemDiscount> result = new ArrayList<>();
        MdmOrder order = syncDTO.getOrder();
        List<MdmOrderDiscount> orderDiscounts = syncDTO.getOrderDiscount();
        List<Long> discountIds = orderDiscounts.stream().map(MdmOrderDiscount::getId).toList();
        if (CollUtil.isEmpty(discountIds)) {
            return result;
        }
        List<OrderDiscountItem> orderDiscountItems = orderRpcService.getOrderDiscountItemsByDiscountIds(discountIds);
        if (CollUtil.isEmpty(orderDiscountItems)) {
            return result;
        }

        for (AfsOrder afsOrder : afsOrders) {
            for (OrderDiscountItem discountItem : orderDiscountItems) {
                if (afsOrder.getShopOrderItemId().equals(discountItem.getItemId())) {
                    Optional<MdmOrderDiscount> orderDiscountOptional = orderDiscounts.stream()
                            .filter(orderDiscount -> orderDiscount.getId().equals(discountItem.getDiscountId())).findFirst();
                    if (orderDiscountOptional.isEmpty()) {
                        continue;
                    }
                    MdmOrderDiscount orderDiscount = orderDiscountOptional.get();

                    MdmOrderRefundItemDiscount refundItemDiscount = new MdmOrderRefundItemDiscount()
                            .setId(discountItem.getId())
                            .setOrderNumber(order.getOrderNumber())
                            .setOrderRefundId(afsOrder.getId())
                            .setRefundNumber(afsOrder.getNo())
                            .setOrderRefundItemId(discountItem.getItemId())
                            .setDiscountType(orderDiscount.getDiscountType())
                            .setDiscountAmount(AmountCalculateHelper.toYuan(discountItem.getDiscountAmount()).toString())
                            .setDiscountName(orderDiscount.getDiscountName())
                            .setDisabled(false)
                            .setCreateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                            .setUpdateAt(LocalDateTime.now().format(DATE_TIME_FORMATTER));
                    result.add(refundItemDiscount);
                }
            }
        }

        return result;
    }

}