package com.medusa.gruul.afs.service.model.vo;

import com.medusa.gruul.afs.service.model.enums.TemplateType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 退款原因VO
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class RefundReasonVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 模板类型
     */
    private TemplateType templateType;
    
    /**
     * 退款原因文案
     */
    private String reasonText;
    
    /**
     * 仅退款场景
     */
    private Boolean scenarioRefundOnly;
    
    /**
     * 退货退款场景
     */
    private Boolean scenarioReturnRefund;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 