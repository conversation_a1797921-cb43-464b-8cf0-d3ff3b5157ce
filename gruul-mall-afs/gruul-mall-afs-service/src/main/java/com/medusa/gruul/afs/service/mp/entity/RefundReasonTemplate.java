package com.medusa.gruul.afs.service.mp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.afs.service.model.enums.TemplateType;
import com.medusa.gruul.common.mp.model.BaseEntity;

import java.io.Serial;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 退款原因模板实体
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_refund_reason_template")
public class RefundReasonTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 8862626162341680237L;
    /**
     * 平台ID
     */
    @TableField("platform_id")
    private Long platformId;
    
    /**
     * 模板类型
     */
    @TableField("template_type")
    private TemplateType templateType;
    
    /**
     * 退款原因文案
     */
    @TableField("reason_text")
    private String reasonText;
    
    /**
     * 仅退款场景
     */
    @TableField("scenario_refund_only")
    private Boolean scenarioRefundOnly;
    
    /**
     * 退货退款场景
     */
    @TableField("scenario_return_refund")
    private Boolean scenarioReturnRefund;
    
    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;
} 