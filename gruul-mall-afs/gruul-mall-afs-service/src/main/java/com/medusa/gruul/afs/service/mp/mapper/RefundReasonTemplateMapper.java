package com.medusa.gruul.afs.service.mp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.afs.service.mp.entity.RefundReasonTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 退款原因模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Mapper
public interface RefundReasonTemplateMapper extends BaseMapper<RefundReasonTemplate> {

    /**
     * 根据平台ID和场景查询启用的退款原因模板列表
     *
     * @param platformId 平台ID
     * @param scenarioRefundOnly 仅退款场景
     * @param scenarioReturnRefund 退货退款场景
     * @return 启用的退款原因模板列表
     */
    List<RefundReasonTemplate> selectEnabledByPlatformAndScenario(@Param("platformId") Long platformId,
                                                                 @Param("scenarioRefundOnly") Boolean scenarioRefundOnly,
                                                                 @Param("scenarioReturnRefund") Boolean scenarioReturnRefund);

    /**
     * 根据平台ID查询所有退款原因模板列表
     *
     * @param platformId 平台ID
     * @return 所有退款原因模板列表
     */
    List<RefundReasonTemplate> selectAllByPlatformId(@Param("platformId") Long platformId);

    /**
     * 检查退款原因文案是否重复
     *
     * @param platformId 平台ID
     * @param reasonText 退款原因文案
     * @param excludeId  排除的ID（用于更新时检查）
     * @return 重复的记录数
     */
    int countByReasonText(@Param("platformId") Long platformId, 
                         @Param("reasonText") String reasonText, 
                         @Param("excludeId") Long excludeId);

    /**
     * 统计平台下指定场景的启用模板数量
     *
     * @param platformId 平台ID
     * @param scenarioRefundOnly 仅退款场景
     * @param scenarioReturnRefund 退货退款场景
     * @return 启用模板数量
     */
    int countEnabledByScenario(@Param("platformId") Long platformId,
                              @Param("scenarioRefundOnly") Boolean scenarioRefundOnly,
                              @Param("scenarioReturnRefund") Boolean scenarioReturnRefund);

    /**
     * 统计平台下指定场景的启用模板数量（排除指定ID）
     *
     * @param platformId 平台ID
     * @param scenarioRefundOnly 仅退款场景
     * @param scenarioReturnRefund 退货退款场景
     * @param excludeId 排除的ID
     * @return 启用模板数量
     */
    int countEnabledByScenarioExcludeId(@Param("platformId") Long platformId,
                                       @Param("scenarioRefundOnly") Boolean scenarioRefundOnly,
                                       @Param("scenarioReturnRefund") Boolean scenarioReturnRefund,
                                       @Param("excludeId") Long excludeId);
} 