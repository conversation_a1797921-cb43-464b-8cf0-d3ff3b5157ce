package com.medusa.gruul.common.system.model.model;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.medusa.gruul.common.model.constant.CommonPool;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * APP平台类型
 * <AUTHOR>
 * date 2021/11/30
 */
@Getter
@RequiredArgsConstructor
public enum Platform {
    /**
     * 微信小程序
     */
    WECHAT_MINI_APP(0),
    /**
     * 公众号
     */
    WECHAT_MP(1),
    /**
     * PC端
     */
    PC(2),
    /**
     * 移动端端h5
     */
    H5(3),
    /**
     * IOS
     */
    IOS(4),
    /**
     * ANDROID
     */
    ANDROID(5),
    /**
     * HARMONY
     */
    HARMONY(6),
    /**
     * 聚合支付
     */
    AGGREGATION(7);


    @EnumValue
    private final Integer value;

    /**
     * 根据平台转换来源
     *
     * @param platform 平台
     * @return 来源
     */
    public static int convertSourceType(Platform platform) {
        if (platform == null){
            return CommonPool.SOURCE_TYPE_PC;
        }

        return switch (platform) {
            case WECHAT_MINI_APP -> CommonPool.SOURCE_TYPE_WECHAT_APPLET;
            case H5 -> CommonPool.SOURCE_TYPE_H5;
            default -> CommonPool.SOURCE_TYPE_PC;
        };
    }
}
