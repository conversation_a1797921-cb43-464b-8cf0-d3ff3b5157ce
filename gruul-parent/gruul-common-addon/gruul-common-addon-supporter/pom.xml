<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-common-addon</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-addon-supporter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-dubbo-rpc</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-addon-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>