package com.medusa.gruul.common.addon.model.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * date 2022/3/4
 */
@Getter
@Setter
@Accessors(chain = true)
public class AddonVersionMenu implements Serializable {

	@Serial
	private static final long serialVersionUID = -6136389285528463586L;
	/**
	 * 插件名称 默认为服务名
	 */
	private String addonName;
	/**
	 * 版本号
	 */
	private String version;

	/**
	 * 菜单列表
	 */
	private List<? extends AddonMenu> menus;
}
