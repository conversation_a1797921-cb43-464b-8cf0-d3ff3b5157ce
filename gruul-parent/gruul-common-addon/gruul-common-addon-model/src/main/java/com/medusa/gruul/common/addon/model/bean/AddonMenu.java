package com.medusa.gruul.common.addon.model.bean;

import com.medusa.gruul.common.security.model.enums.MenuType;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 插件导航栏菜单
 *
 * <AUTHOR>
 * date 2022/3/2
 */
@Getter
@Setter
@Accessors(chain = true)
public class AddonMenu implements Serializable {

	@Serial
	private static final long serialVersionUID = -1320272897107247007L;

	/**
	 * 是否是 管理员 独占
	 */
	@NotNull
	private final Boolean unshared = Boolean.FALSE;

	/**
	 * 类型 目录:CATALOG 菜单:MENU
	 */
	@NotNull
	private MenuType type;

	/**
	 * 排序
	 */
	@NotNull
	@Min(value = 1)
	private final Integer order = 1000;

	/**
	 * 名称
	 */
	@NotBlank
	private String name;

	/**
	 * 导航菜单图标
	 */
	private String icon;

	/**
	 * 菜单路由路径 仅当类型是菜单时不为空
	 */
	private String path;

	/**
	 * 权限设置
	 */
	@NotBlank
	private String perm;

	/**
	 * 菜单组件 仅当类型是菜单时不为空 比如 umd.js文件请求路径
	 */
	private AddonComponent component;

	/**
	 * 菜单列表 仅当类型为目录时不为空
	 */
	private List<? extends AddonMenu> children;

}
