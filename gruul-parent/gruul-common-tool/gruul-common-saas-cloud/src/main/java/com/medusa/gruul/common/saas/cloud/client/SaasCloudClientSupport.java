package com.medusa.gruul.common.saas.cloud.client;

import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.common.saas.cloud.properties.SaasCloudProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@Slf4j
public class SaasCloudClientSupport {

    private static final String CONTENT_TYPE = "Content-type";
    private static final String FORM_CONTENT_TYPE = "application/x-www-form-urlencoded;charset=utf-8";
    private static final String JSON_CONTENT_TYPE = "application/json;charset=utf-8";
    private static final String CLIENT_CREDENTIALS = "grant_type=client_credentials";
    private static final String API_TOKEN_URL = "/realms/paas/protocol/openid-connect/token";
    private static final int TOKEN_EXPIRE_HOURS = 1;

    private final HttpClient httpClient = HttpClient.newHttpClient();
    private SaasCloudProperties saasCloudProperties;

    public SaasCloudClientSupport(SaasCloudProperties saasCloudProperties) {
        this.saasCloudProperties = saasCloudProperties;
    }

    /**
     * 发送GET请求
     *
     * @param url 请求地址
     * @return 响应结果
     */
    public String get(String url) {
        try {
            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .GET()
                    .build();
            HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (Exception e) {
            log.error("请求saas-cloud服务失败, url: {}", url, e);
            throw new GlobalException("请求saas-cloud服务失败");
        }
    }

    /**
     * 发送表单格式的POST请求
     *
     * @param request 请求参数
     * @param url     请求地址
     * @return 响应结果
     */
    public String apiPostFormData(String request, String url) {
        return wrapHttpRequestAndSend(request, url, FORM_CONTENT_TYPE, null);
    }

    /**
     * 发送JSON格式的POST请求
     *
     * @param request 请求参数
     * @param url     请求地址
     * @return 响应结果
     */
    public String apiPostJsonData(String request, String url) {
        return wrapHttpRequestAndSend(request, url, JSON_CONTENT_TYPE, null);
    }

    /**
     * 发送带额外请求头的JSON格式POST请求
     *
     * @param request      请求参数
     * @param url          请求地址
     * @param extraHeaders 额外的请求头
     * @return 响应结果
     */
    public String apiPostJsonDataWithHeaders(String request, String url, Map<String, String> extraHeaders) {
        return wrapHttpRequestAndSend(request, url, JSON_CONTENT_TYPE, extraHeaders);
    }

    /**
     * 包装HTTP请求并发送
     *
     * @param request      请求参数
     * @param url          请求地址
     * @param contentType  请求头类型
     * @param extraHeaders 额外的请求头
     * @return 响应结果
     */
    private String wrapHttpRequestAndSend(String request, String url, String contentType, Map<String, String> extraHeaders) {
        try {
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                    .header(CONTENT_TYPE, contentType)
                    .uri(URI.create(saasCloudProperties.getApiUrl() + url))
                    .POST(HttpRequest.BodyPublishers.ofString(request));

            if (extraHeaders != null) {
                extraHeaders.forEach(requestBuilder::header);
            }
            HttpResponse<String> response = httpClient.send(requestBuilder.build(), HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (Exception e) {
            log.error("请求saas-cloud服务失败, url: {}, request: {}", url, request, e);
            throw new GlobalException("请求saas-cloud服务失败");
        }
    }

}
