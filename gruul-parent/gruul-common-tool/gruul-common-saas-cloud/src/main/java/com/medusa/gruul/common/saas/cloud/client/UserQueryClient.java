package com.medusa.gruul.common.saas.cloud.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.saas.cloud.model.SaasCloudResponse;
import com.medusa.gruul.common.saas.cloud.model.SaasCloudUserAndEnterpriseDTO;
import com.medusa.gruul.common.saas.cloud.model.UserQueryReqDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
public class UserQueryClient implements SaasCloudClient<UserQueryReqDTO, SaasCloudUserAndEnterpriseDTO> {

    private SaasCloudClientSupport clientSupport;

    private String userQueryUrl = "/merchant/user/queryInfo";

    public UserQueryClient(SaasCloudClientSupport clientSupport) {
        this.clientSupport = clientSupport;
    }

    @Override
    public SaasCloudUserAndEnterpriseDTO execute(UserQueryReqDTO request) {
        String response = clientSupport.apiPostJsonData(JSON.toJSONString(request), userQueryUrl);
        log.info("saas-cloud UserQuery response :{}", JSON.toJSONString(response));
        SaasCloudResponse<SaasCloudUserAndEnterpriseDTO> saasCloudResponse = JSON.parseObject(response, new TypeReference<>() {
        });
        if (saasCloudResponse == null || Objects.isNull(saasCloudResponse.getTdata())) {
            return null;
        }
        return saasCloudResponse.getTdata();
    }
}
