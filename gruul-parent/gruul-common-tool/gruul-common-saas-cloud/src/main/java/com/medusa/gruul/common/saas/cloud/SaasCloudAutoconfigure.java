package com.medusa.gruul.common.saas.cloud;

import com.medusa.gruul.common.saas.cloud.client.SaasCloudClientSupport;
import com.medusa.gruul.common.saas.cloud.properties.SaasCloudProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@EnableConfigurationProperties(SaasCloudProperties.class)
@Configuration
public class SaasCloudAutoconfigure {

    @Bean(name = "saasCloudClientSupport")
    public SaasCloudClientSupport saasCloudClientSupport(SaasCloudProperties saasCloudProperties) {
        return new SaasCloudClientSupport(saasCloudProperties);
    }

}
