package com.medusa.gruul.common.member.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2021-12-21 12:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class GradeEquitiesInfoDetailVO {

    /**
     * 权益信息返回vo
     */
    private EquitiesInfoVO hsaEquitiesInfo;

    /**
     * 成长值规则返回VO
     */
    private EquitiesRuleVO equitiesRule;

}
