package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 优惠应用结果: 最终返回给前端
 *
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderShowVO implements Serializable {

    /**
     * 结算台规则基础
     */
    @ApiModelProperty("结算台规则基础")
    private SettlementApplyRuleDetailVO settlementApplyRuleDetailVO;

    /**
     * 订单
     */
    @ApiModelProperty("订单详情")
    private SettlementApplyOderInfoVO oderInfo;

    /**
     * 商品
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

    /**
     * 可用优惠
     */
    @ApiModelProperty("优惠列表")
    private List<SettlementApplyDiscountShowVO> discountList;

    /**
     * 共享优惠商品明细
     */
    @ApiModelProperty("共享优惠商品明细")
    private List<SettlementApplyShareCommodityVO> shareCommodityVOList;

    /**
     * 提示
     */
    @ApiModelProperty("提示信息")
    private String tips;

    /**
     * 构造一个可用空对象
     *
     * @return 空对象
     */
    public static SettlementApplyOrderShowVO buildEmpty() {
        SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO()
                .setDiscountAmount(BigDecimal.ZERO);
        return new SettlementApplyOrderShowVO()
                .setOderInfo(oderInfo)
                .setCommodityList(new ArrayList<>())
                .setDiscountList(new ArrayList<>());

    }
}
