package com.medusa.gruul.common.member.enums;

/**
 * @program: member-marketing
 * @description: 权益规则限制类型枚举
 */
public enum EquitiesLimitedTypeEnum {

    /**
     * 不限制
     */
    UN_LIMITED(0,"不限制"),

    /**
     * 限制
     */
    LIMITED(1,"限制"),

    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    EquitiesLimitedTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
