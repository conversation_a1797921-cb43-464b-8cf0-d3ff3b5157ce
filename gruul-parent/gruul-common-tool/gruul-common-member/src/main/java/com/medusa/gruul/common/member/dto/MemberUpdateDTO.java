package com.medusa.gruul.common.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 会员
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberUpdateDTO {
    /**
     * 会员GUID
     */
    private String memberGuid;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 头像
     */
    private String headImgUrl;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 资料项
     */
    private String dataItemJson;
}
