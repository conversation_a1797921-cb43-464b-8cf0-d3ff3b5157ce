package com.medusa.gruul.common.member.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WeChatConfigInfoQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * appLogo
     */
    private String appLogo;

    /**
     * appName
     */
    private String appName;

    /**
     * 私钥
     */
    private String applyPrivateKey;

    /**
     * appId
     */
    private String appId;
}
