package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 优惠列表
 *
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyDiscountShowVO implements Serializable {

    /**
     * 展示优惠名称
     * @see SettlementDiscountShowTypeEnum
     */
    @ApiModelProperty(value = "优惠类型")
    @JsonInclude
    private Integer showType;

    /**
     * 优惠名称
     */
    @ApiModelProperty(value = "优惠名称")
    @JsonInclude
    private String showName;

    /**
     * 优惠金额
     * todo 展示顺序：金额倒序
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;
    
    /**
     * 具体优惠项
     */
    @ApiModelProperty(value = "具体优惠项")
    private List<SettlementApplyDiscountDetailVO> discountList;
}
