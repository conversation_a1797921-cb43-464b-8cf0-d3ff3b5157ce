package com.medusa.gruul.common.member.dto.settlement;


import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * 商品
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyCommodityDTO implements Serializable {

    private static final long serialVersionUID = 3559064153837638379L;

    /**
     * 唯一id
     */
    @ApiModelProperty("唯一id")
    @NotEmpty(message = "唯一id")
    private String rid;

    /**
     * 商品id
     */
    @ApiModelProperty("商品唯一id")
    @NotEmpty(message = "商品id必传")
    private String commodityId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    @NotEmpty(message = "商品数量必传")
    private BigDecimal commodityNum;
    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    @NotEmpty(message = "商品单价必传")
    private BigDecimal commodityPrice;


    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品总额,减少计算次数
     * 缓存 :一进来就算好
     */
    @ApiModelProperty("商品总额")
    private BigDecimal commodityTotalPrice = BigDecimal.ZERO;

    /**
     * 商品 优惠总额金额
     */
    @ApiModelProperty("商品优惠总额")
    private BigDecimal discountFee = BigDecimal.ZERO;

    /**
     * 不为空 表示这个商品有改价
     * 改价后的单金额
     */
    private BigDecimal discountPriceInShopCar;

    /**
     * 改价后的总额
     */
    private BigDecimal discountTotalPriceInShopCar;

    /**
     * 经过优惠后的商品总额（后端字段）  计算商品总额 优先此字段
     */
    private BigDecimal afterDiscountTotalPrice;

    /**
     * 商品优惠总金额 = 单品优惠 x 数量
     */
    @ApiModelProperty("商品优惠金额")
    private BigDecimal commodityMinistryPrice = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 商品类型  只计算销售中心且无改价的商品
     * 2无码加购  3 快速结账商品  1 来自于销售中心的商品  11 扫码加购的商品
     */
    private Integer goodsType;

    /**
     * 商品 优惠总数量
     */
    @ApiModelProperty("优惠总数量")
    private BigDecimal exchangeDiscountNum = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 门店
     * 档口也传对应的门店
     */
    @ApiModelProperty("门店")
    private String storeGuid;


    /**
     * 唯一主键
     *
     * @return 主键
     */
    public String key() {
        return rid + commodityId;
    }
}
