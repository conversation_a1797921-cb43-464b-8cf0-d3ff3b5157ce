package com.medusa.gruul.common.member.dto.settlement;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * 订单优惠锁定
 */
@Data
@Accessors(chain = true)
public class SettlementOrderLockDTO implements Serializable {

    /**
     * 订单入参
     */
    @ApiModelProperty("锁定订单相关入参")
    private SettlementLockedOrderInfoDTO orderInfo;
    /**
     * 已选优惠： 计算使用
     * 更新时 可为空
     * todo 要按使用顺序传递过来
     */
    @ApiModelProperty("已选优惠")
    private List<SettlementLockedDiscountReqDTO> checkDiscountList;

    /**
     * 参与的商品列表
     */
    private List<ApplyRecordCommodity> limitSpecialsRecordCommodities;

    private Integer codeType;

}
