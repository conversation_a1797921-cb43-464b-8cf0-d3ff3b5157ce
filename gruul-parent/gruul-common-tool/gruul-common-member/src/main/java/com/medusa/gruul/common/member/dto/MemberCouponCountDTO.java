package com.medusa.gruul.common.member.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 优惠券统计
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MemberCouponCountDTO {

    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    @NotBlank(message = "会员guid必填")
    private String memberGuid;

    /**
     * 0可使用（默认） 1历史
     */
    private Integer useType = 0;

    /**
     * 过期时间
     */
    @JsonIgnore
    private LocalDateTime expireDate;
}
