package com.medusa.gruul.common.member.enums.coupon;


/**
 * 优惠券发券方式枚举
 *
 * <AUTHOR>
 */
public enum CouponPackageTypeEnum {

    /**
     * 定向发券
     */
    COUPON_PACKAGE_DIRECTIONAL(0, "定向发券", "发劵宝"),

    /**
     * 自助领券
     */
    COUPON_PACKAGE_SELF(10, "自助领券", "发劵宝"),

    /**
     * 充值赠送
     */
    RECHARGE_GIFT(12, "充值赠送", "储值营销"),

    /**
     * 进店有礼
     */
    COUPON_PACKAGE_GIFT(20, "进店有礼", "发劵宝"),

    /**
     * 手动发放
     */
    COUPON_ADMIN_SEND(21, "手动发放", "手动发放"),


    /**
     * 兑换活动
     */
    COUPON_REDEEM_ACTIVITY(22, "兑换码活动", "兑换码");

    /**
     * code
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    /**
     * 场景
     */
    private final String scene;

    /**
     * 构造
     *
     * @param code  参数值
     * @param des   信息描述
     * @param scene
     */
    CouponPackageTypeEnum(int code, String des, String scene) {
        this.code = code;
        this.des = des;
        this.scene = scene;
    }

    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        CouponPackageTypeEnum[] values = CouponPackageTypeEnum.values();
        for (CouponPackageTypeEnum typeEnum : values) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }

    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getSceneByCode(int code) {
        CouponPackageTypeEnum[] values = CouponPackageTypeEnum.values();
        for (CouponPackageTypeEnum typeEnum : values) {
            if (typeEnum.getCode() == code) {
                return typeEnum.scene + "-" + typeEnum.getDes();
            }
        }
        return "";
    }

    public String getDes() {
        return des;
    }

    public int getCode() {
        return code;
    }
}
