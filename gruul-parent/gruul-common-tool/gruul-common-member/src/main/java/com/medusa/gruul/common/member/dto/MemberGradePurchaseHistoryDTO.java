package com.medusa.gruul.common.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import org.jetbrains.annotations.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;



import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员等级购买流水查询对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors (chain = true)
public class MemberGradePurchaseHistoryDTO  implements Serializable {

    @Serial
    private static final long serialVersionUID = -5796217683329267792L;


    private String guid;

    private String no;

    private String memberInfoGuid;

    private String memberInfoName;

    private String userPhone;

    private String memberInfoGradeGuid;

    private String memberInfoGradeName;


    private Integer vipGrade;


    private Integer effectiveDurationType;


    private BigDecimal payAmount;


    private String payType;


    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    private String orderNo;

    private Integer type;

    private String operSubjectGuid;

    private String remark;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 来源
     * @see com.medusa.gruul.common.member.enums.SourceTypeEnum
     */
    private Integer source;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人
     */
    private String operatorName;
} 