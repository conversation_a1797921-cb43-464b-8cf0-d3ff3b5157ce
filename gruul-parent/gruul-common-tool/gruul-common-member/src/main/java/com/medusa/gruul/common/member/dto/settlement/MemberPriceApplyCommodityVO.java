package com.medusa.gruul.common.member.dto.settlement;

import com.medusa.gruul.common.member.vo.EquitiesStoreRuleVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: member-marketing
 * @description: 会员折扣适用商品
 * @author: rw
 * @create: 2022-02-09 10:21
 */
@Data
@Accessors(chain = true)
public class MemberPriceApplyCommodityVO implements Serializable {

    /**
     * 折扣
     */
    private BigDecimal discountDynamics;

    /**
     * -1：全部商品适用 0：适用商品 1：不适用商品
     */
    private Integer applyGoodsType;

    /**
     * 商品编码
     */
    private List<String> commodityCode;

    /**
     * 商品id
     */
    private List<String> commodityId;

    /**
     * 门店数据集合
     */
    private List<EquitiesStoreRuleVO> equitiesStoreRuleVOList;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     *
     * 部分门店
     */
    private Integer applicableAllStore;

    /**
     * @see com.holderzone.member.common.enums.member.GradeTypeEnum
     */
    private Integer gradeType;


    /**
     * 业务
     */
    private List<String> businessList;

}
