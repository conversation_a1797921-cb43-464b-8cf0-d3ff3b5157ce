package com.medusa.gruul.common.member.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 权益信息返回vo
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EquitiesInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权益基础信息id
     */
    private Long id;

    /**
     * 权益基础信息guid
     */
    private String guid;

    /**
     * 权益所属的企业guid
     */
    private String enterpriseGuid;

    /**
     * 权益名称
     */
    private String equitiesName;

    /**
     * 权益图片
     */
    private String equitiesImg;

    /**
     * 权益描述
     */
    private String equitiesExplain;

    /**
     * 权益适用类型
     * 1:全部运营主体  2:部分运营主体
     * com.holderzone.member.common.enums.member.EquitiesSubjectTypeEnum
     */
    private Integer equitiesType;

    /**
     * 权益适用的运营主体
     *
     */
    private List<String> operSubjectGuidList;

    /**
     * 是否开启 0：正常  1：禁用  2:草稿
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesStatusEnum
     */
    private Integer isStatus;

    /**
     * 权益类型 0:折扣权益 1:成长值权益 2:线下权益 3:积分权益 4:新品优先权 5:消费项权益
     */
    private Integer type;

    /**
     * 是否默认
     */
    private Integer isDefault;

}
