package com.medusa.gruul.common.member.enums.coupon;


import lombok.Getter;

/**
 * 优惠券类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum CouponTypeEnum {

    /**
     * 代金券
     */
    COUPON_VOUCHER(0, "代金券"),

    /**
     * 折扣券
     */
    COUPON_DISCOUNT(10, "折扣券"),

    /**
     * 兑换券
     */
    COUPON_EXCHANGE(20, "兑换券"),

    /**
     * 随机金额券
     */
    COUPON_RANDOM_AMOUNT(30, "随机金额券"),

    /**
     * 商品券
     */
    COUPON_PRODUCT(40, "商品券");

    /**
     * code
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    /**
     * 构造
     *
     * @param code 参数值
     * @param des  信息描述
     */
    CouponTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        CouponTypeEnum[] businessMessages = CouponTypeEnum.values();
        for (CouponTypeEnum businessMessage : businessMessages) {
            if (businessMessage.getCode() == code) {
                return businessMessage.getDes();
            }
        }
        return "";
    }

    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static CouponTypeEnum getEnum(int code) {
        CouponTypeEnum[] businessMessages = CouponTypeEnum.values();
        for (CouponTypeEnum businessMessage : businessMessages) {
            if (businessMessage.getCode() == code) {
                return businessMessage;
            }
        }
        return null;
    }

}
