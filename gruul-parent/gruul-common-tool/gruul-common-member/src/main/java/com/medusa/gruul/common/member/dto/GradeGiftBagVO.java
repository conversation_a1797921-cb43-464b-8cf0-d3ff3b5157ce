package com.medusa.gruul.common.member.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: zhouwei
 * @create: 2022-01-04 18:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GradeGiftBagVO implements Serializable {


    @Serial
    private static final long serialVersionUID = -1045709214308620162L;
    /**
     * 会员等级guid
     */
    private String memberGradeGuid;

    /**
     * 礼包类型 0：成长值 1：积分
     */
    private Integer type;

    /**
     * 赠送多少值
     */
    private Integer value;

}
