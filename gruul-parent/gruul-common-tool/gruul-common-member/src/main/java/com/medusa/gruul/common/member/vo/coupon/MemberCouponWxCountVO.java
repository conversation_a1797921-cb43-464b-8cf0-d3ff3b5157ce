package com.medusa.gruul.common.member.vo.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 优惠劵发放统计
 */
@Data
@Accessors(chain = true)
public class MemberCouponWxCountVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5613078160778177737L;

    /**
     * 当前可用：统计目前“未过期”、“未生效”状态的优惠券数量
     */
    private int unwrittenNum;

    /**
     * 即将过期，3天后
     */
    private int beAboutNum;
}
