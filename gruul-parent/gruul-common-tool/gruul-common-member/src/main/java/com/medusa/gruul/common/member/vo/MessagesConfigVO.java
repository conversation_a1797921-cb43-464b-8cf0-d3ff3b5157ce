package com.medusa.gruul.common.member.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 消息通知配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MessagesConfigVO implements Serializable {

    /**
     * guid
     */
    private String guid;

    /**
     * 小程序消息模板编号
     */
    private String appletTemplateNo;

    /**
     * 公众号消息模板编号
     */
    private String templateNo;

    /**
     * 小程序消息模板id
     */
    private String appletTemplateId;

    /**
     * 公众号消息模板id
     */
    private String megTemplateId;

    /**
     * 消息类型 1 商家通知  0 用户通知
     */
    private Integer msgType;

    /**
     * 消息类型
     */
    private Integer msgCategory;

    /**
     * 消息标题
     */
    private String msgTitle;

    /**
     * 推送规则
     */
    private String pushRule;

    /**
     * 详细字段
     */
    private String msgContent;


    /**
     * 小程序模板状态 0关闭  1 开启
     */
    private Integer appletStatus;

    /**
     * 公众号模板状态 0关闭  1 开启
     */
    private Integer status;

    /**
     * 订阅号关键词
     */
    private String msgKid;

    /**
     * 小程序订阅号关键词
     */
    private String appletMsgKid;

    /**
     * 所需跳转到小程序的具体页面路径，支持带参数,（示例index?foo=bar），要求该小程序已发布，暂不支持小游戏
     */
    private String pagePath;

    /**
     * 模板删除状态 0.未删除，1.已删除
     */
    private Integer isDelete;

    /**
     * 小程序预览字段
     */
    private String appletMsgContent;

    /**
     * 场景说明
     */
    private String scenarioDescription;

    /**
     * 字段顺序
     */
    private String dataSort;

    /**
     * 微信模板标题
     */
    private String title;

    /**
     * 公众号模板标题
     */
    private String mpTitle;

    /**
     * 公众号消息模板实例
     */
    private String mpMsgContent;

    /**
     * 短信模消息模板实例
     */
    private String messageContent;

    /**
     * 短信模板状态 0关闭  1 开启
     */
    private Integer messageStatus;
}
