package com.medusa.gruul.common.member.vo.coupon;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 优惠劵发放明细
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberCouponDetailVO extends MemberCouponVO {

    @Serial
    private static final long serialVersionUID = -9067700595791781351L;

    /**
     * 优惠劵规则
     */
    private MemberCouponGiveRuleVO ruleVO;

    /**
     * 活动备注
     */
    private String remark;
}
