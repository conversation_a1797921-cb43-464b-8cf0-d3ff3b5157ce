package com.medusa.gruul.common.member.vo.unilink;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 会员通-下拉框选项VO
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@Accessors(chain = true)
public class MemberUnilinkSelectOptionVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 8962049593183959475L;

    /**
     * 标签
     */
    private String label;

    /**
     * 值
     */
    private String value;

    /**
     * 系统类型
     */
    private String systemType;
} 