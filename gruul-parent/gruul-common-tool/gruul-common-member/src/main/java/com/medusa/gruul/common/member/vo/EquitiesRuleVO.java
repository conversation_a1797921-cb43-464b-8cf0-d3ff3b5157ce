package com.medusa.gruul.common.member.vo;

import com.fasterxml.jackson.annotation.JsonFormat;


import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: 成长值规则返回VO
 * @author: pan tao
 * @create: 2021-12-21 10:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
public class EquitiesRuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 类型 0：商品折扣 1：满减 2：直减 3：会员价 4：赠送成长值 5：翻倍成长值 6：赠送积分 7：翻倍积分
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum
     */
    private Integer type;

    /**
     * 商品折扣 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer goodsDiscountSet;

    /**
     * 权益时段 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer equitiesTimeSet;

    /**
     * 权益时段限制类型 0：不限制 1：限制时段 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private String equitiesTimeLimitedType;

    /**
     * 限制时段类型 1：日 2：周 3：月 4：年
     *
     * @see com.holderzone.member.common.enums.DateUnitEnum
     */
    private String limitedTimeType;

    /**
     * 适用门店设置 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer applyStoreSet;

    /**
     * 适用门店设置类型 0：全部门店 1：选择部分门店 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum
     */
    private String applyStoreType;

    /**
     * 适用业务 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer applyBusinessSet;

    /**
     * 适用业务类型 0：全部业务 1：部分业务 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    private String applyBusinessType;

    /**
     * 业务类型 0：食堂预定 3：快速收款 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    private String businessType;

    /**
     * 适用终端 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer applyTerminalSet;

    /**
     * 适用终端类型 0：全部终端 1：部分终端 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.ApplyTerminalTypeEnum
     */
    private String applyTerminalType;

    /**
     * 部分终端类型 53：小程序 2：一体机 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private String terminalType;

    /**
     * 适用商品 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer applyCommoditySet;

    /**
     * 适用商品必填 0：非必填 1：必填
     */
    private Integer applyCommodityRequired;

    /**
     * 0：适用商品 1：不适用商品
     *
     * @see ApplyCommodityTypeEnum
     */
    private String applyCommodityType;

    /**
     * 0：全部适用商品 1：部分适用商品
     *
     * @see com.holderzone.member.common.enums.equities.ApplyCommodityEnum
     */
    private String applyCommodity;

    /**
     * 0：全部商品不适用 1：部分商品不适用
     *
     * @see com.holderzone.member.common.enums.equities.NoApplyCommodityTypeEnum
     */
    private String noApplyCommodity;
    /**
     * 满减 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer enoughReduceSet;

    /**
     * 是否可设置多个满减 0：不能 1：能
     *
     * @see com.holderzone.member.common.enums.equities.MultipleEnoughReduceEnum
     */
    private Integer multipleEnoughReduce;

    /**
     * 金额满足计算范围 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer amountSatisfyScopeSet;

    /**
     * 金额满足计算范围类型 0：全部计算 1：部分计算设置 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.AmountSatisfyScopeTypeEnum
     */
    private String amountSatisfyScopeType;

    /**
     * 部分计价类型 0：商品价格 1：桌台费 2：配送费 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.PartValuationTypeEnum
     */
    private String partValuationType;

    /**
     * 直减 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer directReduceSet;

    /**
     * 会员折扣 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer memberDiscountSet;

    /**
     * 单次优惠限制 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer singleDiscountsLimitedSet;

    /**
     * 单次优惠限制类型 0：不限制 1：限制  多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private String singleDiscountsLimitedType;

    /**
     * 累计最高优惠限制 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer totalMostLimitedTypeSet;

    /**
     * 0：不限 1：优惠金额限制  多个用逗号隔开
     */
    private String totalMostLimitedType;

    /**
     * 优惠金额限制 1：日 2：周 3：月 4：年 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.DateUnitEnum
     */
    private String mostLimitedType;

    /**
     * 累计赠送成长值数量 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer singleGiveNumberSet;

    /**
     * 赠送成长值方式 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer giveWaySet;


    /**
     * 赠送成长值周期类型 1：日 2：周 3：月 4：年 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.DateUnitEnum
     */
    private String periodType;

    /**
     * 累计赠送 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer totalGiveSet;

    /**
     * 累计赠送类型 0：不限 1：限制 多个用逗号隔开
     */
    private String totalGiveType;

    /**
     * 翻倍倍数 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer doubleMultipleSet;

    /**
     * 单次翻倍获取成长值上限 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer singleDoubleLimitedSet;

    /**
     * 单次翻倍获取成长值上限类型 0：不限 1：限制 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private String singleDoubleLimitedType;

    /**
     * 限制次数 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer doubleCountSet;

    /**
     * 翻倍次数限制类型 0：不限次数 1：设置可翻倍次数 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private String doubleCountType;

    /**
     * 累计翻倍获取成长值上限 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer totalDoubleLimitedSet;

    /**
     * 累计翻倍获取成长值上限类型 0：不限制 1：设置上限值 多个用逗号隔开
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private String totalDoubleLimitedType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 权益信息guid
     */
    private String equitiesInfoGuid;

    /**
     * 翻倍次数必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    private Integer doubleCountRequired;

    /**
     * 单次翻倍获取成长值上线必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    private Integer singleDoubleLimitedRequired;

    /**
     * 累计翻倍获取成长值上限必填  0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */
    private Integer totalDoubleLimitedRequired;


    /**
     * 周期优惠限制 0：设置 1：未设置
     */
    private Integer periodDiscountLimitedSet;

    /**
     * 周期优惠限制必填 0：非必填 1：必填
     */
    private Integer periodDiscountRequired;

    /**
     * 周期优惠限制类型 -1：不限制 0：天 1：周 2：月 3：天
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private String periodDiscountType;

    /**
     * 优惠叠加设置 0：未设置 1：已设置
     */
    private Integer discountsSuperpositionSet;

    /**
     * 优惠叠加必填 0：非必填 1：必填
     */
    private Integer discountsSuperpositionRequired;


    /**
     * 优惠叠加类型 0：不可叠加 1:可叠加
     */
    private String discountsSuperpositionType;

    /**
     * 适用渠道 0：设置 1：未设置
     */
    private Integer applyChannelSet;

    /**
     * 适用渠道必填 0：非必填 1：必填
     */
    private Integer applyChannelRequired;

    /**
     * 适用渠道限制 0：全部 1：部分
     */
    private String applyChannelLimited;

    /**
     * 适用渠道类型
     */
    private String applyChannelType;

    /**
     * 累计最高优惠限制必填 0：非必填 1：必填
     */
    private Integer totalMostLimitedTypeRequired;

    /**
     * 单次优惠限制必填 0：非必填 1：必填
     */
    private Integer singleDiscountsLimitedRequired;

    /**
     * 适用终端必填 0：非必填 1：必填
     */
    private Integer applyTerminalRequired;


    /**
     * 适用业务必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer applyBusinessRequired;

    /**
     * 权益时段必填 0：非必填 1：必填
     */
    private Integer equitiesTimeRequired;

    /**
     * 周期使用次数限制 0：设置 1：未设置
     */
    private Integer periodUseCountLimitedSet;

    /**
     * 周期使用次数限制必填 0：非必填 1：必填
     */
    private Integer periodUseCountRequired;

    /**
     * 周期使用次数限制类型 -1：不限制 0：天 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private String periodUseCountType;

    /**
     * 商品会员价是否已经配置好了  false：未设置  true：已设置
     */
    private Boolean productShowSet;


    /**
     * 门店必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.BooleanEnum
     */

    private Integer storeRequired;

    /**
     * 共享互斥关系设置 0：未设置 1：已设置
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer relationRuleSet;

    /**
     * 共享互斥关系 0-互斥 1-共享 多个用逗号隔开
     *
     * @see RelationRuleEnum
     */
    private String relationRuleType;

    /**
     * 适用业务必填 0：非必填 1：必填
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesSetEnum
     */
    private Integer relationRuleRequired;

}
