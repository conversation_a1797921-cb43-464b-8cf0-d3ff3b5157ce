package com.medusa.gruul.common.member.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员等级类型 ()
 * <AUTHOR>
 * @version 1.0, 2025/4/21
 */
@Getter
@AllArgsConstructor
public enum GradeTypeEnum {

	/**
	 * 免费会员
	 */
	FREE(0, "免费会员"),

	/**
	 * 付费会员
	 */
	PAID(1, "付费会员");


	private final Integer code;

	private final String des;

	public static String getName(String name) {
		if (name == null) {
			return "";
		}
		GradeTypeEnum[] values = GradeTypeEnum.values();
		for (GradeTypeEnum typeEnum : values) {
			if (Objects.equals(typeEnum.name(), name)) {
				return typeEnum.getDes();
			}
		}
		return "";
	}
}
