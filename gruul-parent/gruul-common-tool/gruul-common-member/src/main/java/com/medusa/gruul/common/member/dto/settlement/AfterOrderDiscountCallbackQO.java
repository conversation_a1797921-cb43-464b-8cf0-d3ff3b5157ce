package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.member.enums.OrderStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单通用回调
 *
 * <AUTHOR>
 */
@Data
public class AfterOrderDiscountCallbackQO implements Serializable {

    @ApiModelProperty(value = "operSubjectGuid")
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单时间
     */
    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;

    /**
     * 订单状态
     * @see OrderStateEnum
     */
    @ApiModelProperty(value = "订单状态")
    private Integer orderState;

    /**
     * 订单类型
     * @see MarketConsumptionOrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    /**
     * 订单优惠金额
     */
    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal orderDiscountFee;

    /**
     * 订单实收金额
     */
    @ApiModelProperty(value = "订单实收金额")
    private BigDecimal orderActuallyFee;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private Integer orderSource;

}
