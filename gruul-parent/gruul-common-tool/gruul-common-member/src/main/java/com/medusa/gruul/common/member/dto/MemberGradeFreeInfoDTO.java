
package com.medusa.gruul.common.member.dto;



import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 免费会员信息
 * <AUTHOR>
 * @version 1.0, 2025/5/16
 */
@Data
@EqualsAndHashCode (callSuper = false)
public class MemberGradeFreeInfoDTO extends MemberGradePaidInfoDTO implements Serializable {
    @Serial
	private static final long serialVersionUID = -1409429887885527721L;


	/**
	 * 下一个等级 需要的成长值
	 */
	private Long nextGrowthValue;

	/**
	 * 背景色
	 */
	private String backgroundColor;
}
