package com.medusa.gruul.common.member.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 会员优惠券显示状态:前端使用
 *
 * <AUTHOR>
 * <p>
 * 继承自
 * @see CouponMemberStateEnum
 **/
@Getter
@AllArgsConstructor
public enum CouponShowStateEnum {

    INVALID(2, "未生效"),

    UN_EXPIRE(3, "可使用"),

    EXPIRE(4, "已过期"),

    APPLY(5, "已使用"),

    OVER(6, "已失效"),

    LOCK(8, "已锁定");

    private final int code;

    private final String des;

    /**
     * 通过code 获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        CouponShowStateEnum[] stateEnums = CouponShowStateEnum.values();
        for (CouponShowStateEnum typeEnum : stateEnums) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }

    public static boolean canUse(Integer state) {
        return Objects.equals(UN_EXPIRE.code, state);
    }

    /**
     * 即将过期状态
     *
     * @return
     */
    public static boolean beAboutState(Integer state) {
        return INVALID.getCode() == state || UN_EXPIRE.getCode() == state;
    }
}
