package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * 优惠应用结果
 *
 */
@Data
@Accessors(chain = true)
public class SettlementApplyRuleDetailVO implements Serializable {

    /**
     * 名称
     */
    private String name;


    /**
     * 核销顺序及规则：0自动 1手动
     */
    private Integer useType;

    /**
     * 退款是否退优惠：0否 1是
     */
    private Integer couponRollback;

    /**
     * 优惠券叠上限限制：0否 1是
     */
    @NotNull(message = "优惠券叠加使用上限必填")
    private Integer couponLimit;

    /**
     * 优惠券单笔限制张数
     */
    private Integer couponLimitNum;

    private List<SettlementRuleDiscountTreeVO> disAppendDiscounts;

    /**
     * 叠加+不可叠加
     */
    @JsonIgnore
    private List<SettlementRuleDiscountTreeVO> allDiscounts;

    /**
     * 所有大项,按规则排序
     */
    private List<Integer> allDiscountOptions;

    /**
     * 优惠券限制数量大于1的规则
     */
    @JsonIgnore
    private Map<String, Integer> couponLimtNumberMap;
}
