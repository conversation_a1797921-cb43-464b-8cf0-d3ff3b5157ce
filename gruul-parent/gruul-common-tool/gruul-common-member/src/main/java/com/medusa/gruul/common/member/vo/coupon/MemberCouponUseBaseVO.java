package com.medusa.gruul.common.member.vo.coupon;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.member.enums.coupon.CouponMemberStateEnum;
import com.medusa.gruul.common.member.enums.coupon.CouponPackageTypeEnum;
import com.medusa.gruul.common.member.enums.coupon.CouponTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 优惠劵发放明细
 */
@Data
@Accessors(chain = true)
public class MemberCouponUseBaseVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3287933141878034656L;

    /**
     * 门店Guid
     */
    private Integer storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 核销时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useTime;


    /**
     * 使用门店Guid
     */
    private String useStoreGuid;

    /**
     * 使用门店名称
     */
    private String useStoreName;

    /**
     * 核销订单号
     */
    private String orderNumber;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券类型
     *
     * @see CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 发放券码
     */
    private String code;

    /**
     * 发放状态
     *
     * @see CouponMemberStateEnum
     */
    private Integer state;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reachTime;


    /**
     * 优惠券有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveStartTime;

    /**
     * 优惠券有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveEndTime;

    /**
     * 发放渠道
     */
    private Integer source;

    /**
     * 发券方式类型（发放场景）
     *
     * @see CouponPackageTypeEnum
     */
    private Integer couponPackageType;

    /**
     * 券包ID
     */
    private String couponPackageCode;
}
