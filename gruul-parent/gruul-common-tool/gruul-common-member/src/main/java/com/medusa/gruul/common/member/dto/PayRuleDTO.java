package com.medusa.gruul.common.member.dto;


import com.medusa.gruul.common.member.enums.EffectiveDurationTypeEnum;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年03月30日 下午12:12
 * @description 付费规则实体
 */
@Data
public class PayRuleDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8402204805198711541L;

    private String id;

    /**
     * 有效期数量
     */
    private Integer num;

    /**
     * 有效期单位：3月 4年
     * @see EffectiveDurationTypeEnum
     */
    private Integer unit;

    /**
     * 有效期价格
     */
    private BigDecimal price;

}
