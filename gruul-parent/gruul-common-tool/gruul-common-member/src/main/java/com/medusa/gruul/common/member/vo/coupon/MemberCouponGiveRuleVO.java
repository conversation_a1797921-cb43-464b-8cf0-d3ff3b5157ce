package com.medusa.gruul.common.member.vo.coupon;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 优惠劵发放明细
 * 规则
 */
@Data
@Accessors(chain = true)
public class MemberCouponGiveRuleVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4496400539348999622L;

    /**
     * 使用门槛
     *  0 无门槛
     *  1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     *  优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 用券时段限制 0：不限制  1：限制
     */
    private Integer applyDateLimited;



    /**
     * 限制时段类型  -1:自定义 0：日 1：周 2：月 3：年
     */
    private Integer applyTimeLimitedType;


    /**
     * 可兑换次数 (商品券存在)
     */
    private Integer exchangeTimes;

    /**
     * 兑换次数限制
     */
    private Integer exchangeLimit;

    /**
     * 限制时段限制类型 json
     */
    private String applyTimeLimitedJson;

    /**
     * 适用场景 0:全部业务  1：部分业务
     *
     */
    private Integer applyBusiness;

    /**
     * 适用终端 0:全部终端 1：部分终端
     */
    private Integer applyTerminal;

    /**
     * 适用终端json
     */
    private List<String> applyTerminalList;

    /**
     * 适用场景json
     */
    private List<String> applyBusinessList;

    /**
     * 打标标签guid
     */
    private String applyLabelGuidJson;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 适用门店集合
     */
    private String applicableAllStoreJson;

    /**
     * 商品集合
     */
    private String applyCommodityJson;

    /**
     * 0：全部商品适用 1：部分商品适用 2 不适用商品
     */
    private Integer applyCommodity;

    /**
     * 活动备注
     */
    private String remark;

    /**
     * 优惠金额上限
     * null/0 表示不限制
     */
    private BigDecimal discountAmountLimit;

    /**
     * 单笔订单限用数量
     * 单笔订单最多使用{singleOrderUsedLimit}张此优惠券
     * 当前优惠券限用数量需 ≤ 结算规则限制数量
     */
    private Integer singleOrderUsedLimit;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;

}
