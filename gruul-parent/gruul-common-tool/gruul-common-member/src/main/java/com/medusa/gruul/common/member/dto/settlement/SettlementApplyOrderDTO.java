package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.medusa.gruul.common.model.enums.BooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 结算规则应用订单数据
 *
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3212351245090146002L;

    /**
     * 自动勾选：0自动（计算最优） 1手动
     */
    @ApiModelProperty("订单相关入参")
    private Integer autoCheck;

    /**
     * 确认选择：0否（校验选择项是否是叠加） 1是
     */
    @ApiModelProperty("订单相关入参")
    private int confirmCheck;

    /**
     * 订单入参
     */
    @ApiModelProperty("订单相关入参")
    @NotNull(message = "订单入参必填！")
    private SettlementApplyOrderInfoDTO orderInfo;

    /**
     * 订单商品
     * 商品需要合并成 id x 数量
     */
    @ApiModelProperty("购物车商品明细")
    private List<SettlementApplyCommodityDTO> orderCommodityList;

    /**
     * 已选优惠： 计算使用
     * 按选择顺序传入，保留当前优惠（当前可叠加时，同时保留其他可叠加项）
     */
    @ApiModelProperty("已选优惠")
    private List<SettlementApplyDiscountBaseReqDTO> checkDiscountList;

    /**
     * 已选优惠同上  数据结构：map<discountGuid,SettlementApplyDiscountBaseReqDTO>
     */
    @ApiModelProperty("已选优惠")
    private Map<String, SettlementApplyDiscountBaseReqDTO> checkDiscountMap;

    /**
     * 指定优惠唯一标识查询  如guid
     */
    private List<String> discountOptionId;

    /**
     * 指定优惠项查询
     */
    private List<Integer> listOptions;

    /**
     * 叠加优惠券
     * map<discountOption,set<discountGuid>>
     */
    private Map<Integer, Set<String>> appendMap;

    /**
     * 优惠券限制数量大于1的规则
     */
    private Map<String, Integer> couponLimtNumberMap;

    /**
     * 是否需要指定优惠查询
     */
    private Integer isAppointDiscount = BooleanEnum.FALSE.getCode();
}
