package com.medusa.gruul.common.member.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 会员优惠券状态
 *
 * <AUTHOR>
 * 与枚举 值对应 {@link CouponShowStateEnum}
 **/
@Getter
@AllArgsConstructor
public enum CouponMemberStateEnum {

    UN_EXPIRE(3, "未过期"),

    EXPIRE(4, "已过期"),


    APPLY(5, "已使用"),

    OVER(6, "已失效");

    private final int code;

    private final String des;

    /**
     * 通过code 获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        CouponMemberStateEnum[] stateEnums = CouponMemberStateEnum.values();
        for (CouponMemberStateEnum typeEnum : stateEnums) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }

    /**
     * 手动处理状态
     *
     * @return
     */
    public static boolean canUse(Integer state) {
        return Objects.equals(UN_EXPIRE.code, state);
    }
}
