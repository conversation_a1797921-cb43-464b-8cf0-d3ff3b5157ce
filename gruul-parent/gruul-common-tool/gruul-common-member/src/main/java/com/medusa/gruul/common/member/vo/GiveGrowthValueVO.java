package com.medusa.gruul.common.member.vo;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 翻倍成长值权益预览对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GiveGrowthValueVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 累计赠送成长值数量
     */
    private Integer totalGiveNumber;

    /**
     * 设置周期 -1:一次性  0:天  1:周  2:月  3:年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer setPeriod;

    /**
     * 周期赠送数量
     */
    private Integer periodGiveNumber;

    /**
     * 周期赠送成长值
     */
    private String periodValue;

}
