package com.medusa.gruul.common.member.dto.settlement;

import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品
 *
 */
@Data
@Accessors(chain = true)
public class OrderCommodityQO implements Serializable {

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    @NotEmpty(message = "商品数量必传")
    private BigDecimal commodityNum;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;
}
