package com.medusa.gruul.common.member.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 会员卡退款
 *
 * <AUTHOR>
 * @date 2025/4/22
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardRefundDTO {

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 回退优惠金额
     */
    @ApiModelProperty(value = "回退优惠金额")
    private BigDecimal discountAmount;

}
