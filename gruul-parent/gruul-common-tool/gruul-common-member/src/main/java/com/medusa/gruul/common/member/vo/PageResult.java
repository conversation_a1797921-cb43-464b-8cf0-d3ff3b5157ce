package com.medusa.gruul.common.member.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/2
 */
@Data
@Accessors(chain = true)
public class PageResult<T> {
    /**
     * 当前页码
     */
    private int current;
    /**
     * 每页数量
     */
    private int size;
    /**
     * 记录总数
     */
    private int total;
    /**
     * 页码总数
     */
    private int pages;
    /**
     * 数据模型
     */
    private List<T> records;

    public PageResult() {
    }

    public PageResult(int current, int size, int total) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.pages = (total + size - 1) / size;
    }
}
