package com.medusa.gruul.common.member.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 适用门店
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Data
@Accessors(chain = true)
public class MemberCouponStoreListDTO {
    /**
     * 当前页数
     */
    private Integer currentPage = 1;

    /**
     * 每页显示条数
     */
    private Integer pageSize = 20;

    /**
     * 关键字搜索
     */
    private String keywords;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 门店id
     */
    private List<MemberCouponStoreDTO> stores;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;
}
