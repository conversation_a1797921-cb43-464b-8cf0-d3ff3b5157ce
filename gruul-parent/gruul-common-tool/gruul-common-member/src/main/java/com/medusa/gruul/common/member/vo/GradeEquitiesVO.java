package com.medusa.gruul.common.member.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 权益
 * copy from member
 *
 * <AUTHOR>
 * @date 2025/5/9
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class GradeEquitiesVO {
    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益图标
     */
    private List<String> icon;

    /**
     * 权益图片
     */
    private String equitiesImg;

    /**
     * 状态 0：不显示 1：显示
     */
    private int status;

    /**
     * 权益类型
     */
    private Integer equitiesRuleType;

    /**
     * 翻倍数
     */
    private BigDecimal multiple;

    /**
     * 翻倍次数
     */
    private Integer doubleCount;

    /**
     * 赠送成长值
     */
    private Integer totalGiveNumber;

    /**
     * 权益guid
     */
    private String guid;

    /**
     * 成长值翻倍次数限制 0:不限制 1:限制
     */
    private Integer doubleCountLimited;

    /**
     * 会员价折扣力度
     */
    private BigDecimal discountDynamics;

    /**
     * 权益guid
     */
    private String equitiesGuid;

    /**
     * 累计翻倍限制 0:不限制 1:限制
     */
    private Integer totalDoubleCountLimited;

    /**
     * 累计翻倍次数上限
     */
    private Integer totalDoubleCountUpperLimit;

    /**
     * 等级guid  或 会员卡guid
     */
    private String memberGradeInfoGuid;
}
