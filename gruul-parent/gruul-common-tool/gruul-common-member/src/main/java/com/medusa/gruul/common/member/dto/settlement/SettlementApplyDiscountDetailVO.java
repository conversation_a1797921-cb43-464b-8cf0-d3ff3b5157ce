package com.medusa.gruul.common.member.dto.settlement;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.medusa.gruul.common.member.enums.EquitiesLimitedTypeEnum;
import com.medusa.gruul.common.member.enums.SettlementDiscountOptionEnum;
import com.medusa.gruul.common.model.enums.BooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * 优惠列表返回基类
 * <p>
 * 规定了必须返回的字段
 * <p>
 * JsonTypeInfo.Id.CLASS feign 序列化子类
 *
 */
@Data
@Accessors(chain = true)
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "type",
        visible = true
)
@JsonSubTypes({
        @JsonSubTypes.Type(value = SettlementApplyDiscountDetailOfDiscountVO.class, name = "discount")
})
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyDiscountDetailVO implements Serializable {

    /**
     * 是否选中(默认否)
     */
    @ApiModelProperty("是否选中")
    private Integer isChecked = BooleanEnum.FALSE.getCode();

    /**
     * 是否叠加(默认否)
     * (是否互斥：叠加（不互斥），不叠加（互斥）)
     */
    @ApiModelProperty("是否叠加")
    private Integer isAppend = BooleanEnum.FALSE.getCode();

    /**
     * 是否可用(默认否)
     */
    @ApiModelProperty("是否可用")
    private Integer isEnabled = BooleanEnum.TRUE.getCode();

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;

    /**
     * 优惠guid，必须
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;
    /**
     * 优惠项的主键
     * <p>
     * eg: 会员已领优惠券id
     */
    @ApiModelProperty("优惠项的主键")
    private String discountOptionId;

    /**
     * 显示名称
     */
    @ApiModelProperty("显示名称")
    private String discountName;

    /**
     * 实际优惠金额
     */
    @ApiModelProperty("实际优惠金额")
    private BigDecimal discountAmount;

    /**
     * 最优标识：0 否 1是
     */
    private int optimal = 0;

    /**
     * 商品
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

    private String applyCommodityJson;

    private Integer applyCommodity;

    /**
     * 使用次数(兑换券 前端传递)
     */
    private Integer requiresTimes;

    /**
     * 排序
     */
    private Integer sort;










    /**
     * 折扣力度
     */
    @ApiModelProperty("折扣力度")
    private BigDecimal discountDynamics;

    /**
     * 单次优惠限制金额
     */
    @ApiModelProperty("单次优惠限制金额")
    private BigDecimal singleDiscountsLimitedAmount;

    /**
     * 单次优惠限制 0：不限制 1：限制
     * <p>
     * todo 去掉，使用 singleDiscountsLimitedAmount > 0限制，<=0不限制
     * todo 考虑将 单次、周期、累计 合并为一个字段
     *
     * @see EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("单次优惠限制")
    private Integer singleDiscountsLimited;

    /**
     * 会员卡guid
     */
    @ApiModelProperty("会员卡guid")
    private String memberCardGuid;

    /**
     * 会员价限制次数
     */
    @ApiModelProperty("会员价限制次数")
    private Integer memberPriceLimited;


    /**
     * 周期优惠限制 0：不限制 1：限制
     *
     * @see EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("周期优惠限制 0：不限制 1：限制")
    private Integer periodDiscountLimited;

    /**
     * 周期最低优惠
     */
    @ApiModelProperty("周期最低优惠")
    private BigDecimal minimumThisMonthAmount;

    /**
     * 累计优惠限制 0：不限制 1：限制
     *
     * @see EquitiesLimitedTypeEnum
     */
    private Integer totalDiscountLimited;
    /**
     * 周期类型 -1:自定义 0：日 1：周 2：月 3：年 4 累计
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 累计优惠限制金额
     */
    private BigDecimal totalDiscountLimitedAmount;

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount == null ? BigDecimal.ZERO : discountAmount;

        if (Objects.nonNull(this.discountOption)
                && (this.discountOption == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode()
                || this.discountOption == SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode())) {
            return;
        }

        //优惠为0，不可用
        if (this.discountAmount.compareTo(BigDecimal.ZERO) == 0) {
            this.isEnabled = BooleanEnum.FALSE.getCode();
        }
    }
}
