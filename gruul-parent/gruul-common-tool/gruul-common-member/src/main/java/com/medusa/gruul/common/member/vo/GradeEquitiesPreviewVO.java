package com.medusa.gruul.common.member.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: 会员等级权益预览响应对象
 * @author: zhanglin
 * @create: 2022-1-5 09:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GradeEquitiesPreviewVO implements Serializable {


    @Serial
    private static final long serialVersionUID = -8357839689865729335L;
    /**
     *
     */
    private String guid;

    /**
     * 是否使用
     */
    private Boolean isUse;

    /**
     * 权益名称
     */
    private String equitiesName;

    /**
     * 权益图标
     */
    private String equitiesIcon;

    /**
     * 权益说明
     */
    private String equitiesExplain;

    /**
     * 升级礼包预览规则信息
     */
    private List<GiftBagPreviewVO> gradeGiftBagPreviewVO;

    /**
     * 翻倍成长值权益预览对象
     */
    private DoubleGrowthValueVO doubleGrowthValueVO;

    /**
     * 赠送成长值权益预览对象
     */
    private GiveGrowthValueVO giveGrowthValueVO;

    /**
     * 会员价权益预览对象
     */
    private MemberPriceVO memberPriceVO;

    /**
     * 翻倍积分权益预览对象
     */
    private DoubleIntegralVO doubleIntegralVO;

    /**
     * 赠送积分权益预览对象
     */
    private GiveIntegralVO giveIntegralVO;

    /**
     * 商品会员价权益预览对象
     */
    private GoodsMemberPriceVO goodsMemberPriceVO;



}
