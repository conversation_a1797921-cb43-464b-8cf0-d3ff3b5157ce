package com.medusa.gruul.common.member.dto.settlement;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @program: member-marketing
 * @description: 会员折扣适用商品
 * @author: rw
 * @create: 2022-02-09 10:21
 */
@Data
@Accessors(chain = true)
public class MemberPriceApplyCommodityQO implements Serializable {

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 终端
     */
    private String terminal;

    /**
     * 业务
     */
    private String business;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 门店
     */
    private String storeGuid;
}
