package com.medusa.gruul.common.member.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员卡支付
 *
 * <AUTHOR>
 * @date 2025/4/22
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardPayDTO {
    /**
     * 会员GUID
     */
    private String memberInfoGuid;

    /**
     * 会员卡持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 消费门店GUID
     */
    private String storeGuid;

    /**
     * 消费门店名称
     */
    private String storeName;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单支付类型
     * @see com.medusa.gruul.common.model.enums.PayType
     */
    private String payType;

    /**
     * 订单时间
     */
    private LocalDateTime orderTime;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单优惠金额
     */
    private BigDecimal orderDiscountAmount;

    /**
     * 卡支付金额
     */
    private BigDecimal cardBalancePayAmount;

}
