package com.medusa.gruul.common.member.vo.unilink;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 会员通-系统配置VO
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Data
@Accessors(chain = true)
public class MemberUnilinkSystemConfigVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -6825358308992514888L;

    /**
     * 终端
     */
    List<MemberUnilinkSelectOptionVO> terminal;

    /**
     * 渠道
     */
    List<MemberUnilinkSelectOptionVO> channel;

    /**
     * 业务
     */
    List<MemberUnilinkSelectOptionVO> biz;
}
