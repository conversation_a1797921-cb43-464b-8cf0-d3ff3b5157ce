package com.medusa.gruul.common.member.vo;



import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: z<PERSON><PERSON>
 * @create: 2022-01-04 18:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MemberPriceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String guid;

    /**
     * 折扣力度
     */
    private BigDecimal discountDynamics;

    /**
     * 单次优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer singleDiscountsLimited;

    /**
     * 单次优惠限制金额
     */
    private BigDecimal singleDiscountsLimitedAmount;

    /**
     * 周期优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer periodDiscountLimited;

    /**
     * 周期优惠信息
     */
    private List<String> periodDiscountLimitedJsons;

    /**
     * 小程序需要的list对象
     */
    private List<PeriodDiscountVO> periodDiscountS;

    /**
     * 周期优惠限制类型 -1 不限制 0：天 1：周 2：月 3：天
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private String periodDiscountType;

    /**
     * 周期优惠限制金额
     */
    private BigDecimal periodDiscountLimitedAmount;

    /**
     * 累计优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer totalDiscountLimited;

    /**
     * 累计优惠限制金额
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private BigDecimal totalDiscountLimitedAmount;

    /**
     * 优惠叠加限制 0：不可叠加 1：可叠加
     */
    private Integer discountsSuperposition;

    /**
     * 权益生效时段限制 0：不限制 1：限制
     */
    private Integer equitiesEffectiveDateLimited;

    /**
     * 限制时段类型 -1:自定义 1：日 2：周 3：月 4：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 限制时段限制类型json
     */
    private List<String> equitiesTimeLimitedJsons;

    /**
     * -1：全部商品适用 0：适用商品 1：不适用商品
     *
     * @see
     */
    private Integer applyGoodsType;

    /**
     * 适用终端 0:全部终端 1：部分终端
     *
     * @see com.holderzone.member.common.enums.equities.ApplyTerminalTypeEnum
     */
    private Integer applyTerminal;

    /**
     * 适用终端json
     */
    private String applyTerminalJson;

    /**
     * 适用业务 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    private Integer applyBusiness;

    /**
     * 适用业务json
     */
    private String applyBusinessJson;

    /**
     * 适用渠道 0:全部渠道 1：部分渠道
     */
    private Integer applyChannel;

    /**
     * 适用渠道json
     */
    private String applyChannelJson;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     *
     * @see com.holderzone.member.common.enums.growth.GoodsApplicableStoreEnum
     */
    private Integer applicableAllStore;

    /**
     * 门店数据
     */
    private List<EquitiesStoreRuleVO> equitiesStoreRuleVOList;

}
