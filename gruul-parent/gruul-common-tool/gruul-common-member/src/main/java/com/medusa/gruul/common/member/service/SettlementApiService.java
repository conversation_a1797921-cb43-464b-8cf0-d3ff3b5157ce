package com.medusa.gruul.common.member.service;

import com.medusa.gruul.common.member.dto.settlement.*;
import io.swagger.annotations.ApiOperation;

public interface SettlementApiService {

    /**
     * 获取结算台优惠列表
     * @param dto 结算台优惠列表查询条件
     * @return 结算台优惠列表
     */
    SettlementApplyOrderShowVO getDiscountApplyList(SettlementApplyOrderDTO dto);

    /**
     * 下单前计算优惠
     *
     * @param dto 订单入参
     * @return 优惠结果对象
     */
    SettlementApplyOrderShowVO discountCalculate(SettlementApplyOrderDTO dto);

    /**
     * 下单后锁定优惠
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    Boolean lockedDiscount(SettlementOrderLockDTO dto);

    /**
     * 取消订单、退款：释放优惠
     * 食堂调用的是 ： /accumulation_discount_release_key
     * @param dto 订单参数
     * @return 优惠列表
     */
    Boolean lockedDiscount(SettlementUnLockedDiscountDTO dto);

    /**
     * 订单退款 优惠活动记录回调
     */
    void barkOrderDiscountCallback(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO);

    /**
     *  订单完成 优惠活动记录回调
     */
    void afterOrderDiscountCallback(AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO);

    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    MemberPriceApplyCommodityVO getMemberPriceApplyCommodity(MemberPriceApplyCommodityQO qo);
}
