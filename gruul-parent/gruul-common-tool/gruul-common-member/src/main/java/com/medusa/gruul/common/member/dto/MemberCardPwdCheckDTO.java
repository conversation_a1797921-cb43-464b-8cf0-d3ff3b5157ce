package com.medusa.gruul.common.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 会员卡支付
 *
 * <AUTHOR>
 * @date 2025/4/24
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardPwdCheckDTO {

    /**
     * 会员卡持卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 密码
     */
    private String password;

}
