package com.medusa.gruul.common.member.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023-09-02
 * @description 结算台叠加类型
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountAppendEnum {

    /**
     * 不可叠加
     */
    DIS_APPEND(0, "不可叠加"),
    /**
     * 可叠加
     */
    APPEND(1, "可叠加"),
    /**
     * 新导入（不可叠加）
     */
    NEW_IMPORT(2, "新导入");

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountAppendEnum anEnum : SettlementDiscountAppendEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }
}
