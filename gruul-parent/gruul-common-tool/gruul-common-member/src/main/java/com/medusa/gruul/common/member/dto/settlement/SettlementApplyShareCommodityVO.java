package com.medusa.gruul.common.member.dto.settlement;

import com.medusa.gruul.common.member.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 商品优惠明细
 *
 */
@Data
@Accessors(chain = true)
public class SettlementApplyShareCommodityVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3559064153837638379L;

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;

    /**
     * 优惠大项guid
     */
    @ApiModelProperty(value = "优惠大项guid")
    private String discountGuid;

    /**
     * 优惠具体id
     */
    @ApiModelProperty(value = "优惠具体id")
    private String discountOptionId;

    /**
     * 商品明细
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

}
