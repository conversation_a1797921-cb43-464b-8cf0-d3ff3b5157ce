package com.medusa.gruul.common.member.vo.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户主动领券响应结果
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@Accessors(chain = true)
public class SelfReceiveCouponPackageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3130362553643391104L;

    /**
     * 券包记录GUID
     */
    private String packageLinkGuid;

    /**
     * 活动名称
     */
    private String activityName;

}
