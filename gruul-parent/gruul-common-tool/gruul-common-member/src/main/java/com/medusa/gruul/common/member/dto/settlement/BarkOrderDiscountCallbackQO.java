package com.medusa.gruul.common.member.dto.settlement;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单通用回调
 *
 * <AUTHOR>
 */
@Data
public class BarkOrderDiscountCallbackQO implements Serializable {

    @ApiModelProperty(value = "operSubjectGuid")
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 0 部分退款 1 整单退款
     */
    @ApiModelProperty(value = "0 部分退款 1 整单退款")
    private Integer refundType;

    /**
     * 部分退款总金额
     */
    @ApiModelProperty(value = "部分退款金额")
    private BigDecimal refundAmount;

    /**
     * 优惠退款金额
     */
    @ApiModelProperty(value = "优惠退款金额")
    private BigDecimal discountAmount;

    /**
     * 积分是否可退回  0 不可退 1 可退
     */
    @ApiModelProperty(value = "积分是否可退回  0 不可退 1 可退  ")
    private Integer isIntegralBack;

    /**
     * 需要退款的商品详细
     */
    @ApiModelProperty(value = "需要退款的商品详细")
    private List<OrderCommodityQO> orderCommodityQOList;
}
