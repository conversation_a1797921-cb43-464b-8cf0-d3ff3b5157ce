package com.medusa.gruul.common.member.service;

import com.medusa.gruul.common.member.dto.AppletGrowthDTO;
import com.medusa.gruul.common.member.dto.AppletIntegralDetailPageDTO;
import com.medusa.gruul.common.member.dto.MallOrderIntegralDTO;
import com.medusa.gruul.common.member.vo.AppletOpenIntegralVO;
import com.medusa.gruul.common.member.vo.CalculateSignIntegralVO;
import com.medusa.gruul.common.member.vo.PageVO;
/**
 * 会员积分服务
 *
 * <AUTHOR>
 */
public interface MemberIntegralApiService {

    PageVO getAppletIntegralDetail(AppletIntegralDetailPageDTO qo);

    AppletOpenIntegralVO getIntegralTotalDetail(AppletGrowthDTO qo);

    CalculateSignIntegralVO calculateSignInTaskSevenDayIntegral();

    Integer getUsableIntegral(String memberInfoGuid);

    Boolean updateMemberIntegral(MallOrderIntegralDTO request);
}
