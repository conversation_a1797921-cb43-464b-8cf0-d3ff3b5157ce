package com.medusa.gruul.common.member.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.member.dto.*;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.utils.HttpApiClient;
import com.medusa.gruul.common.member.vo.*;
import com.medusa.gruul.common.member.vo.coupon.*;
import com.medusa.gruul.common.member.vo.unilink.MemberUnilinkSystemConfigVO;
import com.medusa.gruul.common.system.model.ISystem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员系统服务
 *
 * <AUTHOR>
 * @date 2025/4/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberApiServiceImpl implements MemberApiService {
    private final HttpApiClient httpApiClient;
    private static final String MEMBER_EVERYDAY_RECORD_SAVE_OR_UPDATE = "/open/member/everyday/record/saveOrUpdate";
    private static final String MEMBER_EVERYDAY_RECORD_CALCULATE_INTEGRAL_CHECK = "/open/member/everyday/record/calculateIntegralCheck";
    private static final String MEMBER_INFO = "/member/info";
    private static final String MEMBER_BATCH_INFO = "/member/batch/info";
    private static final String MEMBER_ACCOUNT_INFO = "/member/accountInfo";
    private static final String MEMBER_ADD = "/member/add";
    private static final String MEMBER_UPDATE = "/member/update";
    private static final String MEMBER_DATA_ITEM_LIST = "/member/memberDataItemList";
    private static final String CHECK_USER_DATA_ITEM = "/member/checkUserDataItem";
    private static final String PAY_MEMBER_CARD_LIST = "/card/payList";
    private static final String MY_MEMBER_CARD_LIST = "/card/myList";
    private static final String MEMBER_CARD_SEND_STATUS = "/card/sendStatus";
    private static final String MEMBER_CARD_OPEN_CARD = "/card/openCard";
    private static final String MEMBER_CARD_OPEN_CARD_PAY_CHECK = "/card/openCardPayCheck";
    private static final String MEMBER_CARD_OPEN_CARD_PAY = "/card/openCardPay";
    private static final String MEMBER_CARD_INFO = "/card/info";
    private static final String MEMBER_CARD_BALANCE_TOTAL = "/card/balanceTotal";
    private static final String MEMBER_CARD_BALANCE_RECORD = "/card/balanceRecord";
    private static final String MEMBER_CARD_QRCODE = "/card/qrcode";
    private static final String MEMBER_CARD_UPDATE_PWD = "/card/updatePwd";
    private static final String MEMBER_CARD_UPDATE_DEFAULT = "/card/updateDefault";
    private static final String MEMBER_CARD_BIND_PHYSICAL = "/card/bindPhysicalCard";
    private static final String MEMBER_CARD_RECHARGE_PAGE = "/card/rechargePage";
    private static final String MEMBER_CARD_RECHARGE_CALCULATE_PREMONEY = "/card/calculatePreMoney";
    private static final String MEMBER_CARD_RECHARGE = "/card/recharge";
    private static final String MEMBER_CARD_PWD_CHECK = "/pay/memberCardPwdCheck";
    private static final String MEMBER_CARD_PAY = "/pay/memberCardPay";
    private static final String MEMBER_CARD_PAY_RECORD = "/pay/member_order_record_list";
    private static final String MEMBER_CARD_PAY_CALLBACK = "/pay/memberCardPayCallback";
    private static final String MEMBER_CARD_REFUND = "/pay/memberCardRefund";
    private static final String GRADE_INFO_LIST = "/grade/infoList";
    private static final String GRADE_INFO = "/grade/info";
    private static final String GRADE_ADD_PURCHASE_HISTORY = "/grade/addPurchaseHistory";
    private static final String GRADE_CARD_LIST = "/grade/card/get";
    private static final String GRADE_CARD_UPDATE = "/grade/card/update";
    private static final String GRADE_CARD_ADD = "/grade/card/add";
    private static final String GRADE_USER_PAID_INFO = "/grade/user/paid/info";
    private static final String GRADE_USER_FREE_INFO = "/grade/user/free/info";
    private static final String EQUITIES_PREVIEW_INFO = "/equities/preview/info";
    private static final String EQUITIES_INFO = "/equities/info";
    private static final String WECHAT_MESSAGE_SEND_BATCH = "/wechat/message/send_batch";

    private static final String WECHAT_MESSAGE_MSG_GET_MESSAGES_CONFIG = "/wechat/message/msg/getMessagesConfig";
    private static final String MEMBER_COUPON_PAGE = "/coupon/pageMemberCouponByTime";
    private static final String MEMBER_COUPON_DETAIL = "/coupon/detail";
    private static final String MEMBER_COUPON_QRCODE = "/coupon/getQrcode";
    private static final String MEMBER_COUPON_COUNT = "/coupon/count";
    private static final String MEMBER_COUPON_STORE = "/coupon/getMemberCouponStoreList";
    private static final String COUPON_PACKAGE_SELF_DETAILS = "/coupon_package/self_details";
    private static final String COUPON_PACKAGE_SELF_RECEIVE = "/coupon_package/self_receive";
    private static final String MEMBER_UNILINK_SYSTEM_CONFIG = "/member_unilink_system_config/listConfig";

    //WECHAT_MESSAGE_CONFIG_SAVE
    private static final String WECHAT_MESSAGE_CONFIG_SAVE = "/wechat/message/config/save";


    @Override
    public MemberBasicInfoVO getMemberBasicInfo(MemberQueryDTO dto) {
        MemberBasicInfoVO memberBasicInfoVO = httpApiClient.executeMemberRequest(MEMBER_INFO, dto, MemberBasicInfoVO.class);
        if (memberBasicInfoVO != null && StrUtil.isEmpty(memberBasicInfoVO.getNickName())) {
            memberBasicInfoVO.setNickName(memberBasicInfoVO.getUserName());
        }
        return memberBasicInfoVO;
    }

    @Override
    public List<MemberBasicInfoVO> batchGetMemberBasicInfo(MemberQueryDTO dto) {
        List<MemberBasicInfoVO> memberBasicInfoVOS = httpApiClient.executeMemberRequestList(MEMBER_BATCH_INFO, dto, MemberBasicInfoVO.class);
        if (CollUtil.isNotEmpty(memberBasicInfoVOS)) {
            memberBasicInfoVOS.forEach(memberInfo -> {
                if (StrUtil.isEmpty(memberInfo.getNickName())) {
                    memberInfo.setNickName(memberInfo.getUserName());
                }
            });
        }
        return memberBasicInfoVOS;
    }

    @Override
    public MemberAccountInfoVO getMemberAccountInfo(MemberQueryDTO dto) {
        MemberAccountInfoVO memberAccountInfoVO = httpApiClient.executeMemberRequest(MEMBER_ACCOUNT_INFO, dto, MemberAccountInfoVO.class);
        if (memberAccountInfoVO != null && StrUtil.isEmpty(memberAccountInfoVO.getNickName())) {
            memberAccountInfoVO.setNickName(memberAccountInfoVO.getUserName());
        }
        return memberAccountInfoVO;
    }

    @Override
    public MemberBasicInfoVO addUser(MemberAddDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_ADD, dto, MemberBasicInfoVO.class);
    }

    @Override
    public void updateUser(MemberUpdateDTO dto) {
        httpApiClient.executeMemberRequestBoolean(MEMBER_UPDATE, dto);
    }

    @Override
    public List<DataItemSetVO> memberDataItemList() {
        return httpApiClient.executeMemberRequestList(MEMBER_DATA_ITEM_LIST, null, DataItemSetVO.class);
    }

    @Override
    public Boolean checkUserDataItem(CheckDataItemDTO dataItemDTO) {
        return httpApiClient.executeMemberRequestBoolean(CHECK_USER_DATA_ITEM, dataItemDTO);
    }

    @Override
    public List<PayMemberCardVO> getPayMemberCardList(String memberInfoGuid) {
        return httpApiClient.executeMemberRequestList(PAY_MEMBER_CARD_LIST,
                new MemberCardQueryDTO().setMemberInfoGuid(memberInfoGuid),
                PayMemberCardVO.class);
    }

    @Override
    public MemberCardInfoVO getMemberCardInfo(MemberCardInfoQueryDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_INFO, dto, MemberCardInfoVO.class);
    }

    @Override
    public MemberCardBalanceTotalVO getMemberCardBalanceTotal(MemberCardBalanceQueryDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_BALANCE_TOTAL, dto, MemberCardBalanceTotalVO.class);
    }

    @Override
    public PageVO getMemberCardBalanceRecord(MemberCardBalanceQueryDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_BALANCE_RECORD, dto, PageVO.class);
    }

    @Override
    public MemberCardQrCodeVO getQrcode(String memberInfoCardGuid) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_QRCODE,
                new MemberCardInfoQueryDTO().setMemberInfoCardGuid(memberInfoCardGuid),
                MemberCardQrCodeVO.class);
    }

    @Override
    public Boolean updatePwd(MemberCardPwdUpdateDTO dto) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_UPDATE_PWD, dto);
    }

    @Override
    public Boolean updateDefault(MemberCardDefaultUpdateDTO dto) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_UPDATE_DEFAULT, dto);
    }

    @Override
    public MemberCardBindResultVO bindPhysicalCard(MemberCardBindPhysicalDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_BIND_PHYSICAL, dto, MemberCardBindResultVO.class);
    }

    @Override
    public MemberCardRechargePageVO rechargePage(String memberInfoCardGuid) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_RECHARGE_PAGE,
                new MemberCardInfoQueryDTO().setMemberInfoCardGuid(memberInfoCardGuid),
                MemberCardRechargePageVO.class);
    }

    @Override
    public void saveMemberEverydayRecord(MemberEverydayRecordDTO dto) {
        log.info("保存会员日常记录参数: {}", JSON.toJSONString(dto));
        Boolean result = httpApiClient.executeMemberRequestBoolean(MEMBER_EVERYDAY_RECORD_SAVE_OR_UPDATE, dto);
        log.info("保存会员日常记录结果: {}", result);
    }

    @Override
    public Integer calculateIntegralCheck(MemberEverydayRecordDTO dto) {
        log.info("计算实际赠送积分值参数: {}", JSON.toJSONString(dto));
        Integer result = httpApiClient.executeMemberRequest(MEMBER_EVERYDAY_RECORD_CALCULATE_INTEGRAL_CHECK, dto, Integer.class);
        log.info("计算实际赠送积分值结果: {}", result);
        return result;
    }

    @Override
    public RechargeThresholdVO calculatePreMoney(MemberCardRechargeCalculateDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_RECHARGE_CALCULATE_PREMONEY, dto, RechargeThresholdVO.class);
    }

    @Override
    public MemberCardRechargeVO memberCardRecharge(MemberCardRechargeDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_RECHARGE, dto, MemberCardRechargeVO.class);
    }

    @Override
    public List<MyMemberCardVO> getMyMemberCardList(MyMemberCardQueryDTO dto) {
        return httpApiClient.executeMemberRequestList(MY_MEMBER_CARD_LIST, dto, MyMemberCardVO.class);
    }

    @Override
    public Integer getSendStatus(String cardGuid) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_SEND_STATUS,
                new MemberCardQueryDTO().setCardGuid(cardGuid),
                Integer.class);
    }

    @Override
    public MemberCardOpenVO openCard(MemberCardOpenDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_OPEN_CARD, dto, MemberCardOpenVO.class);
    }

    @Override
    public void openCardPayCheck(MemberCardOpenCardPayCheckDTO dto) {
        httpApiClient.executeMemberRequestRaw(MEMBER_CARD_OPEN_CARD_PAY_CHECK, dto);
    }

    @Override
    public String openCardPay(MemberCardOpenCardPayDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_CARD_OPEN_CARD_PAY, dto, String.class);
    }

    @Override
    public Boolean memberCardPwdCheck(MemberCardPwdCheckDTO dto) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_PWD_CHECK, dto);
    }

    @Override
    public Boolean memberCardPay(List<MemberCardPayDTO> dtoList) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_PAY, dtoList);
    }

    @Override
    public Boolean memberCardPayCallback(List<MemberCardPayCallbackDTO> dtoList) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_PAY_CALLBACK, dtoList);
    }

    @Override
    public Boolean memberCardRefund(MemberCardRefundDTO dto) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_REFUND, dto);
    }

    @Override
    public Boolean memberCardPayRecordRequest(List<MemberCardPayDTO> dtoList) {
        return httpApiClient.executeMemberRequestBoolean(MEMBER_CARD_PAY_RECORD, dtoList);
    }

    @Override
    public List<MemberGradeInfoDTO> getMemberGradeInfoList(MemberGradeQueryDTO dto) {
        return httpApiClient.executeMemberRequestList(GRADE_INFO_LIST, dto, MemberGradeInfoDTO.class);
    }

    @Override
    public MemberGradeInfoDTO getGradeInfoByGradeGuid(MemberGradeQueryDTO dto) {
        return httpApiClient.executeMemberRequest(GRADE_INFO, dto, MemberGradeInfoDTO.class);
    }

    @Override
    public MemberGradeInfoDTO getGradeInfo(MemberGradeQueryDTO dto) {
        return httpApiClient.executeMemberRequest(GRADE_INFO, dto, MemberGradeInfoDTO.class);
    }

    @Override
    public void saveMemberGradePurchaseHistory(MemberGradePurchaseHistoryDTO dto) {
        httpApiClient.executeMemberRequest(GRADE_ADD_PURCHASE_HISTORY, dto, String.class);
    }

    @Override
    public List<MemberGradeCardDTO> getGradeCardList(GradeCardQueryDTO dto) {
        return httpApiClient.executeMemberRequestList(GRADE_CARD_LIST, dto, MemberGradeCardDTO.class);
    }

    @Override
    public void updateGradeCard(GradeCardQueryDTO dto) {
        httpApiClient.executeMemberRequestRaw(GRADE_CARD_UPDATE, dto);
    }

    @Override
    public void addGradeCard(GradeCardQueryDTO dto) {
        httpApiClient.executeMemberRequestRaw(GRADE_CARD_ADD, dto);
    }

    @Override
    public MemberGradePaidInfoDTO getGradePaidInfoByMemberGuid(Long memberGuid) {
        Map<String, Object> params = new HashMap<>();
        params.put("memberGuid", String.valueOf(memberGuid));
        return httpApiClient.executeMemberGetRequest(GRADE_USER_PAID_INFO, params, MemberGradePaidInfoDTO.class);
    }

    @Override
    public MemberGradeFreeInfoDTO getGradeFreeInfoByMemberGuid(Long memberGuid) {
        Map<String, Object> params = new HashMap<>();
        params.put("memberGuid", String.valueOf(memberGuid));
        return httpApiClient.executeMemberGetRequest(GRADE_USER_FREE_INFO, params, MemberGradeFreeInfoDTO.class);
    }

    @Override
    public GradePreviewVO getGradeEquitiesPreviewList(Long memberGradeGuid, Long memberGuid) {
        Map<String, Object> params = new HashMap<>();
        params.put("memberInfoGuid", String.valueOf(memberGuid));
        params.put("memberGradeGuid", String.valueOf(memberGradeGuid));
        return httpApiClient.executeMemberGetRequest(EQUITIES_PREVIEW_INFO, params, GradePreviewVO.class);
    }

    @Override
    public GradeEquitiesInfoDetailVO getGradeEquitiesInfoDetail(Long equitiesGuid) {
        Map<String, Object> params = new HashMap<>();
        params.put("equitiesGuid", String.valueOf(equitiesGuid));
        return httpApiClient.executeMemberGetRequest(EQUITIES_INFO, params, GradeEquitiesInfoDetailVO.class);
    }

    @Override
    public void wechatMessageSendBatch(MessagesSendDTO messageSendDTO) {
        log.info("批量发送消息接收：{}", JSON.toJSONString(messageSendDTO));
        httpApiClient.executeMemberRequestRaw(WECHAT_MESSAGE_SEND_BATCH, messageSendDTO);
    }

    @Override
    public List<MessagesConfigVO> getMessagesConfig() {
        return httpApiClient.executeMemberRequestList(WECHAT_MESSAGE_MSG_GET_MESSAGES_CONFIG, null, MessagesConfigVO.class);
    }
    @Override
    public List<MemberCouponVO> pageMemberCouponByTime(MemberCouponQueryDTO dto) {
        return httpApiClient.executeMemberRequestList(MEMBER_COUPON_PAGE, dto, MemberCouponVO.class);
    }

    @Override
    public MemberCouponDetailVO memberCouponDetail(MemberCouponDetailDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_COUPON_DETAIL, dto, MemberCouponDetailVO.class);
    }

    @Override
    public MemberCouponQrCodeVO memberCouponQrcode(MemberCouponQrcodeDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_COUPON_QRCODE, dto, MemberCouponQrCodeVO.class);
    }

    @Override
    public MemberCouponWxCountVO memberCouponCount(MemberCouponCountDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_COUPON_COUNT, dto, MemberCouponWxCountVO.class);
    }

    @Override
    public MemberUnilinkSystemConfigVO memberUnilinkSystemConfig() {
        return httpApiClient.executeMemberGetRequest(MEMBER_UNILINK_SYSTEM_CONFIG, null, MemberUnilinkSystemConfigVO.class);
    }

    @Override
    public PageResult memberCouponStoreList(MemberCouponStoreListDTO dto) {
        return httpApiClient.executeMemberRequest(MEMBER_COUPON_STORE, dto, PageResult.class);
    }

    @Override
    public CouponPackageSelfDetailsVO getCouponPackageSelfDetails(String guid, String memberGuid) {
        Map<String, String> params = new HashMap<>();
        params.put("guid", guid);
        params.put("memberGuid", memberGuid);
        return httpApiClient.executeMemberGetRequest(COUPON_PACKAGE_SELF_DETAILS, params, CouponPackageSelfDetailsVO.class);
    }

    @Override
    public SelfReceiveCouponPackageVO selfReceiveCouponPackage(SelfReceiveCouponPackageDTO dto) {
        return httpApiClient.executeMemberRequest(COUPON_PACKAGE_SELF_RECEIVE, dto, SelfReceiveCouponPackageVO.class);
    }

    @Override
    public Boolean saveOrUpdateWeChatConfig(WeChatConfigInfoQO request) {
        log.info("保存或更新小程序配置信息:{}", JSON.toJSONString(request));
        return httpApiClient.executeMemberRequestBoolean(WECHAT_MESSAGE_CONFIG_SAVE, request);
    }

}
