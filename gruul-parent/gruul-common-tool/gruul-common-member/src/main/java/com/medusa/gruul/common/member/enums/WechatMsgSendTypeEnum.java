package com.medusa.gruul.common.member.enums;

import lombok.Getter;

/**
 * 微信
 * 小程序、公众号 消息通知标题
 */
@Getter
public enum WechatMsgSendTypeEnum {

    // 账户余额提醒
    AMOUNT_CHANGE("资金变动提醒", "账户余额提醒", "储值成功提醒"),

    // 账户余额提醒
    ORDER_DELIVER("订单发货通知", "交易提醒", "订单发货通知"),

    //订单评价
    ORDER_COMMENT("订单评价提醒", "交易提醒", "订单评价提醒"),

    //退款通知
    ORDER_REFUND("退款通知", "交易提醒", "退款通知"),

    //商品取货提醒
    ORDER_PICKUP("商品取货提醒", "交易提醒", "商品取货提醒"),

    //订单配送通知
    ORDER_DELIVERY("订单配送通知", "交易提醒", "订单配送通知"),

    //订单取消通知
    ORDER_CANCEL("订单取消通知", "交易提醒", "订单取消通知"),

    //优惠券到账提醒
    COUPON_ARRIVAL("优惠券到账提醒", "活动通知", "优惠券到账提醒"),

    //优惠券过期提醒
    COUPON_EXPIRE("优惠券过期提醒", "活动通知", "优惠券过期提醒")

    ;

    /**
     * 业务系统 消息标题
     */
    private final String msgTitle;

    /**
     * 小程序消息标题
     */
    private final String title;

    /**
     * 公众号消息标题
     */
    private final String mpTitle;

    WechatMsgSendTypeEnum(String msgTitle, String title, String mpTitle) {
        this.msgTitle = msgTitle;
        this.title = title;
        this.mpTitle = mpTitle;
    }
}
