package com.medusa.gruul.common.member.dto.settlement;

import com.medusa.gruul.common.member.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 已选优惠
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountBaseReqDTO implements Serializable {

    private static final long serialVersionUID = 5852525875037269382L;

    /**
     * 优惠类型
     * todo 前端计算的时候：代金券、抵扣券、
     *
     * @see SettlementDiscountOptionEnum#code
     */
    @ApiModelProperty(value = "优惠类型")
    @Min(value = 1, message = "优惠类型必传！")
    private Integer discountOption;

    /**
     * 优惠类型guid，大类型guid
     * eg：优惠券1guid、优惠券2guid..
     * todo 无规则时为空
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    /**
     * 优惠类型具体项的id
     */
    @ApiModelProperty(value = "优惠id")
    private String discountOptionId;

    /**
     * 优惠力度（锁定必传）： eg 多少积分，多少折
     */
    @ApiModelProperty(value = "优惠力度")
    private String discountDynamic;

    /**
     * 优惠金额： 锁定必传
     */
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 最后一条标识: 1是 空否
     */
    @ApiModelProperty(value = "最后一条标识")
    private Integer isLast;

    /**
     * 后端使用
     * eg：会员xx已领优惠券1的id
     */
    private List<String> discountOptionIds;

    /**
     * 优惠券单笔限制张数
     * 后端使用
     */
    private Integer couponLimitNum = 1;

    /**
     * 是否叠加
     * 后端使用
     */
    private boolean isAppend;

    /**
     * 兑换券使用次数(前端)
     */
    private Integer requiresTimes;

    /**
     * 积分锁定数量
     */
    @ApiModelProperty("积分锁定数量")
    private Integer lockedIntegral;

    /**
     * 抵扣金额
     */
    @ApiModelProperty("抵扣金额")
    private BigDecimal lockedForNowMoney;
}
