package com.medusa.gruul.common.member.service;

import com.medusa.gruul.common.member.dto.*;
import com.medusa.gruul.common.member.vo.*;
import com.medusa.gruul.common.member.vo.coupon.*;
import com.medusa.gruul.common.member.vo.unilink.MemberUnilinkSystemConfigVO;

import java.util.List;

/**
 * 会员系统服务
 *
 * <AUTHOR>
 * @date 2025/4/9
 */
public interface MemberApiService {

    /**
     * 从会员系统获取用户
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    MemberBasicInfoVO getMemberBasicInfo(MemberQueryDTO dto);

    /**
     * 批量获取会员系统用户信息
     *
     * @param dto 查询条件
     * @return 用户数据
     */
    List<MemberBasicInfoVO> batchGetMemberBasicInfo(MemberQueryDTO dto);

    /**
     * 从会员系统获取用户账户信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    MemberAccountInfoVO getMemberAccountInfo(MemberQueryDTO dto);

    /**
     * 向会员系统注册用户
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    MemberBasicInfoVO addUser(MemberAddDTO dto);

    /**
     * 向会员系统修改用户
     *
     * @param dto 请求参数
     */
    void updateUser(MemberUpdateDTO dto);

    /**
     * 查询会员资料项列表
     */
    List<DataItemSetVO> memberDataItemList();

    Boolean checkUserDataItem(CheckDataItemDTO dataItemDTO);

    //=================== 会员卡相关 ===================

    /**
     * 获取支付会员卡
     *
     * @param memberInfoGuid 会员guid
     * @return 会员卡
     */
    List<PayMemberCardVO> getPayMemberCardList(String memberInfoGuid);

    /**
     * 获取用户会员卡
     *
     * @param dto 请求参数
     * @return 会员卡
     */
    List<MyMemberCardVO> getMyMemberCardList(MyMemberCardQueryDTO dto);

    /**
     * 获取会员卡发卡状态
     *
     * @param cardGuid 会员卡guid
     * @return 发卡状态
     */
    Integer getSendStatus(String cardGuid);

    /**
     * 开通会员卡
     *
     * @param dto 请求参数
     * @return 开卡结果
     */
    MemberCardOpenVO openCard(MemberCardOpenDTO dto);

    /**
     * 开通会员卡（校验）
     *
     * @param dto 请求参数
     */
    void openCardPayCheck(MemberCardOpenCardPayCheckDTO dto);

    /**
     * 开通会员卡（付费）
     *
     * @param dto 请求参数
     * @return 支付结果
     */
    String openCardPay(MemberCardOpenCardPayDTO dto);

    /**
     * 查询会员卡详情
     *
     * @param dto 请求参数
     * @return 会员卡列表
     */
    MemberCardInfoVO getMemberCardInfo(MemberCardInfoQueryDTO dto);

    /**
     * 查询会员卡余额合计
     *
     * @param dto 请求参数
     * @return 余额合计
     */
    MemberCardBalanceTotalVO getMemberCardBalanceTotal(MemberCardBalanceQueryDTO dto);

    /**
     * 查询会员卡余额记录
     *
     * @param dto 请求参数
     * @return 余额记录
     */
    PageVO<MemberCardBalanceGroupVO> getMemberCardBalanceRecord(MemberCardBalanceQueryDTO dto);

    /**
     * 查询会员卡二维码
     *
     * @param memberInfoCardGuid 会员持卡guid
     * @return 二维码
     */
    MemberCardQrCodeVO getQrcode(String memberInfoCardGuid);

    /**
     * 修改会员卡密码
     *
     * @param dto 请求参数
     * @return 结果
     */
    Boolean updatePwd(MemberCardPwdUpdateDTO dto);

    /**
     * 修改默认会员卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    Boolean updateDefault(MemberCardDefaultUpdateDTO dto);

    /**
     * 绑定实体卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    MemberCardBindResultVO bindPhysicalCard(MemberCardBindPhysicalDTO dto);

    /**
     * 会员卡充值页面
     *
     * @param memberInfoCardGuid 会员持卡guid
     * @return 充值页面
     */
    MemberCardRechargePageVO rechargePage(String memberInfoCardGuid);

    /**
     * 计算预计到账金额
     *
     * @param dto 请求参数
     * @return 查询结果
     */
    RechargeThresholdVO calculatePreMoney(MemberCardRechargeCalculateDTO dto);

    /**
     * 会员卡充值
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    MemberCardRechargeVO memberCardRecharge(MemberCardRechargeDTO dto);


    /**
     * 记录会员日常相关的动作
     *
     * @param dto 更新参数
     */

    void saveMemberEverydayRecord(MemberEverydayRecordDTO dto);


    /**
     * 计算实际积分增送
     *
     * @param dto 更新参数
     */

    Integer calculateIntegralCheck(MemberEverydayRecordDTO dto);

    //=================== 支付相关 ===================

    /**
     * 会员卡密码校验
     *
     * @param dto 请求参数
     */
    Boolean memberCardPwdCheck(MemberCardPwdCheckDTO dto);

    /**
     * 会员卡支付
     *
     * @param dtoList 请求参数
     */
    Boolean memberCardPay(List<MemberCardPayDTO> dtoList);

    /**
     * 会员卡支付回调
     *
     * @param dtoList 请求参数
     */
    Boolean memberCardPayCallback(List<MemberCardPayCallbackDTO> dtoList);

    /**
     * 会员卡退款
     *
     * @param dto 请求参数
     */
    Boolean memberCardRefund(MemberCardRefundDTO dto);

    /**
     * 会员卡支付记录
     *
     * @param dtoList 支付信息DTO
     */
    Boolean memberCardPayRecordRequest(List<MemberCardPayDTO> dtoList);

    //=================== 会员等级相关 ===================

    /**
     * 获取生效的会员等级列表
     *
     * @return 会员等级列表
     */
    List<MemberGradeInfoDTO> getMemberGradeInfoList(MemberGradeQueryDTO dto);

    /**
     * 根据会员等级GUID获取会员等级信息
     *
     * @param dto 请求参数
     * @return 会员等级信息
     */
    MemberGradeInfoDTO getGradeInfoByGradeGuid(MemberGradeQueryDTO dto);


    /**
     * 根据查询条件获取会员等级信息
     *
     * @param dto 查询条件
     * @return 会员等级信息
     */
    MemberGradeInfoDTO getGradeInfo(MemberGradeQueryDTO dto);

    /**
     * 新增会员等级购买记录
     *
     * @param dto 请求参数
     * @return 会员等级购买记录GUID
     */
    void saveMemberGradePurchaseHistory(MemberGradePurchaseHistoryDTO dto);


    /**
     * 根据查询条件获取会员等级卡列表
     *
     * @param dto 查询条件
     * @return 会员等级卡列表
     */
    List<MemberGradeCardDTO> getGradeCardList(GradeCardQueryDTO dto);


    /**
     * 更新会员等级卡
     *
     * @param dto 更新参数
     */
    void updateGradeCard(GradeCardQueryDTO dto);

    /**
     * 新增会员等级卡
     *
     * @param dto 新增参数
     */
    void addGradeCard(GradeCardQueryDTO dto);

    /**
     * 根据会员GUID获取会员付费等级信息
     *
     * @param memberGuid 会员GUID
     */
    MemberGradePaidInfoDTO getGradePaidInfoByMemberGuid(Long memberGuid);


    /**
     * 根据会员GUID获取会员免费等级信息
     *
     * @param memberGuid 会员GUID
     * @return 会员免费等级信息
     */
    MemberGradeFreeInfoDTO getGradeFreeInfoByMemberGuid(Long memberGuid);

    /**
     * 根据权益业务id，获取指定会员权益预览信息
     *
     * @param memberGuid 会员GUID
     * @param businessId 权益业务id
     * @return 预览信息
     */
    GradePreviewVO getGradeEquitiesPreviewList(Long businessId, Long memberGuid);


    /**
     * 根据权益id，获取指定会员权益详情信息
     *
     * @param equitiesGuid 权益id
     * @return 详情信息
     */
    GradeEquitiesInfoDetailVO getGradeEquitiesInfoDetail(Long equitiesGuid);


    /**
     * 批量发送微信消息
     * @param messageSendDTO
     */
    void wechatMessageSendBatch(MessagesSendDTO messageSendDTO);

    List<MessagesConfigVO> getMessagesConfig();

    /**
     * 按时间分页查询已领券
     *
     * @param dto 查询参数
     * @return 券列表
     */
    List<MemberCouponVO> pageMemberCouponByTime(MemberCouponQueryDTO dto);

    /**
     * 查询券详情
     *
     * @param dto 查询条件
     * @return 券详情
     */
    MemberCouponDetailVO memberCouponDetail(MemberCouponDetailDTO dto);

    /**
     * 查询券二维码
     *
     * @param dto 查询条件
     * @return 券详情
     */
    MemberCouponQrCodeVO memberCouponQrcode(MemberCouponQrcodeDTO dto);

    /**
     * 统计优惠卷
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    MemberCouponWxCountVO memberCouponCount(MemberCouponCountDTO dto);

    /**
     * 从会员中台查询系统配置（终端、渠道、业务）
     *
     * @return 查询结果
     */
    MemberUnilinkSystemConfigVO memberUnilinkSystemConfig();

    /**
     * 从会员中台查询适用门店
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    PageResult memberCouponStoreList(MemberCouponStoreListDTO dto);

    /**
     * 从会员中台查询自助领券活动详情
     *
     * @param guid 查询条件
     * @return 查询结果
     */
    CouponPackageSelfDetailsVO getCouponPackageSelfDetails(String guid, String memberGuid);

    /**
     * 用户主动领券接口
     *
     * @param dto 领券请求参数
     * @return 领券结果
     */
    SelfReceiveCouponPackageVO selfReceiveCouponPackage(SelfReceiveCouponPackageDTO dto);

    Boolean saveOrUpdateWeChatConfig(WeChatConfigInfoQO request);
}
