package com.medusa.gruul.common.member.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 消息通知配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MessagesSendBaseDTO implements Serializable {

    /**
     * templateGuid
     */
    private String templateGuid;

    /**
     * 消息模板编号
     */
    private String appletTemplateNo;

    /**
     * 消息模板名称
     */
    private String templateName;

    /**
     * 公众号消息模板名称
     */
    private String mpTemplateName;

    /**
     * appletTemplateId
     */
    private String appletTemplateId;

    /**
     * 公众号消息模板id
     */
    private String msgTemplateId;


    private String appId;

    private String openId;

    private String accessToken;

    /**
     * 小程序模板参数
     */
    private  Map<String, String> prams;

    /**
     * 公众号模板参数
     */
    private Map<String, String> mpParams;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    private String enterpriseGuid;

    /**
     * appSecret
     */
    private String appSecret;

    /**
     * 跳转参数
     */
    private String pageParams;

    /**
     * 消息类型
     */
    private Integer messageType;
}
