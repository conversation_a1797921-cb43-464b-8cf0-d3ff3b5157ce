package com.medusa.gruul.common.member.enums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结算台优惠具体项
 * （最底层的类型，后端区分）
 * @description 结算台优惠类型
 * 优惠项大类 {@link SettlementDiscountItemEnum}
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountOptionEnum {

    NONE(0, "无", null, null, 0),
    /**
     * 商品会员价
     */
    MEMBER_PRICE(1, "商品会员价权益", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员折扣权益
     */
    MEMBER_DISCOUNT(2, "会员折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员卡折扣权益
     */
    MEMBER_CARD_DISCOUNT(3, "会员卡折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 代金券
     */
    COUPON_VOUCHER(10, "代金券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_VOUCHER, 0),
    /**
     * 折扣券
     */
    COUPON_DISCOUNT(11, "折扣券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_DISCOUNT, 0),
    /**
     * 兑换券
     */
    COUPON_EXCHANGE(12, "兑换券", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.COUPON_EXCHANGE, 0),
    /**
     * 满减满折
     */
    FULL_OFF(20, "满减满折", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.FULL_OFF, 1),
    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(22, "限时特价", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.LIMITED_TIME_SPECIAL, 1),
    /**
     * 积分兑换
     */
    INTEGRAL_EXPLAIN(91, "积分抵现", SettlementDiscountTypeEnum.PROPERTY, SettlementDiscountItemEnum.ASSET_PREFERENCE, 1);

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 类型
     */
    private final SettlementDiscountTypeEnum type;

    /**
     * 优惠项
     */
    private final SettlementDiscountItemEnum item;

    /**
     * 单笔限用数量
     */
    private final int limitNum;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }

    public static SettlementDiscountOptionEnum getEnum(Integer code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return NONE;
    }

}
