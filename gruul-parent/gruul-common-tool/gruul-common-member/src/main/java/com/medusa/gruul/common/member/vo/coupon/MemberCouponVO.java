package com.medusa.gruul.common.member.vo.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.member.enums.coupon.CouponMemberStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员优惠券
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberCouponVO extends MemberCouponBaseVO{

    @Serial
    private static final long serialVersionUID = -1110825090468803969L;

    private Long id;

    /**
     * 会员优惠券guid
     */
    private String guid;

    /**
     * 0：全部商品适用 1：部分商品适用 2 不适用商品
     */
    private Integer applyCommodity;

    /**
     * 展示状态
     *
     * @see CouponMemberStateEnum
     */
    private Integer showState;

    /**
     * 1 即将过期
     */
    private Integer beAboutState;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pageTime;

    /**
     * 优惠金额上限
     * null/0 表示不限制
     */
    private BigDecimal discountAmountLimit;

    /**
     * 单笔订单限用数量
     * 单笔订单最多使用{singleOrderUsedLimit}张此优惠券
     * 当前优惠券限用数量需 ≤ 结算规则限制数量
     */
    private Integer singleOrderUsedLimit;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;

    /**
     * 可兑换次数 (商品券存在)
     */
    private Integer exchangeTimes;

    /**
     * 兑换次数限制
     */
    private Integer exchangeLimit;
}
