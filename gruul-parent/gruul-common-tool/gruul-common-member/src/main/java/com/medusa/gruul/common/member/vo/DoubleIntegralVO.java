package com.medusa.gruul.common.member.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 翻倍积分权益预览对象
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class DoubleIntegralVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 积分翻倍倍数
     */
    private BigDecimal gradeValueDoubleNumber;

    /**
     * 积分翻倍次数限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer doubleCountLimited;

    /**
     * 翻倍次数上限
     */
    private Integer doubleCountUpperLimit;


    /**
     * 积分单次翻倍限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer singleDoubleLimited;

    /**
     * 单次翻倍积分上限
     */
    private Integer singleDoubleUpperLimit;

    /**
     * 累计翻倍限制 0:不限制 1:限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer totalDoubleCountLimited;

    /**
     * 累计翻倍次数上限
     */
    private Integer totalDoubleCountUpperLimit;

    /**
     * 是否已经使用该权益
     */
    private Integer isUse;

}
