package com.medusa.gruul.common.member.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 我的会员卡
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MyMemberCardQueryDTO {
    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 会员GUID
     */
    @NotBlank(message = "会员guid不能为空")
    private String memberInfoGuid;

    /**
     * 请求类型 0正常 1过期 2未开通
     */
    private Integer type;

}
