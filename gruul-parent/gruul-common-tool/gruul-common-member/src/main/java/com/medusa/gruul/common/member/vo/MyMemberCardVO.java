package com.medusa.gruul.common.member.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 我的会员卡
 * copy from member
 *
 * <AUTHOR>
 * @date 2025/4/23
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MyMemberCardVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -3937661388758417044L;

    /**
     * hsa_member_info_card guid
     */
    private String ownGuid;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 会员卡类型 0 实体卡 1电子卡
     */
    private Integer cardType;

    /**
     * 卡颜色
     */
    private String cardColor;

    /**
     * 获取卡剩余冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 卡图片
     */
    private String cardImage;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 卡面值金额
     */
    private BigDecimal cardValueMoney;

    /**
     * 会员卡使用状态(2:启用；3：禁用；4：已过期；5：草稿)
     */
    private Integer cardStatus;

    /**
     * 已领电子卡状态：0: 已冻结 1:正常 2:已过期
     */
    private Integer cardState;

    /**
     * 已领实体卡状态：-1 未激活  0已冻结 1 正常 2 已过期
     */
    private Integer physicalCardState;
    /**
     * 开卡日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate gmtCreate;

    /**
     * 卡有效期（0永久有效；1领取之日起；2固定日期）
     */
    private Integer cardValidity;

    /**
     * 领取之日起时间
     */
    private Integer cardValidityTime;

    /**
     * 有效固定日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate cardValidityDate;

    /**
     * 领取之日起单位 1日、2周、3月、4年
     */
    private Integer validityUnit;

    /**
     * 账户金额
     */
    private BigDecimal accountMoney;

    /**
     * 开卡时效类型（0永久有效；1固定时间段）
     */
    private Integer openCardTimeType;


    /**
     * 是否是默认卡 null:不是 1:是
     */
    private Integer defaultCard;

    /**
     * 会员关联卡guid
     */
    private String memberInfoCardGuid;

    /**
     * ·
     * 电子卡自主开通类型（0免费；1直接付款；2充值）
     */
    private Integer selfType;

    /**
     * 自主开通直接付款金额
     */
    private BigDecimal selfPaymentMoney;

    /**
     * 自主开通充值金额
     */
    private BigDecimal selfRechargeMoney;

    /**
     * 电子发卡状态(1:未开始;2:发送中;3:已停发;4已结束;5草稿)
     */
    private Integer sendStatus;

    /**
     * 电子卡开卡方式：0自主开通;1注册完成，自动发放；2指定用户直接开卡；3满足条件，自动发放
     */
    private Integer electronicOpenWay;

    /**
     * 应付金额
     */
    private BigDecimal shouldPayMoney;

    /**
     * 立省金额
     */
    private BigDecimal economyMoney;

    /**
     * 适用范围
     */
    private Integer bindingStoreCount;
    /**
     * 使用须知
     */
    private String cardEmployExplain;

    /**
     * 有效时间段
     */
    private String periodOfValidity;

    /**
     * 会员卡卡号
     */
    private String electronicCardNum;

    private int storeNum;

    /**
     * 1:全部门店；0:部分门店
     */
    private int applicableAllStore;

    /**
     * 是否支持电子卡
     */
    private Integer isSupportElectronicCard;

    /**
     * 是否需要密码 1 是 0否
     */
    private Integer isPassword;

    /**
     * 是否支持超额
     */
    private Integer isExcess;

    /**
     * 超额类型 0 次数 1 金额
     */
    private Integer excessType;

    /**
     * 剩余可超金额
     */
    private BigDecimal excessAmount;

    /**
     * 剩余可超次数
     */
    private Integer excessTimes;

    /**
     * 是否适用当前门店
     */
    private Boolean isUsableStore = true;

    /**
     * 是否开启充值功能(一体机、小程序...)
     */
    private Integer isPreStored;

    /**
     * 小程序充值功能
     */
    private Integer appletRecharge;

    /**
     * 充值提示
     */
    private String rechargeTips;

    private List<GradeEquitiesVO> gradeEquitiesVOList;
}
