package com.medusa.gruul.common.member.vo.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 自助领券活动优惠券VO
 * 专门用于自助领券活动中的优惠券信息展示
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@Accessors(chain = true)
public class CouponPackageSelfCouponVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 9161308574435493990L;

    /**
     * 优惠券GUID
     */
    private String guid;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 数量
     */
    private Integer num;
}
