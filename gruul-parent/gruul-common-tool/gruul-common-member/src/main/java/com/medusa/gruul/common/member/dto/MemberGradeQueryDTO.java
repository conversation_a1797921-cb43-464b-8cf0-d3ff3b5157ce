package com.medusa.gruul.common.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberGradeQueryDTO {


    /**
	 * 角色类型
     */
    private String roleType;


	/**
	 * 等级类型
     */
    private Integer gradeType;


	private String guid;

	private String memberGuid;

	/**
	 * vip 等级
	 */
	private Integer vipGrade;

}
