package com.medusa.gruul.common.member.utils;

import cn.hutool.core.lang.Console;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.global.model.exception.GlobalException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0, 2025/4/22
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HttpApiClient {
	private static final HttpClient HTTP_CLIENT = HttpClient.newBuilder()
														  .connectTimeout(Duration.ofSeconds(10))
														  .build();
	private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(10);

	@Value("${member.api.base-url:http://localhost:8080/api}")
	private String memberApiBaseUrl;
	@Value("${member.api.app-id:holder-mall}")
	private String appId;
	@Value("${member.api.secret-key:XNeCSc0knwJGzdQkCFSMGbtTB0AeCyDp}")
	private String secretKey;


	/**
	 * 执行会员系统请求，返回原始响应体
	 *
	 * @param apiPath 接口路径
	 * @param requestBody 请求参数
	 * @return 原始响应体字符串
	 */
	public <T> String executeMemberRequestRaw(String apiPath, T requestBody) {
		return executeMemberRequest(apiPath, requestBody, data -> {
			log.info("executeMemberRequestRaw类型原始响应体: {}", data);
			if (data == null) {
				return null;
			}
			return data.toString();
		});
	}

	/**
	 * 执行会员系统请求
	 *
	 * @param apiPath 接口路径
	 * @param requestBody 请求参数
	 * @param responseType 返回对象类型
	 * @return 处理后的结果
	 */
	public <T, R> R executeMemberRequest(String apiPath, T requestBody, Class<R> responseType) {
		return executeMemberRequest(apiPath, requestBody, data -> {
			if (data == null) {
				return null;
			}
			return JSON.parseObject(data.toString(), responseType);
		});
	}

	/**
	 * 执行会员系统请求
	 *
	 * @param apiPath 接口路径
	 * @param requestBody 请求参数
	 * @param elementType 数组元素类型
	 * @return 处理后的结果列表
	 */
	public <T, R> List<R> executeMemberRequestList(String apiPath, T requestBody, Class<R> elementType) {
		return executeMemberRequest(apiPath, requestBody, data -> {
			if (data == null) {
				return Collections.emptyList();
			}
			return JSON.parseArray(data.toString(), elementType);
		});
	}

	/**
	 * 执行会员系统请求
	 *
	 * @param apiPath 接口路径
	 * @param requestBody 请求参数
	 * @return 是否执行成功
	 */
	public <T> Boolean executeMemberRequestBoolean(String apiPath, T requestBody) {
		return executeMemberRequest(apiPath, requestBody, data -> {
			if (data == null) {
				return false;
			}
			// 安全的类型转换
			if (data instanceof Boolean) {
				return (Boolean) data;
			}
			if (data instanceof String) {
				String strValue = ((String) data).toLowerCase().trim();
				return "true".equals(strValue) || "1".equals(strValue) || "success".equals(strValue);
			}
			if (data instanceof Number) {
				return ((Number) data).intValue() != 0;
			}
			// 默认返回false，避免异常
			log.warn("无法将响应数据转换为Boolean类型，数据类型: {}, 数据值: {}", data.getClass().getSimpleName(), data);
			return false;
		});
	}

	/**
	 * 执行会员系统请求
	 *
	 * @param apiPath 接口路径
	 * @param requestBody 请求参数
	 * @param processor 响应处理器
	 * @return 处理后的结果
	 */
	private <T, R> R executeMemberRequest(String apiPath, T requestBody, Function<Object, R> processor) { // 修改泛型参数
		// 生成请求唯一标识
		String traceId = UUID.randomUUID().toString().replace("-", "");
		String url = memberApiBaseUrl + apiPath;
		HttpRequest request = buildHttpRequest(url, requestBody, traceId);

		try {
			long startTime = System.currentTimeMillis();
			log.info("[TraceId:{}] 开始请求会员系统，URL: {}", traceId, url);

			HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
			long costTime = System.currentTimeMillis() - startTime;

			logRequestTrace(traceId, url, requestBody, costTime, response);
			return processResponse(traceId, response, processor);
		} catch (GlobalException e) {
			log.error("[TraceId:{}] 请求会员系统业务异常！URL: {}", traceId, url, e);
			throw new GlobalException(e.getMessage());
		} catch (Exception e) {
			log.error("[TraceId:{}] 请求会员系统异常！URL: {}", traceId, url, e);
			throw SystemCode.SYSTEM_BUSY.exception();
		}
	}

	/**
	 * 构建HTTP请求
	 */
	private <T> HttpRequest buildHttpRequest(String url, T requestBody, String traceId) {
		var requestBuilder = HttpRequest.newBuilder()
									 .uri(URI.create(url))
									 .timeout(REQUEST_TIMEOUT);

		// 添加请求头
		String[] headers = buildHeaders(traceId);
		for (int i = 0; i < headers.length; i += 2) {
			requestBuilder.header(headers[i], headers[i + 1]);
		}

		// 添加请求体
		if (requestBody != null) {
			requestBuilder.POST(HttpRequest.BodyPublishers.ofString(JSON.toJSONString(requestBody)));
		} else {
			requestBuilder.POST(HttpRequest.BodyPublishers.noBody());
		}

		return requestBuilder.build();
	}

	/**
	 * 构建请求头
	 */
	private String[] buildHeaders(String traceId) {
		String currentTime = String.valueOf(System.currentTimeMillis() / 1000);
		return new String[]{
				"Content-Type", ContentType.JSON.toString(),
				"App-Id", appId,
				"Timestamp", currentTime,
				"Signature", SignUtils.generateSignature(appId, secretKey, currentTime),
				"System", String.valueOf(CommonPool.NUMBER_FOUR),
				"Source", String.valueOf(Platform.convertSourceType(ISystem.platformOpt().get())),
				"EnterpriseGuid", String.valueOf(ISystem.enterpriseGuidMust()),
				"OperSubjectGuid", String.valueOf(ISystem.platformIdMust()),
				"TraceId", traceId};
	}

	/**
	 * 处理响应结果
	 */
	private <R> R processResponse(String traceId, HttpResponse<String> response, Function<Object, R> processor) {
		try {
			// 检查响应体是否为空
			String responseBody = response.body();
			if (responseBody == null || responseBody.trim().isEmpty()) {
				log.error("[TraceId:{}] 响应体为空，HTTP状态码: {}", traceId, response.statusCode());
				throw new GlobalException("服务响应为空");
			}

			log.debug("[TraceId:{}] 原始响应体: {}", traceId, responseBody);

			// 安全解析JSON
			JSONObject responseJson;
			try {
				responseJson = JSON.parseObject(responseBody);
			} catch (Exception e) {
				log.error("[TraceId:{}] JSON解析失败，响应体: {}", traceId, responseBody, e);
				throw new GlobalException("响应格式错误");
			}

			// 检查JSON解析结果
			if (responseJson == null) {
				log.error("[TraceId:{}] JSON解析结果为null，响应体: {}", traceId, responseBody);
				throw new GlobalException("响应格式错误");
			}

			// 检查响应码
			Integer code = responseJson.getInteger("code");
			if (code == null) {
				log.error("[TraceId:{}] 响应中缺少code字段，响应体: {}", traceId, responseBody);
				throw new GlobalException("响应格式错误：缺少code字段");
			}

			if (code != HttpStatus.HTTP_OK) {
				String message = responseJson.getString("message");
				if (message == null || message.trim().isEmpty()) {
					message = "请求失败，错误码: " + code;
				}
				throw new GlobalException(message);
			}

			Object data = responseJson.get("data");
			return processor.apply(data);
		} catch (GlobalException e) {
			// 重新抛出业务异常
			throw e;
		} catch (Exception e) {
			log.error("[TraceId:{}] 处理响应时发生未知异常", traceId, e);
			throw new GlobalException("处理响应失败");
		}
	}

	/**
	 * 结构化日志记录 - 请求响应
	 */
	private void logRequestTrace(String traceId, String url, Object requestBody, long costMs, HttpResponse<String> response) {
		final String logTemplate =
			"%n========================================== Start ==========================================%n" +
			"TraceId        : %s%n" +
			"URL            : %s%n" +
			"Cost           : %d ms%n" +
			"Status         : %d%n" +
			"Request Args   : %s%n" +
			"Response Args  : %s%n" +
			"=========================================== End ===========================================%n";

		log.info(String.format(logTemplate,
				traceId,
				url,
				costMs,
				response.statusCode(),
				abbreviate(JSON.toJSONString(requestBody)),
				abbreviate(response.body()))
		);
	}

	/**
	 * 截断过长的字符串
	 */
	private String abbreviate(String str) {
		if (str == null) {
			return "";
		}
		if (str.length() <= 500) {
			return str;
		}
		return str.substring(0, 500) + "...";
	}

	/**
	 * 执行会员系统GET请求，返回原始响应体
	 *
	 * @param apiPath 接口路径
	 * @param params 请求参数
	 * @return 原始响应体字符串
	 */
	public <T> String executeMemberGetRequestRaw(String apiPath, T params) {
		return executeMemberGetRequest(apiPath, params, data -> {
			if (data == null) {
				return null;
			}
			return data.toString();
		});
	}

	/**
	 * 执行会员系统GET请求
	 *
	 * @param apiPath 接口路径
	 * @param params 请求参数
	 * @param responseType 返回对象类型
	 * @return 处理后的结果
	 */
	public <T, R> R executeMemberGetRequest(String apiPath, T params, Class<R> responseType) {
		return executeMemberGetRequest(apiPath, params, data -> {
			if (data == null) {
				return null;
			}
			return JSON.parseObject(data.toString(), responseType);
		});
	}

	/**
	 * 执行会员系统GET请求
	 *
	 * @param apiPath 接口路径
	 * @param params 请求参数
	 * @param elementType 数组元素类型
	 * @return 处理后的结果列表
	 */
	public <T, R> List<R> executeMemberGetRequestList(String apiPath, T params, Class<R> elementType) {
		return executeMemberGetRequest(apiPath, params, data -> {
			if (data == null) {
				return Collections.emptyList();
			}
			return JSON.parseArray(data.toString(), elementType);
		});
	}

	/**
	 * 执行会员系统GET请求
	 *
	 * @param apiPath 接口路径
	 * @param params 请求参数
	 * @param processor 响应处理器
	 * @return 处理后的结果
	 */
	private <T, R> R executeMemberGetRequest(String apiPath, T params, Function<Object, R> processor) {
		// 生成请求唯一标识
		String traceId = UUID.randomUUID().toString().replace("-", "");
		String url = buildGetUrl(memberApiBaseUrl + apiPath, params);
		HttpRequest request = buildGetHttpRequest(url, traceId);

		try {
			long startTime = System.currentTimeMillis();
			log.info("[TraceId:{}] 开始GET请求会员系统，URL: {}", traceId, url);

			HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
			long costTime = System.currentTimeMillis() - startTime;

			logRequestTrace(traceId, url, params, costTime, response);
			return processResponse(traceId, response, processor);
		} catch (GlobalException e) {
			log.error("[TraceId:{}] GET请求会员系统业务异常！URL: {}", traceId, url, e);
			throw new GlobalException(e.getMessage());
		} catch (Exception e) {
			log.error("[TraceId:{}] GET请求会员系统异常！URL: {}", traceId, url, e);
			throw SystemCode.SYSTEM_BUSY.exception();
		}
	}

	/**
	 * 构建GET请求URL
	 */
	private <T> String buildGetUrl(String baseUrl, T params) {
		if (params == null) {
			return baseUrl;
		}

		try {
			JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(params));
			StringBuilder urlBuilder = new StringBuilder(baseUrl);
			boolean isFirst = true;

			for (String key : jsonObject.keySet()) {
				Object value = jsonObject.get(key);
				if (value != null) {
					// URL编码处理
					String encodedKey = URLEncoder.encode(key, StandardCharsets.UTF_8);
					String encodedValue = URLEncoder.encode(value.toString(), StandardCharsets.UTF_8);

					urlBuilder.append(isFirst ? "?" : "&")
							.append(encodedKey)
							.append("=")
							.append(encodedValue);
					isFirst = false;
				}
			}

			String finalUrl = urlBuilder.toString();

			// URL长度限制检查（一般浏览器限制为2048字符）
			if (finalUrl.length() > 2000) {
				log.warn("构建的URL长度过长: {} 字符，可能导致请求失败", finalUrl.length());
			}

			return finalUrl;
		} catch (Exception e) {
			log.error("构建GET请求URL失败，baseUrl: {}, params: {}", baseUrl, params, e);
			throw new GlobalException("构建请求URL失败");
		}
	}

	/**
	 * 构建GET请求
	 */
	private HttpRequest buildGetHttpRequest(String url, String traceId) {
		var requestBuilder = HttpRequest.newBuilder()
				.uri(URI.create(url))
				.timeout(REQUEST_TIMEOUT)
				.GET();

		// 添加请求头
		String[] headers = buildHeaders(traceId);
		for (int i = 0; i < headers.length; i += 2) {
			requestBuilder.header(headers[i], headers[i + 1]);
		}

		return requestBuilder.build();
	}
}
