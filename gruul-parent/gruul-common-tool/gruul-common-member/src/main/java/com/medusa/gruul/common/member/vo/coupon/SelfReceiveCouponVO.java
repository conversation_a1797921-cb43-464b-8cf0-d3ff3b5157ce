package com.medusa.gruul.common.member.vo.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户领取的优惠券详情
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@Accessors(chain = true)
public class SelfReceiveCouponVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5392264759563246428L;

    /**
     * 优惠券记录GUID
     */
    private String couponLinkGuid;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 券码
     */
    private String code;

    /**
     * 优惠券类型
     */
    private Integer couponType;

    /**
     * 优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 优惠券有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveStartTime;

    /**
     * 优惠券有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveEndTime;

    /**
     * 发放状态
     */
    private Integer state;
}
