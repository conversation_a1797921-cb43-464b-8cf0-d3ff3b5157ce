package com.medusa.gruul.common.member.vo.coupon;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 自助领券活动详情VO
 * 专门用于前端展示自助领券活动页面
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
@Accessors(chain = true)
public class CouponPackageSelfDetailsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3824914245536567093L;

    // ==================================================公共字段
    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 优惠券编码
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    /**
     * 发放状态
     */
    private Integer activityState;

    /**
     * 优惠券集合
     */
    private List<CouponPackageSelfCouponVO> couponList;

    // ==================================================自助领券专用字段
    /**
     * 活动规则说明
     */
    private String activityRuleDescription;

    /**
     * 领券页面背景色（十六进制颜色值，如：#FF5733）
     */
    private String pageBackgroundColor;

    /**
     * 领券页面背景图片URL
     */
    private String pageBackgroundImage;

    /**
     * 是否允许领券分享 0：不允许 1：允许
     */
    private Integer allowShare;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享图片URL
     */
    private String shareImage;

    /**
     * 用户领券上限  0 不限制  1 限制
     */
    private Integer couponUpperLimitedType;

    /**
     * 用户领券上限数量
     */
    private Integer couponUpperLimitNum;

    /**
     * 用户已领取次数
     */
    private Integer receiveNum;

}
