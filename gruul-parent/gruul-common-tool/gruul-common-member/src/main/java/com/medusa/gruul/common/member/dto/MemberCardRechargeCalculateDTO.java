package com.medusa.gruul.common.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 会员卡充值计算
 * copy from member
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class MemberCardRechargeCalculateDTO {

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员卡guid
     */
    private String cardGuid;

    /**
     * 充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 会员持卡guid
     */
    private String memberInfoCardGuid;

}
