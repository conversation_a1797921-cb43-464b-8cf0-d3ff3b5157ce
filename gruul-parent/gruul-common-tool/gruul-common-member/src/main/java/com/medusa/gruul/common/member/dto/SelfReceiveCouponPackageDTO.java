package com.medusa.gruul.common.member.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户主动领券请求参数
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@Accessors(chain = true)
public class SelfReceiveCouponPackageDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -4307944617193952776L;

    /**
     * 活动GUID
     */
    private String activityGuid;

    /**
     * 会员GUID
     */
    private String memberGuid;
}
