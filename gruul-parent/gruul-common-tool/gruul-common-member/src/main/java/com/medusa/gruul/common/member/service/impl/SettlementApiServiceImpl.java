package com.medusa.gruul.common.member.service.impl;

import com.medusa.gruul.common.member.dto.settlement.*;
import com.medusa.gruul.common.member.service.SettlementApiService;
import com.medusa.gruul.common.member.utils.HttpApiClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 会员结算台服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SettlementApiServiceImpl implements SettlementApiService {
    private final HttpApiClient httpApiClient;

    //获取结算台优惠列表
    private static final String GET_DISCOUNT_APPLY_LIST_URL = "/member/settlement/get/discount/list";

    //下单前计算优惠
    private static final String DISCOUNT_CALCULATE_URL = "/member/settlement/discount/calculate";

    //下单后锁定优惠
    private static final String LOCKED_DISCOUNT_URL = "/member/settlement/discount/locked";

    //解锁释放优惠
    private static final String UNLOCKED_DISCOUNT_URL = "/member/settlement/discount/unlocked";

    //订单完成 优惠活动记录回调
    private static final String AFTER_ORDER_DISCOUNT_CALLBACK_URL = "/member/settlement/order/after/discount_callback";

    //订单退款 优惠活动记录回调
    private static final String BARK_ORDER_DISCOUNT_CALLBACK_URL = "/member/settlement/order/bark/discount_callback";

    //获取会员等级折扣商品
    private static final String GET_MEMBER_LEVEL_DISCOUNT_URL = "/member/settlement/get/member/price/commodity";

    /**
     * 获取结算台优惠列表
     * @param dto 结算台优惠列表查询条件
     * @return 结算台优惠列表
     */
    @Override
    public SettlementApplyOrderShowVO getDiscountApplyList(SettlementApplyOrderDTO dto) {
        log.info("获取结算台优惠列表, 入参: {}", dto);
        return httpApiClient.executeMemberRequest(GET_DISCOUNT_APPLY_LIST_URL, dto, SettlementApplyOrderShowVO.class);
    }

    /**
     * 下单前计算优惠
     *
     * @param dto 订单入参
     * @return 优惠结果对象
     */
    @Override
    public SettlementApplyOrderShowVO discountCalculate(SettlementApplyOrderDTO dto) {
        log.info("下单前结算台计算优惠, 入参: {}", dto);
        return httpApiClient.executeMemberRequest(DISCOUNT_CALCULATE_URL, dto, SettlementApplyOrderShowVO.class);
    }

    /**
     * 下单后锁定优惠
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @Override
    public Boolean lockedDiscount(SettlementOrderLockDTO dto) {
        log.info("下单后结算台锁定优惠, 入参: {}", dto);
        return httpApiClient.executeMemberRequest(LOCKED_DISCOUNT_URL, dto, Boolean.class);
    }

    /**
     * 取消订单、退款：释放优惠
     * 食堂调用的是 ： /accumulation_discount_release_key
     * @param dto 订单参数
     * @return 优惠列表
     */
    @Override
    public Boolean lockedDiscount(SettlementUnLockedDiscountDTO dto) {
        log.info("取消订单、退款：释放优惠, 入参: {}", dto);
        return httpApiClient.executeMemberRequest(UNLOCKED_DISCOUNT_URL, dto, Boolean.class);
    }

    /**
     * 订单退款 优惠活动记录回调
     */
    @Override
    public void barkOrderDiscountCallback(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
        log.info("订单退款 优惠活动记录回调, 入参: {}", barkOrderDiscountCallbackQO);
        httpApiClient.executeMemberRequestRaw(BARK_ORDER_DISCOUNT_CALLBACK_URL, barkOrderDiscountCallbackQO);
    }

    /**
     *  订单完成 优惠活动记录回调
     */
    @Override
    public void afterOrderDiscountCallback(AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO) {
        log.info("订单完成 优惠活动记录回调, 入参: {}", afterOrderDiscountCallbackQO);
        httpApiClient.executeMemberRequestRaw(AFTER_ORDER_DISCOUNT_CALLBACK_URL, afterOrderDiscountCallbackQO);
    }

    @Override
    public MemberPriceApplyCommodityVO getMemberPriceApplyCommodity(MemberPriceApplyCommodityQO qo) {
        log.info("获取会员等级折扣商品, 入参: {}", qo);
        return httpApiClient.executeMemberRequest(GET_MEMBER_LEVEL_DISCOUNT_URL, qo, MemberPriceApplyCommodityVO.class);
    }
}
