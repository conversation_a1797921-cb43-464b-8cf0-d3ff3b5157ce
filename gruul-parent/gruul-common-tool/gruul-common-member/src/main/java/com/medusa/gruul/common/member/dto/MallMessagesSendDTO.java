package com.medusa.gruul.common.member.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @description 消息通知配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MallMessagesSendDTO extends MessagesSendBaseDTO implements Serializable {

    /**
     * 商品信息
     */
    private String goodsInfo;

    /**
     * 发货时间
     */
    private String deliveryTime;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 快递单号
     */
    private String expressNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配送方式
     */
    private String deliveryType;

    /**
     * 配送人
     */
    private String deliveryPerson;

    /**
     * 快递员电话
     */
    private String deliveryPhone;

    /**
     * 温馨提示
     */
    private String warmPrompt;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 取货时段
     */
    private String pickupTimeSlot;

    /**
     * 取货码
     */
    private String pickupCode;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 包裹id
     */
    private String packageId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 店铺ID
     */
    private String shopId;
}
