package com.medusa.gruul.common.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.medusa.gruul.common.member.enums.coupon.CouponTypeEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员优惠券
 *
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Accessors(chain = true)
public class MemberCouponQueryDTO {
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 会员guid
     */
    @NotBlank(message = "会员guid必填")
    private String memberGuid;

    /**
     * 0可使用（默认） 1历史
     */
    private Integer useType = 0;

    /**
     * 过期时间
     */
    @JsonIgnore
    private LocalDateTime expireDate;

    /**
     * 历史券才需要，分页查询使用
     * 开始时间:首次为空，第二次为上次最后一条记录的时间(pageTime)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pageTime;

    /**
     * 用于分页
     * 开始id:首次为空，第二次为上次最后一条记录的id
     */
    private Long pageId;

    /**
     * 每页记录数
     */
    private Integer pageSize = 20;

    private List<String> discountOptionId;

    /**
     * 优惠券查询类型
     * @see CouponTypeEnum
     */
    private Integer couponType;

}
