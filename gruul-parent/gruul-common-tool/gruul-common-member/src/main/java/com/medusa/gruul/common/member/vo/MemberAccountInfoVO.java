package com.medusa.gruul.common.member.vo;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员的账户信息 （优惠券、积分、余额、会员卡、会员等级、成长值）
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@EqualsAndHashCode (callSuper = true)
@Data
@Accessors (chain = true)
public class MemberAccountInfoVO extends MemberBasicInfoVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6191108476876439011L;
    /**
     * 优惠券
     */
    private Integer unwrittenCouponCount;

    /**
     * 会员等级
     */
    private String memberGradeInfoName;

    /**
     * 成长值
     */
    private Integer memberGrowthValue;

    /**
     * 有效会员卡数量
     */
    private Integer memberCardCount;


}
