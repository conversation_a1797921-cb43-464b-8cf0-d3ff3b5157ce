<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-parent</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-tool</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>gruul-common-idem</module>
        <module>gruul-common-log</module>
        <module>gruul-common-member</module>
        <module>gruul-common-model</module>
        <module>gruul-common-xxl-job</module>
        <module>gruul-common-wechat</module>
        <module>gruul-common-validator</module>
        <module>gruul-common-encrypt</module>
        <module>gruul-common-ipaas</module>
        <module>gruul-common-saas-cloud</module>
        <module>gruul-common-api</module>
        <module>gruul-common-utils</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul.global</groupId>
            <artifactId>gruul-global-i18n</artifactId>
        </dependency>
    </dependencies>
</project>