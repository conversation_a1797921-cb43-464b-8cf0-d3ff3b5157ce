package com.medusa.gruul.common.ipaas.model.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 平台类目树对象
 *
 * <AUTHOR>
 * @date 2025/5/30 14:35
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformCategoryTreeVO extends PlatformCategoryVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 销售分组商品数量
     */
    @JsonProperty("goods_count")
    private Integer goodsCount;

    /**
     * 销售分组子节点
     */
    private List<PlatformCategoryTreeVO> children;

    /**
     * 图片
     */
    private PictureVO picture;
}
