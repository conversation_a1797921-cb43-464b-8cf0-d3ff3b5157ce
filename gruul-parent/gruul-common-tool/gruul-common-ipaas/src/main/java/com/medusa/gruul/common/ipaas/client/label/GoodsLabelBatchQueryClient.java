package com.medusa.gruul.common.ipaas.client.label;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.ipaas.client.IpaasClient;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.model.IpaasResponse;
import com.medusa.gruul.common.ipaas.model.label.GoodsLabelQueryReqDTO;
import com.medusa.gruul.common.ipaas.model.label.GoodsLabelVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询商品标签列表
 *
 * <AUTHOR>
 * @date 2024/3/21
 */
@Getter
@Setter
@NoArgsConstructor
public class GoodsLabelBatchQueryClient implements IpaasClient<GoodsLabelQueryReqDTO, List<GoodsLabelVO>> {

    private IpaasClientSupport clientSupport;

    private String goodsLabelQueryUrl = "/api/goods-label/list/v1";

    public GoodsLabelBatchQueryClient(IpaasClientSupport clientSupport) {
        this.clientSupport = clientSupport;
    }

    @Override
    public List<GoodsLabelVO> execute(GoodsLabelQueryReqDTO dto) {
        Map<String, Object> params = new HashMap<>();

        Map<String, String> extraHeaders = new HashMap<>();
        extraHeaders.put("company_id", String.valueOf(dto.getEnterpriseId()));

        String response = clientSupport.apiPostJsonDataWithHeaders(JSON.toJSONString(params), goodsLabelQueryUrl, extraHeaders);
        IpaasResponse<GoodsLabelVO, List<GoodsLabelVO>> ipaasResponse = JSON.parseObject(response, new TypeReference<>() {
        });
        if (ipaasResponse == null || ipaasResponse.getData() == null) {
            return null;
        }
        return ipaasResponse.getData();
    }
} 