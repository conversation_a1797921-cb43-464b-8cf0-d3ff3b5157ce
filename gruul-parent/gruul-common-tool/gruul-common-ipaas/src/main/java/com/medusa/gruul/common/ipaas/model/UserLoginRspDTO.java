package com.medusa.gruul.common.ipaas.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserLoginRspDTO{

    /**
     * 认证中心颁发合法令牌
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * 令牌有效期，单位（秒）
     */
    @JSONField(name = "expires_in")
    private Integer expiresIn;

    /**
     * 令牌刷新时效，单位（秒）
     */
    @JSONField(name = "refresh_expires_in")
    private Integer refreshExpiresIn;

    /**
     * 令牌刷新
     */
    @JSONField(name = "refresh_token")
    private String refreshToken;

    /**
     * 默认为 Bearer
     */
    @JSONField(name = "token_type")
    private String tokenType;

    @JSONField(name = "not-before-policy")
    private Integer notBeforePolicy;

    @JSONField(name = "session_state")
    private String sessionState;

    private String scope;

}
