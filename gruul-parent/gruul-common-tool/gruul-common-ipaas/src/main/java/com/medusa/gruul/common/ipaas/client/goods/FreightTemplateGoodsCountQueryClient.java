package com.medusa.gruul.common.ipaas.client.goods;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.medusa.gruul.common.ipaas.client.IpaasClient;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.model.IpaasResponse;
import com.medusa.gruul.common.ipaas.model.goods.FreightTemplateGoodsCountQueryReqDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询运费模板绑定商品数量
 *
 * <AUTHOR>
 * @date 2025/5/28
 */
@Getter
@Setter
@NoArgsConstructor
public class FreightTemplateGoodsCountQueryClient implements IpaasClient<FreightTemplateGoodsCountQueryReqDTO, Long> {

    private IpaasClientSupport clientSupport;

    private String freightTemplateGoodsCountQueryUrl = "/api/store-goods/freight-template/count/v1";

    public FreightTemplateGoodsCountQueryClient(IpaasClientSupport clientSupport) {
        this.clientSupport = clientSupport;
    }

    @Override
    public Long execute(FreightTemplateGoodsCountQueryReqDTO dto) {
        Map<String, Object> params = new HashMap<>();
        params.put("freight_template_id", dto.getFreightTemplateTd());
        Map<String, String> extraHeaders = new HashMap<>();
        extraHeaders.put("company_id", String.valueOf(dto.getEnterpriseId()));

        String response = clientSupport.apiPostJsonDataWithHeaders(JSON.toJSONString(params), freightTemplateGoodsCountQueryUrl, extraHeaders);
        IpaasResponse<Long, Long> ipaasResponse = JSON.parseObject(response, new TypeReference<>() {
        });
        if (ipaasResponse == null || ipaasResponse.getData() == null) {
            return null;
        }
        return ipaasResponse.getData();
    }
}

