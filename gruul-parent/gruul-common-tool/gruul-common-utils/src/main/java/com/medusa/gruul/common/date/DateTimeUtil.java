package com.medusa.gruul.common.date;


import com.medusa.gruul.common.enums.TimeTypeEnum;

import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

import cn.hutool.core.text.CharSequenceUtil;

/**
 *
 * hutool-date工具之外的个性化DateTimeUtil
 * <AUTHOR>
 * @version 1.0, 2025/7/22
 */
public class DateTimeUtil {

	private static final String MONTH_WEEK = "{0}月第{1}周";

	private static final String SEASON = "%s季度";
	private static final String SEASON_ENG = "Q%s";

	private static final ZoneId SYSTEM_DEFAULT = ZoneId.systemDefault();

	public static String localDateTime2String(LocalDateTime localDateTime, String pattern) {
		return DateTimeFormatter.ofPattern(pattern).format(localDateTime);
	}

	/**
	 * date与localDate、localDatetime、时间戳互转
	 *
	 */
	public static LocalDateTime date2LocalDateTime(Date date) {
		if (date ==null) return null;
		return date.toInstant().atZone(SYSTEM_DEFAULT).toLocalDateTime();
	}

	public static Date localDateTime2Date(LocalDateTime localDateTime) {
		if (localDateTime == null) {
			return null;
		}
		return Date.from(localDateTime.atZone(SYSTEM_DEFAULT).toInstant());
	}

	public static Date localDate2Date(LocalDate localDate) {
		return Date.from(localDate.atStartOfDay(SYSTEM_DEFAULT).toInstant());
	}

	public static LocalDate date2LocalDate(Date date) {
		return date.toInstant().atZone(SYSTEM_DEFAULT).toLocalDate();
	}

	public static String localDate2String(LocalDate localDate, String pattern) {
		return DateTimeFormatter.ofPattern(pattern).format(localDate);
	}

	public static LocalDateTime timestamp2LocalDateTime(long timestamp) {
		return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), SYSTEM_DEFAULT);
	}


	/**
	 * @return 最后一天
	 * @description: 获取某年某月的最后一天
	 * @param: year 年
	 * @param: month 月
	 * <AUTHOR>
	 * @date: 12:08
	 */
	public static int getLastDayOfMonth(int year, int month) {
		LocalDateTime localDateTime = LocalDateTime.of(year, month, 1, 0, 0, 0);
		LocalDate localDate = localDateTime.toLocalDate();
		LocalDate lastLocalDate = localDate.with(TemporalAdjusters.lastDayOfMonth());
		return lastLocalDate.getDayOfMonth();
	}


	/**
	 * 根据类型，计算两日期之差【有正负数，时间1大则为负数】
	 * 此方法会计算时分秒，不会以天为单位
	 *
	 * @param date1        时间1
	 * @param date2        时间2
	 * @param timeTypeEnum 事件类型 ：计算 时分秒
	 * @return 计算结果
	 */
	public static long getTimeBetween(Date date1, Date date2, TimeTypeEnum timeTypeEnum) {
		LocalDateTime time1 = DateTimeUtil.date2LocalDateTime(date1);
		LocalDateTime time2 = DateTimeUtil.date2LocalDateTime(date2);
		return getChronoUnit(timeTypeEnum).between(time1, time2);
	}


	/**
	 * 自定义枚举获取日期时间单位
	 *
	 * @param timeTypeEnum 时间类型枚举
	 * @return 日期时间单位
	 */
	private static ChronoUnit getChronoUnit(TimeTypeEnum timeTypeEnum) {
		switch (timeTypeEnum) {
			case MILLISECOND:
				return ChronoUnit.MILLIS;
			case SECOND:
				return ChronoUnit.SECONDS;
			case MINUTE:
				return ChronoUnit.MINUTES;
			case HOUR:
				return ChronoUnit.HOURS;
			case DAY:
				return ChronoUnit.DAYS;
			case WEEK:
				return ChronoUnit.WEEKS;
			case MONTH:
				return ChronoUnit.MONTHS;
			case YEAR:
				return ChronoUnit.YEARS;
			default:
		}
		return ChronoUnit.NANOS;
	}


	/**
	 * 根据类型获取指定日期开始结束时间【如类型为周，则获取此周第一天和最后一天，以此类推】
	 *
	 * @param localDate    指定日期
	 * @param timeTypeEnum 事件类型
	 * @return 返回结果
	 */
	public static LocalDate[] findTimeStartAndEnd(LocalDate localDate, TimeTypeEnum timeTypeEnum) {
		LocalDate start;
		LocalDate end;
		switch (timeTypeEnum) {
			// 天
			case DAY:
				start = localDate;
				end = localDate;
				break;
			// 周
			case WEEK:
				start = localDate.with(DayOfWeek.MONDAY);
				end = localDate.with(DayOfWeek.SUNDAY);
				break;
			// 月
			case MONTH:
				start = localDate.with(TemporalAdjusters.firstDayOfMonth());
				end = localDate.with(TemporalAdjusters.lastDayOfMonth());
				break;
			// 季度
			case SEASON:
				return findSeasonSE(localDate);
			// 年
			case YEAR:
				start = localDate.with(TemporalAdjusters.firstDayOfYear());
				end = localDate.with(TemporalAdjusters.lastDayOfYear());
				break;
			default:
				return new LocalDate[0];
		}
		return new LocalDate[]{start, end};
	}

	public static LocalDate[] findTimeStartAndEnd(LocalDateTime localDateTime, TimeTypeEnum timeTypeEnum) {
		return findTimeStartAndEnd(localDateTime.toLocalDate(), timeTypeEnum);
	}
	/**
	 * 查询季节开始和结束时间
	 *
	 * @param localDate 指定日期
	 * @return 查询结果
	 */
	private static LocalDate[] findSeasonSE(LocalDate localDate) {
		LocalDate start = localDate;
		LocalDate end = localDate;
		switch (localDate.getMonth()) {
			// 1月1日和3月31日
			case JANUARY:
			case FEBRUARY:
			case MARCH:
				start = localDate.withMonth(1).withDayOfMonth(1);
				end = localDate.withMonth(4).withDayOfMonth(1).plusDays(-1);
				break;
			// 4月1日和6月30日
			case APRIL:
			case MAY:
			case JUNE:
				start = localDate.withMonth(4).withDayOfMonth(1);
				end = localDate.withMonth(7).withDayOfMonth(1).plusDays(-1);
				break;
			// 7月1日和9月30日
			case JULY:
			case AUGUST:
			case SEPTEMBER:
				start = localDate.withMonth(7).withDayOfMonth(1);
				end = localDate.withMonth(10).withDayOfMonth(1).plusDays(-1);
				break;
			// 10月1日和12月31日
			case OCTOBER:
			case NOVEMBER:
			case DECEMBER:
				start = localDate.withMonth(10).withDayOfMonth(1);
				end = localDate.withMonth(1).withDayOfMonth(1).plusYears(1).plusDays(-1);
				break;
			default:
		}
		return new LocalDate[]{start, end};
	}


	public static LocalDate string2LocalDate(String time, String pattern) {
		return LocalDate.parse(time, DateTimeFormatter.ofPattern(pattern));
	}

	/**
	 * 获取季度文案：第x季度（按照1，4，7，10计算）
	 *
	 * @param time 时间时间
	 * @return 查询结果
	 */
	public static String getSeasonStr(LocalDate time) {
		int season = 0;
		int monthValue = time.getMonthValue();
		switch (monthValue) {
			case 1:
			case 2:
			case 3:
				season = 1;
				break;
			case 4:
			case 5:
			case 6:
				season = 2;
				break;
			case 7:
			case 8:
			case 9:
				season = 3;
				break;
			case 10:
			case 11:
			case 12:
				season = 4;
				break;
			default:
		}
		return String.format(SEASON, season);
	}

	/**
	 * 获取当前时间所在周是月里的第几周（用周一计算）
	 *
	 * @param localDate 时间
	 * @return 周数
	 */
	public static int getMonthWeekDay(LocalDate localDate) {
		final LocalDate monday = localDate.with(DayOfWeek.MONDAY);
		// 获取其所在月的日数
		final int dayOfMonth = monday.getDayOfMonth();
		// 计算时间为当月第几周
		int weekNum = (dayOfMonth / 7) + 1;
		// 如果刚好是7的倍数，将计算结果-1
		if (dayOfMonth % 7 == 0) {
			weekNum--;
		}
		return weekNum;
	}

	/**
	 * 获取月+周描述（8月第5周）
	 *
	 * @param localDate 时间四件
	 * @return 查询结果
	 */
	public static String getMonthWeek(LocalDate localDate) {
		// 计算当月周数
		int weekNum = getMonthWeekDay(localDate);
		return MessageFormat.format(MONTH_WEEK, localDate.getMonthValue(), weekNum);
	}


	/**
	 * 根据枚举计算指定日期，time为正数往后加，time为负数往前加
	 *
	 * @param localDateTime 指定日期
	 * @param timeTypeEnum  时间类型
	 * @param time          需要计算的刻度
	 * @return 计算后的时间
	 */
	public static LocalDateTime calLocalDate(LocalDateTime localDateTime, TimeTypeEnum timeTypeEnum, int time) {
		switch (timeTypeEnum) {
			case SECOND:
				localDateTime = localDateTime.plusSeconds(time);
				break;
			case MINUTE:
				localDateTime = localDateTime.plusMinutes(time);
				break;
			case HOUR:
				localDateTime = localDateTime.plusHours(time);
				break;
			case DAY:
				localDateTime = localDateTime.plusDays(time);
				break;
			case WEEK:
				localDateTime = localDateTime.plusWeeks(time);
				break;
			case MONTH:
				localDateTime = localDateTime.plusMonths(time);
				break;
			case SEASON:
				localDateTime = localDateTime.plusMonths(time * 3L);
				break;
			case YEAR:
				localDateTime = localDateTime.plusYears(time);
				break;
			default:
				return null;
		}
		return localDateTime;
	}

	/**
	 * 根据类型，计算两日期之差【有正负数，时间1大则为负数】
	 * 以天为单位计算
	 *
	 * @param date1 时间1
	 * @param date2 时间2
	 * @return 计算结果
	 */
	public static long getTimeBetweenDay(Date date1, Date date2) {
		if (date1 != null && date2 != null) {
			LocalDate time1 = DateTimeUtil.date2LocalDate(date1);
			LocalDate time2 = DateTimeUtil.date2LocalDate(date2);
			return getChronoUnit(TimeTypeEnum.DAY).between(time1, time2);
		}
		return 0;
	}

	public static Long localDateTime2Timestamp(LocalDateTime localDateTime) {
		return localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
	}

	/**
	 * 相隔多少天【有正负数，时间1大则为负数】
	 *
	 * @param time1 时间1
	 * @param time2 时间2
	 * @return 天数
	 */
	public static long betweenDay(LocalDateTime time1, LocalDateTime time2) {
		return getChronoUnit(TimeTypeEnum.DAY).between(time1, time2);
	}

	/**
	 * 将分钟数转换为 “n天n小时n分钟”
	 * @param minute 分钟数
	 * @return %d天%d时%d分
	 */
	public static String betweenMinuteFormat(long minute) {
		int minuteOfHour = 60;
		int minuteOfDay = minuteOfHour * 24;

		long dayNum = minute / minuteOfDay;
		// 除开天后的分钟数
		long moduloOfDay = minute % minuteOfDay;

		long hourNum = moduloOfDay / minuteOfHour;
		long minuteNum = moduloOfDay % minuteOfHour;
		return String.format("%d天%d时%d分", dayNum, hourNum, minuteNum);
	}



	/**
	 * 毫秒时间戳与LocalDateTime比较
	 *
	 * @return 毫秒时间戳大于LocalDateTime
	 */
	public static boolean compare(long timestamp, LocalDateTime dateTime) {
		long dateTimeStamp = localDateTime2Timestamp(dateTime) / 1000;
		return timestamp / 1000 > dateTimeStamp;
	}


	/**
	 * 获取上一周时间
	 *
	 * @param localDate 对应时间，传null查询当前时间
	 * @return 查询结果
	 */
	public static LocalDate[] lastWeek(LocalDate localDate) {
		localDate = localDate == null ? LocalDate.now() : localDate;
		LocalDate[] startAndEnd = findTimeStartAndEnd(localDate, TimeTypeEnum.WEEK);
		startAndEnd[0] = startAndEnd[0].minusWeeks(1);
		startAndEnd[1] = startAndEnd[1].minusWeeks(1);
		return startAndEnd;
	}

	/**
	 * 获取下一周时间
	 *
	 * @param localDate 对应时间，传null查询当前时间
	 * @return 查询结果
	 */
	public static LocalDate[] nextWeek(LocalDate localDate) {
		localDate = localDate == null ? LocalDate.now() : localDate;
		LocalDate[] startAndEnd = findTimeStartAndEnd(localDate, TimeTypeEnum.WEEK);
		startAndEnd[0] = startAndEnd[0].plusWeeks(1);
		startAndEnd[1] = startAndEnd[1].plusWeeks(1);
		return startAndEnd;
	}

	/**
	 * 根据时间获取周几
	 *
	 * @param localDateTime 时间
	 * @return 周几
	 */
	public static String getWeekDay(LocalDateTime localDateTime) {
		return getWeekDay(localDateTime.toLocalDate());
	}

	/**
	 * 根据时间获取周几
	 *
	 * @param localDate 时间
	 * @return 周几
	 */
	public static String getWeekDay(LocalDate localDate) {
		final DayOfWeek dayOfWeek = localDate.getDayOfWeek();
		return switch (dayOfWeek) {
			case MONDAY -> "周一";
			case TUESDAY -> "周二";
			case WEDNESDAY -> "周三";
			case THURSDAY -> "周四";
			case FRIDAY -> "周五";
			case SATURDAY -> "周六";
			case SUNDAY -> "周日";
		};
	}

	public static LocalDateTime string2LocalDateTime(String time, String pattern) {
		return LocalDateTime.parse(time, DateTimeFormatter.ofPattern(pattern));
	}

	public static LocalTime date2LocalTime(Date date) {
		return date.toInstant().atZone(SYSTEM_DEFAULT).toLocalTime();
	}

	/**
	 * localDateTime转String
	 *
	 * @param localDateTime 指定日期
	 * @param pattern       指定格式
	 * @return 返回格式化后的字符串
	 */
	public static String formatCron(LocalDateTime localDateTime, String pattern) {
		if (null == localDateTime || ObjectUtils.isEmpty(pattern)) {
			return null;
		}
		int currentYear = localDateTime.getYear();
		String lastYear = String.valueOf(currentYear - 1);
		String cron = CharSequenceUtil.replace(DateUtil.DATEFORMAT, DateUtil.LAST_YEAR, lastYear);
		return localDateTime2String(localDateTime, cron);
	}

	/**
	 * 在数据库中，时间类型会有500ms的进位，如果使用LocalTime.MAX保存后时间会变为第二天
	 * 为了避免此情况写的方法，稍微转化一下（去掉毫秒级）
	 *
	 * @param localDateTime 时间
	 * @return 转化后的时间
	 */
	public static LocalDateTime filterSql(LocalDateTime localDateTime) {
		final String string = localDateTime2String(localDateTime, DateUtil.DEFAULT_FORMAT);
		return string2LocalDateTime(string, DateUtil.DEFAULT_FORMAT);
	}

	/**
	 * 获取季度值（第n季度）
	 *
	 * @param localDate 指定日期
	 * @return 查询结果
	 */
	public static int getSeasonNumber(LocalDate localDate) {
		return switch (localDate.getMonth()) {
			// 第一季度，1、2、3月
			case JANUARY, FEBRUARY, MARCH -> 1;
			// 第二季度，4、5、6月
			case APRIL, MAY, JUNE -> 2;
			// 第三季度，7、8、9月
			case JULY, AUGUST, SEPTEMBER -> 3;
			default -> 4;
		};
	}
	/**
	 * 获取季度文案：第x季度（按照1，4，7，10计算）
	 *
	 * @param time 时间时间
	 * @return 查询结果
	 */
	public static String getSeasonStrBrief(LocalDate time) {
		int season = 0;
		switch (time.getMonthValue()) {
			case 1:
				season = 1;
				break;
			case 4:
				season = 2;
				break;
			case 7:
				season = 3;
				break;
			case 10:
				season = 4;
				break;
			default:
		}
		return String.format(SEASON_ENG, season);
	}

}
