package com.medusa.gruul.common.date;


import com.medusa.gruul.common.enums.TimeTypeEnum;

import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * hutool-date工具之外的个性化DateUtil
 * <AUTHOR>
 * @version 1.0, 2025/7/22
 */
@Slf4j
public class DateUtil {

	/**
	 * 默认日期格式
	 */
	public static final String DEFAULT_DATE = "yyyy/MM/dd";

	public static final String DATE_MINITE_LINE = "yyyy-MM-dd";

	public static final String YEAR_MONTH_DAY = "yyyy年MM月dd日";

	public static final String DEFAULT_DATE_MONTH = "yyyy-MM";

	public static final String YEAR_MONTH = "yyyy/MM";

	/**
	 * cron格式，只执行一次
	 */
	public static final String DATEFORMAT = "ss mm HH dd MM ? lastYYYY-YYYY";

	public static final String DEFAULT_DATE_TIME = "yyyy/MM/dd HH:mm:ss";

	public static final String DEFAULT_DATE_MIN_TIME = "yyyy/MM/dd HH:mm";

	public static final String MONTH_DAY = "MM/dd";

	public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

	public static final String LAST_YEAR = "lastYYYY";

	public static final String DEFAULT_FORMAT_MIN_TIME = "yyyy-MM-dd HH:mm";

	public static final String TIME = "HH:mm";

	public static final String DEFAULT_FORMAT_MONTH_MIN_TIME = "MM月dd日 HH:mm";

	public static final String DEFAULT_DATE_TIME_POINT = "yyyy.MM.dd HH:mm:ss";


	public static String getSameYearDate(Date endTimeDate) {
		//同年  月/日
		if (isSameYear(endTimeDate, new Date())) {
			return format(endTimeDate, MONTH_DAY);
		} else {
			return format(endTimeDate, DEFAULT_DATE);
		}
	}

	/**
	 * 判断两个日期是否是同年
	 *
	 * @param date1 日期1
	 * @param date2 日期2
	 * @return 是否同年
	 */
	public static boolean isSameYear(Date date1, Date date2) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date1);
		int year1 = calendar.get(Calendar.YEAR);
		calendar.setTime(date2);
		int year2 = calendar.get(Calendar.YEAR);
		return year1 == year2;
	}


	/**
	 * 获取指定日期当月第一天
	 *
	 * @param date 日期
	 * @return 操作结果
	 */
	public static Date findMonthStart(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return findDayStart(calendar.getTime());
	}

	/**
	 * 获取指定日期当月最后一天
	 *
	 * @param date 日期
	 * @return 操作结果
	 */
	public static Date findMonthLast(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.roll(Calendar.DAY_OF_MONTH, -1);
		return calendar.getTime();
	}


	/**
	 * 时间戳转String
	 *
	 * @param timestamp 时间戳
	 * @param pattern   指定格式
	 * @return 返回格式化后的字符串
	 * @version 4.0.0
	 */
	public static String format(long timestamp, String pattern) {
		return (0 == timestamp || StrUtil.isEmpty(pattern)) ?
					   null : DateTimeUtil.localDateTime2String(DateTimeUtil.timestamp2LocalDateTime(timestamp), pattern);
	}

	/**
	 * Date转String
	 *
	 * @param date    指定日期
	 * @param pattern 指定格式
	 * @return 返回格式化后的字符串
	 */
	public static String format(Date date, String pattern) {
		if (null == date || !StringUtils.hasText(pattern)) {
			return null;
		}
		return DateTimeUtil.localDateTime2String(DateTimeUtil.date2LocalDateTime(date), pattern);
	}

	/**
	 * 字符串转日期
	 *
	 * @param date   日期
	 * @param format 日期格式
	 * @return 转换后的日期
	 */
	public static Date string2Date(String date, String format) {
		if (StringUtils.hasText(date) && StringUtils.hasText(format)) {
			DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
			try {
				LocalDateTime localDateTime = LocalDateTime.parse(date, dateTimeFormatter);
				return DateTimeUtil.localDateTime2Date(localDateTime);
			} catch (DateTimeException e) {
				LocalDate localDate = LocalDate.parse(date, dateTimeFormatter);
				return localDate2Date(localDate);
			}
		}
		return null;
	}


	public static Date localDate2Date(LocalDate localDate) {
		return DateTimeUtil.localDate2Date(localDate);
	}

	/**
	 * date与localDate、localDatetime、时间戳互转
	 */

	public static LocalDate date2LocalDate(Date date) {
		return DateTimeUtil.date2LocalDate(date);
	}

	/**
	 * 检测对象，返回指定类型时间字符串
	 *
	 * @param obj    对象属性
	 * @param format 指定类型
	 * @return 字符串
	 */
	public static String checkObject(Object obj, String format) {
		return (null == obj) ? null : format((Date) obj, format);
	}


	/**
	 * Date转String
	 *
	 * @param date    指定日期
	 * @param pattern 指定格式
	 * @return 返回格式化后的字符串
	 */
	public static String formatCron(Date date, String pattern) {
		if (null == date || StrUtil.isEmpty(pattern)) {
			return null;
		}
		int currentYear = getYear(date);
		String lastYear = String.valueOf(currentYear - 1);
		String cron = StrUtil.replace(DATEFORMAT, LAST_YEAR, lastYear);
		return DateTimeUtil.localDateTime2String(DateTimeUtil.date2LocalDateTime(date), cron);
	}


	/**
	 * 根据时间获取年份
	 *
	 * @param date 时间
	 * @return 年
	 */
	public static int getYear(Date date) {
		if (date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			return calendar.get(Calendar.YEAR);
		}
		return 0;
	}



	/**
	 * 查询指定日期 00:00:00
	 *
	 * @return 00:00:00
	 *
	 */
	public static Date findDayStart(Date date) {
		if (date == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 查询指定日期 00:00:00
	 *
	 * @return 00:00:00
	 * @version 4.0.0
	 */
	public static Date findDayStart() {
		return findTimeStartAndEnd()[0];
	}

	/**
	 * 获取两个日期相差的天数，如果不满足1天，默认以1天计算，否则相差几天则就是几天
	 *
	 * @param now     当前事件
	 * @param endDate 结束时间
	 * @return 相差天数
	 */
	public static int betweenDay(Date now, Date endDate) {
		if (Objects.isNull(now) || Objects.isNull(endDate)) {
			return 0;
		}
		if (now.after(endDate)) {
			return 0;
		}
		boolean sameDate = cn.hutool.core.date.DateUtil.isSameDay(now, endDate);
		if (sameDate) {
			return 1;
		}
		Date start = findDayStart(now);
		Date endStart = findDayStart(endDate);
		long diffInMilliseconds = endStart.getTime() - start.getTime();
		long diffInDays = TimeUnit.DAYS.convert(diffInMilliseconds, TimeUnit.MILLISECONDS);
		return (int) diffInDays + 1;
	}



	/**
	 * 根据类型，计算两日期之差【有正负数，时间1大则为负数】
	 * 此方法会计算时分秒，不会以天为单位
	 *
	 * @param date1        时间1
	 * @param date2        时间2
	 * @param timeTypeEnum 事件类型 ：计算 时分秒
	 * @return 计算结果
	 * @version 4.0.0
	 */
	public static long getTimeBetween(Date date1, Date date2, TimeTypeEnum timeTypeEnum) {
		return DateTimeUtil.getTimeBetween(date1, date2, timeTypeEnum);
	}

	/**
	 * 根据类型获取指定日期开始结束时间【如类型为周，则获取此周第一天0点和最后一天23:59:59，以此类推】
	 *
	 * @param date         指定日期
	 * @param timeTypeEnum 事件类型
	 * @return 返回结果
	 * @version 4.0.0
	 */
	public static Date[] findTimeStartAndEnd(Date date, TimeTypeEnum timeTypeEnum) {
		LocalDate[] dates = DateTimeUtil.findTimeStartAndEnd(DateTimeUtil.date2LocalDate(date), timeTypeEnum);
		return new Date[]{
				DateTimeUtil.localDateTime2Date(LocalDateTime.of(dates[0], LocalTime.MIN)),
				DateTimeUtil.localDateTime2Date(LocalDateTime.of(dates[1], LocalTime.MAX))
		};
	}

	private static Date[] findTimeStartAndEnd() {
		LocalDate[] dates = DateTimeUtil.findTimeStartAndEnd(LocalDate.now(), TimeTypeEnum.DAY);
		return new Date[]{
				DateTimeUtil.localDateTime2Date(LocalDateTime.of(dates[0], LocalTime.MIN)),
				DateTimeUtil.localDateTime2Date(LocalDateTime.of(dates[1], LocalTime.MAX))
		};
	}

	public static boolean isBetween(Date time, Date startTime, Date endTime) {
		if (time == null) return false;
		return isBetween(time.getTime(), startTime, endTime);
	}

	/**
	 * 判断某个时间是否在两个时间范围内
	 *
	 * @param time      比较时间
	 * @param startTime 时间段的开始时间
	 * @param endTime   时间段的结束时间
	 * @return 结果
	 */
	public static boolean isBetween(Long time, Date startTime, Date endTime) {
		if (time == startTime.getTime() || time == endTime.getTime()) {
			return true;
		}
		return time > startTime.getTime() && time < endTime.getTime();
	}



	/**
	 * 根据枚举计算指定日期，time为正数往后加，time为负数往前加
	 *
	 * @param date         指定日期【未传入默认当前时间】
	 * @param timeTypeEnum 时间类型
	 * @param time         需要计算的刻度
	 * @return 计算后的时间
	 * @version 4.0.0
	 */
	public static Date calDate(Date date, TimeTypeEnum timeTypeEnum, int time) {
		LocalDateTime localDateTime = (null == date) ? LocalDateTime.now() : DateTimeUtil.date2LocalDateTime(date);
		return DateTimeUtil.localDateTime2Date(Objects.requireNonNull(DateTimeUtil.calLocalDate(localDateTime,
				timeTypeEnum, time)));
	}


	/**
	 * 获取指定时间的当天23:59:59.059+0800（用于保存）
	 *
	 * @param date 指定时间
	 * @return 最后一点
	 */
	public static Date getEndTime(Date date) {
		if (date == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 59);
		return calendar.getTime();
	}

	/**
	 * 设置小时、分钟
	 *
	 * @param date 指定时间
	 * @return 最后一点
	 */
	public static Date setTimeHourMinute(Date date, String str) {
		String[] t = str.split(":");
		LocalDateTime localDateTime = DateTimeUtil.date2LocalDateTime(date);
		localDateTime = localDateTime.withHour(Integer.parseInt(t[0]));
		localDateTime = localDateTime.withMinute(Integer.parseInt(t[1]));
		return DateTimeUtil.localDateTime2Date(localDateTime);
	}

	/**
	 * 获取时间段内的每一天
	 *
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 天数集合
	 */
	public static List<Date> getEveryday(Date startTime, Date endTime) {
		LocalDateTime start = DateTimeUtil.date2LocalDateTime(startTime);
		//因为oa系统秒和纳秒没有用到，所以统一设置为0
		start = start.withSecond(0).withNano(0);
		LocalDateTime end = DateTimeUtil.date2LocalDateTime(endTime);
		List<Date> dayList = new ArrayList<>();
		//开始时间小于等于结束时间
		while (!start.isAfter(end)) {
			dayList.add(DateTimeUtil.localDateTime2Date(start));
			start = start.plusDays(1);
		}
		return dayList;
	}


	/**
	 * 返回时间内满足条件的时间:每周
	 *
	 * @param startTime  开始时间
	 * @param dayOfWeek  条件：每周中xx号
	 * @param defaultNum 次数
	 * @return 时间列表
	 */
	public static List<Date> getEverydayByWeek(Date startTime, Integer[] dayOfWeek, int defaultNum) {
		LocalDateTime start = DateTimeUtil.date2LocalDateTime(startTime);
		//因为oa系统秒和纳秒没有用到，所以统一设置为0
		start = start.withSecond(0).withNano(0);
		List<Date> dayList = new ArrayList<>();
		int len = dayOfWeek.length;
		//把0变成7
		for (int j = 0; j < len; j++) {
			if (dayOfWeek[j] == 0) {
				dayOfWeek[j] = 7;
			}
		}
		List<Integer> dayOfWeekConfig = CollUtil.newArrayList(dayOfWeek);
		dayOfWeekConfig.sort(Integer::compareTo);
		//今天周几
		int dw = start.getDayOfWeek().getValue();
		int i = 0;
		//今天及以后要执行的
		for (; i < len; i++) {
			if (dayOfWeekConfig.get(i) > dw) {
				break;
			}
		}
		//没找到就取0
		if (i == len) {
			i = 0;
		}
		int nextDay;
		//开始时间小于等于结束时间
		while (defaultNum > 0) {
			nextDay = dayOfWeekConfig.get(i);
			start = start.plusDays(nextDay - dw);
			Date date = DateTimeUtil.localDateTime2Date(start);
			if (date.after(startTime)) {
				//满足的时间添加到数组
				dayList.add(date);
				defaultNum--;
			}
			dw = nextDay;
			//取下一个
			i++;
			if (i == len) {
				//找完这个星期，找下个星期:加7天到下个星期
				start = start.plusDays(7);
				//重新开始
				i = 0;
			}
		}
		return dayList;
	}


	/**
	 * 从开始时间算，某时间是否是双周：0否（单周）1是（双周）
	 *
	 * @param startTime 开始时间
	 * @param someTime  某个时间
	 * @return 间隔小时数
	 */
	public static int isDoubleWeek(Date startTime, Date someTime) {
		if (null == startTime || null == someTime) {
			return 0;
		}
		long days = getTimeBetweenDay(someTime, startTime);
		return (int) (days / 7) % 2;
	}

	/**
	 * 根据类型，计算两日期之差【有正负数，时间1大则为负数】
	 * 以天为单位计算
	 *
	 * @param date1 时间1
	 * @param date2 时间2
	 * @return 计算结果
	 */
	public static long getTimeBetweenDay(Date date1, Date date2) {
		return DateTimeUtil.getTimeBetweenDay(date1, date2);
	}

	/**
	 * 返回时间内满足条件的时间:每隔周
	 *
	 * @param week       开始时间是第几周：0第一周，1第二周
	 * @param startTime  开始时间
	 * @param dayOfWeek2 条件：每隔周中xx号 {0:[第一周],1:[第二周]}
	 * @param defaultNum 默认记录数
	 * @return 时间list
	 */
	public static List<Date> getEverydayByWeek2(int week, Date startTime,
												Map<Integer, Integer[]> dayOfWeek2, int defaultNum) {
		Calendar calendar = Calendar.getInstance();
		List<Date> dayList = new ArrayList<>();
		calendar.setTime(startTime);
		//秒和毫秒都设置为0
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		//切换隔周
		int j = week;
		int i = 0;
		//缓存：保存某周配置
		Integer[] dayOfWeek;
		while (defaultNum > 0) {
			dayOfWeek = dayOfWeek2.get(j);
			//当前周有配置
			if (dayOfWeek != null) {
				//符合配置的都找出来，可能会出现小于startTime的
				for (int t = dayOfWeek.length; i < t; i++) {
					calendar.set(Calendar.DAY_OF_WEEK, dayOfWeek[i] + 1);
					//大于开始时间
					if (calendar.getTime().after(startTime)) {
						//满足的时间添加到数组
						dayList.add(calendar.getTime());
						defaultNum--;
					}
				}
			}
			//找完这个星期，找下个星期:加7天到下个星期
			calendar.add(Calendar.DATE, 7);
			//切换隔周
			j = j == 0 ? 1 : 0;
			//重新开始
			i = 0;
		}
		return dayList;
	}

	/**
	 * 返回时间内满足条件的时间：每月
	 *
	 * @param startTime  开始时间
	 * @param dayOfMonth 条件：每月中xx号
	 * @param defaultNum 是否默认有一个
	 * @return 时间list
	 */
	public static List<Date> getEverydayByMonth(Date startTime, Integer[] dayOfMonth, int defaultNum) {
		Calendar calStartTime = Calendar.getInstance();
		List<Date> dayList = new ArrayList<>();
		calStartTime.setTime(startTime);
		//秒和毫秒都设置为0
		calStartTime.set(Calendar.SECOND, 0);
		calStartTime.set(Calendar.MILLISECOND, 0);
		int i = 0;
		int len = dayOfMonth.length;
		List<Integer> dayOfMonthConfig = CollUtil.newArrayList(dayOfMonth);
		dayOfMonthConfig.sort(Integer::compareTo);
		//最后一天：29，30，31可能不存在
		int last = calStartTime.getActualMaximum(Calendar.DAY_OF_MONTH);
		int dm;
		//是否有默认
		while (defaultNum > 0) {
			//符合配置的都找出来，可能会出现小于startTime的
			dm = dayOfMonthConfig.get(i++);
			if (dm > last) {
				//找完这个月，找下个月
				calStartTime.add(Calendar.MONTH, 1);
				last = calStartTime.getActualMaximum(Calendar.DAY_OF_MONTH);
				//重新开始
				i = 0;
				continue;
			}
			calStartTime.set(Calendar.DAY_OF_MONTH, dm);
			//开始时间之后的
			if (calStartTime.getTime().after(startTime)) {
				//满足的时间添加到数组
				dayList.add(calStartTime.getTime());
				defaultNum--;
			}
			if (i == len) {
				//找完这个月，找下个月
				calStartTime.add(Calendar.MONTH, 1);
				last = calStartTime.getActualMaximum(Calendar.DAY_OF_MONTH);
				//重新开始
				i = 0;
			}
		}
		return dayList;
	}


	/**
	 * 两时间前后顺序比较【除去秒】
	 *
	 * @param time1 时间1
	 * @param time2 时间2
	 * @return 时间1是否在时间2之前
	 * @version 3.8.0
	 */
	public static boolean compareWithoutSecond(Date time1, Date time2) {
		// 转换并将秒清空
		LocalDateTime localDateTime1 = DateTimeUtil.date2LocalDateTime(time1).withSecond(0);
		LocalDateTime localDateTime2 = DateTimeUtil.date2LocalDateTime(time2).withSecond(0);
		return localDateTime1.isBefore(localDateTime2);
	}

	public static Date findDayEnd() {
		return findTimeStartAndEnd()[1];
	}

	/**
	 * 查询指定日期23:59:59.999+0800 (用于查询，更精确)
	 *
	 * @param date 指定日期
	 * @return 23:59:59
	 */
	public static Date findDayEnd(Date date) {
		if (date == null) {
			return null;
		}
		return findTimeStartAndEnd(date, TimeTypeEnum.DAY)[1];
	}


	/**
	 * 今日时间转化为String
	 *
	 * @param pattern 指定格式
	 * @return 返回格式化后的字符串
	 * @version 4.0.0
	 */
	public static String formatToday(String pattern) {
		return format(System.currentTimeMillis(), pattern);
	}


	/**
	 * 判断两个日期是否同年同月同日
	 *
	 * @param date1 日期1
	 * @param date2 日期2
	 * @return 是否同年同月同日
	 */
	public static boolean isSameMonthDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) return false;
		LocalDate time1 = DateTimeUtil.date2LocalDate(date1);
		LocalDate time2 = DateTimeUtil.date2LocalDate(date2);
		return time1.equals(time2);
	}



	public static Date setHours(Date date, int amount) {
		return set(date, amount);
	}


	private static Date set(final Date date, final int amount) {
//		validateDateNotNull(date);
		// getInstance() returns a new object, so this method is thread safe.
		final Calendar c = Calendar.getInstance();
		c.setLenient(false);
		c.setTime(date);
		c.set(11, amount);
		return c.getTime();
	}


	/**
	 * 传入时间是否包含了今天
	 *
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 判断结果
	 */
	public static boolean containToday(Date startTime, Date endTime) {
		if (null != startTime && null != endTime) {
			LocalDate start = DateTimeUtil.date2LocalDate(startTime);
			LocalDate end = DateTimeUtil.date2LocalDate(endTime);
			LocalDate today = LocalDate.now();
			return today.isEqual(start) || today.isEqual(end) || (today.isAfter(start) && today.isBefore(end));
		}
		return false;
	}


	/**
	 * 比较两个日期大小
	 *
	 * @param date1 第一个日期
	 * @param date2 第二个日期
	 * @return 1为第一个日期较大；-1为第二个日期较大；0为两日期相等
	 */
	public static int compareDate(Date date1, Date date2) {
		return date1.compareTo(date2);
	}

	/**
	 * 参数日期是否属于今天
	 *
	 * @param date
	 * @return
	 */
	public static boolean isToday(Date date) {
		SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
		if (fmt.format(date).equals(fmt.format(new Date()))) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 对比时间:增加判空
	 *
	 * @param d1 日期1
	 * @param d2 日期2
	 * @return 是否一致
	 */
	public static boolean compare(Date d1, Date d2) {
		return (null == d1 ? 0 : d1.getTime()) == (null == d2 ? 0 : d2.getTime());
	}


	/**
	 * 两时间比较年月日是否有变化
	 *
	 * @param time1 时间1
	 * @param time2 时间2
	 * @return 是否有变化
	 */
	public static boolean compareDateYTD(Date time1, Date time2) {
		if (null == time1) {
			return null != time2;
		}
		if (null == time2) {
			return true;
		}
		// 只比较年月日
		LocalDate date1 = DateTimeUtil.date2LocalDate(time1);
		LocalDate date2 = DateTimeUtil.date2LocalDate(time2);
		return !date1.equals(date2);
	}

	/**
	 * 两日期间相差的天数 【按天算】
	 *
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 相差天数
	 */
	public static long getTwoDays(String startTime, String endTime, String format) {
		if (startTime == null || endTime == null) {
			return 0;
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format, Locale.ENGLISH);
		startTime = startTime.substring(0, format.length());
		endTime = endTime.substring(0, format.length());
		LocalDate startDate = LocalDate.parse(startTime, formatter);
		LocalDate endDate = LocalDate.parse(endTime, formatter);
		return endDate.until(startDate, ChronoUnit.DAYS);
	}

	/**
	 * 获取指定日期当周第一天
	 *
	 * @param time 指定日期
	 * @return 操作结果
	 */
	public static Date getWeekByDate(Date time) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(time);
		// 判断要计算的日期是否是周日，如果是则减一天计算周六的，否则会出问题，计算到下一周去了
		int dayWeek = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
		if (1 == dayWeek) {
			cal.add(Calendar.DAY_OF_MONTH, -1);
		}
		cal.setFirstDayOfWeek(Calendar.MONDAY);// 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
		int day = cal.get(Calendar.DAY_OF_WEEK);// 获得当前日期是一个星期的第几天
		cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);// 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
		return findDayStart(cal.getTime());
	}


	/**
	 * 时间是否相同
	 *
	 * @param date1 时间1
	 * @param date2 时间2
	 * @return 判断结果
	 */
	public static boolean isSameDate(Date date1, Date date2) {
		if (date1 != null) {
			return date1.compareTo(date2) == 0;
		}
		return date2 == null;
	}

	/**
	 * 时间是否在当前时间之后
	 *
	 * @param date 传入时间
	 * @return 判断结果
	 */
	public static boolean isAfterToday(Date date) {
		return (date != null) && date.getTime() > System.currentTimeMillis();
	}

	/**
	 * 根据类型，计算两日期之差【有正负数，时间1大则为负数】
	 * 此方法会计算时分秒，不会以天为单位
	 *
	 * @param date1        时间1
	 * @param date2        时间2
	 * @param timeTypeEnum 事件类型 ：计算 时分秒
	 * @return 计算结果
	 * @version 4.0.0
	 */
	public static long getTimeAllDayBetween(Date date1, Date date2, TimeTypeEnum timeTypeEnum) {
		date1 = findDayStart(date1);
		date2 = findDayStart(date2);
		return DateTimeUtil.getTimeBetween(date1, date2, timeTypeEnum);
	}

	/**
	 * 判断时间是否相等 （精确到分）
	 *
	 * @param date    日期
	 * @param Time    日期
	 * @param pattern 日期格式
	 * @return 转换后的日期
	 */
	public static boolean checkEquals(Date date, Date Time, String pattern) {
		if (ObjectUtil.isNotEmpty(date) && ObjectUtil.isNotEmpty(Time)) {
			Date date1 = string2Date(format(date, pattern), pattern);
			Date date2 = string2Date(format(Time, pattern), pattern);
			if (null != date1 && null != date2) {
				return date1.equals(date2);
			}
		}
		return false;
	}


	/**
	 * 时间戳转date
	 *
	 * @param timestamp 时间戳
	 * @return 时间
	 */
	public static Date formatDate(long timestamp) {
		if (0 == timestamp) {
			return null;
		}
		return DateTimeUtil.localDateTime2Date(DateTimeUtil.timestamp2LocalDateTime(timestamp));
	}

	/**
	 * 获取两个时间段的交集（分钟）
	 *
	 * @return 分钟数
	 */
	public static long getMinuteTimeIntersection(Date startDate1, Date endDate1, Date startDate2, Date endDate2) {
		if (null == startDate1 || null == startDate2 || null == endDate1 || null == endDate2) {
			return 0L;
		}
		LocalDateTime start1 = DateTimeUtil.date2LocalDateTime(startDate1);
		LocalDateTime start2 = DateTimeUtil.date2LocalDateTime(startDate2);
		LocalDateTime end1 = DateTimeUtil.date2LocalDateTime(endDate1);
		LocalDateTime end2 = DateTimeUtil.date2LocalDateTime(endDate2);
		LocalDateTime intersectionStart = start1.isAfter(start2) ? start1 : start2;
		LocalDateTime intersectionEnd = end1.isBefore(end2) ? end1 : end2;
		Duration duration = Duration.between(intersectionStart, intersectionEnd);
		long minutes = duration.toMinutes();
		//小于0按0处理
		return minutes < 0 ? 0 : minutes;
	}

	/**
	 * Date对象转换为LocalDateTime
	 *
	 * @return
	 */
	public static LocalDateTime getLocalDateTime(Date oldTime) {
		Instant instant = oldTime.toInstant();
		return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
	}

	/**
	 * 通过秒数获取时间
	 *
	 * @param seconds 秒
	 * @return 时间秒数
	 */
	public static String getTimeStr(long seconds) {
		long minute = seconds / 60;
		long second = seconds % 60;
		return (minute > 0) ? minute + "分" + second + "秒" : second + "秒";
	}

	/**
	 * 判断时间格式是否正确
	 * @param time 时间
	 * @return false格式错误
	 */
	public static boolean isValidTime(String time) {
		if (StrUtil.isEmpty(time)) {
			return true;
		}
		try {
			LocalTime.parse(time);
		} catch (DateTimeParseException e) {
			log.warn("错误的时间格式{}", time);
			return false;
		}
		return true;
	}

}
