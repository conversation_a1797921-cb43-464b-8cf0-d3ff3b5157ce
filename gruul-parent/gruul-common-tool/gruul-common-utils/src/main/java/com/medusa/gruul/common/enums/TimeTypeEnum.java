package com.medusa.gruul.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时间类型枚举
 *
 * <AUTHOR>
 * @version 1.0, 2025/7/22
 */
@Getter
@AllArgsConstructor
public enum TimeTypeEnum {
    /**
     * 天
     */
    DAY("天"),
    /**
     * 周
     */
    WEEK("周"),
    /**
     * 月
     */
    MONTH("月"),
    /**
     * 季
     */
    SEASON("季"),
    /**
     * 年
     */
    YEAR("年"),
    /**
     * 毫秒
     */
    MILLISECOND("毫秒"),
    /**
     * 秒钟
     */
    SECOND("秒钟"),
    /**
     * 分钟
     */
    MINUTE("分钟"),
    /**
     * 小时
     */
    HOUR("小时"),
    /**
     * 无
     */
    NONE("无");

    /**
     * 描述
     */
    private final String desc;

    public static String getDesc(Integer code) {
        TimeTypeEnum typeEnum = getEnum(code);
        if (typeEnum == null) {
            return "";
        }
        return typeEnum.desc;
    }

    public static TimeTypeEnum getEnum(Integer code) {
        for (TimeTypeEnum typeEnum : TimeTypeEnum.values()) {
            if (code == typeEnum.ordinal()) {
                return typeEnum;
            }
        }
        return null;
    }
}
