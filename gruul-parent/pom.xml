<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <version>3.1.5</version>
        <relativePath/>
    </parent>

    <groupId>com.medusa.gruul</groupId>
    <artifactId>gruul-parent</artifactId>
    <packaging>pom</packaging>
    <version>2022.2</version>
    <modules>
        <module>gruul-common-addon</module>
        <module>gruul-common-data</module>
        <module>gruul-common-security</module>
        <module>gruul-common-system</module>
        <module>gruul-common-tool</module>
        <module>gruul-common-web</module>
        <module>gruul-global</module>
    </modules>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>${project.reporting.outputEncoding}</project.build.sourceEncoding>
        <maven.compiler.encoding>${project.reporting.outputEncoding}</maven.compiler.encoding>
        
        <gruul-parent.version>2022.2</gruul-parent.version>
        <spring-cloud.version>2022.0.4</spring-cloud.version>
        <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>

        <hutool.version>5.8.27</hutool.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <mybatis.version>3.5.16</mybatis.version>
        <dynamic-datasource.version>4.3.0</dynamic-datasource.version>
        <dubbo.version>3.2.13</dubbo.version>
        <fastjson2.version>2.0.52</fastjson2.version>
        <jakarta-ws-rs.version>2.1.1</jakarta-ws-rs.version>
        <redisson.version>3.37.0</redisson.version>
        <vividsolutions-jts.version>1.13</vividsolutions-jts.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <vavr.version>0.10.4</vavr.version>
        <smart-doc.version>2.5.3</smart-doc.version>
        <weixin-java-miniapp.verison>4.6.0</weixin-java-miniapp.verison>
        <xxl-job.version>2.3.1</xxl-job.version>
        <netty.version>4.1.91.Final</netty.version>
        <poi.version>5.2.3</poi.version>
        <bcprov-jdk15to18.version>1.76</bcprov-jdk15to18.version>
        <jjwt.version>0.12.5</jjwt.version>
        <findbugs.version>3.0.1</findbugs.version>
        <easyexcel.version>3.3.3</easyexcel.version>
    </properties>

    <dependencies>

        <!-- 日志 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <!-- 可上下文 延伸的 thread-local-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${transmittable-thread-local.version}</version>
        </dependency>
        <!-- vavr -->
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>${vavr.version}</version>
        </dependency>
        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <version>${findbugs.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>provided</scope>
        </dependency>
        <!-- 配置代码提示 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- gruul-common -->
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-system-spring-listener</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-log</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-system-context</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-idem</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul.global</groupId>
                <artifactId>gruul-global-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul.global</groupId>
                <artifactId>gruul-global-i18n</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul.global</groupId>
                <artifactId>gruul-global-config</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-system-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-redis</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-wechat</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-dubbo-rpc</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-mq-rabbit</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-validator</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-web</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-addon-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-addon-supporter</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-addon-provider</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-mybatis-plus-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-mybatis-plus-global</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-mybatis-plus-config</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-security-model</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-security-client</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-security-server</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-geometry</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-xxl-job</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-fastjson2</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-encrypt</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-sharding-jdbc</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-ipaas</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-saas-cloud</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.medusa.gruul</groupId>
                <artifactId>gruul-common-member</artifactId>
                <version>${gruul-parent.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.github.shalousun</groupId>
                    <artifactId>smart-doc-maven-plugin</artifactId>
                    <version>${smart-doc.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
