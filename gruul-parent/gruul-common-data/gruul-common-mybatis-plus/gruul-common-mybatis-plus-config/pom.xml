<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-common-mybatis-plus</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>2022.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-common-mybatis-plus-config</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-mybatis-plus-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- 自动装配 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!-- system -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-system-context</artifactId>
        </dependency>

    </dependencies>

</project>