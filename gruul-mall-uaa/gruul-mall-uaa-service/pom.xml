<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gruul-mall-uaa</artifactId>
        <groupId>com.medusa.gruul</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-mall-uaa-service</artifactId>

    <properties>
        <!--     滑块验证码   -->
        <tianai-captcha-springboot-starter>1.4.1</tianai-captcha-springboot-starter>
    </properties>
    <dependencies>
        <!--    信鸽    -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-carrier-pigeon-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- 新版本 security oauth2 authorization server 为以后升级作准备 -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-security-server</artifactId>
        </dependency>
        <!-- sky walking -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.12.0</version>
        </dependency>
        <!--  uaa api  -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-uaa-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--    shop api    -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-shop-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--    user api    -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-user-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--    payment api    -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-payment-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- wechat -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-wechat</artifactId>
        </dependency>
        <!-- slider captcha -->
        <dependency>
            <groupId>cloud.tianai.captcha</groupId>
            <artifactId>tianai-captcha-springboot-starter</artifactId>
            <version>${tianai-captcha-springboot-starter}</version>
        </dependency>
        <!-- mq -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-mq-rabbit</artifactId>
        </dependency>
        <!-- for qrcode generate -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>
        <!--    easy excel    -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.1</version>
        </dependency>
        <!--    encrypt      -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-encrypt</artifactId>
        </dependency>
        <!--    service    -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-module-service</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-ipaas</artifactId>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-member</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>config/</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>com.medusa.gruul.service.uaa.service.UaaServiceApplication</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>config/**</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <configuration>
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <excludes>
                        <exclude>com.google.guava:guava</exclude>
                    </excludes>
                    <includes>
                        <include>com.medusa.gruul:.*</include>
                        <include>com.medusa.gruul.global:.*</include>
                        <include>com.baomidou:mybatis-plus-extension</include>
                        <include>com.baomidou:mybatis-plus-core</include>
                        <include>cn.hutool:hutool-all</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
