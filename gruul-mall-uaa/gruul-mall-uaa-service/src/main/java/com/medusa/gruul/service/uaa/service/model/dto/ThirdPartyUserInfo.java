package com.medusa.gruul.service.uaa.service.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.annotation.Nullable;

/**
 * 第三方用户信息DTO
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Data
@Accessors(chain = true)
public class ThirdPartyUserInfo {

    /**
     * 手机号(来自JWT的preferred_username字段)
     */
    private String mobile;

    /**
     * 租户ID(来自operSubjectGuid参数)
     */
    private Long tenantId;

    /**
     * 企业ID(来自enterpriseGuid参数)
     */
    private Long enterpriseId;

    /**
     * 用户guid(来自JWT 餐饮云token携带)
     */
    private String userGuid;

    /**
     * 用户名(来自JWT)
     */
    private String username;

    /**
     * 邮箱(来自JWT，可选)
     */
    private String email;

    /**
     * JWT Token的过期时间
     */
    private Long expireTime;

    /**
     * JWT Token的签发时间
     */
    private Long issuedAt;

    /**
     * token来源
     *
     * @see com.medusa.gruul.service.uaa.service.model.enums.ThirdPartyTokenSource
     */
    @Nullable
    private String thirdPartySource;
}