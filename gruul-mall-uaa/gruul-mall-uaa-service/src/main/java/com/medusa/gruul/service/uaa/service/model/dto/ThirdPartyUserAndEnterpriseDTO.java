package com.medusa.gruul.service.uaa.service.model.dto;

import com.google.common.collect.Lists;
import com.medusa.gruul.common.ipaas.model.IpaasUserAndEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.IpassEnterpriseDTO;
import com.medusa.gruul.common.saas.cloud.model.SaasCloudUserAndEnterpriseDTO;
import io.jsonwebtoken.lang.Strings;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class ThirdPartyUserAndEnterpriseDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户电话
     */
    private String phone;

    /**
     * 企业列表
     */
    private List<ThirdPartyEnterpriseDTO> enterprises;


    public static ThirdPartyUserAndEnterpriseDTO transferTo(IpaasUserAndEnterpriseDTO ipaasUserAndEnterpriseDTO) {
        if (Objects.isNull(ipaasUserAndEnterpriseDTO)) {
            return null;
        }
        ThirdPartyUserAndEnterpriseDTO thirdPartyUserAndEnterpriseDTO = new ThirdPartyUserAndEnterpriseDTO();
        BeanUtils.copyProperties(ipaasUserAndEnterpriseDTO, thirdPartyUserAndEnterpriseDTO, "enterprises");

        List<IpassEnterpriseDTO> innerEnterprises = ipaasUserAndEnterpriseDTO.getEnterprises();
        if (CollectionUtils.isNotEmpty(innerEnterprises)) {
            thirdPartyUserAndEnterpriseDTO.setEnterprises(ipaasUserAndEnterpriseDTO.getEnterprises().stream()
                    .map(ThirdPartyEnterpriseDTO::transferTo)
                    .collect(Collectors.toList()));
        }
        return thirdPartyUserAndEnterpriseDTO;
    }

    public static ThirdPartyUserAndEnterpriseDTO transferTo(SaasCloudUserAndEnterpriseDTO saasCloudUserAndEnterpriseDTO,
                                                            String enterpriseGuid,
                                                            String operSubjectGuid) {
        if (Objects.isNull(saasCloudUserAndEnterpriseDTO)) {
            return null;
        }
        ThirdPartyUserAndEnterpriseDTO thirdPartyUserAndEnterpriseDTO = new ThirdPartyUserAndEnterpriseDTO();
        thirdPartyUserAndEnterpriseDTO.setPhone(saasCloudUserAndEnterpriseDTO.getTel());
        thirdPartyUserAndEnterpriseDTO.setUsername(saasCloudUserAndEnterpriseDTO.getName());

        // 构建企业信息
        ThirdPartyEnterpriseDTO thirdPartyEnterpriseDTO = new ThirdPartyEnterpriseDTO();
        String account = saasCloudUserAndEnterpriseDTO.getAccount();
        thirdPartyEnterpriseDTO.setResponsibleUserAccount("10000".equals(account) ? saasCloudUserAndEnterpriseDTO.getTel() : Strings.EMPTY);
        thirdPartyEnterpriseDTO.setId(enterpriseGuid);

        // 构建运营主体信息
        ThirdPartyOperatingSubjectBriefDTO thirdPartyOperatingSubjectBriefDTO = new ThirdPartyOperatingSubjectBriefDTO();
        thirdPartyOperatingSubjectBriefDTO.setId(Long.valueOf(operSubjectGuid));
        thirdPartyEnterpriseDTO.setSubjects(Lists.newArrayList(thirdPartyOperatingSubjectBriefDTO));

        thirdPartyUserAndEnterpriseDTO.setEnterprises(Lists.newArrayList(thirdPartyEnterpriseDTO));
        return thirdPartyUserAndEnterpriseDTO;
    }
}
