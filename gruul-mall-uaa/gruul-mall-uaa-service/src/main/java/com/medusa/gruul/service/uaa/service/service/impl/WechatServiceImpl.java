package com.medusa.gruul.service.uaa.service.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.wechat.WechatServicePreloader;
import com.medusa.gruul.service.uaa.api.constant.UaaConstant;
import com.medusa.gruul.service.uaa.service.model.dto.wechat.WeChatCodeDTO;
import com.medusa.gruul.service.uaa.service.model.dto.wechat.WechatSchemaDTO;
import com.medusa.gruul.service.uaa.service.model.enums.WechatType;
import com.medusa.gruul.service.uaa.service.service.WechatService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * date 2022/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatServiceImpl implements WechatService {

	private final RedissonClient redissonClient;
	private final WechatServicePreloader wechatServicePreloader;

	/**
	 * 获取当前平台的小程序服务
	 */
	private WxMaService getWxMaService() {
		Long platformId = ISystem.platformIdMust();
		log.debug("尝试获取平台 {} 的小程序服务", platformId);
		
		WxMaService service = wechatServicePreloader.getWxMaService(platformId);
		if (service == null) {
			log.error("平台 {} 的微信小程序服务未配置或加载失败", platformId);
			throw SystemCode.FAILURE.exception("系统未知异常");
		}
		
		log.debug("成功获取平台 {} 的小程序服务", platformId);
		return service;
	}

	/**
	 * 获取当前平台的公众号服务
	 */
	private WxMpService getWxMpService() {
		Long platformId = ISystem.platformIdMust();
		log.debug("尝试获取平台 {} 的公众号服务", platformId);
		
		WxMpService service = wechatServicePreloader.getWxMpService(platformId);
		if (service == null) {
			log.error("平台 {} 的微信公众号服务未配置或加载失败", platformId);
			throw SystemCode.FAILURE.exception("系统未知异常");
		}
		
		log.debug("成功获取平台 {} 的公众号服务", platformId);
		return service;
	}

	@Override
	@SneakyThrows
	public WxMaJscode2SessionResult getSession(String code) {
		return getWxMaService().getUserService()
				.getSessionInfo(code);
	}

	@Override
	@SneakyThrows
	public WxMaPhoneNumberInfo getPhoneInfo(String code) {
		return getWxMaService().getUserService()
				.getPhoneNoInfo(code);
	}
	
	@Override
	@SneakyThrows
	public String getWeChatCode(WeChatCodeDTO weChatCode) {
		WxMaService wxMaService = getWxMaService();
		String appid = wxMaService.getWxMaConfig().getAppid();
		String cacheKey = RedisUtil.key(UaaConstant.WECHAT_CODE_PREFIX, appid, weChatCode.getPath());
		String code = RedisUtil.getCacheObject(cacheKey);
		if (StrUtil.isNotEmpty(code)) {
			return code;
		}
		RLock lock = redissonClient.getLock(RedisUtil.getLockKey(UaaConstant.WECHAT_CODE_LOCK, appid));
		if (!lock.tryLock(CommonPool.NUMBER_TEN, TimeUnit.SECONDS)) {
			throw SystemCode.SYSTEM_BUSY.exception();
		}
		byte[] wxaCodeBytes;
		try {
			wxaCodeBytes = wxMaService.getQrcodeService().createWxaCodeBytes(
					weChatCode.getPath(),
					weChatCode.getEnvVersion(),
					weChatCode.getWidth(),
					weChatCode.getAutoColor(),
					weChatCode.getLineColor(),
					weChatCode.getIsHyaline()
			);
		} finally {
			lock.unlock();
		}
		code = "data:image/png;base64," + Base64.encode(wxaCodeBytes);
		RedisUtil.setCacheObject(cacheKey, code);
		return code;
	}

	@Override
	public WxJsapiSignature jsapiSignature(WechatType type, String url) {
		try {
			return WechatType.MP == type ? getWxMpService().createJsapiSignature(url) : getWxMaService().getJsapiService().createJsapiSignature(url);
		} catch (WxErrorException ex) {
			log.error("获取jsapi签名失败", ex);
			throw SystemCode.FAILURE.exception();
		}
	}

	@Override
	public String getWeChatSchemaUrl(WechatSchemaDTO wechatSchema) {
		WxMaService wxMaService = getWxMaService();
		String path = wechatSchema.getPath();
		String query = UrlQuery.of(wechatSchema.getQuery()).toString();
		String key = RedisUtil.key(UaaConstant.WECHAT_SCHEMA_PREFIX, wechatSchema.getEnvVersion(), path + query);
		String schema = RedisUtil.getCacheObject(key);
		if (StrUtil.isNotEmpty(schema)) {
			return schema;
		}
		try {
			schema = wxMaService.getWxMaSchemeService().generate(
					WxMaGenerateSchemeRequest.newBuilder()
							.jumpWxa(
									WxMaGenerateSchemeRequest.JumpWxa.newBuilder()
											.path(path)
											.query(query)
											.envVersion(wechatSchema.getEnvVersion())
											.build()
							)
							.isExpire(true)
							.expireType(CommonPool.NUMBER_ONE)
							.expireInterval(wechatSchema.getExpireDays())
							.build()
			);
		} catch (WxErrorException ex) {
			log.error("获取微信Schema URL失败", ex);
			throw SystemCode.FAILURE.exception();
		}
		RedisUtil.setCacheObject(key, schema, wechatSchema.getExpireDays(), TimeUnit.DAYS);
		return schema;
	}
}
