package com.medusa.gruul.service.uaa.service.model.dto;

import com.medusa.gruul.common.ipaas.model.IpassEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.IpassOperatingSubjectBriefDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
@Accessors(chain = true)
public class ThirdPartyEnterpriseDTO {

    /**
     * 企业ID
     */
    private String id;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业简称
     */
    private String shortName;

    /**
     * 企业负责人账号
     */
    private String responsibleUserAccount;

    /**
     * 企业Logo
     */
    private String logo;

    /**
     * 品牌信息
     */
    private List<ThirdPartyOperatingSubjectBriefDTO> subjects;

    public static ThirdPartyEnterpriseDTO transferTo(IpassEnterpriseDTO ipassEnterpriseDTO) {
        if (Objects.isNull(ipassEnterpriseDTO)) {
            return null;
        }
        ThirdPartyEnterpriseDTO thirdPartyEnterpriseDTO = new ThirdPartyEnterpriseDTO();
        BeanUtils.copyProperties(ipassEnterpriseDTO, thirdPartyEnterpriseDTO, "subjects");

        List<IpassOperatingSubjectBriefDTO> innerSubjects = ipassEnterpriseDTO.getSubjects();
        if (CollectionUtils.isNotEmpty(innerSubjects)) {
            thirdPartyEnterpriseDTO.setSubjects(ipassEnterpriseDTO.getSubjects().stream()
                    .map(ThirdPartyOperatingSubjectBriefDTO::transferTo)
                    .collect(Collectors.toList()));
        }
        return thirdPartyEnterpriseDTO;
    }


}
