package com.medusa.gruul.service.uaa.service.strategy.login.third.impl;

import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.UserQueryClient;
import com.medusa.gruul.common.ipaas.model.IpaasUserAndEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.UserQueryReqDTO;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.resource.exception.SecurityException;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseQueryDTO;
import com.medusa.gruul.service.uaa.service.strategy.login.third.ThirdPartyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component("ipaasThirdPartyServiceImpl")
public class IpaasThirdPartyServiceImpl implements ThirdPartyService {

    private final IpaasClientSupport ipaasClientSupport;

    @Override
    public ThirdPartyUserAndEnterpriseDTO queryUserAndEnterprise(ThirdPartyUserAndEnterpriseQueryDTO queryDTO) {
        // 参考SmsCodeAuthenticationProvider的ipaasAuthenticate方法
        UserQueryReqDTO userQueryReqDTO = new UserQueryReqDTO();
        userQueryReqDTO.setAccount(queryDTO.getMobile());
        IpaasUserAndEnterpriseDTO ipaasUserAndEnterprise = new UserQueryClient(ipaasClientSupport).execute(userQueryReqDTO);
        if (ipaasUserAndEnterprise == null) {
            throw SecurityException.of(SecureCodes.PERMISSION_DENIED);
        }
        ipaasUserAndEnterprise.setPhone(queryDTO.getMobile());
        return ThirdPartyUserAndEnterpriseDTO.transferTo(ipaasUserAndEnterprise);
    }
}
