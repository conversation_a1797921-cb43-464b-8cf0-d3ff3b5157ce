package com.medusa.gruul.service.uaa.service.strategy.login.third;

import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseQueryDTO;

public interface ThirdPartyService {

    /**
     * 查询三方用户和企业信息
     */
    ThirdPartyUserAndEnterpriseDTO queryUserAndEnterprise(ThirdPartyUserAndEnterpriseQueryDTO queryDTO);
}
