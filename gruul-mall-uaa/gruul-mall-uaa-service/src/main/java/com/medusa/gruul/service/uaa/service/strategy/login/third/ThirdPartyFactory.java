package com.medusa.gruul.service.uaa.service.strategy.login.third;


import com.medusa.gruul.service.uaa.service.model.enums.ThirdPartyTokenSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ThirdPartyFactory {

    private final ThirdPartyService ipaasThirdPartyServiceImpl;

    private final ThirdPartyService saasCloudThirdPartyServiceImpl;

    @Autowired
    public ThirdPartyFactory(@Qualifier("ipaasThirdPartyServiceImpl") ThirdPartyService ipaasThirdPartyServiceImpl,
                             @Qualifier("saasCloudThirdPartyServiceImpl") ThirdPartyService saasCloudThirdPartyServiceImpl) {
        this.ipaasThirdPartyServiceImpl = ipaasThirdPartyServiceImpl;
        this.saasCloudThirdPartyServiceImpl = saasCloudThirdPartyServiceImpl;
    }

    public ThirdPartyService create(String thirdPartySource) {
        ThirdPartyTokenSource thirdPartyTokenSource = ThirdPartyTokenSource.valueOf(thirdPartySource);
        return switch (thirdPartyTokenSource) {
            case IPAAS -> ipaasThirdPartyServiceImpl;
            case CLOUD -> saasCloudThirdPartyServiceImpl;
        };
    }
}
