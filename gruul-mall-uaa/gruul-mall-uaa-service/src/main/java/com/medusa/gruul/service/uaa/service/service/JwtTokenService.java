package com.medusa.gruul.service.uaa.service.service;

import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserInfo;

/**
 * JWT Token解析服务
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public interface JwtTokenService {
    
    /**
     * 解析第三方JWT Token获取用户信息
     * 
     * @param jwtToken JWT Token
     * @return 用户信息
     */
    ThirdPartyUserInfo parseJwtToken(String jwtToken);
    
    /**
     * 验证JWT Token是否有效
     * 
     * @param jwtToken JWT Token
     * @return 是否有效
     */
    boolean validateToken(String jwtToken);
} 