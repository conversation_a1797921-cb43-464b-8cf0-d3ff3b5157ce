package com.medusa.gruul.service.uaa.service.service;

import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserInfo;
import com.medusa.gruul.service.uaa.service.strategy.login.ThirdPartyTokenAuthenticationProvider;

/**
 * JWT Token解析服务
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public interface JwtTokenService {

    /**
     * 解析第三方JWT Token获取用户信息
     *
     * @param authentication JWT Token
     * @return 用户信息
     */
    ThirdPartyUserInfo parseJwtToken(ThirdPartyTokenAuthenticationProvider.ThirdPartyToken authentication);

    /**
     * 验证JWT Token是否有效
     *
     * @param jwtToken JWT Token
     * @return 是否有效
     */
    boolean validateToken(String jwtToken);
} 