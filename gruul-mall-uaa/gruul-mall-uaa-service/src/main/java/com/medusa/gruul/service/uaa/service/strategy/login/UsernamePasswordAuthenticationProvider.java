package com.medusa.gruul.service.uaa.service.strategy.login;

import com.medusa.gruul.common.encrypt.Crypt;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.UserLoginClient;
import com.medusa.gruul.common.ipaas.client.UserQueryClient;
import com.medusa.gruul.common.ipaas.model.IpaasUserAndEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.UserLoginReqDTO;
import com.medusa.gruul.common.ipaas.model.UserLoginRspDTO;
import com.medusa.gruul.common.ipaas.model.UserQueryReqDTO;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.resource.exception.SecurityException;
import com.medusa.gruul.common.security.server.annotation.GrantType;
import com.medusa.gruul.common.security.server.model.AuthenticationRequest;
import com.medusa.gruul.common.security.server.provider.IAuthenticationProvider;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.ClientType;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.mp.entity.User;
import com.medusa.gruul.service.uaa.service.mp.service.IUserService;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import static net.sf.jsqlparser.util.validation.metadata.NamedObject.user;

/**
 * <AUTHOR>
 * @since 2024/4/19
 */
@Component
@GrantType("password")
@RequiredArgsConstructor
public class UsernamePasswordAuthenticationProvider implements IAuthenticationProvider<UsernamePasswordAuthenticationProvider.UsernamePassword> {

    private final IUserService userService;
    private final PasswordEncoder passwordEncoder;
    private final ReloadUserProvider reloadUserProvider;
    private final IpaasClientSupport ipaasClientSupport;

    @Override
    public SecureUser<?> authenticate(UsernamePassword authentication) throws AuthenticationException {
        //若是平台端去ipaas进行登录校验
        if(ISystem.clientTypeMust() == ClientType.PLATFORM_CONSOLE){
            return ipaasAuthenticate(authentication);
        }
        return systemAuthenticate(authentication);
    }

    private SecureUser<?> systemAuthenticate(UsernamePassword authentication) {
        String username = authentication.getUsername();
        User user =
                Tenant.disable(()-> userService.lambdaQuery()
						.eq(User::getUsername, username)
						.or()
						.eq(User::getMobile, username)
						.one()
                );
        String decryptPassword = authentication.getPassword();
        //校验账号密码
        if (user == null || !passwordEncoder.matches(decryptPassword, user.getPassword())) {
            throw SecurityException.of(SecureCodes.USERNAME_PASSWORD_INVALID);
        }
        return reloadUserProvider.loadUser(ISystem.clientTypeMust(), ISystem.shopIdOpt().getOrNull(), user);
    }

    private SecureUser<?> ipaasAuthenticate(UsernamePassword authentication) {
        UserLoginReqDTO userLoginReqDTO = new UserLoginReqDTO();
        userLoginReqDTO.setUsername(authentication.getUsername());
        userLoginReqDTO.setPassword(authentication.getPassword());
        UserLoginRspDTO userLoginRsp = new UserLoginClient(ipaasClientSupport).execute(userLoginReqDTO);
        if(userLoginRsp == null){
            throw SecurityException.of(SecureCodes.USERNAME_PASSWORD_INVALID);
        }
        //获取用户下的企业列表
        UserQueryReqDTO userQueryReqDTO = new UserQueryReqDTO();
        userQueryReqDTO.setAccount(authentication.getUsername());
        IpaasUserAndEnterpriseDTO ipaasUserAndEnterprise = new UserQueryClient(ipaasClientSupport).execute(userQueryReqDTO);
        if(ipaasUserAndEnterprise == null){
            throw SecurityException.of(SecureCodes.PERMISSION_DENIED);
        }
        ipaasUserAndEnterprise.setPhone(authentication.getUsername());

        ThirdPartyUserAndEnterpriseDTO thirdPartyUserAndEnterpriseDTO = ThirdPartyUserAndEnterpriseDTO.transferTo(ipaasUserAndEnterprise);
        return reloadUserProvider.loadThirdPartyUser(ISystem.clientTypeMust(), thirdPartyUserAndEnterpriseDTO);
    }

    @Override
    public Class<UsernamePassword> requestType() {
        return UsernamePassword.class;
    }

    @Getter
    @Setter
    @ToString
    public static class UsernamePassword extends AuthenticationRequest {
        /**
         * 用户名
         */
        @NotBlank
        @Crypt
        private String username;

        /**
         * 密码
         */
        @NotBlank
        @Crypt
        private String password;

    }
}
