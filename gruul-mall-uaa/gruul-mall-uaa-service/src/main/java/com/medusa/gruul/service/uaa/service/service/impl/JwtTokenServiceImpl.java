package com.medusa.gruul.service.uaa.service.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import com.medusa.gruul.common.security.server.tool.ThirdPartyJwtParser;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserInfo;
import com.medusa.gruul.service.uaa.service.model.enums.ThirdPartyTokenSource;
import com.medusa.gruul.service.uaa.service.model.enums.UaaError;
import com.medusa.gruul.service.uaa.service.service.JwtTokenService;
import com.medusa.gruul.service.uaa.service.strategy.login.ThirdPartyTokenAuthenticationProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * JWT Token解析服务实现
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Service
public class JwtTokenServiceImpl implements JwtTokenService {

    @Override
    public ThirdPartyUserInfo parseJwtToken(ThirdPartyTokenAuthenticationProvider.ThirdPartyToken authentication) {
        try {
            String jwtToken = authentication.getThirdPartyToken();
            // 验证Token格式
            if (StrUtil.isBlank(jwtToken)) {
                throw UaaError.THIRD_PARTY_TOKEN_INVALID.exception("JWT Token不能为空");
            }
            // 解析JWT Token
            JWT jwt = ThirdPartyJwtParser.parseToken(jwtToken);
            if (jwt == null) {
                throw UaaError.THIRD_PARTY_TOKEN_PARSE_ERROR.exception("JWT Token解析失败");
            }
            // token 来源
            String thirdPartySource = Optional.ofNullable(authentication.getThirdPartySource()).orElse(ThirdPartyTokenSource.IPAAS.name());
            authentication.setThirdPartySource(thirdPartySource);
            if (ThirdPartyTokenSource.IPAAS.name().equals(thirdPartySource)) {
                return parse2IpaasUserInfo(authentication);
            } else if (ThirdPartyTokenSource.CLOUD.name().equals(thirdPartySource)) {
                return parse2CloudUserInfo(authentication);
            } else {
                throw UaaError.THIRD_PARTY_TOKEN_PARSE_ERROR.exception("JWT Token解析失败");
            }
        } catch (Exception e) {
            log.error("JWT Token解析失败，error: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 解析 ipaas token
     */
    private ThirdPartyUserInfo parse2IpaasUserInfo(ThirdPartyTokenAuthenticationProvider.ThirdPartyToken authentication) {
        // 提取用户信息
        String jwtToken = authentication.getThirdPartyToken();
        String preferredUsername = ThirdPartyJwtParser.getStringPayload(jwtToken, "preferred_username");
        String username = ThirdPartyJwtParser.getStringPayload(jwtToken, "name");
        String email = ThirdPartyJwtParser.getStringPayload(jwtToken, "email");
        Long exp = ThirdPartyJwtParser.getLongPayload(jwtToken, "exp");
        Long iat = ThirdPartyJwtParser.getLongPayload(jwtToken, "iat");

        // 验证必要字段
        if (StrUtil.isBlank(preferredUsername)) {
            throw UaaError.THIRD_PARTY_TOKEN_MISSING_FIELD.exception("JWT Token缺少preferred_username字段");
        }
        log.info("JWT Token解析成功，preferred_username: {}, username: {}", preferredUsername, username);
        return new ThirdPartyUserInfo()
                .setMobile(preferredUsername)
                .setUsername(username)
                .setEmail(email)
                .setExpireTime(exp)
                .setIssuedAt(iat)
                .setThirdPartySource(authentication.getThirdPartySource());
    }


    /**
     * 解析 餐饮云 token
     */
    private ThirdPartyUserInfo parse2CloudUserInfo(ThirdPartyTokenAuthenticationProvider.ThirdPartyToken authentication) {
        // 提取用户信息
        String jwtToken = authentication.getThirdPartyToken();
        String userGuid = ThirdPartyJwtParser.getStringPayload(jwtToken, "userGuid");
        Long iat = ThirdPartyJwtParser.getLongPayload(jwtToken, "iat");

        // 验证必要字段
        if (StrUtil.isBlank(userGuid)) {
            throw UaaError.THIRD_PARTY_TOKEN_MISSING_FIELD.exception("JWT Token缺少userGuid字段");
        }
        log.info("JWT Token解析成功，userGuid: {}", userGuid);
        return new ThirdPartyUserInfo()
                .setUserGuid(userGuid)
                .setIssuedAt(iat)
                .setThirdPartySource(authentication.getThirdPartySource());
    }


    @Override
    public boolean validateToken(String jwtToken) {
        return ThirdPartyJwtParser.validateToken(jwtToken);
    }
} 