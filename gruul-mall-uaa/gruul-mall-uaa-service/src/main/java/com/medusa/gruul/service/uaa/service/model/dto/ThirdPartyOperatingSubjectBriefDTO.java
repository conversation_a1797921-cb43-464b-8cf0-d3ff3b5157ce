
package com.medusa.gruul.service.uaa.service.model.dto;

import com.medusa.gruul.common.ipaas.model.IpassOperatingSubjectBriefDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

/**
 * thirdParty返回的品牌信息
 */
@Getter
@Setter
@Accessors(chain = true)
public class ThirdPartyOperatingSubjectBriefDTO {

    /**
     * 品牌ID
     */
    private Long id;
    /**
     * 品牌名称
     */
    private String name;
    /**
     * 品牌logo
     */
    private String logo;

    /**
     * 私域商城的-user
     */
    private Long mallUserId;

    /**
     * 私域商城的-shopId
     */
    private Long mallShopId;


    public static ThirdPartyOperatingSubjectBriefDTO transferTo(IpassOperatingSubjectBriefDTO ipassOperatingSubjectBriefDTO) {
        if (Objects.isNull(ipassOperatingSubjectBriefDTO)) {
            return null;
        }
        ThirdPartyOperatingSubjectBriefDTO thirdPartyOperatingSubjectBriefDTO = new ThirdPartyOperatingSubjectBriefDTO();
        BeanUtils.copyProperties(ipassOperatingSubjectBriefDTO, thirdPartyOperatingSubjectBriefDTO);
        return thirdPartyOperatingSubjectBriefDTO;
    }
}
