package com.medusa.gruul.service.uaa.service.model.vo;

import com.medusa.gruul.service.uaa.api.enums.Gender;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/6/12
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class UserDataVO implements Serializable {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Gender gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 分销码
     */
    private String distributorCode;
    /**
     * 手机号码
     */
    private String phone;

    /**
     * 资料项
     */
    private String dataItemJson;

    /**
     * 会员编号
     */
    private String memberNum;

    /**
     * 微信 open id
     */
    private String openid;

    /**
     * 所属部门
     */
    private String departmentName;

    private String gmtCreate;
}
