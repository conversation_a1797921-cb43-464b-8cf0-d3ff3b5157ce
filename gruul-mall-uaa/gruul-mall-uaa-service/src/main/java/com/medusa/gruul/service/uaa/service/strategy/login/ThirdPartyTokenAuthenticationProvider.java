package com.medusa.gruul.service.uaa.service.strategy.login;

import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.encrypt.Crypt;
import com.medusa.gruul.common.ipaas.client.IpaasClientSupport;
import com.medusa.gruul.common.ipaas.client.UserQueryClient;
import com.medusa.gruul.common.ipaas.model.IpaasUserAndEnterpriseDTO;
import com.medusa.gruul.common.ipaas.model.UserQueryReqDTO;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.security.model.bean.SecureUser;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.resource.exception.SecurityException;
import com.medusa.gruul.common.security.server.annotation.GrantType;
import com.medusa.gruul.common.security.server.model.AuthenticationRequest;
import com.medusa.gruul.common.security.server.provider.IAuthenticationProvider;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.ClientType;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseQueryDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserInfo;
import com.medusa.gruul.service.uaa.service.model.enums.UaaError;
import com.medusa.gruul.service.uaa.service.mp.entity.User;
import com.medusa.gruul.service.uaa.service.mp.service.IUserService;
import com.medusa.gruul.service.uaa.service.service.JwtTokenService;
import com.medusa.gruul.service.uaa.service.strategy.login.third.ThirdPartyFactory;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.io.Serial;

/**
 * 第三方Token认证提供者
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Component
@GrantType("third_party_token")
@RequiredArgsConstructor
public class ThirdPartyTokenAuthenticationProvider implements IAuthenticationProvider<ThirdPartyTokenAuthenticationProvider.ThirdPartyToken> {

    private final JwtTokenService jwtTokenService;
    private final IUserService userService;
    private final ReloadUserProvider reloadUserProvider;
    private final IpaasClientSupport ipaasClientSupport;
    private final ThirdPartyFactory thirdPartyFactory;

    @Override
    public SecureUser<?> authenticate(ThirdPartyToken authentication) throws AuthenticationException {
        try {
            // 1. 解析JWT Token获取用户信息
            ThirdPartyUserInfo userInfo = jwtTokenService.parseJwtToken(authentication);

            Long platformId = ISystem.platformIdOpt().getOrNull();
            Long enterpriseId = ISystem.enterpriseGuidOpt().getOrNull();
            // 2. 设置租户ID和企业ID到用户信息中
            userInfo.setTenantId(platformId)
                    .setEnterpriseId(enterpriseId);

            log.info("第三方Token解析成功，mobile: {}, tenantId: {}, enterpriseId: {}, userId:{}",
                    userInfo.getMobile(), userInfo.getTenantId(), userInfo.getEnterpriseId(), userInfo.getUserGuid());

            // 3. 根据客户端类型进行用户匹配和认证
            ClientType clientType = ISystem.clientTypeMust();
            return switch (clientType) {
                case SHOP_CONSOLE -> shopConsoleAuthenticate(userInfo, clientType);
                case CONSUMER -> consumerAuthenticate(userInfo, clientType);
                case PLATFORM_CONSOLE -> platformConsoleAuthenticate(userInfo, clientType);
                default -> defaultAuthenticate(userInfo, clientType);
            };

        } catch (Exception e) {
            log.error("第三方Token认证失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 商家控制台认证
     */
    private SecureUser<?> shopConsoleAuthenticate(ThirdPartyUserInfo userInfo, ClientType clientType) {
        // 根据手机号和租户ID查询用户
        User user = findUserByMobileAndTenant(userInfo.getMobile(), userInfo.getTenantId());

        if (user == null) {
            log.warn("商家控制台用户不存在，mobile: {}, tenantId: {}", userInfo.getMobile(), userInfo.getTenantId());
            throw UaaError.MERCHANDISER_NOT_EXIST.exception();
        }

        return reloadUserProvider.loadUser(clientType, ISystem.shopIdOpt().getOrNull(), user);
    }

    /**
     * 消费者端认证
     */
    private SecureUser<?> consumerAuthenticate(ThirdPartyUserInfo userInfo, ClientType clientType) {
        // 消费者端根据手机号查询用户
        User user = findUserByMobile(userInfo.getMobile());

        if (user == null) {
            log.info("消费者用户不存在，将创建新用户，mobile: {}", userInfo.getMobile());
            // 可以考虑自动创建用户，这里先抛出异常
            throw SecurityException.of(SecureCodes.ACCOUNT_INVALID);
        }

        return reloadUserProvider.loadUser(clientType, ISystem.shopIdOpt().getOrNull(), user);
    }

    /**
     * 平台控制台认证
     */
    private SecureUser<?> platformConsoleAuthenticate(ThirdPartyUserInfo userInfo, ClientType clientType) {
        // 获取用户下的企业列表
        ThirdPartyUserAndEnterpriseQueryDTO queryDTO = new ThirdPartyUserAndEnterpriseQueryDTO();
        queryDTO.setMobile(userInfo.getMobile());
        queryDTO.setEnterpriseId(userInfo.getEnterpriseId());
        queryDTO.setUserId(StringUtils.isEmpty(userInfo.getUserGuid()) ? null : Long.valueOf(userInfo.getUserGuid()));
        queryDTO.setTenantId(userInfo.getTenantId());
        ThirdPartyUserAndEnterpriseDTO thirdPartyUserAndEnterpriseDTO = thirdPartyFactory
                .create(userInfo.getThirdPartySource()).queryUserAndEnterprise(queryDTO);
        return reloadUserProvider.loadThirdPartyUser(ISystem.clientTypeMust(), thirdPartyUserAndEnterpriseDTO);
    }

    /**
     * 默认认证方式
     */
    private SecureUser<?> defaultAuthenticate(ThirdPartyUserInfo userInfo, ClientType clientType) {
        // 优先根据手机号和租户ID查询
        User user = findUserByMobileAndTenant(userInfo.getMobile(), userInfo.getTenantId());

        if (user == null) {
            log.warn("用户不存在，mobile: {}, tenantId: {}", userInfo.getMobile(), userInfo.getTenantId());
            throw SecurityException.of(SecureCodes.ACCOUNT_INVALID);
        }

        return reloadUserProvider.loadUser(clientType, ISystem.shopIdOpt().getOrNull(), user);
    }

    /**
     * 根据手机号和租户ID查询用户
     */
    private User findUserByMobileAndTenant(String mobile, Long tenantId) {

        try {
            return userService.lambdaQuery()
                    .eq(User::getMobile, mobile)
                    .eq(User::getPlatformId, tenantId)
                    .eq(User::getDeleted, Boolean.FALSE)
                    .one();
        } catch (NumberFormatException e) {
            log.warn("租户ID格式错误: {}", tenantId);
            return null;
        }
    }

    /**
     * 根据手机号查询用户
     */
    private User findUserByMobile(String mobile) {
        return Tenant.disable(() -> userService.lambdaQuery()
                .eq(User::getMobile, mobile)
                .eq(User::getDeleted, Boolean.FALSE)
                .one());
    }

    @Override
    public Class<ThirdPartyToken> requestType() {
        return ThirdPartyToken.class;
    }

    @Getter
    @Setter
    @ToString
    public static class ThirdPartyToken extends AuthenticationRequest {

        @Serial
        private static final long serialVersionUID = -4072234620140702256L;
        /**
         * 第三方JWT Token
         */
        @NotBlank
        private String thirdPartyToken;

        /**
         * 第三方来源
         *
         * @see com.medusa.gruul.service.uaa.service.model.enums.ThirdPartyTokenSource
         */
        @Nullable
        private String thirdPartySource;
    }
} 