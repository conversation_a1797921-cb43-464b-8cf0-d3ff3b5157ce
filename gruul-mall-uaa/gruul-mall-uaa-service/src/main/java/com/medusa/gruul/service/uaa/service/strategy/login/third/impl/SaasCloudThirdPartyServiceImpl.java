package com.medusa.gruul.service.uaa.service.strategy.login.third.impl;

import com.medusa.gruul.common.saas.cloud.client.SaasCloudClientSupport;
import com.medusa.gruul.common.saas.cloud.client.UserQueryClient;
import com.medusa.gruul.common.saas.cloud.model.SaasCloudUserAndEnterpriseDTO;
import com.medusa.gruul.common.saas.cloud.model.UserQueryReqDTO;
import com.medusa.gruul.common.security.model.enums.SecureCodes;
import com.medusa.gruul.common.security.resource.exception.SecurityException;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseDTO;
import com.medusa.gruul.service.uaa.service.model.dto.ThirdPartyUserAndEnterpriseQueryDTO;
import com.medusa.gruul.service.uaa.service.strategy.login.third.ThirdPartyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component("saasCloudThirdPartyServiceImpl")
public class SaasCloudThirdPartyServiceImpl implements ThirdPartyService {

    private final SaasCloudClientSupport saasCloudClientSupport;

    @Override
    public ThirdPartyUserAndEnterpriseDTO queryUserAndEnterprise(ThirdPartyUserAndEnterpriseQueryDTO queryDTO) {
        UserQueryReqDTO userQueryReqDTO = new UserQueryReqDTO();
        userQueryReqDTO.setGuid(String.valueOf(queryDTO.getUserId()));
        SaasCloudUserAndEnterpriseDTO saasCloudUserAndEnterpriseDTO = new UserQueryClient(saasCloudClientSupport).execute(userQueryReqDTO);
        if (saasCloudUserAndEnterpriseDTO == null) {
            throw SecurityException.of(SecureCodes.PERMISSION_DENIED);
        }
        return ThirdPartyUserAndEnterpriseDTO.transferTo(saasCloudUserAndEnterpriseDTO,
                userQueryReqDTO.getEnterpriseGuid(), String.valueOf(queryDTO.getTenantId()));
    }
}
