<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.service.uaa.service.mp.mapper.MenuMapper">


    <resultMap id="queryMenusByParentIdMap" type="com.medusa.gruul.service.uaa.service.model.vo.MenuVO">
        <result column="id" property="id"/>
        <result column="parentId" property="parentId"/>
        <result column="unshared" property="unshared"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="abbreviationName" property="abbreviationName"/>
        <result column="icon" property="icon"/>
        <result column="perms" property="perms"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="path" property="path"/>
        <result column="order" property="order"/>
        <result column="version" property="version"/>
    </resultMap>

    <select id="queryMenusByParentId" resultMap="queryMenusByParentIdMap">
        SELECT menu.id AS id,
        menu.parent_id AS parentId,
        menu.unshared AS unshared,
        menu.type AS type,
        menu.`name` AS `name`,
        menu.`abbreviation_name` AS `abbreviationName`,
        menu.icon AS icon,
        menu.perms AS perms,
        menu.path AS path,
        menu.`order` AS `order`,
        menu.version AS version
        FROM `t_menu` AS menu
        WHERE menu.parent_id = #{parentId}
        AND menu.client_type = #{clientType.value}
        AND menu.deleted = 0
        <if test="unshared != null">
            AND menu.unshared = #{unshared}
        </if>
        ORDER BY menu.order
    </select>
    <select id="queryCustomAdminMenus" resultMap="queryMenusByParentIdMap">
        SELECT
        menu.id AS id,
        menu.parent_id AS parentId,
        menu.type AS type,
        menu.`name` AS `name`,
        menu.icon AS icon,
        menu.perms AS perms,
        menu.path AS path,
        menu.`order` AS `order`,
        menu.version AS version
        FROM
        `t_menu` AS menu
        WHERE (
        menu.type = 0
        OR
        menu.id in (
        <foreach collection="menuIds" item="menuId" separator=",">
            #{menuId}
        </foreach>
        )
        )
        AND menu.parent_id = #{parentId}
        AND menu.client_type = #{clientType.value}
        AND menu.unshared = 0
        AND menu.deleted = 0
        ORDER BY menu.order
    </select>
</mapper>
