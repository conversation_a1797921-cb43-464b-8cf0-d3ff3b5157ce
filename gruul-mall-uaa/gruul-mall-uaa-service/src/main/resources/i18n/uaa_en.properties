uaa.user.has.other.role=The user already has other permissions. Please modify or adjust the user's permissions directly
uaa.user.mobile.incorrect.format=Incorrect phone number format
uaa.user.confirm.password.incorrect=incorrect second confirmation password
uaa.user.username.has.been.used=Username already in use
uaa.user.mobile.has.been.used=The phone number has been bound by another user
uaa.captcha.slide.invalid=Slider verification code verification failed
uaa.role.name.has.been.used=A role with the same name already exists
uaa.excel.template.name=importBatchUsers-template.xls
uaa.excel.data.read.error=Excel data conversion exception, line number: {0}, column number: {1}, error message: {2}, please check if the data format is correct: {3}
uaa.member.type.and.level.must.exist.or.empty=Line {0}, member type and member level must both exist or be empty
uaa.member.type.and.level.not.match=Line {0}, member type and member level do not match
uaa.free.member.level.setting.error=Free membership level setting error, maximum level is' {0} '
uaa.paid.member.level.setting.error=Paid member level setting error
uaa.role.has.user=The role has already been bound to the user, and it needs to be unbound first
uaa.special.role.cannot.delete=Special roles cannot be deleted
uaa.access.path.cannot.be.empty=access path cannot be empty
uaa.access.perm.cannot.be.empty=access permissions cannot be empty
uaa.menu.name.or.path.has.exist=There is a menu with the same name or path
uaa.sms.code.error=Incorrect SMS verification code input
uaa.sms.code.expired=The SMS verification code has expired
uaa.menu.has.children=Cannot operate menu data with children
uaa.can.not.delete.enable.admin=Cannot delete enabled administrator account 
uaa.merchandiser.not.exist=Merchandiser does not exist
uaa.merchandiser.has.forbidden=Merchandiser has been forbidden
uaa.merchandiser.not.has.shop=Merchandiser does not have a shop
uaa.user.forbidden=User has been forbidden
uaa.third.party.token.invalid=Third party token is invalid
uaa.third.party.token.expired=Third party token has expired
uaa.third.party.token.missing.field=Third party token is missing required field
uaa.third.party.token.parse.error=Third party token parse failed