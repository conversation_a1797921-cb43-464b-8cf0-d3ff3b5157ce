# 使用 Ubuntu Jammy 基础镜像（基于 Eclipse Temurin 的镜像）
FROM eclipse-temurin:17.0.15_6-jdk-jammy

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo 'Asia/Shanghai' > /etc/timezone

# 设置环境变量
ENV TZ=Asia/Shanghai

ADD config/ /config/
ADD gruul-mall-cart-service-1.0.jar gruul-mall-cart-service-1.0.jar
ADD lib/ /lib/
ENTRYPOINT ["java","-jar","--add-opens=java.base/java.lang=ALL-UNNAMED","--add-opens=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED","--add-opens=java.base/java.math=ALL-UNNAMED","-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:38580","gruul-mall-cart-service-1.0.jar"]
