package com.medusa.gruul.addon.distribute.model.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.addon.distribute.model.enums.Level;
import com.medusa.gruul.addon.distribute.mp.entity.Distributor;

import java.io.Serial;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * date 2022/11/22
 */
@Getter
@Setter
@ToString
public class DistributorTeamQueryDTO extends Page<Distributor> {

	@Serial
	private static final long serialVersionUID = 8054628148930436062L;
	/**
	 * 分销商用户id
	 */
	private Long userId;

	/**
	 * 查询的分销员等级
	 */
	private Level level;

	/**
	 * 一级分销员数量
	 */
	private Long count1;

	/**
	 * 所属二级分销员数量
	 */
	private Long count2;

	/**
	 * 所属三级分销员数量
	 */
	private Long count3;

	/**
	 * 关键词（模糊匹配会员姓名或手机号）
	 */
	private String keywords;
}
