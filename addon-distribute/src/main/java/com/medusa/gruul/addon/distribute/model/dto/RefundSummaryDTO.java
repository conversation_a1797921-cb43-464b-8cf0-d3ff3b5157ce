package com.medusa.gruul.addon.distribute.model.dto;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

/**
 * 退款汇总信息DTO
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Getter
@Setter
@ToString
public class RefundSummaryDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 退款总金额
	 */
	private Long totalRefundAmount;

	/**
	 * 一级佣金退款总额
	 */
	private Long totalRefundOneBonus;

	/**
	 * 二级佣金退款总额
	 */
	private Long totalRefundTwoBonus;

	/**
	 * 三级佣金退款总额
	 */
	private Long totalRefundThreeBonus;

	public RefundSummaryDTO setTotalRefundAmount (Long totalRefundAmount) {
		this.totalRefundAmount = totalRefundAmount;
		return this;
	}

	public RefundSummaryDTO setTotalRefundOneBonus (Long totalRefundOneBonus) {
		this.totalRefundOneBonus = totalRefundOneBonus;
		return this;
	}

	public RefundSummaryDTO setTotalRefundTwoBonus (Long totalRefundTwoBonus) {
		this.totalRefundTwoBonus = totalRefundTwoBonus;
		return this;
	}

	public RefundSummaryDTO setTotalRefundThreeBonus (Long totalRefundThreeBonus) {
		this.totalRefundThreeBonus = totalRefundThreeBonus;
		return this;
	}
}
