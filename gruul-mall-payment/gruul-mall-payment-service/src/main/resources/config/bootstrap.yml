server:
  port: 9210
spring:
  main:
    allow-circular-references: true
  application:
    name: gruul-mall-payment
  profiles:
    active: dev
  cloud:
    nacos:
      username: ${NACOS_USER_NAME:nacos}
      password: ${NACOS_PASSWORD:nacos}
      server-addr: ${NACOS_HOST:s2b2c-nacos:8848}
      discovery:
        # 服务注册地址
        server-addr: ${NACOS_HOST:s2b2c-nacos:8848}
        namespace: ${spring.profiles.active}
        ip: ${POD_IP:127.0.0.1}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: ${spring.cloud.nacos.discovery.namespace}
        shared-configs:
          - dataId: application-common.${spring.cloud.nacos.config.file-extension}
