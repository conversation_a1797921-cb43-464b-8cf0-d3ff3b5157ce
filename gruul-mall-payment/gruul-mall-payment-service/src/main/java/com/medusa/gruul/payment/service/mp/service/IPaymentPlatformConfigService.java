package com.medusa.gruul.payment.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.payment.api.entity.PaymentPlatformConfig;
import java.util.List;

/**
 * 平台支付类型配置表 服务接口
 */
public interface IPaymentPlatformConfigService extends IService<PaymentPlatformConfig> {
    
    /**
     * 切换支付类型开启状态
     *
     * @param platformId 平台ID
     * @param payType 支付类型
     * @param isOpen 是否开启
     */
    void switchPaymentStatus(Long platformId, PayType payType, Integer isOpen);

    /**
     * 获取支付类型开启状态
     *
     * @param platformId 平台ID
     * @param payType 支付类型
     * @return 是否开启 0-关闭 1-开启，如果配置不存在返回0
     */
    Integer getPayTypeStatus(Long platformId, PayType payType);

    /**
     * 获取平台下所有支付类型配置
     *
     * @param platformId 平台ID
     * @return 支付类型配置列表
     */
    List<PaymentPlatformConfig> getPlatformConfigs(Long platformId);

    /**
     * 获取平台下已开启的支付类型列表
     *
     * @param platformId 平台ID
     * @return 已开启的支付类型列表
     */
    PayType getEnabledPayTypes(Long platformId);
} 