package com.medusa.gruul.payment.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.payment.api.enums.AggPayStateEnum;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.service.common.enums.AggRefundStateEnum;
import com.medusa.gruul.payment.service.config.AggPayConfig;
import com.medusa.gruul.payment.service.config.DeveloperConfig;
import com.medusa.gruul.payment.service.config.PayConstant;

import com.medusa.gruul.payment.service.model.dto.HandlerPayDTO;
import com.medusa.gruul.payment.service.model.vo.MerchantDetailsVO;
import com.medusa.gruul.payment.service.service.AggPayRedisService;
import com.medusa.gruul.payment.service.service.AggPayRpcService;
import com.medusa.gruul.payment.service.service.AggPollingService;
import com.medusa.gruul.payment.service.service.AggPayMqService;
import com.medusa.gruul.payment.service.service.AggResultRpcService;
import com.medusa.gruul.payment.service.util.TradingUtils;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class AggPollingServiceImpl implements AggPollingService {

    private final DeveloperConfig developerConfig;

    private final AggPayConfig aggPayConfig;

    private final AggPayMqService aggPayMqService;

    private final AggPayRedisService aggPayRedisService;

    private final AggPayRpcService aggPayRpcService;

    private final AggResultRpcService aggResultRpcService;

    private static final ScheduledExecutorService se = Executors.newScheduledThreadPool(2);

    @Override
    public void startPrePayPolling(AggPayPollingDTO aggPayPollingDTO, HandlerPayDTO handlerPayBO) {
        log.info("开始聚合支付轮训，aggPayPollingDTO：{}，handlerPayBO：{}",
                JSON.toJSONString(aggPayPollingDTO),
                JSON.toJSONString(handlerPayBO));
        if (!aggPayMqService.sendAggPayPollingMessage(aggPayPollingDTO, handlerPayBO)) {
            // mq发送失败时使用定时线程池补偿
            doPrePayPollingByScheduledTask(handlerPayBO, aggPayPollingDTO);
        }
    }

    /**
     * 和redis、中数据做比较
     * 如果和redis中比对相同，返回数据，不需要回调，
     * 返回空：需要回调
     */
    @Override
    public Mono<String> compareStatWithCache(String orderGuid, String payGuid, String paySt) {
        //查redis上一次或者
        return aggPayRedisService.getCallBackResp(orderGuid, payGuid)
                //筛选状态不对的
                .filter(state -> AggPayStateEnum.isFinish(paySt))
                //存redis
                .doOnNext(state -> aggPayRedisService.putCallBackResp(orderGuid, payGuid, paySt))
                //返回result结果
                .flatMap(state -> Mono.just("SUCCESS"));
    }

    @Override
    public void startRefundPolling(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingDTO aggRefundPollingDTO) {
        doRefundPollingByScheduledTask(saasAggRefundDTO, aggRefundPollingDTO);
    }

    @Override
    public boolean handlePollingResult(AggPayPollingDTO payPollingDTO, HandlerPayDTO handlerPayBO, boolean useMq) {
        String orderGUID = payPollingDTO.getOrderGUID();
        String payGUID = payPollingDTO.getPayGUID();
        AggPayPollingRespDTO pollingRespDTO = aggPayRpcService.doPolling(payPollingDTO);
        // 公众号支付存在signature为空的场景，表示当前时刻操作暂时失败（如：订单尚不存在），应该继续轮询
//        MerchantDetailsVO paymentInfoDTO = handlerPayBO.getMerchantDetailsVO();
        if (!StringUtils.hasText(pollingRespDTO.getSignature())) {
            log.warn("轮询聚合支付结果业务报警：{}", pollingRespDTO.getMsg());
            return isGoOn(payPollingDTO, handlerPayBO, useMq);
        }
        // 验证签名
//        if (!validatedSignature(pollingRespDTO, paymentInfoDTO.getKeyPrivate())) {
//            log.error("轮询聚合支付结果验签失败：{}", JSON.toJSONString(pollingRespDTO));
//            return false;
//        }
        // 存储轮询结果
        aggPayRedisService.putPollingResp(orderGUID, payGUID, pollingRespDTO);
        // 是否需要回调
        boolean needCallBack = compareStatWithCache(orderGUID, payGUID, pollingRespDTO.getPaySt()).block() == null;
        if (!needCallBack) {
            return true;
        }
        // 支付状态不为"终结状态（成功、失败、关闭）"时：继续轮询
        String paySt = pollingRespDTO.getPaySt();
        boolean paymentInProgress = !Lists.newArrayList(AggPayStateEnum.SUCCESS.getId(), AggPayStateEnum.FAILURE.getId(),
                AggPayStateEnum.CLOSED.getId(), AggPayStateEnum.DOESNOTSUPPORT.getId()).contains(paySt);
        // 支付中
        boolean pending = handlerPayBO.getHandlerType() == 0 && (AggPayStateEnum.READY.getId().equals(paySt) || AggPayStateEnum.PENDING.getId().equals(paySt));
        if (PayConstant.SUCCESS_CODE.equals(pollingRespDTO.getCode()) && paymentInProgress) {
            if (pending) {
                SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
                saasPollingDTO.setOrderGuid(orderGUID);
                saasPollingDTO.setPayGuid(payGUID);
                AggPayPollingRespDTO aggPayPollingRespDTO = aggPayRedisService.getPollingResp(saasPollingDTO).block();
                if (aggPayPollingRespDTO != null && aggPayPollingRespDTO.getPaySt() != null && aggPayPollingRespDTO.getPaySt().equals(AggPayStateEnum.FAILURE.getId())) {
                    //save payRecordDO
                    aggPayPollingRespDTO.setOrderGuid(orderGUID);
                    aggPayPollingRespDTO.setPayGuid(payGUID);
                    log.info("轮询聚合支付结果10000：payPollingDTO:{}", JSON.toJSONString(aggPayPollingRespDTO));
                    aggResultRpcService.handlePayResult(handlerPayBO, aggPayPollingRespDTO);
                    return true;
                }
            }
            return isGoOn(payPollingDTO, handlerPayBO, useMq);
        }
        // 响应失败、支付状态"终结"情况下：缓存结果，停止轮询
        // (useMq时) 仅返回消费成功，不重新投递
        // (useThreadPool时) 返回执行成功，停止定时任务
        aggResultRpcService.handlePayResult(handlerPayBO, pollingRespDTO);
        return true;
    }

    private boolean isGoOn(AggPayPollingDTO payPollingDTO, HandlerPayDTO handlerPayBO, boolean useMq) {
        if (useMq) {
            // 重新投递消息，并返回当前消息消费成功标识true
            aggPayMqService.sendAggPayPollingMessage(payPollingDTO, handlerPayBO);
            return true;
        }
        // 返回支付状态未终结标识false，使得定时任务得以继续执行
        return false;
    }


    public void doPrePayPollingByScheduledTask(HandlerPayDTO handlerPayBO, AggPayPollingDTO pollingPayDTO) {
        log.error("消息轮训查询，pollingPayDTO：{}，handlerPayBO：{}",
                JSON.toJSONString(pollingPayDTO),
                JSON.toJSONString(handlerPayBO));
        // 获取主线程的系统信息
        Systems mainThreadSystem = ISystem.systemOpt().getOrNull();
        // 补偿措施
        Integer maxPollingTimes = aggPayConfig.getPollingTimes();
        Map<String, Future<?>> map = new HashMap<>(1);
        AtomicInteger pollingTimes = new AtomicInteger(0);
        ScheduledFuture<?> scheduledFuture = se.scheduleAtFixedRate(() -> {
            try {
                // 在子线程中设置系统信息
                if (mainThreadSystem != null) {
                    SystemContextHolder.set(mainThreadSystem);
                }
                int curDeliveryTimes = pollingTimes.incrementAndGet();
                if (curDeliveryTimes > maxPollingTimes) {
                    log.warn("当前投递次数：{}，超过上限：{}，停止投递", curDeliveryTimes, maxPollingTimes);
                    map.get("key").cancel(true);
                    map.clear();
                    return;
                }
                log.info("当前投递次数：{}，上限：{}", curDeliveryTimes, maxPollingTimes);
                if (handlePollingResult(pollingPayDTO, handlerPayBO, false)) {
                    map.get("key").cancel(true);
                }
            } finally {
                // 清理线程上下文
                SystemContextHolder.clear();
            }
        }, 2, 5, TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }

    private void doRefundPollingByScheduledTask(SaasAggRefundDTO saasAggRefundDTO,
                                                AggRefundPollingDTO aggRefundPollingDTO) {
        Integer maxPollingTimes = aggPayConfig.getRefundPollingTimes();
        Map<String, Future<?>> map = new HashMap<>(1);
        AtomicInteger pollingTimes = new AtomicInteger(0);
        Systems sysSystem = ISystem.systemOpt().get();
        ScheduledFuture<?> scheduledFuture = se.scheduleAtFixedRate(() -> {
            SystemContextHolder.set(sysSystem);
            int curDeliveryTimes = pollingTimes.incrementAndGet();
            if (curDeliveryTimes > aggPayConfig.getRefundPollingTimes()) {
                log.warn("当前投递次数：{}，超过上限：{}，停止投递", curDeliveryTimes, maxPollingTimes);
                map.get("key").cancel(true);
                map.clear();
                return;
            }
            log.info("当前投递次数：{}，上限：{}", curDeliveryTimes, maxPollingTimes);
            AggRefundPollingRespDTO aggRefundPollingRespDTO = aggPayRpcService.doRefundPolling(aggRefundPollingDTO);
            if (PayConstant.SUCCESS_CODE.equals(aggRefundPollingRespDTO.getCode())) {
                List<AggRefundDetailRespDTO> refundOrderDetail = aggRefundPollingRespDTO.getRefundOrderDetial();
                AggRefundDetailRespDTO aggRefundDetailRespDTO = refundOrderDetail.get(refundOrderDetail.size() - 1);
                if (AggRefundStateEnum.REFUND_SUCCESS.getState().equals(aggRefundDetailRespDTO.getStatus())
                        || AggRefundStateEnum.REFUND_FAILURE.getState().equals(aggRefundDetailRespDTO.getStatus())) {
                    // handle result
                    aggPayRedisService.putRefundPollingResp(saasAggRefundDTO.getAggRefundReqDTO().getOrderGUID(),
                            saasAggRefundDTO.getAggRefundReqDTO().getPayGUID(), aggRefundPollingRespDTO);
                    // 业务执行完后才取消定时任务，否则数据库获取连接时会被interrupt
                    map.get("key").cancel(true);
                }
            }
        }, 1, 1, TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }
}
