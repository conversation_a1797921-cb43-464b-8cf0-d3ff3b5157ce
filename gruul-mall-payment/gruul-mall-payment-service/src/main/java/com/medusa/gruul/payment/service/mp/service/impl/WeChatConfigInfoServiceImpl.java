package com.medusa.gruul.payment.service.mp.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.wechat.WechatServicePreloader;
import com.medusa.gruul.payment.api.entity.WechatConfigInfo;
import com.medusa.gruul.payment.api.model.dto.WeChatConfigInfoDTO;
import com.medusa.gruul.payment.api.model.vo.WeChatConfigInfoVO;
import com.medusa.gruul.payment.service.mp.mapper.WechatConfigInfoMapper;
import com.medusa.gruul.payment.service.mp.service.WeChatConfigInfoService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 小程序配置实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WeChatConfigInfoServiceImpl extends ServiceImpl<WechatConfigInfoMapper, WechatConfigInfo> implements WeChatConfigInfoService {

    private static final String WE_CHAT_CONFIG_KEY = "WE_CHAT_CONFIG_KEY:";

    private final WechatServicePreloader wechatServicePreloader;

    @Override
    public void saveOrUpdateWeChatConfig(WeChatConfigInfoDTO request) {
        log.info("保存或更新小程序配置信息:{}", JSON.toJSONString(request));

        Long platformId = ISystem.platformIdMust();
        
        baseMapper.delete(new LambdaQueryWrapper<WechatConfigInfo>().eq(WechatConfigInfo::getPlatformId, platformId));
        WechatConfigInfo info = new WechatConfigInfo();
        info.setAppName(request.getAppName());
        info.setAppLogo(request.getAppLogo());
        info.setApplyPrivateKey(request.getApplyPrivateKey());
        info.setAppId(request.getAppId());
        info.setAppSecret(request.getAppSecret());
        info.setMpAppId(request.getMpAppId());
        info.setMpAppSecret(request.getMpAppSecret());
        info.setMiniAppDeliver(request.getMiniAppDeliver());
        info.setPlatformId(platformId);
        baseMapper.insert(info);
        RedisUtil.setCacheObject(getCacheKey(), JSON.toJSONString(info));
        
        // 配置更新后自动刷新预加载缓存
        if (wechatServicePreloader != null) {
            try {
                log.info("微信配置已更新，刷新平台 {} 的预加载服务缓存", platformId);
                wechatServicePreloader.refreshPlatformServices(platformId);
                log.info("平台 {} 的微信服务缓存刷新完成", platformId);
            } catch (Exception e) {
                log.error("刷新平台 {} 的微信服务缓存失败: {}", platformId, e.getMessage(), e);
            }
        } else {
            log.warn("WechatServicePreloader 未注入，跳过缓存刷新");
        }
    }


    private WechatConfigInfo getWeChatConfigInfo() {
        return baseMapper.selectOne(new LambdaQueryWrapper<WechatConfigInfo>()
                .eq(WechatConfigInfo::getPlatformId, ISystem.platformIdMust()));
    }

    @Override
    public WeChatConfigInfoVO queryByPlatformId() {
        WeChatConfigInfoVO weChatConfigInfoVO = null;
        log.info("查询小程序配置信息,平台ID:{}", ISystem.platformIdMust());
        String cache = RedisUtil.getCacheObject(getCacheKey());

        if (StringUtils.isNotEmpty(cache)) {
            log.info("从缓存中查询小程序配置信息:{}", cache);
            weChatConfigInfoVO = JSON.parseObject(cache, WeChatConfigInfoVO.class);
        } else {
            log.info("从数据库查询小程序配置信息");
            WechatConfigInfo hsaWeChatConfigInfo = getWeChatConfigInfo();
            if (Objects.nonNull(hsaWeChatConfigInfo)) {
                weChatConfigInfoVO = new WeChatConfigInfoVO();
                BeanUtils.copyProperties(hsaWeChatConfigInfo, weChatConfigInfoVO);
                RedisUtil.setCacheObject(getCacheKey(), JSON.toJSONString(weChatConfigInfoVO));
            }
        }
        log.info("查询小程序配置信息:{}", JSON.toJSONString(weChatConfigInfoVO));
        return weChatConfigInfoVO;
    }

    private static String getCacheKey() {
        return WE_CHAT_CONFIG_KEY + ISystem.platformIdMust();
    }

}
