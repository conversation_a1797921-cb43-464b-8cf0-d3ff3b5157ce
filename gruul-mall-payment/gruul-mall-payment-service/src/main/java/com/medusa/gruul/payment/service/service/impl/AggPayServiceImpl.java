package com.medusa.gruul.payment.service.service.impl;

import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.payment.api.enums.AggPayStateEnum;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.service.model.dto.HandlerPayDTO;
import com.medusa.gruul.payment.service.assembler.AggPayMapstructAssembler;
import com.medusa.gruul.payment.service.common.enums.AggRefundStateEnum;
import com.medusa.gruul.payment.service.config.AggPayConfig;
import com.medusa.gruul.payment.service.config.DeveloperConfig;
import com.medusa.gruul.payment.service.config.PayConstant;
import com.medusa.gruul.payment.service.model.vo.AggPayAttachDataVO;
import com.medusa.gruul.payment.service.model.vo.MerchantDetailsVO;
import com.medusa.gruul.payment.service.mp.service.IPaymentMerchantConfigService;
import com.medusa.gruul.payment.service.service.*;
import com.medusa.gruul.payment.service.util.RespFriendlyUtils;
import com.medusa.gruul.payment.service.util.TradingUtils;
import io.vavr.control.Option;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * 聚合支付实现
 */
@Slf4j
@Service
@DubboService(interfaceClass = AggPayService.class)
@RequiredArgsConstructor
public class AggPayServiceImpl implements AggPayService {

    private final DeveloperConfig developerConfig;

    private final AggPayConfig aggPayConfig;

    private final AggPayRedisService aggPayRedisService;

    private final AggPayRpcService aggPayRpcService;

    private final AggPollingService aggPollingService;

    private final RespFriendlyUtils respFriendlyUtils;

    private final IPaymentMerchantConfigService paymentMerchantConfigService;


    @Lazy
    @Autowired
    private AggResultRpcService aggResultRpcService;


    @Override
    public Mono<AggPayRespDTO> prePay(SaasAggPayDTO saasAggPayDTO) {
        Systems mainThreadSystem = ISystem.systemOpt().getOrNull();
        return Mono.just(saasAggPayDTO)
                .flatMap(req -> {
                    // 设置系统信息
                    if (mainThreadSystem != null) {
                        SystemContextHolder.set(mainThreadSystem);
                    }
                    return Mono.just(req);
                })
                // 查询 appId, appSecret
                .flatMap(req -> getPaymentInfoAsync(PayType.AGGREGATION,
                        Long.parseLong(saasAggPayDTO.getStoreGuid()), saasAggPayDTO.getRevenueType()))
                // 预下单、后台轮询
                .flatMap(merchantDetailsVO -> executePrePayThenPolling(saasAggPayDTO, merchantDetailsVO))
                // 为空处理
                .defaultIfEmpty(AggPayRespDTO.errorPaymentResp("10005", "聚合支付下单返回空"))
                // 错误处理
                .onErrorResume(respFriendlyUtils::convertPay2Friendly)
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese);
    }

    @Override
    public Mono<AggPayPollingRespDTO> prePayPolling(SaasPollingDTO saasPollingDTO) {
        return Flux.concat(queryPrePayResult(saasPollingDTO)).next();
    }


    @Override
    public Mono<AggPayPollingRespDTO> queryPrePayResult(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 前台主动轮询最新支付结果
                .flatMap(req -> aggPayRpcService.prePayQueryBankAsync(buildPrePayQueryReq(req)))
                // 错误处理
                .onErrorResume(e -> respFriendlyUtils.convertPayPolling2Friendly(e, "聚合支付查询异常"))
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese)
                //存redis,以供轮询插用
                .doOnNext(a -> aggPayRedisService.putPollingResp(saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid(), a));
    }

    @Override
    public Mono<AggPayPollingRespDTO> queryPayState(SaasPollingOdooDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> getPaymentInfoAsync(PayType.AGGREGATION,
                        Long.parseLong(saasPollingDTO.getStoreGuid()), saasPollingDTO.getRevenueType()))
                // 前台主动轮询最新支付结果
                .flatMap(merchantDetailsVO -> aggPayRpcService.payPayQueryBankAsync(buildPrePayQueryReq(saasPollingDTO)))
                // 错误处理
                .onErrorResume(e -> respFriendlyUtils.convertPayPolling2Friendly(e, "聚合支付查询状态异常"))
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese)
                //存redis,以供轮询插用
                .doOnNext(a -> aggPayRedisService.putPollingResp(saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid(), a));
    }

    @Override
    public Mono<AggRefundRespDTO> refund(SaasAggRefundDTO saasAggRefundDTO) {
        Systems mainThreadSystem = ISystem.systemOpt().getOrNull();
        return Mono.just(saasAggRefundDTO)
                .flatMap(req -> {
                    // 设置系统信息
                    if (mainThreadSystem != null) {
                        SystemContextHolder.set(mainThreadSystem);
                    }
                    return Mono.just(req);
                })
                // 查询 appId, appSecret
                .flatMap(req -> getPaymentInfoAsync(PayType.AGGREGATION,
                        Long.parseLong(saasAggRefundDTO.getStoreGuid()), saasAggRefundDTO.getRevenueType()))
                // 执行退款
                .flatMap(merchantDetailsVO -> executeRefundThenPolling(saasAggRefundDTO, merchantDetailsVO))
                // 错误处理
                .onErrorResume(respFriendlyUtils::convertRefund2Friendly)
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese);
    }

    @Override
    public Mono<AggRefundPollingRespDTO> refundPolling(SaasPollingDTO saasPollingDTO) {
        return Flux.concat(aggPayRedisService.getRefundPollingResp(saasPollingDTO), queryRefundResult(saasPollingDTO)).next();
    }

    @Override
    public Mono<AggRefundPollingRespDTO> queryRefundResult(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> getPaymentInfoAsync(PayType.AGGREGATION,
                        Long.parseLong(saasPollingDTO.getStoreGuid()), saasPollingDTO.getRevenueType()))
                // 前台主动轮询最新退款结果
                .flatMap(merchantDetailsVO -> aggPayRpcService.doRefundPollingAsync(buildRefundQueryReq(saasPollingDTO, merchantDetailsVO)));
    }

    @Override
    public Mono<String> callback(AggPayCallbackDTO aggPayCallbackDTO) {
        // 先判断redis  间接防止线程安全问题  和 减少订单的回调请求量
        return Flux.concat(aggPollingService.compareStatWithCache(
                        aggPayCallbackDTO.getOrderGuid(),
                        aggPayCallbackDTO.getPayGuid(),
                        aggPayCallbackDTO.getPaySt()),
                doCallBack(aggPayCallbackDTO)).next();
    }

    /**
     * 回调内部服务
     */
    private Mono<String> doCallBack(AggPayCallbackDTO aggPayCallbackDTO) {
        return Mono.just(aggPayCallbackDTO)
                //转换callback DTO
                .flatMap(callbackDTO -> Mono.just(AggPayMapstructAssembler.makeCallBackDto(callbackDTO)))
                // 处理 返回true
                .doOnNext(pollingRespDTO -> aggResultRpcService.handlePayResult(buildHandlerPayDo(aggPayCallbackDTO), pollingRespDTO))
                // 存redis，更新支付结果
                .doOnNext(aggPayPollingRespDTO -> aggPayRedisService.putPollingResp(aggPayCallbackDTO.getOrderGuid(),
                        aggPayCallbackDTO.getPayGuid(), aggPayPollingRespDTO))
                //返回result
                .flatMap(pollingRespDTO -> Mono.just("SUCCESS"))
                //fail
                .onErrorReturn(Exception.class::isInstance, "FAIL");
    }

    private AggPayPreTradingReqDTO buildPrePayRequest(SaasAggPayDTO saasAggPayDTO, MerchantDetailsVO merchantDetailsVO) {
        AggPayPreTradingReqDTO payPreTradingReqDTO = saasAggPayDTO.getReqDTO();
        payPreTradingReqDTO.setAppId(merchantDetailsVO.getMchId());
        payPreTradingReqDTO.setDeveloperId(developerConfig.getId());
        payPreTradingReqDTO.setTimestamp(System.currentTimeMillis());
        payPreTradingReqDTO.setAmount(payPreTradingReqDTO.getAmount()
                // 聚合支付是以分为单位
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
        );
        payPreTradingReqDTO.setOutNotifyUrl(aggPayConfig.getCallBack());

        // 透传字段
        AggPayAttachDataVO aggPayAttachDataVO = AggPayMapstructAssembler.makeAttachVo(saasAggPayDTO);
        aggPayAttachDataVO.setSaasCallBackUrl(saasAggPayDTO.getSaasCallBackUrl());
        aggPayAttachDataVO.setIsLast(saasAggPayDTO.getIsLast());
        aggPayAttachDataVO.setOrderGuid(saasAggPayDTO.getReqDTO().getOrderGUID());
        Systems systems = ISystem.systemOpt().get();
        aggPayAttachDataVO.setSystems(JSON.toJSONString(systems));
        payPreTradingReqDTO.setAttachData(JSON.toJSONString(aggPayAttachDataVO));
        payPreTradingReqDTO.setEnterpriseName(saasAggPayDTO.getEnterpriseName());
        payPreTradingReqDTO.setSignature(TradingUtils.getSignature(
                payPreTradingReqDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate()
        ));
        log.info("签名：{}", JSON.toJSONString(payPreTradingReqDTO.getSignature()));
        return payPreTradingReqDTO;
    }

    private Mono<AggPayRespDTO> executePrePayThenPolling(SaasAggPayDTO saasAggPayDTO, MerchantDetailsVO merchantDetailsVO) {
        AggPayPreTradingReqDTO payPreTradingReqDTO = buildPrePayRequest(saasAggPayDTO, merchantDetailsVO);
        Systems sysSystem = ISystem.systemOpt().get();
        return aggPayRpcService.preTradingAsync(payPreTradingReqDTO)
                .doOnNext(aggPayRespDTO -> {
                    SystemContextHolder.set(sysSystem);
                    // 预下单成功则开启后台轮询最新支付结果
                    if (PayConstant.SUCCESS_CODE.equals(aggPayRespDTO.getCode()) || PayConstant.ICBC_SUCCESS_CODE.equals(aggPayRespDTO.getCode())) {
                        // 填充payGuid和orderGuid到结果中
                        aggPayRespDTO.setPayGuid(payPreTradingReqDTO.getPayGUID());
                        aggPayRespDTO.setOrderGuid(payPreTradingReqDTO.getOrderGUID());
                        // 将当前支付结果写入Redis，供调用方轮询
                        cachePrePayRespForPolling(aggPayRespDTO);
                        // 后台轮询最新支付结果（使用MQ或定时任务）
                        postMsgToStartInnerPolling(saasAggPayDTO, merchantDetailsVO, payPreTradingReqDTO);
                    }
                });
    }

    private void postMsgToStartInnerPolling(SaasAggPayDTO saasAggPayDTO, MerchantDetailsVO merchantDetailsVO,
                                            AggPayPreTradingReqDTO payPreTradingReqDTO) {
        // 后台轮询最新支付结果（先消息队列后定时任务）
        AggPayPollingDTO pollingPayDTO = AggPayMapstructAssembler.createPollingDto(saasAggPayDTO, payPreTradingReqDTO);
        pollingPayDTO.setSignature(TradingUtils.getSignature(
                pollingPayDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate()
        ));
        // 食堂传过来的时间戳是没有年月日的时间戳，这里替换成后台预支付时生成的时间戳
        SaasAggPayDTO finalSaasAggPayDTO = new SaasAggPayDTO();
        BeanUtils.copyProperties(saasAggPayDTO, finalSaasAggPayDTO);
        finalSaasAggPayDTO.setRequestTimestamp(saasAggPayDTO.getReqDTO().getTimestamp());
        HandlerPayDTO handlerPayDTO = HandlerPayDTO.builder()
                .baseInfo(AggPayMapstructAssembler.createBaseInfo(finalSaasAggPayDTO))
                .merchantDetailsVO(merchantDetailsVO)
                .innerCallBackUrl(saasAggPayDTO.getSaasCallBackUrl())
                .handlerType(0)
                .orderGUID(saasAggPayDTO.getReqDTO().getOrderGUID())
                .times(0)
                .build();
        aggPollingService.startPrePayPolling(pollingPayDTO, handlerPayDTO);
    }

    private void cachePrePayRespForPolling(AggPayRespDTO aggPayRespDTO) {
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode(aggPayRespDTO.getCode());
        pollingRespDTO.setMsg(aggPayRespDTO.getMsg());
        pollingRespDTO.setPaySt(AggPayStateEnum.READY.getId());
        aggPayRedisService.putPollingResp(aggPayRespDTO.getOrderGuid(), aggPayRespDTO.getPayGuid(), pollingRespDTO);
    }

    private AggPayPollingDTO buildPrePayQueryReq(SaasPollingDTO saasPollingDTO) {
        AggPayPollingDTO pollingPayDTO = new AggPayPollingDTO();
        pollingPayDTO.setAppId(saasPollingDTO.getMchId());
        pollingPayDTO.setDeveloperId(developerConfig.getId());
        pollingPayDTO.setTimestamp(System.currentTimeMillis());
        pollingPayDTO.setPayGUID(saasPollingDTO.getPayGuid());
        pollingPayDTO.setOrderGUID(saasPollingDTO.getOrderGuid());
        pollingPayDTO.setSignature(TradingUtils.getSignature(
                pollingPayDTO, developerConfig.getKey(), saasPollingDTO.getKeyPrivate()
        ));
        return pollingPayDTO;
    }

    private Mono<AggRefundRespDTO> executeRefundThenPolling(SaasAggRefundDTO saasAggRefundDTO,
                                                            MerchantDetailsVO merchantDetailsVO) {
        AggRefundReqDTO aggRefundReqDTO = buildAggRefundRequest(saasAggRefundDTO, merchantDetailsVO);
        Systems sysSystem = ISystem.systemOpt().get();
        // 退款
        return aggPayRpcService.refundAsync(aggRefundReqDTO)
                .doOnNext(aggRefundRespDTO -> {
                    SystemContextHolder.set(sysSystem);
                    // 退款处理中则开启后台轮询最新退款结果
                    if (PayConstant.SUCCESS_CODE.equals(aggRefundRespDTO.getCode())
                            && AggRefundStateEnum.REFUND_PROCESSED.getState().equals(aggRefundRespDTO.getState())) {
                        // 将当前退款结果写入Redis，供调用方轮询
                        cacheRefundRespForPolling(aggRefundReqDTO);
                        // 后台轮询最新退款结果（使用定时任务）
                        postMsgToStartRefundInnerPolling(saasAggRefundDTO, merchantDetailsVO, aggRefundReqDTO);
                    }
                });
    }

    private void cacheRefundRespForPolling(AggRefundReqDTO aggRefundReqDTO) {
        AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode(PayConstant.SUCCESS_CODE);
        aggRefundPollingRespDTO.setMsg("退款中");
        aggPayRedisService.putRefundPollingResp(
                aggRefundReqDTO.getOrderGUID(), aggRefundReqDTO.getPayGUID(), aggRefundPollingRespDTO
        );
    }

    private void postMsgToStartRefundInnerPolling(SaasAggRefundDTO saasAggRefundDTO, MerchantDetailsVO merchantDetailsVO, AggRefundReqDTO aggRefundReqDTO) {
        AggRefundPollingDTO aggRefundPollingDTO = AggPayMapstructAssembler.createRefundPollingDto(aggRefundReqDTO);
        aggRefundPollingDTO.setSignature(TradingUtils.getSignature(
                aggRefundPollingDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate()
        ));
        aggPollingService.startRefundPolling(saasAggRefundDTO, aggRefundPollingDTO);
    }

    private AggRefundReqDTO buildAggRefundRequest(SaasAggRefundDTO saasAggRefundDTO, MerchantDetailsVO merchantDetailsVO) {
        AggRefundReqDTO aggRefundReqDTO = saasAggRefundDTO.getAggRefundReqDTO();
        aggRefundReqDTO.setDeveloperId(developerConfig.getId());
        aggRefundReqDTO.setAppId(merchantDetailsVO.getMchId());
        aggRefundReqDTO.setTimestamp(System.currentTimeMillis());
        aggRefundReqDTO.setReason(StringUtil.isEmpty(saasAggRefundDTO.getAggRefundReqDTO().getReason()) ?
                "聚合支付退款" : saasAggRefundDTO.getAggRefundReqDTO().getReason());
        aggRefundReqDTO.setRefundFee(aggRefundReqDTO.getRefundFee()
                // 聚合支付是以分为单位
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
        );
        aggRefundReqDTO.setSignature(TradingUtils.getSignature(
                aggRefundReqDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate()
        ));
        return aggRefundReqDTO;
    }

    private AggRefundPollingDTO buildRefundQueryReq(SaasPollingDTO saasPollingDTO, MerchantDetailsVO merchantDetailsVO) {
        AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setPayGUID(saasPollingDTO.getPayGuid());
        aggRefundPollingDTO.setOrderGUID(saasPollingDTO.getOrderGuid());
        aggRefundPollingDTO.setAppId(merchantDetailsVO.getMchId());
        aggRefundPollingDTO.setDeveloperId(developerConfig.getId());
        aggRefundPollingDTO.setTimestamp(System.currentTimeMillis());
        aggRefundPollingDTO.setSignature(TradingUtils.getSignature(
                aggRefundPollingDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate()
        ));
        return aggRefundPollingDTO;
    }

    public Mono<MerchantDetailsVO> getPaymentInfoAsync(PayType payType, Long storeGuid, Integer revenueType) {
        return Mono.create(
                sink -> {
                    MerchantDetailsVO merchantDetailsVO = paymentMerchantConfigService.getMerchantDetailByPayTypeAndShopId(revenueType, payType, String.valueOf(storeGuid));
                    if (merchantDetailsVO == null) {
                        log.error("聚合支付账户信息不存在,请检查配置,storeGuid:{}", storeGuid);
                    } else {
                        sink.success(merchantDetailsVO);
                    }
                });
    }

    private HandlerPayDTO buildHandlerPayDo(AggPayCallbackDTO aggPayCallbackDTO) {
        log.info("aggPayCallbackDTO:{}", aggPayCallbackDTO);
        HandlerPayDTO handlerPayBO = new HandlerPayDTO();
        String attachData = aggPayCallbackDTO.getAttachData();
        if (attachData == null || !attachData.startsWith("{")) {
            log.error("attchData is not json: {}", attachData);
            return null;
        }
        //检查时候有签名
        if (!StringUtils.hasText(aggPayCallbackDTO.getSignature())) {
            log.warn("轮询聚合支付结果业务报警：无签名");
            return null;
        }
        AggPayAttachDataVO aggPayAttachDataVO = JSON.parseObject(attachData, AggPayAttachDataVO.class);
        log.info("aggPayAttachDataVO:{}", JSON.toJSONString(aggPayAttachDataVO));
//        MerchantDetailsVO merchantDetailsVO = paymentMerchantConfigService
//                .getMerchantDetailByPayTypeAndShopId(null, PayType.AGGREGATION, String.valueOf(aggPayAttachDataVO.getStoreGuid()));
//        if (merchantDetailsVO == null) {
//            log.error("获取支付配置失败");
//            return null;
//        }
//        String signatureForCheck = TradingUtils.getSignatureForCheck(aggPayCallbackDTO, developerConfig.getKey(), merchantDetailsVO.getKeyPrivate());
//        // 验证签名
//        if (!Objects.equals(aggPayCallbackDTO.getSignature(), signatureForCheck)) {
//            log.error("回调聚合支付结果验签失败：{}", JSON.toJSONString(aggPayCallbackDTO));
//            return null;
//        }
        handlerPayBO.setInnerCallBackUrl(aggPayAttachDataVO.getSaasCallBackUrl());
        BaseInfoDTO baseInfo = AggPayMapstructAssembler.createBaseInfo(aggPayAttachDataVO);
        baseInfo.setHeaderUserInfo(aggPayAttachDataVO.getHeaderUserInfo());
        handlerPayBO.setBaseInfo(baseInfo);
        log.info("buildHandlerPayDo:{}", handlerPayBO);
        return handlerPayBO;
    }
}
