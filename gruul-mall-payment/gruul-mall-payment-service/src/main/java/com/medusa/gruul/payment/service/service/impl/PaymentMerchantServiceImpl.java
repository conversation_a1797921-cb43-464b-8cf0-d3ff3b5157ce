package com.medusa.gruul.payment.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.common.model.enums.BooleanEnum;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.web.handler.Handler;
import com.medusa.gruul.common.web.util.SpringUtils;
import com.medusa.gruul.payment.api.entity.MerchantDetails;
import com.medusa.gruul.payment.api.entity.PaymentMerchantConfig;
import com.medusa.gruul.payment.service.common.annotation.MerchantHandler;
import com.medusa.gruul.payment.service.common.helper.WechatHelper;
import com.medusa.gruul.payment.service.model.dto.MerchantDetailsDTO;
import com.medusa.gruul.payment.service.model.enums.PaymentError;
import com.medusa.gruul.payment.service.model.vo.MerchantDetailsVO;
import com.medusa.gruul.shop.api.model.vo.QueryShopListVO;
import com.medusa.gruul.payment.service.mp.service.IMerchantDetailsService;
import com.medusa.gruul.payment.service.mp.service.IPaymentMerchantConfigService;
import com.medusa.gruul.payment.service.service.PaymentMerchantService;
import com.medusa.gruul.shop.api.model.dto.ShopPageDTO;
import com.medusa.gruul.shop.api.rpc.ShopRpcService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.transaction.annotation.Transactional;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.payment.service.mp.mapper.MerchantDetailsMapper;

import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ Description
 * @date 2022-07-13 09:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentMerchantServiceImpl implements PaymentMerchantService {

    private final IMerchantDetailsService merchantDetailsService;
    private final IPaymentMerchantConfigService paymentMerchantConfigService;
    private final ShopRpcService shopService;
    private final MerchantDetailsMapper merchantDetailsMapper;

    /**
     * 编辑(新增/或修改)商户支付配置
     *
     * @param paymentMerchantDetailsDTO 支付商户渠道配置DTO
     */
    @Override
    public void editPaymentMerchant(MerchantDetailsDTO paymentMerchantDetailsDTO) {
        Handler<Void> handler = SpringUtils.getBean(MerchantHandler.class, paymentMerchantDetailsDTO.getPayType());
        handler.handle(paymentMerchantDetailsDTO);
    }

    /**
     * 获取商户支付配置
     *
     * @param payType 支付渠道
     * @return 商户支付配置
     */
    @Override
    public List<MerchantDetailsVO> getMerchantDetail(PayType payType) {
        List<MerchantDetailsVO> merchantDetails = paymentMerchantConfigService.getMerchantDetail(payType);

        if (CollUtil.isNotEmpty(merchantDetails)) {
            // 获取所有商户的门店信息
            for (MerchantDetailsVO vo : merchantDetails) {
                if (vo.getShopJson() != null && !vo.getShopJson().isEmpty()) {
                    List<Long> shopIds = JSONUtil.parseArray(vo.getShopJson()).toList(Long.class);
                    vo.setShopIds(shopIds);
                    ShopPageDTO queryDTO = new ShopPageDTO();
                    queryDTO.setPlatformId(ISystem.platformIdMust());
                    Set<Long> shopIdSet = new HashSet<>(shopIds);
                    queryDTO.setShopIds(shopIdSet);
                    IPage<QueryShopListVO> shopPage = shopService.pageShopList(queryDTO);
                    vo.setShops(shopPage.getRecords());
                }
            }
        }
        return merchantDetails;
    }

    /**
     * 支付证书上传
     *
     * @param file 证书文件
     */
    @Override
    public String uploadCertificate(MultipartFile file, String detailsId) {
        if (detailsId != null) {
            MerchantDetails merchantDetails = merchantDetailsService.getById(detailsId);
            if (merchantDetails == null) {
                throw PaymentError.PAYMENT_CERTIFICATE_UPLOAD_ERROR.exception();
            }
        }
        String originalFilename = file.getOriginalFilename();
        if (!WechatHelper.nameVerify(originalFilename)) {
            throw PaymentError.PAYMENT_CERTIFICATE_FORMAT_ERROR.exception();
        }
        String absolutePath = WechatHelper.absolutePath(ISystem.shopIdMust().toString(), originalFilename);
        log.debug("支付证书存放路径:{}", absolutePath);
        try {
            file.transferTo(new File(absolutePath));
        } catch (IOException exception) {
            log.error("保存文件发生错误", exception);
            throw PaymentError.PAYMENT_CERTIFICATE_UPLOAD_ERROR.exception();
        }
        return absolutePath;
    }

    /**
     * 获取门店分页列表，并设置门店的可选状态
     * 可选状态规则：
     * 1. 如果门店属于当前商户，则可选
     * 2. 如果门店未被其他商户使用，则可选
     * 3. 如果门店已被其他商户使用，则不可选
     *
     * @param queryPageDTO 查询参数
     * @return 门店分页列表
     */
    @Override
    public IPage<QueryShopListVO> getShopPageList(ShopPageDTO queryPageDTO) {
        log.info("开始获取门店列表，查询参数: {}", queryPageDTO);
        try {
            // 设置默认查询参数
            setDefaultQueryParams(queryPageDTO);

            // 获取门店列表
            IPage<QueryShopListVO> shopPage = shopService.pageShopList(queryPageDTO);
            if (shopPage == null || CollUtil.isEmpty(shopPage.getRecords())) {
                return shopPage;
            }

            // 处理门店可选状态
            processShopSelectableStatus(shopPage, queryPageDTO.getDetailsId());

            return shopPage;
        } catch (Exception e) {
            log.error("获取门店列表失败:", e);
            throw PaymentError.PAYMENT_CHANNEL_NOT_CONFIGURED.exception();
        }
    }

    /**
     * 设置默认查询参数
     *
     * @param queryPageDTO 查询参数
     */
    private void setDefaultQueryParams(ShopPageDTO queryPageDTO) {
        if (queryPageDTO.getPageSize() == null) {
            queryPageDTO.setPageSize(10);
        }
        if (queryPageDTO.getCurrentPage() == null) {
            queryPageDTO.setCurrentPage(1);
        }
        if (queryPageDTO.getPlatformId() == null) {
            queryPageDTO.setPlatformId(ISystem.platformIdMust());
        }
    }

    /**
     * 处理门店可选状态
     *
     * @param shopPage 门店分页数据
     * @param detailsId 当前商户ID
     */
    private void processShopSelectableStatus(IPage<QueryShopListVO> shopPage, String detailsId) {
        // 获取所有商户信息
        List<MerchantDetails> allMerchants = merchantDetailsService.list();
        if (CollUtil.isEmpty(allMerchants)) {
            // 如果没有商户，所有门店都可选
            shopPage.getRecords().forEach(shop -> shop.setSelectable(1));
            return;
        }

        // 获取当前商户的门店和其他商户的门店
        ShopIdCollector shopIdCollector = collectShopIds(allMerchants, detailsId);

        // 设置门店可选状态
        for (QueryShopListVO shop : shopPage.getRecords()) {
            boolean isSelectable = shopIdCollector.currentMerchantShopIds.contains(shop.getId()) ||
                    !shopIdCollector.otherMerchantShopIds.contains(shop.getId());
            shop.setSelectable(isSelectable ? 1 : 0);
        }
    }

    /**
     * 收集商户的门店ID
     */
    private static class ShopIdCollector {
        final Set<Long> currentMerchantShopIds = new HashSet<>();
        final Set<Long> otherMerchantShopIds = new HashSet<>();
    }

    /**
     * 收集当前商户和其他商户的门店ID
     *
     * @param allMerchants 所有商户列表
     * @param currentMerchantId 当前商户ID
     * @return 收集结果
     */
    private ShopIdCollector collectShopIds(List<MerchantDetails> allMerchants, String currentMerchantId) {
        ShopIdCollector collector = new ShopIdCollector();

        if (StringUtil.isNotBlank(currentMerchantId)) {
            // 查找当前商户并收集其门店ID
            allMerchants.stream()
                    .filter(m -> m.getDetailsId().equals(currentMerchantId))
                    .findFirst()
                    .ifPresent(merchant -> {
                        if (CharSequenceUtil.isNotEmpty(merchant.getShopJson())) {
                            collector.currentMerchantShopIds.addAll(
                                    JSONUtil.parseArray(merchant.getShopJson()).toList(Long.class)
                            );
                        }
                    });
            // 移除当前商户
            allMerchants.removeIf(m -> m.getDetailsId().equals(currentMerchantId));
        }

        // 收集其他商户的门店ID
        for (MerchantDetails merchant : allMerchants) {
            if (CharSequenceUtil.isNotEmpty(merchant.getShopJson())) {
                collector.otherMerchantShopIds.addAll(
                        JSONUtil.parseArray(merchant.getShopJson()).toList(Long.class)
                );
            }
        }

        return collector;
    }

    /**
     * 删除商户配置
     * 删除时会进行如下操作：
     * 1. 验证商户是否存在且不是默认商户
     * 2. 将被删除商户的门店转移到默认商户
     * 3. 删除商户配置和商户信息
     *
     * @param detailsId 商户配置ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMerchant(String detailsId) {
        log.info("开始删除商户配置，detailsId: {}", detailsId);

        // 1. 检查并获取要删除的商户
        MerchantDetails merchantToDelete = validateAndGetMerchant(detailsId);

        // 2. 转移门店到默认商户
        transferShopsToDefaultMerchant(merchantToDelete);

        try {
            // 3. 删除商户配置
            log.debug("删除商户配置，detailsId: {}", detailsId);
            paymentMerchantConfigService.removeByDetailsId(detailsId);

            // 4. 删除商户
            log.debug("删除商户信息，detailsId: {}", detailsId);
            merchantDetailsService.removeById(detailsId);

            log.info("商户删除成功，detailsId: {}", detailsId);
        } catch (Exception e) {
            log.error("删除商户失败，detailsId: {}", detailsId, e);
            throw PaymentError.MERCHANT_DELETE_ERROR.exception();
        }
    }

    /**
     * 验证并获取商户信息
     *
     * @param detailsId 商户ID
     * @return 商户信息
     */
    private MerchantDetails validateAndGetMerchant(String detailsId) {
        MerchantDetails merchantDetails = merchantDetailsService.getById(detailsId);
        if (merchantDetails == null) {
            log.warn("商户不存在，detailsId: {}", detailsId);
            throw PaymentError.MERCHANT_NOT_EXISTS.exception();
        }

        if (merchantDetails.getIsDefault() != null && merchantDetails.getIsDefault() == 1) {
            log.warn("尝试删除默认商户，detailsId: {}", detailsId);
            throw PaymentError.MERCHANT_DEFAULT_CANNOT_DELETE.exception();
        }

        return merchantDetails;
    }

    /**
     * 将要删除商户的门店转移到默认商户
     *
     * @param merchantToDelete 要删除的商户
     */
    private void transferShopsToDefaultMerchant(MerchantDetails merchantToDelete) {
        // 获取默认商户
        MerchantDetails defaultMerchant = merchantDetailsService.getDefaultMerchant(merchantToDelete.getPayType());
        if (defaultMerchant == null) {
            log.warn("默认商户不存在，payType: {}", merchantToDelete.getPayType());
            return;
        }

        // 如果要删除的商户没有门店，直接返回
        if (CharSequenceUtil.isEmpty(merchantToDelete.getShopJson())) {
            return;
        }

        // 合并门店ID
        Set<Long> mergedShopIds = new HashSet<>();

        // 添加默认商户现有的门店
        if (CharSequenceUtil.isNotEmpty(defaultMerchant.getShopJson())) {
            mergedShopIds.addAll(JSONUtil.parseArray(defaultMerchant.getShopJson()).toList(Long.class));
        }

        // 添加要删除商户的门店
        mergedShopIds.addAll(JSONUtil.parseArray(merchantToDelete.getShopJson()).toList(Long.class));

        // 更新默认商户的shopJson
        defaultMerchant.setShopJson(JSONUtil.parseArray(mergedShopIds).toString());
        merchantDetailsService.updateById(defaultMerchant);

        log.info("门店转移完成，从商户 {} 转移到默认商户 {}", merchantToDelete.getDetailsId(), defaultMerchant.getDetailsId());
    }

    @Override
    public Boolean hasRevenueTypeMerchant(Long platformId, String detailsId) {
        // 查询指定平台下revenueType为1的商户配置
        MerchantDetailsVO merchantDetail = Tenant.disable(() -> (merchantDetailsMapper.getMerchantDetailInfoByPlatform(
                ISystem.platformIdMust(),
                PayType.AGGREGATION,
                null,
                1)));
        if (merchantDetail == null) {
            return false;
        }
        // 如果没有提供detailsId，直接返回true
        if (CharSequenceUtil.isBlank(detailsId)) {
            return true;
        }
        // 如果提供了detailsId，且查询到的商户详情ID与提供的detailsId不匹配，则返回false
        return !merchantDetail.getDetailsId().equals(detailsId);
    }

    @Override
    public MerchantDetailsVO getAggregateMerchantDetail(String detailsId) {

        // 1. 查询商户配置
        List<MerchantDetailsVO> merchantDetails = paymentMerchantConfigService.getMerchantDetail(PayType.AGGREGATION);
        if (CollUtil.isEmpty(merchantDetails)) {
            return null;
        }

        // 2. 查找指定的商户详情
        MerchantDetailsVO targetDetail = merchantDetails.stream()
                .filter(detail -> detail.getDetailsId().equals(detailsId))
                .findFirst()
                .orElse(null);

        if (targetDetail == null) {
            return null;
        }

        // 3. 获取商户的门店信息
        if (targetDetail.getShopJson() != null && !targetDetail.getShopJson().isEmpty()) {
            List<Long> shopIds = JSONUtil.parseArray(targetDetail.getShopJson()).toList(Long.class);
            targetDetail.setShopIds(shopIds);
            ShopPageDTO queryDTO = new ShopPageDTO();
            queryDTO.setPlatformId(ISystem.platformIdMust());
            Set<Long> shopIdSet = new HashSet<>(shopIds);
            queryDTO.setShopIds(shopIdSet);
            IPage<QueryShopListVO> shopPage = shopService.pageShopList(queryDTO);
            targetDetail.setShops(shopPage.getRecords());
        }

        return targetDetail;
    }
}
