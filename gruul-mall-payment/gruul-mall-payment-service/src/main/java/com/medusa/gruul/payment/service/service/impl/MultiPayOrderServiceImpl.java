package com.medusa.gruul.payment.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.egzosn.pay.common.bean.PayMessage;
import com.egzosn.pay.common.bean.TransactionType;
import com.medusa.gruul.common.member.dto.MemberCardPayCallbackDTO;
import com.medusa.gruul.common.member.dto.MemberCardPayDTO;
import com.medusa.gruul.common.member.dto.settlement.AfterOrderDiscountCallbackQO;
import com.medusa.gruul.common.member.enums.OrderStateEnum;
import com.medusa.gruul.common.member.enums.OrderTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.member.service.SettlementApiService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.BalanceHistoryOperatorType;
import com.medusa.gruul.common.model.enums.ChangeType;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.model.message.DataChangeMessage;
import com.medusa.gruul.common.model.pay.Transaction;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.global.model.helper.AmountCalculateHelper;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.payment.api.entity.PaymentHistory;
import com.medusa.gruul.payment.api.entity.PaymentInfo;
import com.medusa.gruul.payment.api.entity.PaymentRecord;
import com.medusa.gruul.payment.api.enums.*;
import com.medusa.gruul.payment.api.model.dto.PayNotifyResultDTO;
import com.medusa.gruul.payment.api.model.dto.PaymentInfoDTO;
import com.medusa.gruul.payment.api.model.dto.agg.AggPayPreTradingReqDTO;
import com.medusa.gruul.payment.api.model.dto.agg.SaasAggPayDTO;
import com.medusa.gruul.payment.api.model.dto.agg.AggPayRespDTO;
import com.medusa.gruul.payment.service.common.helper.PayHelper;
import com.medusa.gruul.payment.service.common.model.OrderPaymentRecord;
import com.medusa.gruul.payment.service.common.model.PayRequestOrder;
import com.medusa.gruul.payment.service.model.enums.PaymentError;
import com.medusa.gruul.payment.service.model.vo.MerchantDetailsVO;
import com.medusa.gruul.payment.service.mp.service.*;
import com.medusa.gruul.payment.service.service.AggPayRedisService;
import com.medusa.gruul.payment.service.service.AggPayService;
import com.medusa.gruul.payment.service.service.MultiPayOrderService;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;
import org.apache.dubbo.config.annotation.DubboService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商户业务订单支付实现
 * </p>
 *
 * <AUTHOR>
 * Description MultiPayOrderServiceImpl.java
 * date 2022-07-25 16:01
 */
@Service
@Slf4j
@DubboService(interfaceClass = MultiPayOrderService.class)
@RequiredArgsConstructor
public class MultiPayOrderServiceImpl implements MultiPayOrderService {

    private final WeChatConfigInfoService weChatConfigInfoService;

    private final IPaymentMerchantConfigService paymentMerchantConfigService;

    private final IPaymentInfoService paymentInfoService;

    private final IPaymentRecordService paymentRecordService;

    private final RabbitTemplate rabbitTemplate;

    private final UserRpcService userRpcService;

    private final IPaymentHistoryService paymentHistoryService;

    private final MemberApiService memberApiService;

    private final SettlementApiService settlementApiService;

    private final OrderRpcService orderRpcService;

    @Lazy
    @Autowired
    private AggPayService aggPayService;

    private final UaaRpcService uaaRpcService;

    private final AggPayRedisService aggPayRedisService;

    public static final String AGG_PAY_WECHAT_CALLBACK = "https://s2mall.holderzone.cn/api/gruul-mall-order/gruul-mall-order/agg/callback";

    /**
     * 业务订单预处理
     *
     * @param paymentInfoDTO 支付信息DTO
     */
    @Override
    public PayRequestOrder pretreatmentOrder(PaymentInfoDTO paymentInfoDTO) {
        MerchantDetailsVO merchantDetail;
        //缓存聚合支付信息
        Systems system = ISystem.systemMust();
        paymentInfoDTO.setSystems(JSON.toJSONString(system));
        aggPayRedisService.putPayResp(paymentInfoDTO.getOrderNum(), paymentInfoDTO);
        if (paymentInfoDTO.getPayType() != PayType.BALANCE && paymentInfoDTO.getPayType() != PayType.MEMBER_CARD) {
            if (Boolean.TRUE.equals(paymentInfoDTO.getIsAggPay())) {
                int revenueType = getRevenueType(paymentInfoDTO.getSubject());
                paymentInfoDTO.setRevenueType(revenueType);
                MerchantDetailsVO merchantDetailsVO = paymentMerchantConfigService
                        .getMerchantDetailByPayTypeAndShopId(revenueType, PayType.AGGREGATION, paymentInfoDTO.getShopId().toString());
                if (merchantDetailsVO == null) {
                    throw PaymentError.PAYMENT_CHANNEL_NOT_CONFIGURED.dataEx(paymentInfoDTO.getPayType());
                }
                // 封装支付业务订单信息
                PayRequestOrder payRequestOrder = getPayRequestOrder(merchantDetailsVO, AGG_PAY_WECHAT_CALLBACK, paymentInfoDTO);
                payRequestOrder.setAppId(merchantDetailsVO.getAppid());
                return payRequestOrder;
            }
            merchantDetail = paymentMerchantConfigService.getMerchantDetailByPlatform(paymentInfoDTO.getPayType(), paymentInfoDTO.getPayPlatform());
            if (merchantDetail == null) {
                throw PaymentError.PAYMENT_CHANNEL_NOT_CONFIGURED.dataEx(paymentInfoDTO.getPayType());
            }
        } else {
            //余额支付可不配置支付渠道
            merchantDetail = new MerchantDetailsVO();
        }
        // 封装支付业务订单信息
        return getPayRequestOrder(merchantDetail, merchantDetail.getNotifyUrl(), paymentInfoDTO);
    }

    private static int getRevenueType(String subject) {
        int revenueType;
        //判断支付类型
        if (MemberRevenueTypeEnum.getByDesc(subject) != null) {
            revenueType = 1;
        } else {
            revenueType = 2;
        }
        log.debug("支付类型:{}", revenueType);
        return revenueType;
    }

    @NotNull
    private PayRequestOrder getPayRequestOrder(MerchantDetailsVO merchantDetailsVO,
                                               String aggPayWechatCallback,
                                               PaymentInfoDTO paymentInfoDTO) {
        PayRequestOrder payOrder = this.createOrder(merchantDetailsVO, aggPayWechatCallback, paymentInfoDTO);
        log.debug("业务订单信息 ".concat(payOrder.toString()));
        //初始化支付信息记录
        this.init(payOrder, paymentInfoDTO);
        return payOrder;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void success(PayMessage payMessage) {
        log.debug("进入支付成功处理-{}", payMessage);
        String orderNo = payMessage.getOutTradeNo();
        PaymentInfoDTO paymentInfoDTO = aggPayRedisService.getPayResp(orderNo);
        log.info("success获取支付信息缓存:{}", JSON.toJSONString(paymentInfoDTO));
        checkSystems(paymentInfoDTO);
        PaymentInfo paymentInfo =
                Tenant.disable(() -> paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum
                        , orderNo).one());
        if (paymentInfo == null) {
            log.debug("未查询到payment记录:orderNo:{}", orderNo);
            throw PaymentError.NOT_FOUND_PAYMENT_RECORD.dataEx(orderNo);
        }
        //业务已处理 结束后续操作
        if (paymentInfo.getBusinessNotifyStatus().equals(NotifyStatus.ACCOMPLISH) &&
                paymentInfo.getThirdPartyNotifyStatus().equals(NotifyStatus.ACCOMPLISH)) {
            return;
        }
        //设置回调参数
        PaymentRecord record =
                Tenant.disable(() -> paymentRecordService.lambdaQuery().eq(PaymentRecord::getPaymentId, paymentInfo.getId()).one());
        record.setNotifyParam(JSON.toJSONString(payMessage));
        Tenant.disable(() -> paymentRecordService.updateById(record));
        //设置交易成功
        if (paymentInfo.getPayType() == PayType.WECHAT) {
            paymentInfo.setTransactionId(payMessage.getPayMessage().get("transaction_id").toString());
        } else {
            paymentInfo.setTransactionId(payMessage.getPayMessage().get("notify_id").toString());
        }
        paymentInfo.setThirdPartyNotifyNumber(paymentInfo.getThirdPartyNotifyNumber() + 1);
        paymentInfo.setThirdPartyNotifyStatus(NotifyStatus.ACCOMPLISH);
        paymentInfo.setTradeStatus(TradeStatus.SUCCESS_PAYMENT);
        paymentInfo.setBusinessNotifyStatus(NotifyStatus.ACCOMPLISH);
        Tenant.disable(() -> paymentInfoService.updateById(paymentInfo));
        if (StringUtil.isBlank(paymentInfo.getExchange())) {
            return;
        }
        log.debug("发送mq消息.....:{}-{}", paymentInfo.getExchange(), paymentInfo.getRouteKey());
        //支付成功回调
        rabbitTemplate.convertAndSend(paymentInfo.getExchange(), paymentInfo.getRouteKey(),
                new PayNotifyResultDTO()
                        .setPayOrderType(PayOrderType.COMMON)
                        .setOutTradeNo(paymentInfo.getOrderNum())
                        .setBusinessParams(JSON.parseObject(paymentInfo.getBusinessParams(), PaymentInfoDTO.class))
                        .setShopIdTransactionMap(Map.of(
                                0L, new Transaction()
                                        .setProfitSharing(false)
                                        .setTransactionId(paymentInfo.getTransactionId())
                        ))
        );
        //同步会员消费记录
        if (Objects.nonNull(paymentInfoDTO) && paymentInfoDTO.getPayType() != PayType.MEMBER_CARD) {
            List<MemberCardPayDTO> buildMemberCardPayDTOList = buildMemberCardPayDTO(paymentInfoDTO);
            Boolean isSuccess = memberApiService.memberCardPayRecordRequest(buildMemberCardPayDTOList);
            log.info("同步会员消费记录结果:{}", isSuccess);
        }
        sendAfterOrderDiscountCallback(paymentInfoDTO);
        aggPayRedisService.deletePayResp(orderNo);
    }

    private void sendAfterOrderDiscountCallback(PaymentInfoDTO paymentInfoDTO) {
        List<MemberCardPayDTO> buildMemberCardPayDTOList = buildMemberCardPayDTO(paymentInfoDTO);
        //结算台支付回调
        try {
            for (MemberCardPayDTO memberCardPayDTO : buildMemberCardPayDTOList) {
                AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO = new AfterOrderDiscountCallbackQO();
                afterOrderDiscountCallbackQO.setMemberGuid(String.valueOf(paymentInfoDTO.getUserId()));
                afterOrderDiscountCallbackQO.setOrderActuallyFee(memberCardPayDTO.getCardBalancePayAmount());
                afterOrderDiscountCallbackQO.setOrderDiscountFee(memberCardPayDTO.getOrderDiscountAmount());
                afterOrderDiscountCallbackQO.setOrderFee(memberCardPayDTO.getOrderAmount());
                afterOrderDiscountCallbackQO.setOrderGuid(memberCardPayDTO.getOrderNumber());
                afterOrderDiscountCallbackQO.setOrderNo(memberCardPayDTO.getOrderNumber());
                afterOrderDiscountCallbackQO.setOrderTime(memberCardPayDTO.getOrderTime());
                afterOrderDiscountCallbackQO.setOrderSource(Platform.convertSourceType(ISystem.platformOpt().get()));
                afterOrderDiscountCallbackQO.setOrderType(memberCardPayDTO.getOrderType());
                afterOrderDiscountCallbackQO.setStoreName(memberCardPayDTO.getStoreName());
                afterOrderDiscountCallbackQO.setOrderState(OrderStateEnum.FINISH.getCode());
                settlementApiService.afterOrderDiscountCallback(afterOrderDiscountCallbackQO);
            }
        } catch (Exception e) {
            log.info("结算台支付回调异常:{}", e.getMessage());
        }
    }

    private static void checkSystems(PaymentInfoDTO paymentInfoDTO) {
        try {
            Systems systems = ISystem.systemOpt().getOrNull();
            log.info("获取系统信息:{}", JSON.toJSONString(systems));
            if (Objects.nonNull(paymentInfoDTO) &&
                    (Objects.isNull(systems) || Objects.isNull(systems.getPlatformId()))) {
                Systems parseSystems = JSON.parseObject(paymentInfoDTO.getSystems(), Systems.class);
                SystemContextHolder.set(parseSystems);
                log.info("Systems:{}", JSON.toJSONString(ISystem.systemOpt()));
            }
        } catch (Exception e) {
            log.error("同步会员消费记录失败:{}", e.getMessage());
        }
    }

    /**
     * 获取支付记录
     *
     * @param orderNum 内部业务订单号
     * @return OrderPaymentRecord.java
     */
    @Override
    public OrderPaymentRecord paymentRecord(String orderNum) {
        PaymentInfo payment = Tenant.disable(() -> paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum, orderNum).one());
        if (payment == null) {
            throw PaymentError.NOT_FOUND_PAYMENT_RECORD.exception();
        }
        PaymentRecord record =
                Tenant.disable(() -> paymentRecordService.lambdaQuery().eq(PaymentRecord::getPaymentId, payment.getId()).one());
        String sendParam;
        if (record == null || StrUtil.isBlank(sendParam = record.getSendParam())) {
            throw PaymentError.NOT_FOUND_PAYMENT_RECORD.exception();
        }
        OrderPaymentRecord paymentRecord = JSON.parseObject(sendParam, OrderPaymentRecord.class);
        paymentRecord.setNotifyParam(record.getNotifyParam());
        log.debug("查询支付记录:{}", paymentRecord);
        paymentRecord.setPayType(payment.getPayType());
        return paymentRecord;
    }

    /**
     * 根据业务单号 获取业务订单处理状态
     *
     * @param outTradeNo 业务订单号
     * @return NotifyStatus.java
     */
    @Override
    public NotifyStatus orderPayStatus(String outTradeNo) {
        PaymentInfo paymentInfo =
                Tenant.disable(() -> paymentInfoService.lambdaQuery().select(PaymentInfo::getBusinessNotifyStatus).eq(PaymentInfo::getOrderNum, outTradeNo).one());
        if (paymentInfo == null) {
            return NotifyStatus.ACCOMPLISH;
        }
        return paymentInfo.getBusinessNotifyStatus();
    }


    /**
     * 用户进行余额支付
     *
     * @param paymentInfoDTO 支付信息DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void balancePay(PaymentInfoDTO paymentInfoDTO) {
        DataChangeMessage message = wrapDataChangeMessage(paymentInfoDTO);
        Boolean flag = userRpcService.userBalancePayment(message);
        PaymentError.USER_BALANCE_NOT_ENOUGH.falseThrow(flag);
        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.SHOPPING_PURCHASE);
        paymentHistory.setUserId(paymentInfoDTO.getUserId());
        paymentHistory.setSubject(paymentInfoDTO.getSubject());
        paymentHistory.setMoney(paymentInfoDTO.getTotalFee());
        paymentHistory.setChangeType(ChangeType.REDUCE);
        paymentHistory.setPayType(PayType.BALANCE);
        paymentHistory.setPlatformId(paymentInfoDTO.getPlatformId());
        paymentHistory.setOrderGuid(paymentInfoDTO.getOrderNum());
        paymentHistoryService.save(paymentHistory);

        //封装回调数据
        Map<String, Object> payMessageInfo = new HashMap<>(CommonPool.NUMBER_FIVE);
        payMessageInfo.put("subject", paymentInfoDTO.getSubject());
        payMessageInfo.put("total_amount", paymentInfoDTO.getTotalFee());
        payMessageInfo.put("out_trade_no", paymentInfoDTO.getOrderNum());
        payMessageInfo.put("notify_id", "16".concat(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(new PaymentInfo()).toString()));
        PayMessage payMessage = new PayMessage(payMessageInfo);
        this.success(payMessage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberCardPay(PaymentInfoDTO paymentInfoDTO) {
        if (StrUtil.isEmpty(paymentInfoDTO.getMemberInfoCardGuid())) {
            throw PaymentError.MEMBER_CARD_PAY_ERROR.exception();
        }

        //会员卡支付
        List<MemberCardPayDTO> memberCardPayDTO = buildMemberCardPayDTO(paymentInfoDTO);
        Boolean flag = memberApiService.memberCardPay(memberCardPayDTO);
        PaymentError.MEMBER_CARD_PAY_ERROR.falseThrow(flag);

        //会员卡支付回调
        List<MemberCardPayCallbackDTO> memberCardPayCallbackDTO = memberCardPayDTO.stream().map(dto -> new MemberCardPayCallbackDTO()
                .setOrderNumber(dto.getOrderNumber())).collect(Collectors.toList());
        memberApiService.memberCardPayCallback(memberCardPayCallbackDTO);

        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.SHOPPING_PURCHASE);
        paymentHistory.setUserId(paymentInfoDTO.getUserId());
        paymentHistory.setSubject(paymentInfoDTO.getSubject());
        paymentHistory.setMoney(paymentInfoDTO.getTotalFee());
        paymentHistory.setChangeType(ChangeType.REDUCE);
        paymentHistory.setPayType(PayType.MEMBER_CARD);
        paymentHistory.setOrderGuid(paymentInfoDTO.getOrderNum());
        paymentHistoryService.save(paymentHistory);

        //封装回调数据
        Map<String, Object> payMessageInfo = new HashMap<>(CommonPool.NUMBER_FIVE);
        payMessageInfo.put("subject", paymentInfoDTO.getSubject());
        payMessageInfo.put("total_amount", paymentInfoDTO.getTotalFee());
        payMessageInfo.put("out_trade_no", paymentInfoDTO.getOrderNum());
        payMessageInfo.put("notify_id", "16".concat(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(new PaymentInfo()).toString()));
        PayMessage payMessage = new PayMessage(payMessageInfo);
        this.success(payMessage);
    }

    /**
     * 构建会员卡支付参数
     *
     * @param paymentInfoDTO 支付信息
     * @return 支付参数
     */
    private List<MemberCardPayDTO> buildMemberCardPayDTO(PaymentInfoDTO paymentInfoDTO) {
        // 确定最终订单类型
        final Integer orderType = Optional.ofNullable(paymentInfoDTO.getOrderType())
                .orElseGet(() -> Optional.ofNullable(orderRpcService.getOrder(paymentInfoDTO.getOrderNum()))
                        .map(order -> getOrderType(order.getDistributionMode()))
                        .orElseThrow(PaymentError.MEMBER_CARD_PAY_ERROR::exception));

        if (orderType == OrderTypeEnum.MALL_ORDER_PAID_MEMBER.getCode()) {
            // 冲会员
            return Collections.singletonList(new MemberCardPayDTO()
                    .setMemberInfoGuid(String.valueOf(paymentInfoDTO.getUserId()))
                    .setMemberInfoCardGuid(paymentInfoDTO.getMemberInfoCardGuid())
                    .setOrderNumber(paymentInfoDTO.getOrderNum())
                    .setOrderType(orderType)
                    .setPayType(paymentInfoDTO.getPayType().toString())
                    .setOrderTime(LocalDateTime.now())
                    .setOrderAmount(AmountCalculateHelper.toYuan(paymentInfoDTO.getTotalFee()))
                    .setOrderDiscountAmount(new BigDecimal("0"))
                    .setCardBalancePayAmount(AmountCalculateHelper.toYuan((paymentInfoDTO.getTotalFee()))));
        } else {
            if (orderType == OrderTypeEnum.MALL_ORDER_INTEGRAL_EXCHANGE.getCode()) {
                log.debug("积分商城订单同步，订单号：{}", paymentInfoDTO.getOrderNum());
                return Collections.singletonList(new MemberCardPayDTO()
                        .setMemberInfoGuid(String.valueOf(paymentInfoDTO.getUserId()))
                        .setMemberInfoCardGuid(paymentInfoDTO.getMemberInfoCardGuid())
                        .setStoreName("积分商城")
                        .setOrderNumber(paymentInfoDTO.getOrderNum())
                        .setOrderType(orderType)
                        .setPayType(paymentInfoDTO.getPayType().toString())
                        .setOrderTime(LocalDateTime.now())
                        .setOrderAmount(AmountCalculateHelper.toYuan(paymentInfoDTO.getTotalFee()))
                        .setOrderDiscountAmount(new BigDecimal("0"))
                        .setCardBalancePayAmount(AmountCalculateHelper.toYuan((paymentInfoDTO.getTotalFee()))));
            }

            // 获取并验证店铺订单
            List<ShopOrder> shopOrders = orderRpcService.getShopOrderList(paymentInfoDTO.getOrderNum());
            if (CollUtil.isEmpty(shopOrders)) {
                throw PaymentError.MEMBER_CARD_PAY_ERROR.exception();
            }

            // 构建DTO列表
            return shopOrders.stream().map(shopOrder -> {
                long total = shopOrder.getTotalAmount();
                Long freightAmount = shopOrder.getFreightAmount();
                long discount = shopOrder.getDiscountAmount();
                log.info("订单号：{}，总金额：{}，运费：{}，优惠金额：{}", shopOrder.getNo(), total, freightAmount, discount);
                total = freightAmount != null ? total + freightAmount : total;
                return new MemberCardPayDTO()
                        .setMemberInfoGuid(String.valueOf(paymentInfoDTO.getUserId()))
                        .setMemberInfoCardGuid(paymentInfoDTO.getMemberInfoCardGuid())
                        .setStoreGuid(String.valueOf(shopOrder.getShopId()))
                        .setStoreName(shopOrder.getShopName())
                        .setOrderNumber(shopOrder.getNo())
                        .setOrderType(orderType)
                        .setPayType(paymentInfoDTO.getPayType().toString())
                        .setOrderTime(LocalDateTime.now())
                        .setOrderAmount(AmountCalculateHelper.toYuan(total))
                        .setOrderDiscountAmount(AmountCalculateHelper.toYuan(discount))
                        .setCardBalancePayAmount(AmountCalculateHelper.toYuan(total - discount));
            }).collect(Collectors.toList());
        }
    }

    /**
     * 获取订单类型
     *
     * @param distributionMode 配送方式
     * @return 订单类型
     */
    private int getOrderType(DistributionMode distributionMode) {
        if (distributionMode == null) {
            return OrderTypeEnum.MALL_ORDER_VIRTUAL.getCode();
        }
        return switch (distributionMode) {
            case EXPRESS -> OrderTypeEnum.MALL_ORDER_EXPRESS.getCode();
            case INTRA_CITY_DISTRIBUTION -> OrderTypeEnum.MALL_ORDER_INTRA_CITY_DISTRIBUTION.getCode();
            case SHOP_STORE -> OrderTypeEnum.MALL_ORDER_SHOP_STORE.getCode();
            default -> OrderTypeEnum.MALL_ORDER_VIRTUAL.getCode();
        };
    }

    @NotNull
    private static DataChangeMessage wrapDataChangeMessage(PaymentInfoDTO paymentInfoDTO) {
        DataChangeMessage message = new DataChangeMessage();
        message.setValue(paymentInfoDTO.getTotalFee());
        message.setUserId(paymentInfoDTO.getUserId());
        message.setPlatformId(paymentInfoDTO.getPlatformId());
        message.setChangeType(ChangeType.REDUCE);
        message.setOperatorType(BalanceHistoryOperatorType.SHOPPING_CONSUMPTION);
        message.setOrderNo(paymentInfoDTO.getOrderNum());
        message.setOperatorUserId(paymentInfoDTO.getUserId());
        message.setSubject(paymentInfoDTO.getSubject());
        return message;
    }


    /**
     * 初始化支付记录
     *
     * @param payOrder       支付业务订单信息
     * @param paymentInfoDTO 支付信息DTO
     */
    private void init(PayRequestOrder payOrder, PaymentInfoDTO paymentInfoDTO) {
        //封装业务支付信息
        PaymentInfo paymentInfo = this.savePaymentInfo(payOrder, paymentInfoDTO);
        this.newPaymentRecord(paymentInfo.getId(), payOrder);

    }

    /**
     * 初始化支付记录表
     *
     * @param paymentId 支付信息id
     * @param payOrder  paymentInfo
     */
    private void newPaymentRecord(Long paymentId, PayRequestOrder payOrder) {
        Tenant.disable(() -> paymentRecordService.lambdaUpdate().eq(PaymentRecord::getPaymentId,
                paymentId).remove());
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setPaymentId(paymentId);
        paymentRecord.setSendParam(JSON.toJSONString(payOrder));
        paymentRecord.setRequestParams(JSON.toJSONString(payOrder));
        paymentRecordService.save(paymentRecord);
    }


    /**
     * 保存业务支付信息
     *
     * @param payOrder       支付业务订单信息
     * @param paymentInfoDTO 支付信息DTO
     */
    private PaymentInfo savePaymentInfo(PayRequestOrder payOrder, PaymentInfoDTO paymentInfoDTO) {
        PaymentInfo paymentInfo = new PaymentInfo();
        Tenant.disable(() -> paymentInfoService.lambdaUpdate().eq(PaymentInfo::getOrderNum, payOrder.getOrderId()).remove());
        paymentInfo
                .setOrderNum(payOrder.getOrderId())
                .setPayType(Boolean.TRUE.equals(paymentInfoDTO.getIsAggPay())
                        ? PayType.AGGREGATION : payOrder.getPayType())
                .setFeeType(FeeType.CNY)
                .setTotalFee(paymentInfoDTO.getTotalFee())
                .setBody(payOrder.getBody())
                .setBusinessParams(payOrder.getAddition())
                .setTerminalIp(payOrder.getSpbillCreateIp())
                .setBusinessNotifyUrl(payOrder.getNotifyUrl())
                .setRouteKey(payOrder.getRouteKey())
                .setExchange(paymentInfoDTO.getExchange())
                .setBusinessNotifyUrl(payOrder.getNotifyUrl())
                .setFeeType(FeeType.valueOf(payOrder.getCurType().getType()))
                .setThirdPartyNotifyStatus(NotifyStatus.UNFINISHED)
                .setThirdPartyNotifyNumber(CommonPool.NUMBER_ZERO)
                .setBusinessNotifyStatus(NotifyStatus.UNFINISHED)
                .setTradeStatus(TradeStatus.PENDING_PAYMENT)
                .setShopId(payOrder.getShopId())
                .setOpenId(payOrder.getOpenid())
                .setSubject(payOrder.getSubject())
                .setUserId(payOrder.getUserId())
                .setSeconds(payOrder.getExpirationTime().toString())
                .setMchId(payOrder.getMchId())
                .setKeyPrivate(payOrder.getKeyPrivate())
                .setRevenueType(paymentInfoDTO.getRevenueType())
                .setBusinessParams(JSON.toJSONString(paymentInfoDTO));
        paymentInfoService.save(paymentInfo);
        return paymentInfo;
    }


    /**
     * 封装支付订单信息
     *
     * @param merchantDetailsVO      列表id
     * @param notifyUrl      回调地址
     * @param paymentInfoDTO 支付信息DTO
     */
    private PayRequestOrder createOrder(MerchantDetailsVO merchantDetailsVO,
                                        String notifyUrl,
                                        PaymentInfoDTO paymentInfoDTO) {
        //获取交易类型
        Platform platform = paymentInfoDTO.getPayPlatform();
        PayType payType = paymentInfoDTO.getPayType();
        TransactionType transactionType = PayHelper.wayType(platform, payType);

        //渲染支付订单数据
        PayRequestOrder payOrder = new PayRequestOrder();

        payOrder.setOrderId(paymentInfoDTO.getOrderNum());
        payOrder.setShopId(paymentInfoDTO.getShopId());
        payOrder.setPayType(paymentInfoDTO.getPayType());
        payOrder.setTradeType(PayHelper.tradeType(platform));
        payOrder.setRouteKey(paymentInfoDTO.getRouteKey());
        payOrder.setSubject(paymentInfoDTO.getSubject());
        payOrder.setOutTradeNo(paymentInfoDTO.getOrderNum());
        payOrder.setDetailsId(merchantDetailsVO.getDetailsId());
        payOrder.setNotifyUrl(notifyUrl);
        payOrder.setWayTrade(transactionType.getType());
        payOrder.setTransactionType(transactionType);
        payOrder.setSubject(paymentInfoDTO.getSubject());
        payOrder.setBody(paymentInfoDTO.getBody());
        //支付宝（元 ） 微信（分） 余额（豪） 单位转换 bigDecimal 元 保留两位小数
        payOrder.setPrice(PayHelper.getPrice(paymentInfoDTO.getTotalFee(), payType));
        payOrder.setCurType(PayHelper.curType(paymentInfoDTO.getFeeType()));
        //订单过期时间
        payOrder.setExpirationTime(new Date(System.currentTimeMillis() + (paymentInfoDTO.getSeconds() + 20) * 1000));
        payOrder.setWapName(paymentInfoDTO.getSubject());
        payOrder.setAddition(paymentInfoDTO.getAttach());
        //终端ip
        payOrder.setSpbillCreateIp(paymentInfoDTO.getTerminalIp());
        payOrder.setOpenid(paymentInfoDTO.getOpenId());
        payOrder.setUserId(paymentInfoDTO.getUserId());
        payOrder.setAuthCode(paymentInfoDTO.getAuthCode());
        payOrder.setWapName(paymentInfoDTO.getWapName());
        payOrder.setWapUrl(paymentInfoDTO.getWapUrl());
        payOrder.setAppId(merchantDetailsVO.getAppid());
        payOrder.setMchId(merchantDetailsVO.getMchId());
        payOrder.setKeyPrivate(merchantDetailsVO.getKeyPrivate());
        payOrder.setRevenueType(paymentInfoDTO.getRevenueType());
        return payOrder;
    }

    private static final String SUCCESS_CODE = "10000";

    private static final String ORDER_REPEAT_SUBMIT = "订单重复提交";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AggPayRespDTO aggregationPay(PaymentInfoDTO paymentInfoDTO, PayRequestOrder payRequestOrder) {
        final BigDecimal divisor = CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND;
        final RoundingMode roundingMode = RoundingMode.HALF_EVEN;
        log.info("开始处理聚合支付，订单号：{}", payRequestOrder.getOutTradeNo());
        String orderNum = paymentInfoDTO.getOrderNum();
        try {
            UserInfoVO userInfoVO = uaaRpcService.getUserDataByUserId(paymentInfoDTO.getUserId()).get();
            log.info("获取用户信息：{}", JSON.toJSONString(userInfoVO));
            if (StringUtils.isEmpty(userInfoVO.getOpenid())) {
                log.warn("用户OpenID为空，尝试从支付信息中获取");
                userInfoVO.setOpenid(paymentInfoDTO.getOpenId());
            }

            // 构建聚合支付预交易请求DTO
            // 构建SAAS聚合支付DTO
            SaasAggPayDTO aggPayDTO = getSaasAggPayDTO(paymentInfoDTO, payRequestOrder, orderNum, divisor, roundingMode, userInfoVO);
            // 调用聚合支付服务
            AggPayRespDTO respDTO = aggPayService.prePay(aggPayDTO).block();
            log.info("聚合支付预下单结果：{}", JSON.toJSONString(respDTO));
            if (!Objects.equals(respDTO.getCode(), SUCCESS_CODE)) {
                log.error("聚合支付预下单失败，订单号：{}，错误信息：{}", payRequestOrder.getOutTradeNo(), respDTO.getMsg());
                if (respDTO.getMsg().equals(ORDER_REPEAT_SUBMIT)) {
                    return null;
                } else {
                    throw PaymentError.AGGREGATE_PAYMENT_ERROR.dataEx(respDTO.getMsg());
                }
            }
            return respDTO;
        } catch (Exception e) {
            log.error("聚合支付处理异常，订单号：{}，错误：{}",
                    payRequestOrder.getOutTradeNo(), e.getMessage());
            throw PaymentError.AGGREGATE_PAYMENT_ERROR.exception();
        }
    }


    private static SaasAggPayDTO getSaasAggPayDTO(PaymentInfoDTO paymentInfoDTO,
                                                  PayRequestOrder payRequestOrder,
                                                  String orderNum,
                                                  BigDecimal divisor,
                                                  RoundingMode roundingMode,
                                                  UserInfoVO userInfoVO) {
        SaasAggPayDTO aggPayDTO = new SaasAggPayDTO();
        aggPayDTO.setIsLast(true);
        AggPayPreTradingReqDTO aggWeChatPayDTO = new AggPayPreTradingReqDTO();
        aggWeChatPayDTO.setAttachData(payRequestOrder.getAddition());
        aggWeChatPayDTO.setStoreName(paymentInfoDTO.getShopName());
        aggWeChatPayDTO.setGoodsName(paymentInfoDTO.getOrderNum());
        aggWeChatPayDTO.setBody(paymentInfoDTO.getOrderNum());
        aggWeChatPayDTO.setPayPowerId(PayPowerIdEnum.WX_MINI_PROGRAM_ONLINE.getId());
        aggWeChatPayDTO.setTerminalId(orderNum);
        aggWeChatPayDTO.setAmount(BigDecimal.valueOf(paymentInfoDTO.getTotalFee()).divide(divisor, 2, roundingMode));
        aggWeChatPayDTO.setOrderGUID(orderNum);
        aggWeChatPayDTO.setPayGUID(orderNum);

        aggWeChatPayDTO.setSubAppId(payRequestOrder.getAppId());
        aggWeChatPayDTO.setSubOpenId(userInfoVO.getOpenid());
        aggWeChatPayDTO.setOutNotifyUrl(AGG_PAY_WECHAT_CALLBACK);

        aggWeChatPayDTO.setClientIp(paymentInfoDTO.getTerminalIp());
        aggPayDTO.setUserGuid(paymentInfoDTO.getUserId().toString());
        aggPayDTO.setUserName(userInfoVO.getUsername());
        aggPayDTO.setEnterpriseGuid(ISystem.enterpriseGuidMust().toString());
        aggPayDTO.setDevice_id(ISystem.deviceIdMust());
        aggPayDTO.setOperSubjectGuid(ISystem.platformIdMust().toString());
        aggPayDTO.setEnterpriseName(paymentInfoDTO.getShopName());
        aggPayDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT_MINI.getCode());
        aggPayDTO.setIsLast(true);
        aggPayDTO.setSaasCallBackUrl(payRequestOrder.getNotifyUrl());
        aggPayDTO.setStoreGuid(String.valueOf(paymentInfoDTO.getShopId()));
        aggPayDTO.setReqDTO(aggWeChatPayDTO);
        aggPayDTO.setRevenueType(payRequestOrder.getRevenueType());
        return aggPayDTO;
    }
}
