package com.medusa.gruul.payment.service.config;

/**
 * 聚合支付常量
 */
public interface PayConstant {

    String REGEX = ":";

    String SUCCESS_CODE = "10000";

    /**
     * 工行免密支付成功返回
     */
    String ICBC_SUCCESS_CODE = "10013";

    String ENTERPRISE_GUID_KEY = "enterpriseGuid";

    String COMPANY_ID_KEY = "companyId";

    String PLATFORM_EXCEPTION = "-10000";

    String HANDLER_PAY = "handler_pay";

    String AGG_PAY_POLLING = "agg_pay_polling";

    String REFUND_RESP = "refund_resp";

    /**
     * callBack和pulling都可以标记为正确的结果 防止重复回调  加上此参数
     */
    String AGG_PAY_CALLBACK = "agg_pay_callback";

    String PLACE_AN_ORDER_TO_CHECK_THE_BANK = "下单查询银行";

    String AGGREGATE_PAYMENT_PLATFORM_ABNORMAL = "聚合支付平台异常";

    String PLACE_ORDER_QUERY_BANK_REQUEST = "向聚合支付平台发起下单查询银行请求，请求参数：{}";

    /**
     * 轮询超时时间
     */
    int POLLING_RESP_EXPIRE_TIME = 10;

    int STEP_TIME = 50;

    /**
     * 默认的时间间隔 ms
     */
    long DEFAULT_INTERVAL_TIME = 15 * 60 * 1000L;

    /**
     * 默认的时间间隔 ms
     */
    long DEFAULT_INTERVAL_TIME_FIVE = 5 * 60 * 1000L;

}
