package com.medusa.gruul.payment.service.model.vo;

import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.shop.api.model.vo.QueryShopListVO;
import lombok.Data;

import java.util.List;

/**
 * 支付商户渠道配置VO
 *
 * <AUTHOR>
 * @ Description 支付商户渠道配置VO
 * @date 2022-07-20 17:04
 */
@Data
public class MerchantDetailsVO {

	/**
	 * 列表id
	 */
	private String detailsId;

	/**
	 * 支付渠道
	 */
	private PayType payType;

	/**
	 * 应用appid
	 */
	private String appid;

	/**
	 * 应用私钥
	 */
	private String keyPrivate;

	/**
	 * 支付宝公钥
	 */
	private String keyPublic;
	/**
	 * 商户号
	 */
	private String mchId;
	/**
	 * 支付证书
	 */
	private String keyCert;

	/**
	 * 回调url
	 */
	private String notifyUrl;

	/**
	 * 主体名称
	 */
	private String subjectName;

	private Long platformId;


	/**
	 *
	 */
	private List<Platform> platforms;

	/**
	 * 是否为默认支付方式 1-是 0-否
	 */
	private Integer isDefault;

	/**
	 * 收款类型列表 1:会员营收、2:门店营收
	 */
	private List<Integer> revenueType;

	/**
	 * 是否开启 0-关闭 1-开启
	 */
	private Integer isOpen;

	/**
	 * 门店id列表，JSON字符串格式
	 */
	private String shopJson;

	/**
	 * 门店id列表
	 */
	private List<Long> shopIds;

	/**
	 * 门店列表
	 */
	private List<QueryShopListVO> shops;

}
