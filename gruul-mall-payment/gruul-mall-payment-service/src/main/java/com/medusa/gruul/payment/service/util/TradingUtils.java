package com.medusa.gruul.payment.service.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 聚合支付平台验券
 */
@Slf4j
@Component
public final class TradingUtils {

    private TradingUtils() {

    }

    private static final String KEY_DEVELOPER_KEY = "developerKey";

    private static final String KEY_APP_SECRET = "appSecret";

    private static final String KEY_SIGNATURE = "signature";


    public static Map<String, Object> beanToMapWithoutSignature(Object obj) {
        Map<String, Object> map = bean2map(obj);
        map.remove(KEY_SIGNATURE);
        return map;
    }

    public static String getSignatureForCheck(Object obj, String developerKey, String appSecret) {
        return getSignature(beanToMapWithoutSignature(obj), developerKey, appSecret);
    }

    public static String getSignature(Object obj, String developerKey, String appSecret) {
        return getSignature(bean2map(obj), developerKey, appSecret);
    }

    public static Map<String, Object> bean2map(Object bean) {
        Map<String, Object> map = new TreeMap<>();
        BeanInfo beanInfo;

        try {
            beanInfo = Introspector.getBeanInfo(bean.getClass());
            PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
            PropertyDescriptor[] var4 = pds;
            int var5 = pds.length;

            for(int var6 = 0; var6 < var5; ++var6) {
                PropertyDescriptor property = var4[var6];
                String key = property.getName();
                if (key.compareToIgnoreCase("class") != 0) {
                    Method getter = property.getReadMethod();
                    Object value = null;

                    try {
                        value = getter != null ? getter.invoke(bean) : null;
                    } catch (InvocationTargetException | IllegalAccessException var12) {
                        var12.printStackTrace();
                    }

                    map.put(key, value);
                }
            }

            return map;
        } catch (IntrospectionException var13) {
            var13.printStackTrace();
            return Collections.emptyMap();
        }
    }

    public static String getSignature(Map<String, Object> map, String developerKey, String appSecret) {
        // 字典排序map
        Map<String, Object> filteredSortedMap = map.entrySet().stream()
                .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (u, v) -> {
                            throw new IllegalStateException(String.format("Duplicate key %s", u));
                        }, TreeMap::new)
                );
        // 移除signature字段，
        filteredSortedMap.remove(KEY_SIGNATURE);
        // 参数拼接
        String joinedParam = filteredSortedMap.entrySet().stream()
                .map(stringObjectEntry -> String.format("%s=%s", stringObjectEntry.getKey(), stringObjectEntry.getValue()))
                .collect(Collectors.joining("&"));

        // appSecret拼接
        String joinedParamWithKey = String.format("%s&%s=%s&%s=%s", joinedParam, KEY_DEVELOPER_KEY, developerKey, KEY_APP_SECRET, appSecret);
        log.info("拼接参数: {}", JSON.toJSONString(joinedParamWithKey));
        // 使用 1.13 版本的 DigestUtils
        return DigestUtils.sha1Hex(joinedParamWithKey.trim().getBytes());
    }

    private static boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }
}
