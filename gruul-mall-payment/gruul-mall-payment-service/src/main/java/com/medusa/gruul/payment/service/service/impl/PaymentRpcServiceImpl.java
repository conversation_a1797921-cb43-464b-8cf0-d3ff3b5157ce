package com.medusa.gruul.payment.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.mp.model.Tenant;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.redis.util.RedisUtil;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.web.handler.Handler;
import com.medusa.gruul.common.web.util.SpringUtils;
import com.medusa.gruul.payment.api.entity.PaymentInfo;
import com.medusa.gruul.payment.api.entity.PaymentRefund;
import com.medusa.gruul.payment.api.model.dto.PaymentInfoDTO;
import com.medusa.gruul.payment.api.model.dto.RefundRequestDTO;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.api.model.param.CombineOrderParam;
import com.medusa.gruul.payment.api.model.param.ProfitSharingParam;
import com.medusa.gruul.payment.api.model.param.ProfitSharingUnfreezeParam;
import com.medusa.gruul.payment.api.model.pay.PayResult;
import com.medusa.gruul.payment.api.model.transfer.TransferParam;
import com.medusa.gruul.payment.api.model.transfer.TransferResult;
import com.medusa.gruul.payment.api.rpc.PaymentRpcService;
import com.medusa.gruul.payment.service.common.annotation.PlatformHandler;
import com.medusa.gruul.payment.service.common.annotation.RefundHandler;
import com.medusa.gruul.payment.service.common.annotation.TransferHandler;
import com.medusa.gruul.payment.service.common.model.OrderPaymentRecord;
import com.medusa.gruul.payment.service.model.enums.PaymentError;
import com.medusa.gruul.payment.service.mp.service.IPaymentInfoService;
import com.medusa.gruul.payment.service.mp.service.IPaymentPlatformConfigService;
import com.medusa.gruul.payment.service.mp.service.IPaymentRefundService;
import com.medusa.gruul.payment.service.properties.PaymentProperty;
import com.medusa.gruul.payment.service.service.AggPayRedisService;
import com.medusa.gruul.payment.service.service.AggPayService;
import com.medusa.gruul.payment.service.service.MultiPayOrderService;
import com.medusa.gruul.payment.service.service.WxServiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import reactor.core.scheduler.Schedulers;
import com.medusa.gruul.payment.service.mp.service.WeChatConfigInfoService;
import com.medusa.gruul.payment.api.model.vo.WeChatConfigInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ Description
 * date 2022-07-25 14:59
 */
@Service
@DubboService
@RequiredArgsConstructor
@Slf4j
public class PaymentRpcServiceImpl implements PaymentRpcService {

    private final PaymentProperty payProperty;
    private final WxServiceService wxServiceService;
    private final MultiPayOrderService multiPayOrderService;
    private final IPaymentInfoService paymentInfoService;

    private final AggPayService aggPayService;

    private final IPaymentPlatformConfigService paymentPlatformConfigService;

    private final IPaymentRefundService paymentRefundService;


    private final WeChatConfigInfoService weChatConfigInfoService;

    private static final Integer DEFAULT_EXPIRE_TIME = 15;


    private static final String SETTLEMENT_LOCK_CACHE_KEY = "settlement:lock:cache:info:";

    @Override
    public PayResult combinePay(CombineOrderParam param) {
        //已开启服务商模式  并且是微信支付
        List<CombineOrderParam.SubOrder> subOrders = param.getSubOrders();
        if (param.getPayType() == PayType.WECHAT || param.getPayType() == PayType.ALIPAY) {
            param.setIsAggPay(paymentPlatformConfigService.getEnabledPayTypes(ISystem.platformIdMust()) == PayType.AGGREGATION);
        }
        //走普通支付的第一种方式 未开启服务商模式或者不是微信支付
        if (!payProperty.isEnableServiceMode() || PayType.WECHAT != param.getPayType()
                || Boolean.TRUE.equals(param.getIsAggPay())) {
            PaymentInfoDTO paymentInfo = param.toPaymentInfo();
            paymentInfo.setShopId(subOrders.get(0).getShopId());
            paymentInfo.setShopName(subOrders.get(0).getShopName());
            paymentInfo.setIsAggPay(param.getIsAggPay());
            return payRequest(paymentInfo);
        }
        Map<Long, CombineOrderParam.SubOrder> shopIdSubOrderMap = subOrders.stream()
                .collect(Collectors.toMap(CombineOrderParam.SubOrder::getShopId, v -> v));
        //店铺id对应的签约商户id
        Map<Long, String> shopSubMchIdMap = wxServiceService.shopSubMchIdMap(shopIdSubOrderMap.keySet());
        //走普通支付的第二种方式  订单里没有一个签约商户
        if (CollUtil.isEmpty(shopSubMchIdMap)) {
            PaymentInfoDTO paymentInfo = param.toPaymentInfo();
            paymentInfo.setShopId(subOrders.get(0).getShopId());
            return payRequest(paymentInfo);
        }

        Object combine = wxServiceService.combineOrder(param, shopSubMchIdMap);
        PayResult payResult = new PayResult();
        payResult.setOutTradeNo(param.getOrderNo());
        payResult.setData(combine);
        return payResult;
    }

    @Override
    public void profitSharing(ProfitSharingParam param) {
        wxServiceService.profitSharing(param);
    }

    @Override
    public void profitSharingUnfreeze(ProfitSharingUnfreezeParam param) {
        wxServiceService.profitSharingUnfreeze(param);
    }

    /**
     * 支付业务发生一笔请求
     *
     * @param paymentInfoDTO 支付信息DTO
     * @return 支付结果
     */
    @Override
    public PayResult payRequest(PaymentInfoDTO paymentInfoDTO) {
        Handler<PayResult> handler = SpringUtils.getBean(PlatformHandler.class, paymentInfoDTO.getPayPlatform());
        return handler.handle(paymentInfoDTO);
    }

    /**
     * 退款业务发生一笔请求
     *
     * @param request 退款信息DTO
     */
    @Override
    public void refundRequest(RefundRequestDTO request) {
        if (wxServiceService.refund(request)) {
            return;
        }
        OrderPaymentRecord orderPaymentRecord = multiPayOrderService.paymentRecord(request.getOrderNum());
        if (StrUtil.isBlank(orderPaymentRecord.getNotifyParam())) {
            throw PaymentError.NOT_FOUND_PAYMENT_RECORD.exception();
        }
        Handler<Void> handler = SpringUtils.getBean(RefundHandler.class, orderPaymentRecord.getPayType());
        if (orderPaymentRecord.getDetailsId() == null) {
            orderPaymentRecord.setDetailsId("0");
        }
        handler.handle(request, orderPaymentRecord.getDetailsId(), orderPaymentRecord.getNotifyParam(), orderPaymentRecord.getPayType());
    }


    @Override
    public TransferResult transfer(TransferParam transferParam) {
        Handler<TransferResult> handler = SpringUtils.getBean(TransferHandler.class, transferParam.getPayType());
        return handler.handle(transferParam);
    }

    @Override
    public boolean serviceEnable() {
        return wxServiceService.serviceEnable();
    }

    /**
     * 获取支付订单信息
     *
     * @param orderNo 订单号
     * @return 支付订单信息
     */
    @Override
    public PaymentInfo getPaymentInfo(String orderNo) {
        return TenantShop.disable(() -> paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum, orderNo).one());
    }

    @Override
    public AggPayPollingRespDTO prePayPolling(SaasPollingDTO saasPollingDTO) {
        PaymentInfo paymentInfo =
                Tenant.disable(() -> paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum
                        , saasPollingDTO.getOrderGuid()).one());
        saasPollingDTO.setKeyPrivate(paymentInfo.getKeyPrivate());
        saasPollingDTO.setMchId(paymentInfo.getMchId());
        return aggPayService.prePayPolling(saasPollingDTO).subscribeOn(Schedulers.boundedElastic()).block();
    }

    @Override
    public AggRefundPollingRespDTO refundPolling(SaasPollingDTO saasPollingDTO) {
        return aggPayService.refundPolling(saasPollingDTO).subscribeOn(Schedulers.boundedElastic()).block();
    }

    @Override
    public String callback(AggPayCallbackDTO aggPayCallbackDTO) {
        return aggPayService.callback(aggPayCallbackDTO)
                .block();
    }

    @Override
    public PaymentRefund getPaymentRefundByAfsNum(String afsNum) {
        return paymentRefundService.lambdaQuery().eq(PaymentRefund::getAfsNum, afsNum).one();
    }

}
