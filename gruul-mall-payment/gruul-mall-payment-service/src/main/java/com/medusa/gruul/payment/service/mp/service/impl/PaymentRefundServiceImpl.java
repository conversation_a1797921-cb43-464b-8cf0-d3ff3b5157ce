package com.medusa.gruul.payment.service.mp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.filter.Filter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.egzosn.pay.common.bean.PayMessage;
import com.egzosn.pay.common.bean.RefundResult;
import com.medusa.gruul.afs.api.model.AfsOrderItemDTO;
import com.medusa.gruul.afs.api.rpc.AfsRpcService;
import com.medusa.gruul.common.member.dto.MemberCardPayDTO;
import com.medusa.gruul.common.member.dto.MemberCardRefundDTO;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.BalanceHistoryOperatorType;
import com.medusa.gruul.common.model.enums.ChangeType;
import com.medusa.gruul.common.model.message.DataChangeMessage;
import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.common.mp.IManualTransaction;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.order.api.entity.OrderDiscount;
import com.medusa.gruul.order.api.entity.OrderDiscountItem;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.entity.ShopOrderItem;
import com.medusa.gruul.order.api.enums.DiscountSourceOrigin;
import com.medusa.gruul.order.api.enums.DiscountSourceType;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.payment.api.entity.PaymentHistory;
import com.medusa.gruul.payment.api.entity.PaymentInfo;
import com.medusa.gruul.payment.api.entity.PaymentRefund;
import com.medusa.gruul.payment.api.enums.BaseDeviceTypeEnum;
import com.medusa.gruul.payment.api.enums.DealType;
import com.medusa.gruul.payment.api.enums.PayPowerIdEnum;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDTO;
import com.medusa.gruul.payment.api.model.dto.RefundRequestDTO;
import com.medusa.gruul.payment.api.model.dto.agg.AggPayPreTradingReqDTO;
import com.medusa.gruul.payment.api.model.dto.agg.AggRefundReqDTO;
import com.medusa.gruul.payment.api.model.dto.agg.AggRefundRespDTO;
import com.medusa.gruul.payment.service.model.dto.MerchantDetailsDTO;
import com.medusa.gruul.payment.service.model.dto.SaasAggRefundOdooDTO;
import com.medusa.gruul.payment.service.model.enums.PaymentError;
import com.medusa.gruul.payment.service.mp.mapper.PaymentHistoryMapper;
import com.medusa.gruul.payment.service.mp.mapper.PaymentRefundMapper;
import com.medusa.gruul.payment.service.mp.service.IPaymentInfoService;
import com.medusa.gruul.payment.service.mp.service.IPaymentRefundService;
import com.medusa.gruul.payment.service.service.AggPayService;
import com.medusa.gruul.service.uaa.api.rpc.UaaRpcService;
import com.medusa.gruul.service.uaa.api.vo.UserInfoVO;
import com.medusa.gruul.user.api.rpc.UserRpcService;
import io.jsonwebtoken.lang.Objects;
import io.vavr.control.Option;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Description
 * date 2022-08-08 11:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentRefundServiceImpl extends ServiceImpl<PaymentRefundMapper, PaymentRefund> implements IPaymentRefundService {

    private final PaymentRefundMapper paymentRefundMapper;

    private final IPaymentInfoService paymentInfoService;

    private final RabbitTemplate rabbitTemplate;

    private final UserRpcService userRpcService;

    private final UaaRpcService uaaRpcService;

    private final PaymentHistoryMapper paymentHistoryMapper;

    private final MemberApiService memberApiService;

    private final OrderRpcService orderRpcService;

    private final AfsRpcService afsRpcService;

    private final AggPayService aggPayService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void balanceRefundOrder(RefundRequestDTO refundRequest, String notifyParam, String refundNo) {
        log.debug("余额退款");
        String orderNum = refundRequest.getOrderNum();
        PaymentInfo paymentInfo = paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum, orderNum).one();
        if (paymentInfo.getTotalFee() < refundRequest.getRefundFee()) {
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        Long sumRefundAmount = this.getSumRefundAmount(orderNum);
        if (paymentInfo.getTotalFee() < sumRefundAmount) {
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        PayMessage payMessage = JSON.parseObject(notifyParam, PayMessage.class, (Filter) null, JSONReader.Feature.FieldBased);
        String outTradeNo = payMessage.getPayMessage().get("out_trade_no").toString();

        PaymentRefund paymentRefund = paymentRefundMapper.selectOne(new QueryWrapper<PaymentRefund>().eq("refund_no", refundNo));
        if (paymentRefund == null) {
            return;
        }
        if (StrUtil.isNotEmpty(paymentRefund.getSynCallback())) {
            return;
        }
        //更新退款信息
        paymentRefund.setSynCallback(JSON.toJSONString(payMessage));
        paymentRefundMapper.updateById(paymentRefund);

        DataChangeMessage dataChangeMessage = new DataChangeMessage();
        dataChangeMessage.setChangeType(ChangeType.INCREASE);
        dataChangeMessage.setUserId(paymentInfo.getUserId());
        dataChangeMessage.setValue(refundRequest.getRefundFee());
        dataChangeMessage.setOperatorType(BalanceHistoryOperatorType.REFUND_SUCCESSFUL);
        dataChangeMessage.setOrderNo(orderNum);
        dataChangeMessage.setOperatorUserId(paymentInfo.getUserId());
        Boolean flag = userRpcService.userBalancePayment(dataChangeMessage);
        SystemCode.DATA_UPDATE_FAILED.falseThrow(flag);
        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.REFUND_SUCCEED);
        paymentHistory.setUserId(paymentInfo.getUserId());
        paymentHistory.setSubject(paymentInfo.getSubject());
        paymentHistory.setMoney(refundRequest.getRefundFee());
        paymentHistory.setChangeType(ChangeType.INCREASE);
        paymentHistory.setPayType(paymentInfo.getPayType());
        paymentHistoryMapper.insert(paymentHistory);
        // mq发送
        IManualTransaction.afterCommit(
                () -> {
                    log.debug("::::余额退款回调接口执行完毕::OutTradeNo:{}::RefundNo:{}", outTradeNo, refundNo);
                    rabbitTemplate.convertAndSend(
                            paymentRefund.getExchange(),
                            paymentRefund.getRouteKey(),
                            new RefundNotifyResultDTO()
                                    .setOutTradeNo(outTradeNo)
                                    .setAfsNum(paymentRefund.getAfsNum())
                                    .setRefundNum(refundNo)
                    );
                }
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void memberCardRefundOrder(RefundRequestDTO refundRequest, String notifyParam, String refundNo) {
        log.debug("会员卡退款");
        String orderNum = refundRequest.getOrderNum();
        PaymentInfo paymentInfo = paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum, orderNum).one();
        if (paymentInfo.getTotalFee() < refundRequest.getRefundFee()) {
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        Long sumRefundAmount = this.getSumRefundAmount(orderNum);
        if (paymentInfo.getTotalFee() < sumRefundAmount) {
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        PayMessage payMessage = JSON.parseObject(notifyParam, PayMessage.class, (Filter) null, JSONReader.Feature.FieldBased);
        String outTradeNo = payMessage.getPayMessage().get("out_trade_no").toString();

        PaymentRefund paymentRefund = paymentRefundMapper.selectOne(new QueryWrapper<PaymentRefund>().eq("refund_no", refundNo));
        if (paymentRefund == null) {
            return;
        }
        if (StrUtil.isNotEmpty(paymentRefund.getSynCallback())) {
            return;
        }
        //更新退款信息
        paymentRefund.setSynCallback(JSON.toJSONString(payMessage));
        paymentRefundMapper.updateById(paymentRefund);
        Boolean flag = memberCardRefundSyn(refundRequest);
        SystemCode.DATA_UPDATE_FAILED.falseThrow(flag);
        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.REFUND_SUCCEED);
        paymentHistory.setUserId(paymentInfo.getUserId());
        paymentHistory.setSubject(paymentInfo.getSubject());
        paymentHistory.setMoney(refundRequest.getRefundFee());
        paymentHistory.setChangeType(ChangeType.INCREASE);
        paymentHistory.setPayType(paymentInfo.getPayType());
        paymentHistoryMapper.insert(paymentHistory);
        // mq发送
        IManualTransaction.afterCommit(
                () -> {
                    log.debug("::::余额退款回调接口执行完毕::OutTradeNo:{}::RefundNo:{}", outTradeNo, refundNo);
                    rabbitTemplate.convertAndSend(
                            paymentRefund.getExchange(),
                            paymentRefund.getRouteKey(),
                            new RefundNotifyResultDTO()
                                    .setOutTradeNo(outTradeNo)
                                    .setAfsNum(paymentRefund.getAfsNum())
                                    .setRefundNum(refundNo)
                    );
                }
        );
    }

    @Override
    public Boolean memberCardRefundSyn(RefundRequestDTO refundRequest) {
        log.info("会员退款入参：{}", JSON.toJSONString(refundRequest));
        String orderNum = refundRequest.getOrderNum();
        Long shopId = refundRequest.getShopId();
        String afsNum = refundRequest.getAfsNum();
        MemberCardRefundDTO memberCardRefundDTO = new MemberCardRefundDTO();
        //查询店铺订单号
        List<ShopOrder> shopOrderList = orderRpcService.getShopOrderList(orderNum);
        String no = shopOrderList.stream().filter(shopOrder -> shopOrder.getShopId()
                        .equals(shopId))
                .findFirst()
                .orElseThrow(PaymentError.ORDER_REFUND_ERROR::exception)
                .getNo();

        // 使用AfsRpcService获取售后订单商品信息
        BigDecimal discountAmount = getSettlementDiscountAmount(afsNum, orderNum, shopId);
        log.info("会员卡退款回退优惠金额: {}", discountAmount);
        if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
            memberCardRefundDTO.setDiscountAmount(discountAmount);
        }
        memberCardRefundDTO.setOrderNumber(no)
                .setRefundAmount(BigDecimal.valueOf(refundRequest.getRefundFee()).divide(CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND, 2, RoundingMode.HALF_EVEN));
        return memberApiService.memberCardRefund(memberCardRefundDTO);
    }

    private BigDecimal getSettlementDiscountAmount(String afsNum, String orderNum, Long shopId) {
        // 如果参数为空，直接返回零
        if (CharSequenceUtil.isBlank(afsNum) || CharSequenceUtil.isBlank(orderNum) || shopId == null) {
            log.info("参数不完整，无法计算退款优惠金额");
            return BigDecimal.ZERO;
        }

        // 获取售后订单商品信息
        List<AfsOrderItemDTO> afsOrderItems = afsRpcService.getAfsOrderItemsByAfsNo(afsNum);
        log.info("获取售后订单商品信息: {}", JSON.toJSONString(afsOrderItems));
        if (CollUtil.isEmpty(afsOrderItems)) {
            log.info("售后订单商品信息为空，无法进行会员卡退款");
            return BigDecimal.ZERO;
        }

        // 收集售后订单商品ID
        Set<Long> afsProductIds = afsOrderItems.stream()
                .map(AfsOrderItemDTO::getProductId)
                .collect(Collectors.toSet());
        if (afsProductIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 获取订单优惠信息
        List<OrderDiscount> orderDiscounts = orderRpcService.getOrderDiscountsByOrderNo(orderNum);
        log.info("获取订单优惠信息: {}", JSON.toJSONString(orderDiscounts));
        if (CollUtil.isEmpty(orderDiscounts)) {
            return BigDecimal.ZERO;
        }

        // 获取所有订单商品，并过滤出当前店铺且在售后商品中的商品ID
        List<ShopOrderItem> shopItems = Option.of(orderRpcService.getShopOrderItemList(orderNum))
                .getOrElse(Collections.emptyList())
                .stream()
                .filter(item -> item.getShopId().equals(shopId) && afsProductIds.contains(item.getProductId()))
                .toList();

        if (shopItems.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 收集符合条件的订单商品ID
        Set<Long> itemIds = shopItems.stream()
                .map(ShopOrderItem::getId)
                .collect(Collectors.toSet());

        // 计算总优惠金额
        return orderDiscounts.stream()
                .map(orderDiscount -> {
                    // 查询优惠活动相关的订单商品
                    List<OrderDiscountItem> discountItems = orderRpcService.getOrderDiscountItemsByIds(
                            shopId, orderDiscount.getId(), new java.util.ArrayList<>(itemIds));
                    log.info("获取订单优惠商品信息: {}", JSON.toJSONString(discountItems));


                    // 计算该优惠活动的总优惠金额
                    return CollUtil.isEmpty(discountItems) ? BigDecimal.ZERO :
                            discountItems.stream()
                                    .map(item -> new BigDecimal(item.getDiscountAmount())
                                            .divide(CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND, 2, RoundingMode.HALF_EVEN))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }



    @Override
    public void refundSuccess(boolean async, String refundNo, Object msg) {
        String outTradeNo;
        if (msg instanceof PayMessage message) {
            outTradeNo = message.getOutTradeNo();
        } else if (msg instanceof RefundResult refund) {
            outTradeNo = refund.getOutTradeNo();
        } else {
            throw new RuntimeException("unsupported msg type");
        }
        PaymentRefund refund = this.lambdaQuery()
                .eq(PaymentRefund::getRefundNo, refundNo)
                .one();
        if (refund == null) {
            return;
        }
        if (StrUtil.isNotEmpty(refund.getSynCallback())) {
            return;
        }
        //更新退款信息
        refund.setRefundNo(refundNo);
        if (async) {
            refund.setSynCallback(JSON.toJSONString(msg));
        } else {
            refund.setSynCallback(JSON.toJSONString(msg));
        }
        this.updateById(refund);
        log.debug("::::退款已完成::OutTradeNo:{}::RefundNo:{}::AFS{}", outTradeNo, refundNo, refund.getAfsNum());
        //如果路由key为空不进行回调
        if (StrUtil.isEmpty(refund.getExchange())) {
            return;
        }
        rabbitTemplate.convertAndSend(
                refund.getExchange(),
                refund.getRouteKey(),
                new RefundNotifyResultDTO()
                        .setOutTradeNo(outTradeNo)
                        .setAfsNum(refund.getAfsNum())
                        .setRefundNum(refundNo)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aggregationRefundOrder(RefundRequestDTO refundRequest, String notifyParam, String refundNo) {
        log.info("聚合支付退款");
        String orderNum = refundRequest.getOrderNum();
        PaymentInfo paymentInfo = paymentInfoService.lambdaQuery().eq(PaymentInfo::getOrderNum, orderNum).one();
        if (paymentInfo.getTotalFee() < refundRequest.getRefundFee()) {
            log.info("聚合支付退款金额不能大于支付金额: {}", refundRequest.getRefundFee());
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        Long sumRefundAmount = this.getSumRefundAmount(orderNum);
        if (paymentInfo.getTotalFee() < sumRefundAmount) {
            log.info("聚合支付退款金额不能大于支付金额: {}", refundRequest.getRefundFee());
            throw PaymentError.REFUND_AMOUNT_CANNOT_GREATER_THAN_PAYMENT_AMOUNT.exception();
        }
        PayMessage payMessage = JSON.parseObject(notifyParam, PayMessage.class, (Filter) null, JSONReader.Feature.FieldBased);
        String outTradeNo = payMessage.getPayMessage().get("out_trade_no").toString();

        PaymentRefund paymentRefund = paymentRefundMapper.selectOne(new QueryWrapper<PaymentRefund>().eq("refund_no", refundNo));
        if (paymentRefund == null) {
            log.info("聚合支付退款单号不存在: {}", refundNo);
            return;
        }
        if (CharSequenceUtil.isNotEmpty(paymentRefund.getSynCallback())) {
            log.info("聚合支付退款单号已处理: {}", refundNo);
            return;
        }
        UserInfoVO userInfoVO = uaaRpcService.getUserDataByUserId(paymentInfo.getUserId()).get();
        log.info("获取用户信息：{}", com.alibaba.fastjson.JSON.toJSONString(userInfoVO));

        SaasAggRefundOdooDTO saasAggRefundOdooDTO = new SaasAggRefundOdooDTO();
        MerchantDetailsDTO paymentInfoDTO = new MerchantDetailsDTO();
        paymentInfoDTO.setKeyPrivate(paymentInfo.getKeyPrivate());
        paymentInfoDTO.setMchId(paymentInfo.getMchId());
        saasAggRefundOdooDTO.setPaymentInfo(paymentInfoDTO);
        AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setRefundFee(BigDecimal.valueOf(refundRequest.getRefundFee()).divide(CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND, 2, RoundingMode.HALF_EVEN));
        //是否部分退款
        if (refundRequest.getRefundFee() < paymentInfo.getTotalFee()) {
            aggRefundReqDTO.setRefundType(1);
        } else {
            aggRefundReqDTO.setRefundType(0);
        }
        aggRefundReqDTO.setOrderGUID(paymentInfo.getOrderNum());
        aggRefundReqDTO.setPayGUID(paymentInfo.getOrderNum());
        aggRefundReqDTO.setReason(refundRequest.getDesc());
        aggRefundReqDTO.setRefundNo(refundNo);
        aggRefundReqDTO.setAppId(paymentInfo.getMchId());
        aggRefundReqDTO.setOutTradeNo(outTradeNo);

        aggRefundReqDTO.setRefundFeeLong(refundRequest.getRefundFee());
        saasAggRefundOdooDTO.setRevenueType(paymentInfo.getRevenueType());
        saasAggRefundOdooDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundOdooDTO.setUserGuid(userInfoVO.getUserId().toString());
        saasAggRefundOdooDTO.setUserName(userInfoVO.getUsername());

        saasAggRefundOdooDTO.setDevice_id(ISystem.deviceIdMust());
        saasAggRefundOdooDTO.setOperSubjectGuid(ISystem.platformIdMust().toString());
        saasAggRefundOdooDTO.setEnterpriseName(paymentInfoDTO.getSubjectName());
        saasAggRefundOdooDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT_MINI.getCode());
        saasAggRefundOdooDTO.setStoreGuid(String.valueOf(paymentInfo.getShopId()));

        // 调用聚合支付退款接口
        log.info("聚合支付退款入参: {}", JSON.toJSONString(saasAggRefundOdooDTO));
        AggRefundRespDTO refundResult = aggPayService.refund(saasAggRefundOdooDTO).block();
        log.info("聚合支付退款结果: {}", JSON.toJSONString(refundResult));
        if (Objects.isEmpty(refundResult) ||
                !java.util.Objects.equals(refundResult.getCode(), "10000")) {
            log.error("聚合支付退款失败: {}", JSON.toJSONString(refundResult));
            // 如果退款失败，抛出异常
            throw PaymentError.ORDER_REFUND_ERROR.exception();
        }
        //更新退款信息
        paymentRefund.setSynCallback(JSON.toJSONString(payMessage));
        paymentRefundMapper.updateById(paymentRefund);

        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.REFUND_SUCCEED);
        paymentHistory.setUserId(paymentInfo.getUserId());
        paymentHistory.setSubject(paymentInfo.getSubject());
        paymentHistory.setMoney(refundRequest.getRefundFee());
        paymentHistory.setChangeType(ChangeType.INCREASE);
        paymentHistory.setPayType(paymentInfo.getPayType());
        paymentHistoryMapper.insert(paymentHistory);

        memberCardRefundSyn(refundRequest);
        // mq发送
        IManualTransaction.afterCommit(
                () -> {
                    log.debug("::::聚合支付退款回调接口执行完毕::OutTradeNo:{}::RefundNo:{}", outTradeNo, refundNo);
                    rabbitTemplate.convertAndSend(
                            paymentRefund.getExchange(),
                            paymentRefund.getRouteKey(),
                            new RefundNotifyResultDTO()
                                    .setOutTradeNo(outTradeNo)
                                    .setAfsNum(paymentRefund.getAfsNum())
                                    .setRefundNum(refundNo)
                    );
                }
        );
    }

    /**
     * 获取该业务单号退款总计金额
     *
     * @param orderNum 业务单号
     * @return 业务单号退款总计金额
     */
    private Long getSumRefundAmount(String orderNum) {
        return paymentRefundMapper.querySumRefundAmount(orderNum);
    }

}
