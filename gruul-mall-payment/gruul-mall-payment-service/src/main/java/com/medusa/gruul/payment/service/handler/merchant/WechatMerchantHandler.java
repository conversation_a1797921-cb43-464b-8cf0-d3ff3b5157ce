package com.medusa.gruul.payment.service.handler.merchant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.egzosn.pay.common.bean.CertStoreType;
import com.egzosn.pay.common.util.sign.SignUtils;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.mp.model.TenantShop;
import com.medusa.gruul.common.system.model.model.Platform;
import com.medusa.gruul.payment.api.entity.MerchantDetails;
import com.medusa.gruul.payment.api.entity.PaymentMerchantConfig;
import com.medusa.gruul.payment.service.common.annotation.MerchantHandler;
import com.medusa.gruul.payment.service.model.dto.MerchantDetailsDTO;
import com.medusa.gruul.payment.service.mp.service.IMerchantDetailsService;
import com.medusa.gruul.payment.service.mp.service.IPaymentMerchantConfigService;
import com.medusa.gruul.payment.service.properties.PaymentProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 微信渠道配置
 *
 * <AUTHOR>
 * @ Description 微信渠道配置
 * @date 2022-07-14 09:47
 */
@Component
@MerchantHandler(PayType.WECHAT)
public class WechatMerchantHandler extends AbstractMerchantHandler {

    public WechatMerchantHandler(IMerchantDetailsService merchantDetailsService, 
                               IPaymentMerchantConfigService paymentMerchantConfigService, 
                               PaymentProperty payProperty) {
        super(merchantDetailsService, paymentMerchantConfigService, payProperty);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleMerchant(MerchantDetailsDTO wxpayMerchant) {
        if (ObjectUtil.isEmpty(wxpayMerchant.getDetailsId())) {
            // 创建新商户
            MerchantDetails details = createBaseMerchantDetails(wxpayMerchant, PayType.WECHAT);
            // 保存商户基本信息
            TenantShop.disable(() -> merchantDetailsService.save(details));
            // 保存商户配置并同步状态
            saveMerchantConfig(details, wxpayMerchant);
        } else {
            // 更新商户信息
            MerchantDetails details = updateMerchantDetails(wxpayMerchant);
            // 更新商户配置并同步状态
            saveMerchantConfig(details, wxpayMerchant);
        }
    }
}
