package com.medusa.gruul.payment.service.assembler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.service.model.vo.AggPayAttachDataVO;

public class AggPayMapstructAssembler {

    private AggPayMapstructAssembler() {
        // Private constructor to prevent instantiation
    }

    public static AggPayPollingDTO createPollingDto(SaasAggPayDTO saasAggPayDTO, AggPayPreTradingReqDTO payPreTradingReqDTO) {
        if (payPreTradingReqDTO == null) {
            return null;
        }

        AggPayPollingDTO aggPayPollingDTO = new AggPayPollingDTO();

        aggPayPollingDTO.setOrderGUID(payPreTradingReqDTO.getOrderGUID());
        aggPayPollingDTO.setAttachData(payPreTradingReqDTO.getAttachData());
        aggPayPollingDTO.setPayGUID(payPreTradingReqDTO.getPayGUID());
        aggPayPollingDTO.setAppId(payPreTradingReqDTO.getAppId());
        aggPayPollingDTO.setTimestamp(payPreTradingReqDTO.getTimestamp());
        aggPayPollingDTO.setDeveloperId(payPreTradingReqDTO.getDeveloperId());


        if (StringUtils.isNotEmpty(aggPayPollingDTO.getPayGUID()) ||
                StringUtils.isNotEmpty(aggPayPollingDTO.getOrderGUID())) {
            aggPayPollingDTO.setPayGUID(saasAggPayDTO.getReqDTO().getOrderGUID());
            aggPayPollingDTO.setOrderGUID(saasAggPayDTO.getReqDTO().getOrderGUID());
        }
        return aggPayPollingDTO;
    }

    public static AggRefundPollingDTO createRefundPollingDto(AggRefundReqDTO aggRefundReqDTO) {
        if (aggRefundReqDTO == null) {
            return null;
        }

        AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();

        aggRefundPollingDTO.setAppId(aggRefundReqDTO.getAppId());
        aggRefundPollingDTO.setAttachData(aggRefundReqDTO.getAttachData());
        aggRefundPollingDTO.setDeveloperId(aggRefundReqDTO.getDeveloperId());
        aggRefundPollingDTO.setOrderGUID(aggRefundReqDTO.getOrderGUID());
        aggRefundPollingDTO.setPayGUID(aggRefundReqDTO.getPayGUID());
        aggRefundPollingDTO.setTimestamp(aggRefundReqDTO.getTimestamp());
        aggRefundPollingDTO.setRefundNo(aggRefundReqDTO.getRefundNo());
        return aggRefundPollingDTO;
    }

    public static BaseInfoDTO createBaseInfo(BaseDTO baseDTO) {
        if (baseDTO == null) {
            return null;
        }

        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();

        baseInfoDTO.setDeviceType(baseDTO.getDeviceType());
        baseInfoDTO.setDevice_id(baseDTO.getDevice_id());
        baseInfoDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        baseInfoDTO.setEnterpriseName(baseDTO.getEnterpriseName());
        baseInfoDTO.setStoreGuid(baseDTO.getStoreGuid());
        baseInfoDTO.setStoreName(baseDTO.getStoreName());
        baseInfoDTO.setUserGuid(baseDTO.getUserGuid());
        baseInfoDTO.setUserName(baseDTO.getUserName());
        baseInfoDTO.setAccount(baseDTO.getAccount());
        baseInfoDTO.setRequestTimestamp(baseDTO.getRequestTimestamp());
        baseInfoDTO.setOperSubjectGuid(baseDTO.getOperSubjectGuid());
        baseInfoDTO.setHeaderUserInfo(baseDTO.getHeaderUserInfo());

        return baseInfoDTO;
    }


    public static AggPayPollingRespDTO makeCallBackDto(AggPayCallbackDTO aggPayCallbackDTO) {
        if (aggPayCallbackDTO == null) {
            return null;
        }

        AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();

        if (aggPayCallbackDTO.getAmount() != null) {
            aggPayPollingRespDTO.setAmount(aggPayCallbackDTO.getAmount());
        }
        aggPayPollingRespDTO.setAttachData(aggPayCallbackDTO.getAttachData());
        aggPayPollingRespDTO.setBankOrderNo(aggPayCallbackDTO.getBankOrderNo());
        aggPayPollingRespDTO.setBankTransactionId(aggPayCallbackDTO.getBankTransactionId());
        aggPayPollingRespDTO.setBody(aggPayCallbackDTO.getBody());
        aggPayPollingRespDTO.setCode(aggPayCallbackDTO.getCode());
        aggPayPollingRespDTO.setFee(aggPayCallbackDTO.getFee());
        aggPayPollingRespDTO.setMsg(aggPayCallbackDTO.getMsg());
        aggPayPollingRespDTO.setOrderDt(aggPayCallbackDTO.getOrderDt());
        aggPayPollingRespDTO.setOrderGuid(aggPayCallbackDTO.getOrderGuid());
        aggPayPollingRespDTO.setOrderHolderNo(aggPayCallbackDTO.getOrderHolderNo());
        aggPayPollingRespDTO.setOrderNo(aggPayCallbackDTO.getOrderNo());
        aggPayPollingRespDTO.setPayGuid(aggPayCallbackDTO.getPayGuid());
        aggPayPollingRespDTO.setPayPowerId(aggPayCallbackDTO.getPayPowerId());
        aggPayPollingRespDTO.setPaySt(aggPayCallbackDTO.getPaySt());
        aggPayPollingRespDTO.setSignature(aggPayCallbackDTO.getSignature());
        aggPayPollingRespDTO.setSubject(aggPayCallbackDTO.getSubject());

        return aggPayPollingRespDTO;
    }

    public static AggPayAttachDataVO makeAttachVo(BaseDTO baseDTO) {
        if (baseDTO == null) {
            return null;
        }
        AggPayAttachDataVO aggPayAttachDataVO = new AggPayAttachDataVO();

        aggPayAttachDataVO.setAccount(baseDTO.getAccount());
        aggPayAttachDataVO.setDeviceType(baseDTO.getDeviceType());
        aggPayAttachDataVO.setDevice_id(baseDTO.getDevice_id());
        aggPayAttachDataVO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        aggPayAttachDataVO.setEnterpriseName(baseDTO.getEnterpriseName());
        aggPayAttachDataVO.setHeaderUserInfo(baseDTO.getHeaderUserInfo());
        aggPayAttachDataVO.setOperSubjectGuid(baseDTO.getOperSubjectGuid());
        aggPayAttachDataVO.setRequestTimestamp(baseDTO.getRequestTimestamp());
        aggPayAttachDataVO.setStoreGuid(baseDTO.getStoreGuid());
        aggPayAttachDataVO.setStoreName(baseDTO.getStoreName());
        aggPayAttachDataVO.setUserGuid(baseDTO.getUserGuid());
        aggPayAttachDataVO.setUserName(baseDTO.getUserName());
        return aggPayAttachDataVO;
    }
}
