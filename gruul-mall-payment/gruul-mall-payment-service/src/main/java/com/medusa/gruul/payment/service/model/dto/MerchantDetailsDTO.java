package com.medusa.gruul.payment.service.model.dto;

import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.system.model.model.Platform;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 商户支付配置DTO
 *
 * <AUTHOR>
 * @ Description 支付商户渠道配置DTO
 * @date 2022-07-13 09:49
 */
@Data
@Validated
public class MerchantDetailsDTO {

    /**
     * 商户详情id
     */
    private String detailsId;

    /**
     * 支付类型
     */
    @NotNull
    private PayType payType;

    /**
     * 应用id
     */
    @NotBlank
    private String appid;

    /**
     * 商户私钥
     */
    @NotBlank
    private String keyPrivate;

    /**
     * 商户公钥
     */
    @NotBlank(groups = Ali.class)
    private String keyPublic;

    /**
     * 商户号
     */
    @NotBlank(groups = Wechat.class)
    private String mchId;

    /**
     * 支付证书
     */
    @NotBlank(groups = Wechat.class)
    private String keyCert;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * APP平台类型
     */
    private List<Platform> platforms;

    /**
     * 是否为默认支付方式
     */
    private Integer isDefault;

    /**
     * 收款类型列表 1:会员营收、2:门店营收
     */
    private List<Integer> revenueType;

    /**
     * 门店id列表
     */
    private List<Long> shopIds;

    public interface Wechat {
    }

    public interface Ali {
    }
}
