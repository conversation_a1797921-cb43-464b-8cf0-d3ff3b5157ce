package com.medusa.gruul.payment.service.mp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.payment.api.entity.MerchantDetails;
import com.medusa.gruul.common.model.enums.PayType;

/**
 *
 *
 *
 *
 * <AUTHOR>
 * @ Description
 * @date 2022-07-14 13:26
 */
public interface IMerchantDetailsService extends IService<MerchantDetails> {
    /**
     * 获取默认商户
     *
     * @param payType 支付类型
     * @return 默认商户信息
     */
    MerchantDetails getDefaultMerchant(PayType payType);
}
