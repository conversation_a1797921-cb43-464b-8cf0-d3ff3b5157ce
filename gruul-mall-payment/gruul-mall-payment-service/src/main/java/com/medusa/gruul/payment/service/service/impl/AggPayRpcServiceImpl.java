package com.medusa.gruul.payment.service.service.impl;


import com.alibaba.fastjson2.JSON;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.api.model.response.LogicResponse;
import com.medusa.gruul.payment.api.model.response.LogicStatus;
import com.medusa.gruul.payment.service.config.AggPayConfig;
import com.medusa.gruul.payment.service.service.AggPayRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import org.springframework.web.reactive.function.client.WebClient;

import static com.medusa.gruul.payment.service.config.PayConstant.*;
import static org.springframework.http.HttpMethod.POST;


/**
 * 聚合支付平台接口实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AggPayRpcServiceImpl implements AggPayRpcService {

    private final AggPayConfig aggPayConfig;

    private final RestTemplate remoteRestTemplate;

    private static final String REFUND_STRING = "向聚合支付平台发起退款请求，请求参数：{}";


    @Override
    public Mono<AggPayRespDTO> preTradingAsync(AggPayPreTradingReqDTO aggPayPreTradingReqDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getPay())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggPayPreTradingReqDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayRespDTO>>() {
                })
                .publishOn(Schedulers.boundedElastic())
                .map(logicResponse -> resp("预下单", logicResponse,
                        AggPayRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起预下单请求，请求参数：{}",
                        JSON.toJSONString(aggPayPreTradingReqDTO)));
    }

    @Override
    public AggPayPollingRespDTO doPolling(AggPayPollingDTO pollingPayDTO) {
        log.info("向聚合支付平台发起下单轮询请求，请求参数：{}", JSON.toJSONString(pollingPayDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            ResponseEntity<LogicResponse<AggPayPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getPolling(), POST,
                    new HttpEntity<>(pollingPayDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("下单轮询", responseEntity.getBody(),
                    AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingPayDTO.getAttachData()));
        } catch (RestClientException e) {
            log.error("调用聚合支付下单轮询接口异常：{}", e.getMessage());
            return AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingPayDTO.getAttachData());
        }
    }

    @Override
    public Mono<AggPayPollingRespDTO> prePayQueryBankAsync(AggPayPollingDTO pollingPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getQuery())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(pollingPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.boundedElastic())
                .map(logicResponse -> resp(PLACE_AN_ORDER_TO_CHECK_THE_BANK, logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(PLACE_ORDER_QUERY_BANK_REQUEST,
                        JSON.toJSONString(pollingPayDTO)));
    }

    @Override
    public Mono<AggPayPollingRespDTO> payPayQueryBankAsync(AggPayPollingDTO pollingPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getQueryPaySt())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(pollingPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.boundedElastic())
                .map(logicResponse -> resp(PLACE_AN_ORDER_TO_CHECK_THE_BANK, logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(PLACE_ORDER_QUERY_BANK_REQUEST,
                        JSON.toJSONString(pollingPayDTO)));
    }

    @Override
    public Mono<AggRefundRespDTO> refundAsync(AggRefundReqDTO aggRefundReqDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getRefund())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggRefundReqDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggRefundRespDTO>>() {
                })
                .publishOn(Schedulers.boundedElastic())
                .map(logicResponse -> resp("退款", logicResponse,
                        AggRefundRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(REFUND_STRING,
                        JSON.toJSONString(aggRefundReqDTO)));
    }

    @Override
    public AggRefundPollingRespDTO doRefundPolling(AggRefundPollingDTO aggRefundPollingDTO) {
        log.info("向聚合支付平台发起退款轮询请求，请求参数：{}", JSON.toJSONString(aggRefundPollingDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            ResponseEntity<LogicResponse<AggRefundPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getRefundPolling(), POST,
                    new HttpEntity<>(aggRefundPollingDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggRefundPollingRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("退款轮询", responseEntity.getBody(),
                    AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, aggRefundPollingDTO.getAttachData())
            );
        } catch (RestClientException e) {
            log.error("调用聚合支付退款轮询接口异常：{}", e.getMessage());
            return AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, aggRefundPollingDTO.getAttachData());
        }
    }

    @Override
    public Mono<AggRefundPollingRespDTO> doRefundPollingAsync(AggRefundPollingDTO aggRefundPollingDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getRefundPolling())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggRefundPollingDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggRefundPollingRespDTO>>() {
                })
                .publishOn(Schedulers.boundedElastic())
                .map(logicResponse -> resp("退款轮询", logicResponse,
                        AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(REFUND_STRING,
                        JSON.toJSONString(aggRefundPollingDTO)));
    }

    private <T> T resp(String msg, LogicResponse<T> logicResponse, T errorResp) {
        final String errorPre = "聚合支付平台返回";
        if (logicResponse == null) {
            log.error(errorPre + msg + "失败 ：{}", "logicResponse为空");
            return errorResp;
        }
        if (logicResponse.getStatus() == null) {
            log.error(errorPre + msg + "失败：{}", "logicStatus为空");
            return errorResp;
        }
        if (logicResponse.getStatus().getValue() != LogicStatus.SUCCESS.getValue()) {
            log.error(errorPre + msg + "失败，状态值：{}", JSON.toJSONString(logicResponse.getStatus()));
            return errorResp;
        }
        if (logicResponse.getBody() == null) {
            log.error(errorPre + msg + "失败：{}", "业务实体为空");
            return errorResp;
        }
        log.info(errorPre + msg + "结果：{}", JSON.toJSONString(logicResponse));
        return logicResponse.getBody();
    }
}
