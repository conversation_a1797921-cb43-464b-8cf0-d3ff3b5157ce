package com.medusa.gruul.payment.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.egzosn.pay.common.bean.PayMessage;
import com.medusa.gruul.common.member.dto.MemberCardPayDTO;
import com.medusa.gruul.common.member.dto.MemberCardRefundDTO;
import com.medusa.gruul.common.member.enums.OrderTypeEnum;
import com.medusa.gruul.common.member.service.MemberApiService;
import com.medusa.gruul.common.model.constant.CommonPool;
import com.medusa.gruul.common.model.enums.ChangeType;
import com.medusa.gruul.common.model.enums.DistributionMode;
import com.medusa.gruul.common.model.enums.PayType;
import com.medusa.gruul.common.mp.IManualTransaction;
import com.medusa.gruul.common.mp.config.MybatisPlusConfig;
import com.medusa.gruul.common.system.model.ISystem;
import com.medusa.gruul.common.system.model.SystemAutoConfigure;
import com.medusa.gruul.common.system.model.context.SystemContextHolder;
import com.medusa.gruul.common.system.model.model.Systems;
import com.medusa.gruul.order.api.entity.ShopOrder;
import com.medusa.gruul.order.api.rpc.OrderRpcService;
import com.medusa.gruul.payment.api.entity.PaymentHistory;
import com.medusa.gruul.payment.api.entity.PaymentInfo;
import com.medusa.gruul.payment.api.entity.PaymentRefund;
import com.medusa.gruul.payment.api.enums.AggPayStateEnum;
import com.medusa.gruul.payment.api.enums.DealType;
import com.medusa.gruul.payment.api.model.dto.PaymentInfoDTO;
import com.medusa.gruul.payment.api.model.dto.RefundNotifyResultDTO;
import com.medusa.gruul.payment.api.model.dto.agg.*;
import com.medusa.gruul.payment.api.util.ThrowableExtUtils;
import com.medusa.gruul.payment.service.assembler.AggPayMapstructAssembler;
import com.medusa.gruul.payment.service.common.enums.AggRefundStateEnum;
import com.medusa.gruul.payment.service.config.PayConstant;
import com.medusa.gruul.payment.service.model.dto.HandlerPayDTO;
import com.medusa.gruul.payment.service.model.dto.SaasNotifyDTO;
import com.medusa.gruul.payment.service.model.enums.PaymentError;
import com.medusa.gruul.payment.service.model.vo.AggPayAttachDataVO;
import com.medusa.gruul.payment.service.mp.mapper.PaymentHistoryMapper;
import com.medusa.gruul.payment.service.mp.mapper.PaymentRefundMapper;
import com.medusa.gruul.payment.service.mp.service.IPaymentHistoryService;
import com.medusa.gruul.payment.service.mp.service.IPaymentInfoService;
import com.medusa.gruul.payment.service.service.AggPayRedisService;
import com.medusa.gruul.payment.service.service.AggPayService;
import com.medusa.gruul.payment.service.service.AggResultRpcService;
import com.medusa.gruul.payment.service.service.MultiPayOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.context.annotation.Lazy;
import org.apache.dubbo.config.annotation.DubboService;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.springframework.http.HttpMethod.POST;

@Slf4j
@Service
@DubboService(interfaceClass = AggResultRpcService.class)
@RequiredArgsConstructor
public class AggResultRpcServiceImpl implements AggResultRpcService {

    private final AggPayRedisService aggPayRedisService;

    private final IPaymentHistoryService paymentHistoryService;

    private final PaymentHistoryMapper paymentHistoryMapper;

    @Lazy
    @Autowired
    private MultiPayOrderService multiPayOrderService;

    private final OrderRpcService orderRpcService;

    private final MemberApiService memberApiService;
    @Override
    public void handlePayResult(HandlerPayDTO handlerPayBO, AggPayPollingRespDTO pollingRespDTO) {
        log.info("handlePayResult:handlerPayBO:{},AggPayPollingRespDTO:{}", JSON.toJSONString(handlerPayBO),
                JSON.toJSONString(pollingRespDTO));
        if (handlerPayBO == null) {
            return;
        }
        AggPayAttachDataVO aggPayAttachDataVO = JSON.parseObject(pollingRespDTO.getAttachData(), AggPayAttachDataVO.class);
        if (AggPayStateEnum.SUCCESS.getId().equals(pollingRespDTO.getPaySt())) {
            String orderGuid = StringUtils.isEmpty(pollingRespDTO.getOrderGuid()) ? handlerPayBO.getOrderGUID() : pollingRespDTO.getOrderGuid();
            if (StringUtils.isEmpty(orderGuid)) {
                orderGuid = aggPayAttachDataVO.getOrderGuid();
            }
            log.info("支付成功，订单号：{}", orderGuid);

            //补充线程变量
            if (StringUtils.isNotEmpty(aggPayAttachDataVO.getSystems())) {
                Systems systems = JSON.parseObject(aggPayAttachDataVO.getSystems(), Systems.class);
                log.info("聚合支付系统上下文：{}", JSON.toJSONString(systems));
                SystemContextHolder.set(systems);
            }
            // 持久化支付记录
            PaymentInfoDTO paymentInfoDTO = aggPayRedisService.getPayResp(orderGuid);
            updatePaymentInfo(paymentInfoDTO);
        }
        String callBackUrl = handlerPayBO.getInnerCallBackUrl();
        if (StringUtils.isEmpty(callBackUrl)) {
            log.info("callBackUrl is null");
        }
    }

    private void updatePaymentInfo(PaymentInfoDTO paymentInfoDTO) {
        log.info("聚合支付更新支付记录：{}", JSON.toJSONString(paymentInfoDTO));
        if (Objects.isNull(paymentInfoDTO)) {
            log.info("聚合支付更新支付记录明细已存在，直接返回");
            return;
        }
        Systems mainThreadSystem = ISystem.systemOpt().getOrNull();
        log.info("mainThreadSystem：{}", JSON.toJSONString(mainThreadSystem));
        Long num = paymentHistoryMapper.selectCount(new LambdaQueryWrapper<PaymentHistory>()
                .eq(PaymentHistory::getOrderGuid, paymentInfoDTO.getOrderNum()));
        log.info("聚合支付更新支付记录明细已存在数量：{}", num);
        if (num > 0) {
            log.info("聚合支付更新支付记录明细已存在，直接返回");
            return;
        }
        //生成明细
        PaymentHistory paymentHistory = new PaymentHistory();
        paymentHistory.setDealType(DealType.SHOPPING_PURCHASE);
        paymentHistory.setUserId(paymentInfoDTO.getUserId());
        paymentHistory.setSubject(paymentInfoDTO.getSubject());
        paymentHistory.setMoney(paymentInfoDTO.getTotalFee());
        paymentHistory.setChangeType(ChangeType.REDUCE);
        paymentHistory.setPayType(PayType.AGGREGATION);
        paymentHistory.setOrderGuid(paymentInfoDTO.getOrderNum());
        boolean isSuccess = paymentHistoryService.save(paymentHistory);
        log.info("聚合支付更新支付记录明细结果：{}", isSuccess);

        //封装回调数据
        Map<String, Object> payMessageInfo = new HashMap<>(CommonPool.NUMBER_FIVE);
        payMessageInfo.put("subject", paymentInfoDTO.getSubject());
        payMessageInfo.put("total_amount", paymentInfoDTO.getTotalFee());
        payMessageInfo.put("out_trade_no", paymentInfoDTO.getOrderNum());
        payMessageInfo.put("notify_id", "16".concat(MybatisPlusConfig.IDENTIFIER_GENERATOR.nextId(new PaymentInfo()).toString()));
        PayMessage payMessage = new PayMessage(payMessageInfo);
        log.info("聚合支付更新支付记录PayMessage：{}", JSON.toJSONString(payMessage));
        multiPayOrderService.success(payMessage);
    }

    /**
     * 构建会员卡支付参数
     *
     * @param paymentInfoDTO 支付信息
     * @return 支付参数
     */
    private List<MemberCardPayDTO> buildMemberCardPayDTO(PaymentInfoDTO paymentInfoDTO) {
        final BigDecimal divisor = CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND;
        final RoundingMode roundingMode = RoundingMode.HALF_EVEN;

        // 确定最终订单类型
        final Integer orderType = Optional.ofNullable(paymentInfoDTO.getOrderType())
                .orElseGet(() -> Optional.ofNullable(orderRpcService.getOrder(paymentInfoDTO.getOrderNum()))
                        .map(order -> getOrderType(order.getDistributionMode()))
                        .orElseThrow(PaymentError.MEMBER_CARD_PAY_ERROR::exception));

        // 获取并验证店铺订单
        List<ShopOrder> shopOrders = orderRpcService.getShopOrderList(paymentInfoDTO.getOrderNum());
        if (CollUtil.isEmpty(shopOrders)) {
            throw PaymentError.MEMBER_CARD_PAY_ERROR.exception();
        }

        // 提取当前时间
        final LocalDateTime now = LocalDateTime.now();

        // 构建DTO列表
        return shopOrders.stream()
                .map(shopOrder -> createMemberCardPayDTO(paymentInfoDTO, shopOrder, orderType, divisor, roundingMode, now))
                .collect(Collectors.toList());
    }

    private MemberCardPayDTO createMemberCardPayDTO(PaymentInfoDTO paymentInfoDTO, ShopOrder shopOrder,
                                                    Integer orderType, BigDecimal divisor,
                                                    RoundingMode roundingMode, LocalDateTime now) {
        return new MemberCardPayDTO()
                .setMemberInfoGuid(String.valueOf(paymentInfoDTO.getUserId()))
                .setMemberInfoCardGuid(paymentInfoDTO.getMemberInfoCardGuid())
                .setStoreGuid(String.valueOf(shopOrder.getShopId()))
                .setStoreName(shopOrder.getShopName())
                .setOrderNumber(shopOrder.getNo())
                .setOrderType(orderType)
                .setOrderTime(now)
                .setOrderAmount(convertToBigDecimal(shopOrder.getTotalAmount(), divisor, roundingMode))
                .setOrderDiscountAmount(convertToBigDecimal(shopOrder.getDiscountAmount(), divisor, roundingMode))
                .setCardBalancePayAmount(convertToBigDecimal(shopOrder.getTotalAmount() - shopOrder.getDiscountAmount(), divisor, roundingMode));
    }

    private BigDecimal convertToBigDecimal(long value, BigDecimal divisor, RoundingMode roundingMode) {
        return BigDecimal.valueOf(value).divide(divisor, 2, roundingMode);
    }

    /**
     * 获取订单类型
     *
     * @param distributionMode 配送方式
     * @return 订单类型
     */
    private int getOrderType(DistributionMode distributionMode) {
        if (distributionMode == null) {
            return OrderTypeEnum.MALL_ORDER_VIRTUAL.getCode();
        }
        return switch (distributionMode) {
            case EXPRESS -> OrderTypeEnum.MALL_ORDER_EXPRESS.getCode();
            case INTRA_CITY_DISTRIBUTION -> OrderTypeEnum.MALL_ORDER_INTRA_CITY_DISTRIBUTION.getCode();
            case SHOP_STORE -> OrderTypeEnum.MALL_ORDER_SHOP_STORE.getCode();
            default -> OrderTypeEnum.MALL_ORDER_VIRTUAL.getCode();
        };
    }

    @Override
    public void handleRefundResult(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingRespDTO
            aggRefundPollingRespDTO) {
        List<AggRefundDetailRespDTO> refundOrderDetail = aggRefundPollingRespDTO.getRefundOrderDetial();
        if (!CollectionUtils.isEmpty(refundOrderDetail)) {
            AggRefundDetailRespDTO aggRefundDetailRespDTO = refundOrderDetail.get(refundOrderDetail.size() - 1);
            if (AggRefundStateEnum.REFUND_SUCCESS.getState().equals(aggRefundDetailRespDTO.getStatus())) {
                String orderNum = saasAggRefundDTO.getAggRefundReqDTO().getOrderGUID();
                BigDecimal refundFee = new BigDecimal(saasAggRefundDTO.getAggRefundReqDTO().getRefundFeeLong())
                        .divide(CommonPool.BIG_DECIMAL_UNIT_CONVERSION_TEN_THOUSAND, 2, RoundingMode.HALF_EVEN);
                //同步会员后台
                MemberCardRefundDTO memberCardRefundDTO = new MemberCardRefundDTO()
                        .setOrderNumber(orderNum)
                        .setRefundAmount(refundFee);
                try {
                    Boolean flag = memberApiService.memberCardRefund(memberCardRefundDTO);
                    log.info("会员支付记录退款同步结果：{}", flag);
                }catch (Exception ex){
                    log.error("会员卡退款同步失败：{}", ThrowableExtUtils.asStringIfAbsent(ex));
                }
            }
        }
    }
}
