package com.medusa.gruul.payment.service.controller;

import com.github.binarywang.wxpay.bean.transfer.TransferNotifyResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.medusa.gruul.common.log.annotation.Log;
import com.medusa.gruul.common.model.resp.Result;
import com.medusa.gruul.overview.api.rpc.WithdrawRpcService;
import com.medusa.gruul.payment.api.enums.NotifyStatus;
import com.medusa.gruul.payment.service.model.HttpRequestNoticeParams;
import com.medusa.gruul.payment.service.service.MultiPayNotifyService;
import com.medusa.gruul.payment.service.service.MultiPayOrderService;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 商户支付控制层
 *
 * <AUTHOR>
 * @since 2022-07-27 14:33
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/merchant/pay")
@Slf4j
public class MultiPayController {

    private final MultiPayNotifyService multiPayNotifyService;
    private final MultiPayOrderService multiPayOrderService;
    private final WxPayService wxPayService;
    private final WithdrawRpcService withdrawRpcService;

    /**
     * 业务回调
     *
     * @param request   回调请求体
     * @param detailsId 支付商户表id
     * @return 支付数据
     */
    @Log("回调")
    @RequestMapping(value = "notify/{detailsId}", produces = MediaType.TEXT_PLAIN_VALUE)
    public String payNotify(HttpServletRequest request, @PathVariable String detailsId) {
        return multiPayNotifyService.payNotify(detailsId, new HttpRequestNoticeParams(request));
    }

    @Log("商家转账到零钱回调")
    @RequestMapping("/transfer/notify")
    public ResponseEntity<String> transferNotify(@RequestBody String json) {
        try {
            TransferNotifyResult transferNotifyResult = wxPayService.getTransferService().parseTransferNotifyResult(json, null);
            TransferNotifyResult.DecryptNotifyResult result = transferNotifyResult.getResult();
            String outBatchNo = result.getOutBatchNo();
            String batchStatus = result.getBatchStatus();
            String closeReason = result.getCloseReason();
            withdrawRpcService.updateWithdrawOrderStatus(outBatchNo,batchStatus,closeReason);
        } catch (WxPayException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
        return ResponseEntity.ok("success");

    }

    /**
     * 根据业务订单号获取业务订单支付状态
     *
     * @param outTradeNo 业务订单号
     * @return NotifyStatus.java
     **/
    @GetMapping("order/status")
    @PreAuthorize("@S.matcher().role(@S.R.USER).match()")
    @Log("支付轮训")
    public Result<NotifyStatus> orderPayStatus(@NotNull String outTradeNo) {
        return Result.ok(multiPayOrderService.orderPayStatus(outTradeNo));
    }



}
