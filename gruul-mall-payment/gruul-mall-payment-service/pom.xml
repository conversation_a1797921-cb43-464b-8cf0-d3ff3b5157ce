<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.medusa.gruul</groupId>
        <artifactId>gruul-mall-payment</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gruul-mall-payment-service</artifactId>
    <properties>
        <pay.version>2.14.6</pay.version>
        <pay-spring-boot.version>1.0.3</pay-spring-boot.version>
    </properties>

    <repositories>
        <repository>
            <id>holderzone-snapshots</id>
            <name>HolderZone Snapshot Repository</name>
            <url>http://nexus.holderzone.cn/repository/maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <artifactId>gruul-mall-overview-api</artifactId>
            <groupId>com.medusa.gruul</groupId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-payment-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--user api-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-user-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--uaa api-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-uaa-api</artifactId>
            <version>1.0</version>
        </dependency>
        <!--order api-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-order-api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-shop-api</artifactId>
            <version>1.0</version>
        </dependency>

        <!-- service-->
        <dependency>
            <artifactId>gruul-common-module-service</artifactId>
            <groupId>com.medusa.gruul</groupId>
        </dependency>

        <!-- mq -->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-mq-rabbit</artifactId>
        </dependency>

        <!-- member-->
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-common-member</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- 聚合pay-->
        <dependency>
            <groupId>com.egzosn</groupId>
            <artifactId>pay-spring-boot-starter</artifactId>
            <version>${pay-spring-boot.version}</version>
        </dependency>

        <dependency>
            <groupId>com.egzosn</groupId>
            <artifactId>pay-java-web-support</artifactId>
            <version>${pay.version}</version>
        </dependency>

        <!--支付渠道——ali -->
        <dependency>
            <groupId>com.egzosn</groupId>
            <artifactId>pay-java-ali</artifactId>
            <version>${pay.version}</version>
        </dependency>

        <!--支付渠道——wx -->
        <dependency>
            <groupId>com.egzosn</groupId>
            <artifactId>pay-java-wx</artifactId>
            <version>${pay.version}</version>
        </dependency>


        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
<!--            <version>${weixin-java-miniapp.verison}</version>-->
            <version>4.6.6.B</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.13</version>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>gruul-mall-afs-api</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.medusa.gruul</groupId>
            <artifactId>addon-coupon</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>config/</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>com.medusa.gruul.payment.service.PaymentApplication</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>config/*</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <configuration>
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <excludes>
                        <exclude>com.google.guava:guava</exclude>
                    </excludes>
                    <includes>
                        <include>com.medusa.gruul:.*</include>
                        <include>com.medusa.gruul.global:.*</include>
                        <include>com.baomidou:mybatis-plus-extension</include>
                        <include>com.baomidou:mybatis-plus-core</include>
                        <include>cn.hutool:hutool-all</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>