# 使用 Ubuntu Jammy 基础镜像（基于 Eclipse Temurin 的镜像）
FROM eclipse-temurin:17.0.15_6-jdk-jammy

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo 'Asia/Shanghai' > /etc/timezone

# 设置环境变量
ENV TZ=Asia/Shanghai

ADD config/ /config/
ADD gruul-mall-payment-service-1.0.jar gruul-mall-payment-service-1.0.jar
ADD lib/ /lib/
ENTRYPOINT ["java","-jar","-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:39210","--add-opens=java.base/java.lang=ALL-UNNAMED","--add-opens=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED","--add-opens=java.base/java.util=ALL-UNNAMED","--add-opens=java.base/java.util.concurrent=ALL-UNNAMED","--add-opens=java.base/java.math=ALL-UNNAMED","gruul-mall-payment-service-1.0.jar"]
