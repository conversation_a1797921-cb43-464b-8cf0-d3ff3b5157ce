package com.medusa.gruul.payment.api.util;


import com.medusa.gruul.common.model.resp.SystemCode;
import com.medusa.gruul.global.model.exception.GlobalException;
import com.medusa.gruul.payment.api.model.dto.agg.*;

public class ValidatorUtils {

    private static final String PAY_GUID = "支付Guid";
    private static final String ORDER_GUID = "订单Guid";

    private ValidatorUtils() {

    }

    private static void validator(Validator validator) {
        if (!validator.isValid()) {
            throw new GlobalException(SystemCode.REQ_REJECT_CODE, "参数校验失败");
        }
    }

    public static void validatePrePay(SaasAggPayDTO saasAggPayDTO) {
        Validator validator = Validator.create()
                .notNull(saasAggPayDTO.getReqDTO(), "支付请求实体");
        if (saasAggPayDTO.getReqDTO() != null) {
            AggPayPreTradingReqDTO reqDTO = saasAggPayDTO.getReqDTO();
            validator.notBlank(reqDTO.getOrderGUID(), "商户方订单编号")
                    .notBlank(reqDTO.getPayGUID(), "支付唯一标示")
                    .notNull(reqDTO.getAmount(), "支付金额")
                    .notBlank(reqDTO.getGoodsName(), "商品名称")
                    .notBlank(reqDTO.getBody(), "订单描述");
        }
        validator(validator);
    }

    public static void validatePolling(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validateRefund(SaasAggRefundDTO saasAggRefundDTO) {
        Validator validator = Validator.create()
                .notNull(saasAggRefundDTO.getAggRefundReqDTO(), "退款请求实体");
        if (saasAggRefundDTO.getAggRefundReqDTO() != null) {
            AggRefundReqDTO aggRefundReqDTO = saasAggRefundDTO.getAggRefundReqDTO();
            validator.notBlank(aggRefundReqDTO.getPayGUID(), PAY_GUID)
                    .notBlank(aggRefundReqDTO.getOrderGUID(), ORDER_GUID)
                    .notNull(aggRefundReqDTO.getRefundType(), "退款类型")
                    .notNull(aggRefundReqDTO.getRefundFee(), "退款金额")
                    .notNull(aggRefundReqDTO.getReason(), "退款理由");
        }
        validator(validator);
    }

    public static void validateRefundPolling(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validateQueryResult(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    private static void validateSaasPollingDTO(SaasPollingDTO saasPollingDTO) {
        Validator validator = Validator.create()
                .notBlank(saasPollingDTO.getPayGuid(), PAY_GUID)
                .notBlank(saasPollingDTO.getOrderGuid(), ORDER_GUID);
        validator(validator);
    }


}
