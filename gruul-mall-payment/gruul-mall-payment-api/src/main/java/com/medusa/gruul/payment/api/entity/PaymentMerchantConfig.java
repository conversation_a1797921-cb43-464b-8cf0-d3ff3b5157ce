package com.medusa.gruul.payment.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.mp.model.BaseEntity;
import com.medusa.gruul.common.system.model.model.Platform;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 * 商户配置支付信息
 *
 *
 * <AUTHOR>
 * @ description 商户配置支付信息
 * @date 2022-07-12 15:26
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_payment_merchant_config")
public class PaymentMerchantConfig extends BaseEntity {

    /**
     * 商户渠道配置表id
     */
    private String detailsId;

    private Long platformId;

    /**
     * 商户id
     */
    private Long shopId;

    /**
     *
     * 可支付平台类型
     */
    private Platform platform;
}
