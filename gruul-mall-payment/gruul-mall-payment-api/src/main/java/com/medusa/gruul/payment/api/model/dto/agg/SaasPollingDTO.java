package com.medusa.gruul.payment.api.model.dto.agg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Saas轮询DTO
 * 用于轮询支付状态
 */
@ApiModel
@Data
public class SaasPollingDTO extends BaseDTO {

    @ApiModelProperty("订单号")
    private String orderGuid;

    @ApiModelProperty("支付guid")
    private String payGuid;

    /**
     * 商户私钥
     */
    private String keyPrivate;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 1 会员营收、2 门店营收
     */
    private Integer revenueType;

}
