package com.medusa.gruul.payment.api.model.dto.agg;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/** * 聚合支付回调DTO
 * 用于接收聚合支付的回调信息
 */
@Data
public class AggPayCallbackDTO implements Serializable {

    private String code;
    private String msg;
    private String orderNo;
    private String orderHolderNo;
    private String orderGuid;
    private String payGuid;
    private String payPowerId;
    private String code_url;
    private String authUrl;
    private String bankTransactionId;
    private String prepay_info;
    private String mweb_url;
    private String orderDt;
    private String fee;
    private String paidTime;
    private String openId;
    private String body;
    private String subject;
    private String paySt;
    private String signature;
    private String jdResult;
    private String attachData;
    /**
     * 银行流水号
     */
    private String bankOrderNo;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付成功时间
     */
    private LocalDateTime successTime;

    /**
     * 商户订单号
     */
    private String merchantOrderNo;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 原始通知数据
     */
    private String rawData;
}
