package com.medusa.gruul.payment.api.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 会员营收类型枚举
 */
@Getter
public enum MemberRevenueTypeEnum {

    /**
     * 会员卡充值
     */
    MEMBER_CARD_RECHARGE(1, "会员卡充值"),

    /**
     * 用户开通付费会员
     */
    MEMBER_VIP_OPEN(2, "用户开通付费会员"),

    /**
     * 会员卡开卡
     */
    MEMBER_CARD_OPEN(3, "会员卡开卡");
    
    

    /**
     * 编码
     */
    @EnumValue
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    MemberRevenueTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static MemberRevenueTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getCode(), code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据描述获取枚举
     *
     * @param desc 描述
     * @return 枚举值
     */
    public static MemberRevenueTypeEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getDesc(), desc))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否包含指定的code
     *
     * @param code 编码
     * @return true-包含，false-不包含
     */
    public static boolean containsCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否包含指定的描述
     *
     * @param desc 描述
     * @return true-包含，false-不包含
     */
    public static boolean containsDesc(String desc) {
        return getByDesc(desc) != null;
    }

    /**
     * 获取所有的code
     *
     * @return code数组
     */
    public static Integer[] getCodes() {
        return Arrays.stream(values())
                .map(MemberRevenueTypeEnum::getCode)
                .toArray(Integer[]::new);
    }

    /**
     * 获取所有的描述
     *
     * @return 描述数组
     */
    public static String[] getDescs() {
        return Arrays.stream(values())
                .map(MemberRevenueTypeEnum::getDesc)
                .toArray(String[]::new);
    }

    @Override
    public String toString() {
        return String.format("%s(%d, %s)", name(), code, desc);
    }
} 